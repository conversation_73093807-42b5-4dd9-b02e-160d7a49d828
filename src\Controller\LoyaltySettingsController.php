<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;

use Cake\Core\Configure;

/**
 * LoyaltySettings Controller
 *
 * @property \App\Model\Table\LoyaltySettingsTable $LoyaltySettings
 */
class LoyaltySettingsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\CustomerGroupsTable $CustomerGroups;
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->CustomerGroups = $this->fetchTable('CustomerGroups');
    }


    public function index()
    {
        $query = $this->LoyaltySettings->find()
            ->where(['LoyaltySettings.status !=' => 'D'])
            ->contain(['CustomerGroups'])
            ->applyOptions(['order' => ['LoyaltySettings.id' => 'ASC']]);

        $loyaltySettings = $query->all();

        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('loyaltySettings', 'status', 'statusMap', 'decimalSeparator', 'thousandSeparator', 'currencySymbol'));
    }

    /**
     * View method
     *
     * @param string|null $id Loyalty Setting id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $loyaltySetting = $this->LoyaltySettings->get($id, contain: ['CustomerGroups']);
        $statuses = Configure::read('Constants.STATUS');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';
        $this->set(compact('loyaltySetting','statuses','decimalSeparator', 'thousandSeparator','currencySymbol',));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $loyaltySetting = $this->LoyaltySettings->newEmptyEntity();
        if ($this->request->is('post')) {
            $loyaltySetting = $this->LoyaltySettings->patchEntity($loyaltySetting, $this->request->getData());
            if ($this->LoyaltySettings->save($loyaltySetting)) {
                $this->Flash->success(__('The loyalty setting has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The loyalty setting could not be saved. Please, try again.'));
        }
        $customerGroup = $this->CustomerGroups->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($customerGroup)) {
            $customerGroup = ['' => 'No Customer Group available'];
        }
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $this->set(compact('loyaltySetting', 'customerGroup', 'currencySymbol'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Loyalty Setting id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $loyaltySetting = $this->LoyaltySettings->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $loyaltySetting = $this->LoyaltySettings->patchEntity($loyaltySetting, $this->request->getData());
            if ($this->LoyaltySettings->save($loyaltySetting)) {
                $this->Flash->success(__('The loyalty setting has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The loyalty setting could not be saved. Please, try again.'));
        }
        $statuses = Configure::read('Constants.STATUS');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $customerGroup = $this->CustomerGroups->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($customerGroup)) {
            $customerGroup = ['' => 'No Customer Group available'];
        }
        $this->set(compact('loyaltySetting', 'customerGroup','statuses','currencySymbol'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Loyalty Setting id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $loyaltySettings = $this->LoyaltySettings->get($id);
        $response = ['success' => false, 'message' => 'The Loyalty Settings could not be deleted. Please, try again.'];
        if ($loyaltySettings) {
            if ($this->LoyaltySettings->delete($loyaltySettings)) {
                $response = ['success' => true, 'message' => 'The Loyalty Settings has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Loyalty Settings could not be deleted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Loyalty Settings does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
