<?php
declare(strict_types=1);

namespace App\Controller;

use Cake\ORM\TableRegistry;
use Cake\Core\Configure;

/**
 * SupportCategories Controller
 *
 * Manages both Parent and Child Categories
 */
class SupportCategoriesController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }
    public function index()
    {
        // Fetch parent categories with related child categories
        $supportCategories = $this->SupportCategories->find('all', [
            'contain' => ['SupportSubcategories']
        ]);
        $this->set(compact('supportCategories'));
    }

    public function add()
    {
        $supportCategory = $this->SupportCategories->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (!empty($data['is_child']) && $data['is_child'] === '1') {
                if (empty($data['support_category_id'])) {
                    $this->Flash->error(__('Please select a parent category.'));
                    return $this->redirect($this->referer());
                }
                $supportSubCategoryTable = TableRegistry::getTableLocator()->get('SupportSubcategories');
                $childCategory = $supportSubCategoryTable->newEntity($data);

                if ($supportSubCategoryTable->save($childCategory)) {
                    $this->Flash->success(__('Child category has been saved.'));
                    return $this->redirect(['action' => 'index']);
                }
                $this->Flash->error(__('Unable to save child category. Please try again.'));
            } else {
                // Add parent category
                $supportCategory = $this->SupportCategories->patchEntity($supportCategory, $data);
                if ($this->SupportCategories->save($supportCategory)) {
                    $this->Flash->success(__('Parent category has been saved.'));
                    return $this->redirect(['action' => 'index']);
                }
                $this->Flash->error(__('Unable to save parent category. Please try again.'));
            }
        }

        // Fetch parent categories for dropdown (if adding a child category)
        $parentCategories = $this->SupportCategories->find('list', ['limit' => 200]);
        $this->set(compact('supportCategory', 'parentCategories'));
    }

    public function edit($id = null)
    {
        $isChild = (bool) $this->request->getQuery('is_child', false);
        if ($isChild) {
            // Handle Child Category Editing
            $supportSubCategoryTable = TableRegistry::getTableLocator()->get('SupportSubcategories');
            $supportCategory = $supportSubCategoryTable->get($id);

            if ($this->request->is(['patch', 'post', 'put'])) {
                $data = $this->request->getData();
                if (empty($data['support_category_id'])) {
                    $this->Flash->error(__('Please select a parent category.'));
                    return $this->redirect($this->referer());
                }
                $supportCategory = $supportSubCategoryTable->patchEntity($supportCategory, $data);

                if ($supportSubCategoryTable->save($supportCategory)) {
                    $this->Flash->success(__('Child category has been updated.'));
                    return $this->redirect(['action' => 'index']);
                }
                $this->Flash->error(__('Unable to update child category. Please try again.'));
            }
        } else {
            // Handle Parent Category Editing
            $supportCategory = $this->SupportCategories->get($id, ['contain' => []]);

            if ($this->request->is(['patch', 'post', 'put'])) {
                $data = $this->request->getData();
                $supportCategory = $this->SupportCategories->patchEntity($supportCategory, $data);

                if ($this->SupportCategories->save($supportCategory)) {
                    $this->Flash->success(__('Parent category has been updated.'));
                    return $this->redirect(['action' => 'index']);
                }
                $this->Flash->error(__('Unable to update parent category. Please try again.'));
            }
        }

        // Fetch parent categories for dropdown (only for child categories)
        $parentCategories = $this->SupportCategories->find('list', ['limit' => 200]);
        $statuses = Configure::read('Constants.STATUS');
        $this->set(compact('supportCategory', 'isChild', 'parentCategories','statuses'));
    }


    public function delete(?string $id = null)
    {
        $response = ['success' => false, 'message' => 'Invalid request.'];

        if ($this->request->is(['post', 'ajax', 'delete'])) {
            // Get the isChild flag from query parameters
            $isChild = $this->request->getQuery('is_child', false);

            try {
                if ($isChild) {
                    // Delete from child category table
                    $supportSubCategoryTable = TableRegistry::getTableLocator()->get('SupportSubcategories');
                    $childCategory = $supportSubCategoryTable->get($id);
                    $childCategory->status = 'D'; // Soft delete (optional)

                    if ($supportSubCategoryTable->save($childCategory)) {
                        $response = ['success' => true, 'message' => 'The child category has been deleted.'];
                    } else {
                        $response = ['success' => false, 'message' => 'Unable to delete the child category.'];
                    }
                } else {
                    // Delete from parent category table
                    $supportCategory = $this->SupportCategories->get($id);
                    $supportCategory->status = 'D'; // Soft delete (optional)

                    if ($this->SupportCategories->save($supportCategory)) {
                        $response = ['success' => true, 'message' => 'The parent category has been deleted.'];
                    } else {
                        $response = ['success' => false, 'message' => 'Unable to delete the parent category.'];
                    }
                }
            } catch (\Exception $e) {
                $response = ['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()];
            }
        }

        // Handle AJAX Response
        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
            return $this->response;
        } else {
            // Fallback for non-AJAX requests
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
