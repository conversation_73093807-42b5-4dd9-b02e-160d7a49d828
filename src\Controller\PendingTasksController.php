<?php

namespace App\Controller;

use App\Controller\AppController;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;

class PendingTasksController extends AppController
{
    protected $CashDesks;
    protected $StockRequests;
    protected $Orders;
    protected $SupplierPurchaseOrders;
    protected $ShowroomExpenses;
    protected $Shipments;
    protected $Users;
    protected $Roles;
    protected $Showrooms;
    protected $Warehouses;

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Global'); // Load Global Component for email & push notifications

        // Load necessary models
        $this->CashDesks = TableRegistry::getTableLocator()->get('CashDesks');
        $this->StockRequests = TableRegistry::getTableLocator()->get('StockRequests');
        $this->Orders = TableRegistry::getTableLocator()->get('Orders');
        $this->SupplierPurchaseOrders = TableRegistry::getTableLocator()->get('SupplierPurchaseOrders');
        $this->ShowroomExpenses = TableRegistry::getTableLocator()->get('ShowroomExpenses');
        $this->Shipments = TableRegistry::getTableLocator()->get('Shipments');
        $this->Users = TableRegistry::getTableLocator()->get('Users');
        $this->Roles = TableRegistry::getTableLocator()->get('Roles');
        $this->Showrooms = TableRegistry::getTableLocator()->get('Showrooms');
        $this->Warehouses = TableRegistry::getTableLocator()->get('Warehouses');
    }

    /**
     * Check and send reminders for pending tasks.
     */

    // public function sendPendingTaskNotifications()
    // {
    //     $tasks = [];

    //     // 1. Check if stock verification/approval is pending
    //     $pendingStockVerifications = $this->StockRequests->find()
    //         ->where([
    //             'requestor_type' => 'Showroom',
    //             'request_status' => 'Pending',
    //             'status' => 'A'
    //         ])
    //         ->count();

    //     if ($pendingStockVerifications > 0) {
    //         $tasks[] = "$pendingStockVerifications stock verification/approval request" . ($pendingStockVerifications > 1 ? "s are" : " is") . " pending.";
    //     }

    //     // 2. Check pending supplier purchase order payments
    //     $pendingSupplierPayments = $this->SupplierPurchaseOrders->find()
    //         ->where([
    //             'payment_status' => 'Pending',
    //             'status' => 'A'
    //         ])
    //         ->count();

    //     if ($pendingSupplierPayments > 0) {
    //         $tasks[] = "$pendingSupplierPayments supplier payment" . ($pendingSupplierPayments > 1 ? "s need" : " needs") . " approval.";
    //     }

    //     // 3. Check pending showroom expenses
    //     $pendingShowroomExpenses = $this->ShowroomExpenses->find()
    //         ->where([
    //             'payment_status' => 'Unpaid',
    //             'status' => 'A'
    //         ])
    //         ->count();

    //     if ($pendingShowroomExpenses > 0) {
    //         $tasks[] = "$pendingShowroomExpenses showroom expense" . ($pendingShowroomExpenses > 1 ? "s need" : " needs") . " approval.";
    //     }

    //     // 4. Check pending order assignments from shipments table
    //     $pendingOrderAssignments = $this->Shipments->find()
    //         ->where([
    //             'stock_type' => 'In Stock',
    //             'assignment_status' => 'unassigned',
    //             'delivery_status' => 'pending'
    //         ])
    //         ->count();

    //     if ($pendingOrderAssignments > 0) {
    //         $tasks[] = "$pendingOrderAssignments order" . ($pendingOrderAssignments > 1 ? "s are" : " is") . " pending assignment.";
    //     }

    //     // 5. Check pending deliveries from shipments table
    //     $pendingOrderDeliveries = $this->Shipments->find()
    //         ->where([
    //             'stock_type' => 'In Stock',
    //             'assignment_status' => 'assigned',
    //             'delivery_status' => 'pending'
    //         ])
    //         ->count();

    //     if ($pendingOrderDeliveries > 0) {
    //         $tasks[] = "$pendingOrderDeliveries order" . ($pendingOrderDeliveries > 1 ? "s are" : " is") . " pending delivery.";
    //     }

    //     // If there are pending tasks, send notifications
    //     if (!empty($tasks)) {
    //         $taskList = "- " . implode("\n- ", $tasks);
    //         $subject = 'Urgent: Pending Tasks Notification';
    //         $emailMessage = "The following tasks are still pending:\n\n" . $taskList;

    //         $to = "<EMAIL>";

    //         // Send Email Notification
    //         $this->Global->send_email(
    //             $to,
    //             null, // From email (uses default if null)
    //             $subject,
    //             'pending_tasks', // Email template
    //             ['tasks' => $tasks]
    //         );

    //         // Get Admin Push Notification Token (Modify as needed)
    //         $adminDeviceToken = ['example_device_token_123'];

    //         // Send Push Notification
    //         $this->Global->pushNotification(
    //             [
    //                 'title' => 'Pending Tasks Alert',
    //                 'message' => $taskList
    //             ],
    //             $adminDeviceToken
    //         );
    //     }
    // }

    public function sendPendingTaskNotifications()
    {
        // Fetch all relevant users
        $users = $this->Users->find()
            ->where(['Users.status' => 'A'])
            ->contain(['Roles', 'Showrooms']) // Ensure roles and showrooms are loaded
            ->all();

        // echo "<pre>";print_r($users);die;

        // Store user references by role & showroom
        $showroomManagers = [];
        $showroomSupervisors = [];
        $warehouseManagers = [];
        $admins = [];

        foreach ($users as $user) {
            $roleName = strtolower($user->role->name);

            if ($roleName === 'showroom manager') {
                $showroomManagers[$user->showroom_id][] = $user;
            } elseif ($roleName === 'showroom supervisor') {
                $showroomSupervisors[$user->showroom_id][] = $user;
            } elseif ($roleName === 'warehouse manager') {
                $warehouseManagers[$user->id] = $user;
            } elseif ($roleName === 'admin') {
                $admins[] = $user;
            }
        }

        // echo "<pre>";print_r($showroomManagers);die;

        // 1. Pending Stock Verification → Send to Showroom Manager & Showroom Supervisor
        // $pendingStockVerifications = $this->StockRequests->find()
        //     ->where([
        //         'requestor_type' => 'Showroom',
        //         'request_status' => 'Pending',
        //         'status' => 'A'
        //     ])
        //     ->all();

        // foreach ($pendingStockVerifications as $request) {

        //     $showroom = $this->Showrooms->find()
        //         ->where(['id' => $request->showroom_id])
        //         ->first();

        //     if ($showroom && !empty($showroom->showroom_supervisor)) {
        //         $supervisor = $showroom->showroom_supervisor; // Get the first supervisor

        //         // Assuming the supervisor ID refers to a user in the Users table
        //         $supervisorUser = $this->Users->find()
        //             ->where(['id' => $supervisor])
        //             ->first();

        //         if ($supervisorUser && !empty($supervisorUser->email)) {
        //             $tasks = ["Stock verification request is pending for your showroom."];
        //             // $this->sendEmailsToUsers([$supervisorUser], 'Pending Stock Verification', $tasks);
        //         }
        //     }
        // }


        $pendingStockBySupervisor = [];

        // Fetch all pending stock verification requests
        $pendingStockVerifications = $this->StockRequests->find()
            ->where([
                'requestor_type' => 'Showroom',
                'request_status' => 'Pending',
                'status' => 'A'
            ])
            ->all();

        foreach ($pendingStockVerifications as $request) {
            $showroom = $this->Showrooms->find()
                ->where(['id' => $request->showroom_id])
                ->first();

            if ($showroom && !empty($showroom->showroom_supervisor)) {
                $supervisorId = $showroom->showroom_supervisor;

                // Count pending requests per supervisor
                if (!isset($pendingStockBySupervisor[$supervisorId])) {
                    $pendingStockBySupervisor[$supervisorId] = 0;
                }
                $pendingStockBySupervisor[$supervisorId]++;
            }
        }

        // Send grouped email notifications
        foreach ($pendingStockBySupervisor as $supervisorId => $stockCount) {
            $supervisorUser = $this->Users->find()
                ->where(['id' => $supervisorId])
                ->first();

            if ($supervisorUser && !empty($supervisorUser->email)) {
                $tasks = ["$stockCount Stock verification request" . ($stockCount > 1 ? "s are" : " is") . " pending for your showroom."];
                $this->sendEmailsToUsers([$supervisorUser], 'Pending Stock Verification', $tasks);
            }
        }
        die;

        // 2. Pending Supplier Payments → Send to Warehouse Manager responsible
        $pendingSupplierPayments = $this->SupplierPurchaseOrders->find()
            ->where(['deliver_to' => 'Warehouse', 'payment_status' => 'Pending', 'status' => 'A'])
            ->all();

        // echo "<pre>";print_r($pendingSupplierPayments);die;

        // foreach ($pendingSupplierPayments as $payment) {
        //     // Fetch the warehouse manager ID from the Warehouses table
        //     $warehouse = $this->Warehouses->find()
        //         ->where(['id' => $payment->id_deliver_to])
        //         ->first();

        //     // echo "<pre>";print_r($warehouse);

        //     if ($warehouse && !empty($warehouse->manager_id)) {
        //         // Fetch the warehouse manager's details from Users table
        //         $warehouseManager = $this->Users->find()
        //             ->where(['id' => $warehouse->manager_id])
        //             ->first();

        //         // echo "<pre>";print_r($warehouseManager);

        //         if ($warehouseManager && !empty($warehouseManager->email)) {
        //             $tasks = [" "];
        //             $this->sendEmailsToUsers([$warehouseManager], 'Pending Supplier Payment', $tasks);
        //         }
        //     }
        // }

        $pendingPaymentsByManager = [];

        foreach ($pendingSupplierPayments as $payment) {
            // Fetch warehouse details
            $warehouse = $this->Warehouses->find()
                ->where(['id' => $payment->id_deliver_to])
                ->first();

            if ($warehouse && !empty($warehouse->manager_id)) {
                // Store pending payment count per manager
                if (!isset($pendingPaymentsByManager[$warehouse->manager_id])) {
                    $pendingPaymentsByManager[$warehouse->manager_id] = 0;
                }
                $pendingPaymentsByManager[$warehouse->manager_id]++;
            }
        }

        // echo '<pre>';print_r($pendingPaymentsByManager);die;

        foreach ($pendingPaymentsByManager as $managerId => $paymentCount) {
            // Fetch manager details
            $warehouseManager = $this->Users->find()
                ->where(['id' => $managerId])
                ->first();

            // echo '<pre>';print_r($warehouseManager);

            if ($warehouseManager && !empty($warehouseManager->email)) {
                $tasks = ["$paymentCount Supplier payment approval" . ($paymentCount > 1 ? "s are" : " is") . " pending for your warehouse."];
                $this->sendEmailsToUsers([$warehouseManager], 'Pending Supplier Payment', $tasks);
            }
        }



        die;

        // 3. Pending Showroom Expenses → Send to Showroom Manager & Showroom Supervisor
        $pendingShowroomExpenses = $this->ShowroomExpenses->find()
            ->where(['payment_status' => 'Unpaid', 'status' => 'A'])
            ->all();

        foreach ($pendingShowroomExpenses as $expense) {
            $showroomId = $expense->showroom_id;
            $recipients = array_merge(
                $showroomManagers[$showroomId] ?? [],
                $showroomSupervisors[$showroomId] ?? []
            );

            $tasks = ["Showroom expense approval is pending."];
            $this->sendEmailsToUsers($recipients, 'Pending Showroom Expenses', $tasks);
        }

        // 4. Pending Order Assignments & Deliveries → Send to Admins
        $pendingAssignments = $this->Shipments->find()
            ->where([
                'stock_type' => 'In Stock',
                'assignment_status' => 'unassigned',
                'delivery_status' => 'pending'
            ])
            ->count();

        $pendingDeliveries = $this->Shipments->find()
            ->where([
                'stock_type' => 'In Stock',
                'assignment_status' => 'assigned',
                'delivery_status' => 'pending'
            ])
            ->count();

        if ($pendingAssignments > 0 || $pendingDeliveries > 0) {
            $tasks = [];
            if ($pendingAssignments > 0) {
                $tasks[] = "$pendingAssignments order assignment" . ($pendingAssignments > 1 ? "s are" : " is") . " pending.";
            }
            if ($pendingDeliveries > 0) {
                $tasks[] = "$pendingDeliveries order" . ($pendingDeliveries > 1 ? "s are" : " is") . " pending delivery.";
            }

            $this->sendEmailsToUsers($admins, 'Pending Order Assignments & Deliveries', $tasks);
        }
    }

    public function testNotification()
    {

        $deviceToken = 'e43VdFGVQsqZ33Ky1E4lBp:APA91bElKKsd1_Gx35N2xSP3snuymuScttghCBEbqnZI10eAEwJ8heNOIvQx0Jng71VGxJ3FZdbthxjlT-PU7Rj3itov8HTS-l8rMjQDLcFSAmSIzhiLPS8';

        if (empty($deviceToken)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => false,
                    'message' => 'Device token is missing.',
                ]));
        }

        $title = 'Test Notification Dhiren';
        $body  = 'Testing App Notification Dhiren';
        $customData = []; // Optional

        $response = $this->Global->sendNotification(
            [$deviceToken],
            $title,
            $body,
            $customData
        );

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }


    /**
     * Helper function to send emails
     */
    private function sendEmailsToUsers($users, $subject, $tasks)
    {
        foreach ($users as $user) {
            $this->Global->send_email(
                // $user->email,
                '<EMAIL>',
                null, // Uses default from email
                $subject,
                'pending_tasks', // Email template
                ['tasks' => $tasks, 'user' => $user]
            );

            if (!empty($user->device_token)) {
                $this->Global->pushNotification(
                    ['title' => $subject, 'message' => implode("\n- ", $tasks)],
                    [$user->device_token]
                );
            }
        }
    }



}
