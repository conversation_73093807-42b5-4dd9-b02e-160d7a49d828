<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\ORM\Query;
use Cake\Mailer\Mailer;
use Cake\I18n\FrozenTime;

class ShowroomStockRequestsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $ProductStocks;
    protected $SiteSettings;
    protected $Showrooms;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $Roles;
    protected $StockRequestModifications;
    protected $Users;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->Roles = $this->fetchTable('Roles');
        $this->StockRequestModifications = $this->fetchTable('StockRequestModifications');
        $this->Users = $this->fetchTable('Users');
    }
    
    public function index()
    {
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);

            $stockRequestsQuery = $this->StockRequests->find()
                ->select([
                    'StockRequests.id',
                    'StockRequests.request_date',
                    'StockRequests.request_status',
                    'StockRequests.warehouse_id',
                    'StockRequests.to_showroomID',
                    'StockRequests.showroom_id',
                    'StockRequests.status',
                    'Warehouses.name',
                    'Showrooms.name',
                    'ToShowroom.name', // Alias for the joined showroom based on to_showroomID
                    'total_items' => $this->StockRequests->StockRequestItems->find()
                        ->func()
                        ->sum(
                            '(CASE 
                                WHEN StockRequests.request_status IN ("Approved", "Completed") 
                                    THEN StockRequestItems.fulfilled_quantity 
                                ELSE StockRequestItems.requested_quantity 
                            END)'
                        ),
                    'total_value' => $this->StockRequests->StockRequestItems->find()
                        ->func()
                        ->sum(
                            '(CASE 
                                WHEN StockRequests.request_status IN ("Approved", "Completed") AND StockRequestItems.product_variant_id IS NULL 
                                    THEN Products.promotion_price * StockRequestItems.fulfilled_quantity 
                                WHEN StockRequests.request_status IN ("Approved", "Completed") AND StockRequestItems.product_variant_id IS NOT NULL 
                                    THEN ProductVariants.promotion_price * StockRequestItems.fulfilled_quantity 
                                WHEN StockRequestItems.product_variant_id IS NULL 
                                    THEN Products.promotion_price * StockRequestItems.requested_quantity 
                                ELSE ProductVariants.promotion_price * StockRequestItems.requested_quantity 
                              END)'
                        )
                ])
                ->contain([
                    'Warehouses',
                    'Showrooms'
                ])
                ->leftJoinWith('StockRequestItems')
                ->leftJoinWith('StockRequestItems.Products')
                ->leftJoinWith('StockRequestItems.ProductVariants')
                ->leftJoin(
                    ['ToShowroom' => 'showrooms'],
                    ['ToShowroom.id = StockRequests.to_showroomID']
                );

            $managerShowroomId = null;

            // Apply role-based filtering
            if (strtolower($role->name) === 'showroom manager') {
                // Find the showroom associated with the manager
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $managerShowroomId = $managerShowroom->id;

                    // Filter requests related to the manager's showroom
                    $stockRequestsQuery->where([
                        'OR' => [
                            'StockRequests.to_showroomID' => $managerShowroomId,
                            'StockRequests.showroom_id' => $managerShowroomId
                        ]
                    ]);
                } else {
                    // If no showroom is assigned to the manager, return an empty result
                    $stock_requests = [];
                    return;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {
                // Find all showrooms assigned to the supervisor
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->toArray();

                if (!empty($supervisorShowrooms)) {
                    $showroomIds = array_map(function ($showroom) {
                        return $showroom->id;
                    }, $supervisorShowrooms);

                    // Filter requests related to any of the supervisor's showrooms
                    $stockRequestsQuery->where([
                        'OR' => [
                            'StockRequests.to_showroomID IN' => $showroomIds,
                            'StockRequests.showroom_id IN' => $showroomIds
                        ]
                    ]);
                } else {
                    // If no showrooms are assigned to the supervisor, return an empty result
                    $stock_requests = [];
                    return;
                }

            } elseif (strtolower($role->name) === 'warehouse manager') {

                $warehouse = $this->Warehouses->find()
                    ->select(['id'])
                    ->where(['manager_id' => $requested_user->id])
                    ->first();

                if ($warehouse) {
                    $warehouseId = $warehouse->id;
                    $stockRequestsQuery->where(
                        [
                            'StockRequests.requestor_type' => 'Showroom',
                            'StockRequests.warehouse_id' => $warehouseId
                        ]
                    );
                } else {
                    $stock_requests = [];
                    return;
                }

            } else {
                // For admin, fetch all stock requests for showrooms
                $stockRequestsQuery->where([
                    'StockRequests.requestor_type' => 'Showroom'
                ]);
            }

            // Common conditions for all roles
            $stockRequestsQuery->where([
                'StockRequests.status IN' => ['A', 'I'] // Active and Inactive
            ]);

            $stockRequestsQuery->order(['StockRequests.id' => 'DESC']);
            $stockRequestsQuery->group(['StockRequests.id']);

            $stock_requests = $stockRequestsQuery->toArray();
        }


        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('stock_requests', 'requested_user', 'role', 'managerShowroomId', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }

    public function add()
    {
        $requested_user = $this->Authentication->getIdentity();

        $stock_request = $this->StockRequests->newEmptyEntity();
        if ($this->request->is('post')) {

            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) == 'showroom supervisor' || strtolower($role->name) == 'admin') 
            {
            
                $data = $this->request->getData();

                $data['requestor_type'] = 'Showroom';
                $data['manager_review_status'] = 'Pending';
                $data['supervisor_verify_status'] = 'Approved';
                $data['supervisor_verified_time'] = date('Y-m-d H:i:s');
                $data['verified_by'] = $requested_user->id;
                $data['request_status'] = 'Approved';

                $stock_request = $this->StockRequests->patchEntity($stock_request, $data);

                if ($this->StockRequests->save($stock_request)) {

                    $this->saveStockRequestItemsApproved($stock_request);

                    // Send email notification to the receiving showroom's Manager and Supervisor
                    $this->sendStockRequestEmail($stock_request, 'New Request');
                    
                    $this->Flash->success(__('The stock request has been saved.'));

                    return $this->redirect(['action' => 'index']);

                }
                else
                {
                    $this->Flash->error(__('The stock request could not be saved. Please, try again.'));
                }

            }   
            else
            {
                $data = $this->request->getData();

                $data['requestor_type'] = 'Showroom';
                $data['manager_review_status'] = 'Pending';
                $data['supervisor_verify_status'] = 'Pending';
                $data['request_status'] = 'Pending';

                $stock_request = $this->StockRequests->patchEntity($stock_request, $data);


                if ($this->StockRequests->save($stock_request)) {

                    $this->saveStockRequestItems($stock_request);

                    $this->sendStockRequestEmailForManager($stock_request, 'New Request');
                    
                    $this->Flash->success(__('The stock request has been saved.'));

                    return $this->redirect(['action' => 'index']);

                }
                else
                {
                    $this->Flash->error(__('The stock request could not be saved. Please, try again.'));
                }
            }
        }

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();
            
        $products = $this->Products->find()
            ->where(['Products.status' => 'A'])
            ->order(['Products.name' => 'ASC'])
            ->toArray();   

        if($requested_user)
        {
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) == 'showroom supervisor') {
                
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.showroom_supervisor' => $requested_user->id,'Showrooms.status' => 'A'])
                    ->toArray();

            }else
            {
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();
            }
        }

        $all_showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();
 
        $this->set(compact('showrooms', 'warehouses', 'products', 'requested_user', 'role' ,'all_showrooms')); 

    }

    private function sendStockRequestEmail($stock_request, $status)
    {

        // Initialize email recipients
        $toEmails = [];

        // Determine request type
        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Request
            $toShowroomId = $stock_request->to_showroomID;
            $fromShowroomId = $stock_request->showroom_id;

            // Fetch showroom names
            $showrooms = $this->Showrooms->find()
                ->where(['id IN' => [$fromShowroomId, $toShowroomId]])
                ->all()
                ->combine('id', 'name')
                ->toArray();

            $fromShowroomName = $showrooms[$fromShowroomId] ?? 'Unknown Showroom';
            $toShowroomName = $showrooms[$toShowroomId] ?? 'Unknown Showroom';

            // Fetch only the Manager of the receiving showroom
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            $managerEmail = $showroom->manager ? $showroom->manager->email : null;

            $toEmails = array_filter([$managerEmail]); // Remove null values

            $toLocation = "Showroom: {$toShowroomName}";
            $greeting = "Dear Manager,";
        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Request
            $fromShowroomId = $stock_request->showroom_id;

            // Fetch showroom name
            $showroom = $this->Showrooms->find()
                ->where(['id' => $fromShowroomId])
                ->first();

            $fromShowroomName = $showroom ? $showroom->name : 'Unknown Showroom';

            // Fetch Warehouse Name and Manager Email
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            $toEmails = array_filter([$warehouseManagerEmail]); // Remove null values

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for stock request ID: " . $stock_request->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stock_request->created ? $stock_request->created->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => ucfirst($status),
            'from_showroom' => $fromShowroomName,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'request_date' => $formattedDate,
        ];

        $subject = "New Stock Request #{$stock_request->id} from {$fromShowroomName}";

        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'stock_request_notification', // Template name
            $emailData
        );
    }

    private function sendStockRequestEmailForManager($stock_request, $status)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Originating Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id);

        if (!empty($stock_request->to_showroomID)) {
            // Requesting stock from another showroom
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            $managerEmail = $showroom->manager ? $showroom->manager->email : null;

            if ($managerEmail) {
                $toEmails[] = $managerEmail;
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Requesting stock from a warehouse
            $fromShowroomId = $stock_request->showroom_id;

            $showroom = $this->Showrooms->get($fromShowroomId, [
                'contain' => ['ShowroomSupervisor']
            ]);

            $supervisorEmail = $showroom->supervisor ? $showroom->supervisor->email : null;

            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            // $warehouseManagerEmail = $warehouse ? $warehouse->email : null;
            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            if ($supervisorEmail) {
                $toEmails[] = $supervisorEmail; // Supervisor must approve first
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail;
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => ucfirst($status),
            'from_showroom' => $fromShowroom->name, // ✅ Send From Showroom
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'request_date' => $stock_request->created ? $stock_request->created->format('d-m-y') : 'N/A',
        ];

        $subject = "Stock Request #{$stock_request->id} - Approval Required";

        $this->Global->send_email(
            $toEmails, 
            null, 
            $subject,
            'stock_request_notification', 
            $emailData
        );
    }

    protected function saveStockRequestItemsApproved($stock_request)
    {
        if (!empty($stock_request->product_id) && is_array($stock_request->product_id)) {

            $stock_request_id = $stock_request->id;
            $product_id = $stock_request->product_id;
            $product_variant_id = $stock_request->product_variant_id;
            $product_attribute_id = $stock_request->product_attribute_id;
            $requested_quantity = $stock_request->quantity;

            $this->StockRequestItems->deleteAll(['stock_request_id' => $stock_request_id]);

            for($i=0; $i < sizeof($product_id); $i++)
            {
                $mapping = $this->StockRequestItems->find()
                    ->where(['stock_request_id' => $stock_request_id, 'product_id' => $product_id[$i], 'requested_quantity' => $requested_quantity[$i]])
                    ->first();

                if ($mapping) {
                    $mapping->product_id = $product_id[$i];
                } else {
                    $mapping = $this->StockRequestItems->newEntity([
                        'stock_request_id' => $stock_request_id,
                        'product_id' => $product_id[$i],
                        'product_variant_id' => $product_variant_id[$i],
                        'product_attribute_id' => $product_attribute_id[$i],
                        'requested_quantity' => $requested_quantity[$i],
                        'supervisor_approved_quantity' => $requested_quantity[$i],
                        'fulfilled_quantity' => $requested_quantity[$i],
                        'status' => 'Approved'
                    ]);
                }

                if (!$this->StockRequestItems->save($mapping)) {
                    return false;
                }

                // Update product stock reserved_quantity
                $this->updateProductStocks(
                    strtolower($stock_request->requestor_type),
                    $stock_request->to_showroomID ?? null,
                    $stock_request->warehouse_id ?? null,
                    $product_id[$i],
                    $product_variant_id[$i],
                    $product_attribute_id[$i],
                    $requested_quantity[$i]
                );
            }
        }

        return true;
    }

    protected function saveStockRequestItems($stock_request)
    {
        if (!empty($stock_request->product_id) && is_array($stock_request->product_id)) {

            $stock_request_id = $stock_request->id;
            $product_id = $stock_request->product_id;
            $product_variant_id = $stock_request->product_variant_id;
            $product_attribute_id = $stock_request->product_attribute_id;
            $requested_quantity = $stock_request->quantity;

            $this->StockRequestItems->deleteAll(['stock_request_id' => $stock_request_id]);

            for($i=0; $i < sizeof($product_id); $i++)
            {
                $mapping = $this->StockRequestItems->find()
                    ->where(['stock_request_id' => $stock_request_id, 'product_id' => $product_id[$i], 'requested_quantity' => $requested_quantity[$i]])
                    ->first();

                if ($mapping) {
                    $mapping->product_id = $product_id[$i];
                } else {
                    $mapping = $this->StockRequestItems->newEntity([
                        'stock_request_id' => $stock_request_id,
                        'product_id' => $product_id[$i],
                        'product_variant_id' => $product_variant_id[$i],
                        'product_attribute_id' => $product_attribute_id[$i],
                        'requested_quantity' => $requested_quantity[$i]
                    ]);
                }

                if (!$this->StockRequestItems->save($mapping)) {
                    return false;
                }

                // Update product stock reserved_quantity
                // $this->updateProductStocks(
                //     $stock_request->requestTo,
                //     $stock_request->to_showroomID ?? null,
                //     $stock_request->warehouse_id ?? null,
                //     $product_id[$i],
                //     $product_variant_id[$i],
                //     $product_attribute_id[$i],
                //     $requested_quantity[$i]
                // );
            }
        }

        return true;
    }

    protected function updateProductStocks($requestTo, $showroom_id, $warehouse_id, $product_id, $product_variant_id, $product_attribute_id, $requested_quantity)
    {
        // Initialize conditions with product_id
        $conditions = [
            'product_id' => $product_id,
        ];

        // Add product_variant_id only if it's not null
        if ($product_variant_id) {
            $conditions['product_variant_id'] = $product_variant_id;
        }

        if ($product_attribute_id) {
            $conditions['product_attribute_id'] = $product_attribute_id;
        }

        // Add either showroom_id or warehouse_id condition
        // if ($requestTo === 'showroom' && $showroom_id) {
        //     $conditions['showroom_id'] = $showroom_id;
        // } elseif ($requestTo === 'warehouse' && $warehouse_id) {
        //     $conditions['warehouse_id'] = $warehouse_id;
        // }

        if (!empty($showroom_id)) {
            $conditions['showroom_id'] = $showroom_id;
        } elseif (!empty($warehouse_id)) {
            $conditions['warehouse_id'] = $warehouse_id;
        }

        // Fetch product stock
        $productStock = $this->ProductStocks->find()
            ->where($conditions)
            ->first();

        if ($productStock) {
            // Update reserved quantity
            $productStock->reserved_stock += $requested_quantity;
            $this->ProductStocks->save($productStock);
        }
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The stock request could not be deleted. Please, try again.'];

        try {
            $record = $this->StockRequests->get($id);
            $record->status = 'D';

            if ($this->StockRequests->save($record)) {
                $response = ['success' => true, 'message' => 'The stock request has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function edit($id = null)
    {

        $stock_request = $this->StockRequests->get($id, contain: []);

        $requested_user = $this->Authentication->getIdentity();

        $editLockTime = new FrozenTime($stock_request->edit_lock_time);
        $now = FrozenTime::now();
        $minutesPassed = $editLockTime->diffInMinutes($now);

        if (
            !empty($stock_request->edit_lock_by) &&
            $stock_request->edit_lock_by != $requested_user->id &&
            $minutesPassed < 10
        ) {
            $minutesRemaining = 10 - $minutesPassed;
            $this->Flash->error(__('This request is currently being edited/validated. Please try again in {0} minute(s).', $minutesRemaining));
            return $this->redirect(['action' => 'index']);
        }

        // Lock the request for the current user
        $stock_request->edit_lock_by = $requested_user->id;
        $stock_request->edit_lock_time = FrozenTime::now();
        $this->StockRequests->save($stock_request);

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $stock_request->edit_lock_by = null;
            $stock_request->edit_lock_time = null;

            $stock_request = $this->StockRequests->patchEntity($stock_request, $this->request->getData());

            if ($this->StockRequests->save($stock_request)) {

                $this->editStockRequestItemsApproved($stock_request);

                $this->Flash->success(__('The stock request has been saved.'));

                return $this->redirect(['action' => 'index']);

            }
            else
            {
                $this->Flash->error(__('The stock request could not be saved. Please, try again.'));
            }
        }

        $stockRequest = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.request_status',
                'StockRequests.supervisor_verified_time',
                'StockRequests.requested_by',
                'StockRequests.verified_by',
                'StockRequests.warehouse_id',
                'StockRequests.to_showroomID',
                'StockRequests.status',
                'Warehouses.name',
                'Showrooms.name',
                'Users.first_name',
                'Users.last_name',
                'ToShowroom.name'
            ])
            ->contain([
                'Warehouses',
                'Showrooms',
                'Users'
            ])
            ->leftJoin(
                ['ToShowroom' => 'showrooms'],
                ['ToShowroom.id = StockRequests.to_showroomID']
            )
            ->where(['StockRequests.id' => $id])
            ->first();

        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_id',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.supervisor_approved_quantity',
                'Products.name',
                'Products.purchase_price',
                'Products.sku',
                'ProductVariants.id',
                'ProductVariants.variant_name',
                'ProductVariants.purchase_price',
                'ProductVariants.sku',
                // 'quantity' => 'COALESCE(AttributeStocks.quantity, WarehouseStocks.quantity, ShowroomStocks.quantity)'
                'quantity' => 'COALESCE(AttributeStock.quantity - AttributeStock.reserved_stock, VariantStock.quantity - VariantStock.reserved_stock, GeneralStock.quantity - GeneralStock.reserved_stock)'
            ])
            ->leftJoinWith('Products')
            ->leftJoinWith('ProductVariants')
            ->leftJoin(
                            ['StockRequests' => 'stock_requests'],
                            ['StockRequests.id = StockRequestItems.stock_request_id']
                        )
                        ->leftJoin(
                ['AttributeStock' => 'product_stocks'],
                [
                    'AttributeStock.product_id = StockRequestItems.product_id',
                    'AttributeStock.product_variant_id = StockRequestItems.product_variant_id',
                    'AttributeStock.product_attribute_id = StockRequestItems.product_attribute_id',
                    'OR' => [
                        ['AttributeStock.warehouse_id = StockRequests.warehouse_id'],
                        ['AttributeStock.showroom_id = StockRequests.to_showroomID']
                    ]
                ]
            )
            ->leftJoin(
                ['VariantStock' => 'product_stocks'],
                [
                    'VariantStock.product_id = StockRequestItems.product_id',
                    'VariantStock.product_variant_id = StockRequestItems.product_variant_id',
                    'VariantStock.product_attribute_id IS' => null,
                    'OR' => [
                        ['VariantStock.warehouse_id = StockRequests.warehouse_id'],
                        ['VariantStock.showroom_id = StockRequests.to_showroomID']
                    ]
                ]
            )
            ->leftJoin(
                ['GeneralStock' => 'product_stocks'],
                [
                    'GeneralStock.product_id = StockRequestItems.product_id',
                    'GeneralStock.product_variant_id IS' => null,
                    'GeneralStock.product_attribute_id IS' => null,
                    'OR' => [
                        ['GeneralStock.warehouse_id = StockRequests.warehouse_id'],
                        ['GeneralStock.showroom_id = StockRequests.to_showroomID']
                    ]
                ]
            )
            ->contain(['Products', 'ProductVariants'])
            ->where(['StockRequestItems.stock_request_id' => $id])
            ->groupBy('StockRequestItems.id')
            ->toArray();

        // echo "<pre>";print_r($stockRequestItems);die;

        foreach ($stockRequestItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $products = $this->Products->find()
            ->where(['Products.status' => 'A'])
            ->order(['Products.name' => 'ASC'])
            ->toArray();

        $this->set(compact('stockRequest', 'stockRequestItems', 'products'));
    }

    protected function editStockRequestItemsApproved($stock_request)
    {
        if (!empty($stock_request->product_id) && is_array($stock_request->product_id)) {

            $stock_request_id = $stock_request->id;
            $product_id = $stock_request->product_id;
            $product_variant_id = $stock_request->product_variant_id;
            $product_attribute_id = $stock_request->product_attribute_id;
            $requested_quantity = $stock_request->quantity;

            $this->StockRequestItems->deleteAll(['stock_request_id' => $stock_request_id]);

            for($i=0; $i < sizeof($product_id); $i++)
            {
                $mapping = $this->StockRequestItems->find()
                    ->where(['stock_request_id' => $stock_request_id, 'product_id' => $product_id[$i], 'requested_quantity' => $requested_quantity[$i]])
                    ->first();

                if ($mapping) {
                    $mapping->product_id = $product_id[$i];
                } else {
                    $mapping = $this->StockRequestItems->newEntity([
                        'stock_request_id' => $stock_request_id,
                        'product_id' => $product_id[$i],
                        'product_variant_id' => $product_variant_id[$i],
                        'product_attribute_id' => $product_attribute_id[$i],
                        'requested_quantity' => $requested_quantity[$i],
                        'supervisor_approved_quantity' => $requested_quantity[$i],
                        'fulfilled_quantity' => $requested_quantity[$i],
                        'status' => 'Approved'
                    ]);
                }

                if (!$this->StockRequestItems->save($mapping)) {
                    return false;
                }

                // Update product stock reserved_quantity
                $this->deductAndUpdateProductStocks(
                    $stock_request->requestTo,
                    $stock_request->to_showroomID ?? null,
                    $stock_request->warehouse_id ?? null,
                    $product_id[$i],
                    $product_variant_id[$i],
                    $product_attribute_id[$i],
                    $requested_quantity[$i]
                );
            }
        }

        return true;
    }

    protected function deductAndUpdateProductStocks($requestTo, $showroom_id, $warehouse_id, $product_id, $product_variant_id, $product_attribute_id, $requested_quantity)
    {
        // Initialize conditions with product_id
        $conditions = [
            'product_id' => $product_id,
        ];

        // Add product_variant_id only if it's not null
        if ($product_variant_id) {
            $conditions['product_variant_id'] = $product_variant_id;
        }

        // Add product_variant_id only if it's not null
        if ($product_attribute_id) {
            $conditions['product_attribute_id'] = $product_attribute_id;
        }

        // Add either showroom_id or warehouse_id condition
        // if ($requestTo === 'showroom' && $showroom_id) {
        //     $conditions['showroom_id'] = $showroom_id;
        // } elseif ($requestTo === 'warehouse' && $warehouse_id) {
        //     $conditions['warehouse_id'] = $warehouse_id;
        // }

        if (!empty($showroom_id)) {
            $conditions['showroom_id'] = $showroom_id;
        } elseif (!empty($warehouse_id)) {
            $conditions['warehouse_id'] = $warehouse_id;
        }

        // Fetch product stock
        $productStock = $this->ProductStocks->find()
            ->where($conditions)
            ->first();

        if ($productStock) {
            // Update reserved quantity
            // Deduct the reserved_stock first
            $productStock->reserved_stock -= $requested_quantity;

            // Ensure reserved_stock doesn't go below zero
            if ($productStock->reserved_stock < 0) {
                $productStock->reserved_stock = 0;
            }

            // Add the requested_quantity to reserved_stock
            $productStock->reserved_stock += $requested_quantity;
            $this->ProductStocks->save($productStock);
        }
    }

    public function view($id = null)
    {
        $stockRequest = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.request_status',
                'StockRequests.requested_by',
                'StockRequests.to_showroomID',
                'StockRequests.manager_review_status',
                'StockRequests.manager_reviewed_time',
                'StockRequests.reviewed_by',
                'StockRequests.supervisor_verify_status',
                'StockRequests.supervisor_verified_time',
                'StockRequests.verified_by',
                'StockRequests.status',
                'Warehouses.name',
                'Showrooms.name',
                'Users.first_name',
                'Users.last_name',
                'ToShowroom.name',
                // Conditionally include first_name and last_name for reviewed_by or verified_by
                'ReviewedUser.first_name',
                'ReviewedUser.last_name',
                'VerifiedUser.first_name',
                'VerifiedUser.last_name',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'DriverUser.first_name',
                'DriverUser.last_name'
            ])
            ->contain([
                'Warehouses',
                'Showrooms',
                'Users'
            ])
            ->leftJoin(
                ['ToShowroom' => 'showrooms'],
                ['ToShowroom.id = StockRequests.to_showroomID']
            )
            ->leftJoin(
                ['ReviewedUser' => 'users'],
                ['ReviewedUser.id = StockRequests.reviewed_by']
            )
            ->leftJoin(
                ['VerifiedUser' => 'users'],
                ['VerifiedUser.id = StockRequests.verified_by']
            )
            ->leftJoin(
                ['StockMovements' => 'stock_movements'],
                [
                    'StockMovements.referenceID = StockRequests.id',
                    'StockMovements.movement_type' => 'Incoming', // Filter for Incoming stock movements
                ]
            )
            ->leftJoin(
                ['Drivers' => 'drivers'], // Join with the drivers table
                ['Drivers.id = StockMovements.driver_id']
            )
            ->leftJoin(
                ['DriverUser' => 'users'], // Join with the users table for driver details
                ['DriverUser.id = Drivers.user_id']
            )
            ->where(['StockRequests.id' => $id])
            ->first();

        $driver_name = $this->StockRequests->find()
            ->select([
                'DriverUser.first_name',
                'DriverUser.last_name'
            ])
            ->leftJoin(
                ['StockMovements' => 'stock_movements'],
                [
                    'StockMovements.referenceID = StockRequests.id',
                    'StockMovements.movement_type' => 'Outgoing' // Updated filter
                ]
            )
            ->leftJoin(
                ['Drivers' => 'drivers'],
                ['Drivers.id = StockMovements.driver_id']
            )
            ->leftJoin(
                ['DriverUser' => 'users'],
                ['DriverUser.id = Drivers.user_id']
            )
            ->where(['StockRequests.id' => $id])
            ->first();

        // Fetch StockRequestItems for the StockRequest with id = 7
        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.manager_approved_quantity',
                'StockRequestItems.supervisor_approved_quantity',
                'StockRequestItems.fulfilled_quantity',
                'StockRequestItems.status',
                'Products.name',
                'Products.promotion_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.promotion_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockRequestItems.stock_request_id' => $id])
            ->toArray();

        foreach ($stockRequestItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $requested_user = $this->Authentication->getIdentity();
        
        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('stockRequest', 'stockRequestItems', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'role', 'driver_name')); 
    }

    public function approve($id = null)
    {

        $request = $this->StockRequests->get($id);

        $requested_user = $this->Authentication->getIdentity();

        $editLockTime = new FrozenTime($request->edit_lock_time);
        $now = FrozenTime::now();
        $minutesPassed = $editLockTime->diffInMinutes($now);

        if (
            !empty($request->edit_lock_by) &&
            $request->edit_lock_by != $requested_user->id &&
            $minutesPassed < 10
        ) {
            $minutesRemaining = 10 - $minutesPassed;
            $this->Flash->error(__('This request is currently being edited/validated. Please try again in {0} minute(s).', $minutesRemaining));
            return $this->redirect(['action' => 'index']);
        }

        $request->edit_lock_by = $requested_user->id;
        $request->edit_lock_time = FrozenTime::now();
        $this->StockRequests->save($request);

        if ($this->request->is(['patch', 'post', 'put'])) {

            try {

                $user_detail = $this->Authentication->getIdentity();

                if(!empty($user_detail))
                {
                    $role = $this->Roles->find()
                            ->where(['id' => $user_detail->role_id])
                            ->first();

                    if (strtolower($role->name) == 'showroom supervisor') {

                        $data = $this->request->getData();

                        $supervisor_verified_time = date('Y-m-d H:i:s');
                        $verified_by = $user_detail->id;

                        $product_id = $this->request->getData('product_id');
                        $product_variant_id = $this->request->getData('product_variant_id');
                        $product_attribute_id = $this->request->getData('product_attribute_id');
                        $requested_quantity = $this->request->getData('quantity');
                        $accepted_quantity = $this->request->getData('accepted_quantity');
                        $statuses = $this->request->getData('status');

                        // ✅ Check if all are rejected
                        $allRejected = true;
                        foreach ($statuses as $status) {
                            if (strtolower($status) !== 'reject') {
                                $allRejected = false;
                                break;
                            }
                        }

                        $request_status = $allRejected ? 'Rejected' : 'Approved';
                        $supervisor_verify_status = $request_status;

                        // Fetch and update the stock request data
                        $stock_request_data = $this->StockRequests->get($id);

                        $stock_request_data->supervisor_verify_status = $supervisor_verify_status;
                        $stock_request_data->supervisor_verified_time = $supervisor_verified_time;
                        $stock_request_data->verified_by = $verified_by;
                        $stock_request_data->request_status = $request_status;
                        $stock_request_data->edit_lock_by = null;
                        $stock_request_data->edit_lock_time = null;

                        if ($this->StockRequests->save($stock_request_data)) {

                            // ✅ Send email based on status
                            if ($request_status === 'Rejected') {
                                $this->sendStockRequestRejectionEmail($stock_request_data); // ← You should implement this method
                            } else {
                                $this->sendStockRequestApprovalEmail($stock_request_data);
                            }

                            $this->StockRequestItems->deleteAll(['stock_request_id' => $id]);

                            for($i=0; $i < sizeof($product_id); $i++)
                            {
                                $status = (isset($statuses[$i]) && strtolower($statuses[$i]) === 'reject') ? 'Rejected' : 'Approved';

                                $conditions = [
                                    'stock_request_id' => $id,
                                    'product_id' => $product_id[$i],
                                    'requested_quantity' => $requested_quantity[$i]
                                ];

                                $variantId = !empty($product_variant_id[$i]) ? $product_variant_id[$i] : null;
                                if ($variantId === null) {
                                    $conditions['product_variant_id IS'] = null;
                                } else {
                                    $conditions['product_variant_id'] = $variantId;
                                }

                                $attributeId = !empty($product_attribute_id[$i]) ? $product_attribute_id[$i] : null;
                                if ($attributeId === null) {
                                    $conditions['product_attribute_id IS'] = null;
                                } else {
                                    $conditions['product_attribute_id'] = $variantId;
                                }

                                $mapping = $this->StockRequestItems->find()
                                    ->where($conditions)
                                    ->first();

                                if ($mapping) {
                                    $mapping->product_id = $product_id[$i];
                                } else {
                                    $mapping = $this->StockRequestItems->newEntity([
                                        'stock_request_id' => $id,
                                        'product_id' => $product_id[$i],
                                        'product_variant_id' => $product_variant_id[$i],
                                        'product_attribute_id' => $product_attribute_id[$i],
                                        'requested_quantity' => $requested_quantity[$i],
                                        'supervisor_approved_quantity' => $accepted_quantity[$i],
                                        'fulfilled_quantity' => $accepted_quantity[$i],
                                        'status' => $status
                                    ]);
                                }

                                if (!$this->StockRequestItems->save($mapping)) {
                                    $this->Flash->error(__('Failed to approve the stock request.'));
                                }

                                if ($status !== 'Rejected') {

                                    $this->updateProductStocks(
                                        strtolower($stock_request_data->requestor_type),
                                        $stock_request_data->to_showroomID ?? null,
                                        $stock_request_data->warehouse_id ?? null,
                                        $product_id[$i] ?? null,
                                        $product_variant_id[$i] ?? null,
                                        $product_attribute_id[$i] ?? null,
                                        $accepted_quantity[$i] ?? 0
                                    );
                                }
                            }

                            // ✅ Flash message based on final request status
                            if ($request_status === 'Rejected') {
                                $this->Flash->success(__('The stock request has been rejected.'));
                            } else {
                                $this->Flash->success(__('The stock request and items have been approved successfully.'));
                            }

                        } else {
                            $this->Flash->error(__('Failed to approve the stock request.'));
                        }

                        return $this->redirect(['action' => 'index']);
                    }
                    else {

                        $data = $this->request->getData();

                        $manager_reviewed_time = date('Y-m-d H:i:s');
                        $reviewed_by = $user_detail->id;

                        $product_id = $this->request->getData('product_id');
                        $product_variant_id = $this->request->getData('product_variant_id');
                        $product_attribute_id = $this->request->getData('product_attribute_id');
                        $requested_quantity = $this->request->getData('quantity');
                        $accepted_quantity = $this->request->getData('accepted_quantity');
                        $statuses = $this->request->getData('status');

                        // Check if all items are rejected
                        $allRejected = true;
                        foreach ($statuses as $status) {
                            if ($status !== 'Reject') {
                                $allRejected = false;
                                break;
                            }
                        }

                        $request_status = $allRejected ? 'Rejected' : 'Approved';
                        $manager_review_status = $request_status;

                        $stock_request_data = $this->StockRequests->get($id);

                        $stock_request_data->manager_review_status = $manager_review_status;
                        $stock_request_data->manager_reviewed_time = $manager_reviewed_time;
                        $stock_request_data->reviewed_by = $reviewed_by;
                        $stock_request_data->request_status = $request_status;
                        $stock_request_data->edit_lock_by = null;
                        $stock_request_data->edit_lock_time = null;

                        if ($this->StockRequests->save($stock_request_data)) {

                            // Send appropriate email
                            if ($request_status === 'Rejected') {
                                $this->sendStockRequestRejectionEmail($stock_request_data);
                            } else {
                                $this->sendStockRequestApprovalEmail($stock_request_data);
                            }

                            $this->StockRequestItems->deleteAll(['stock_request_id' => $id]);

                            for ($i = 0; $i < sizeof($product_id); $i++) {
                                $status = (isset($statuses[$i]) && $statuses[$i] === 'Reject') ? 'Rejected' : 'Approved';

                                $conditions = [
                                    'stock_request_id' => $id,
                                    'product_id' => $product_id[$i],
                                    'requested_quantity' => $requested_quantity[$i]
                                ];

                                $variantId = !empty($product_variant_id[$i]) ? $product_variant_id[$i] : null;
                                if ($variantId === null) {
                                    $conditions['product_variant_id IS'] = null;
                                } else {
                                    $conditions['product_variant_id'] = $variantId;
                                }

                                $mapping = $this->StockRequestItems->find()
                                    ->where($conditions)
                                    ->first();

                                if ($mapping) {
                                    $mapping->product_id = $product_id[$i];
                                } else {
                                    $mapping = $this->StockRequestItems->newEntity([
                                        'stock_request_id' => $id,
                                        'product_id' => $product_id[$i],
                                        'product_variant_id' => $product_variant_id[$i],
                                        'product_attribute_id' => $product_attribute_id[$i],
                                        'requested_quantity' => $requested_quantity[$i],
                                        'manager_approved_quantity' => $accepted_quantity[$i],
                                        'fulfilled_quantity' => $accepted_quantity[$i],
                                        'status' => $status
                                    ]);
                                }

                                if (!$this->StockRequestItems->save($mapping)) {
                                    $this->Flash->error(__('Failed to approve the stock request.'));
                                }

                                if ($status !== 'Rejected') {
                                    $this->updateProductStocks(
                                        strtolower($stock_request_data->requestor_type),
                                        $stock_request_data->to_showroomID ?? null,
                                        $stock_request_data->warehouse_id ?? null,
                                        $product_id[$i] ?? null,
                                        $product_variant_id[$i] ?? null,
                                        $product_attribute_id[$i] ?? null,
                                        $accepted_quantity[$i] ?? 0
                                    );
                                }
                            }

                            $this->Flash->success(__('The stock request and items have been ' . strtolower($request_status) . ' successfully.'));
                        } else {
                            $this->Flash->error(__('Failed to process the stock request.'));
                        }

                        return $this->redirect(['action' => 'index']);
                    }
                }
                else
                {
                    $this->Flash->success(__('Failed to approve the stock request.'));

                    return $this->redirect(['action' => 'index']);
                }

            } catch (\Exception $e) {
                $response['message'] = $e->getMessage();
            }

        }

        $stockRequest = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.request_status',
                'StockRequests.supervisor_verified_time',
                'StockRequests.requested_by',
                'StockRequests.verified_by',
                'StockRequests.to_showroomID',
                'StockRequests.status',
                'Warehouses.name',
                'Showrooms.name',
                'Users.first_name',
                'Users.last_name',
                'ToShowroom.name'
            ])
            ->contain([
                'Warehouses',
                'Showrooms',
                'Users'
            ])
            ->leftJoin(
                ['ToShowroom' => 'showrooms'],
                ['ToShowroom.id = StockRequests.to_showroomID']
            )
            ->where(['StockRequests.id' => $id])
            ->first();

        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_id',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.manager_approved_quantity',
                'StockRequestItems.supervisor_approved_quantity',
                'StockRequestItems.fulfilled_quantity',
                'StockRequestItems.status',
                'Products.name',
                'Products.promotion_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.promotion_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockRequestItems.stock_request_id' => $id])
            ->toArray();

        // echo "<pre>";print_r($stockRequestItems);die;
                
        foreach ($stockRequestItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $products = $this->Products->find()
            ->where(['Products.status' => 'A'])
            ->order(['Products.name' => 'ASC'])
            ->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('stockRequest', 'stockRequestItems', 'products', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));

    }

    // public function approve($id = null)
    // {

    //     $request = $this->StockRequests->get($id);

    //     $requested_user = $this->Authentication->getIdentity();

    //     if (!empty($request->edit_lock_by) && $request->edit_lock_by != $requested_user->id) {
    //         $this->Flash->error(__('This request is currently being edited/validated by supervisor.'));
    //         return $this->redirect(['action' => 'index']);
    //     }

    //     $request->edit_lock_by = $requested_user->id;
    //     $this->StockRequests->save($request);

    //     if ($this->request->is(['patch', 'post', 'put'])) {

    //         try {

    //             $user_detail = $this->Authentication->getIdentity();

    //             if(!empty($user_detail))
    //             {
    //                 $role = $this->Roles->find()
    //                         ->where(['id' => $user_detail->role_id])
    //                         ->first();

    //                 if (strtolower($role->name) == 'showroom supervisor') {

    //                     $data = $this->request->getData();

    //                     $supervisor_verify_status = 'Approved';
    //                     $supervisor_verified_time = date('Y-m-d H:i:s');
    //                     $verified_by = $user_detail->id;
    //                     $request_status = 'Approved';

    //                     $product_id = $this->request->getData('product_id');
    //                     $product_variant_id = $this->request->getData('product_variant_id');
    //                     $product_attribute_id = $this->request->getData('product_attribute_id');
    //                     $requested_quantity = $this->request->getData('quantity');
    //                     $accepted_quantity = $this->request->getData('accepted_quantity');
    //                     $statuses = $this->request->getData('status');

    //                     // Fetch and update the stock request data
    //                     $stock_request_data = $this->StockRequests->get($id);

    //                     $stock_request_data->supervisor_verify_status = $supervisor_verify_status;
    //                     $stock_request_data->supervisor_verified_time = $supervisor_verified_time;
    //                     $stock_request_data->verified_by = $verified_by;
    //                     $stock_request_data->request_status = $request_status;
    //                     $stock_request_data->edit_lock_by = null;

    //                     if ($this->StockRequests->save($stock_request_data)) {

    //                         $this->sendStockRequestApprovalEmail($stock_request_data);

    //                         $this->StockRequestItems->deleteAll(['stock_request_id' => $id]);

    //                         for($i=0; $i < sizeof($product_id); $i++)
    //                         {
    //                             $status = (isset($statuses[$i]) && $statuses[$i] === 'Reject') ? 'Rejected' : 'Approved';

    //                             // Search using product_id, variant_id (handling NULL), and attribute_id
    //                             $conditions = [
    //                                 'stock_request_id' => $id,
    //                                 'product_id' => $product_id[$i],
    //                                 'requested_quantity' => $requested_quantity[$i]
    //                             ];

    //                             $variantId = !empty($product_variant_id[$i]) ? $product_variant_id[$i] : null;
    //                             if ($variantId === null) {
    //                                 $conditions['product_variant_id IS'] = null;
    //                             } else {
    //                                 $conditions['product_variant_id'] = $variantId;
    //                             }

    //                             $mapping = $this->StockRequestItems->find()
    //                                 ->where($conditions)
    //                                 ->first();

    //                             if ($mapping) {
    //                                 $mapping->product_id = $product_id[$i];
    //                             } else {
    //                                 $mapping = $this->StockRequestItems->newEntity([
    //                                     'stock_request_id' => $id,
    //                                     'product_id' => $product_id[$i],
    //                                     'product_variant_id' => $product_variant_id[$i],
    //                                     'product_attribute_id' => $product_attribute_id[$i],
    //                                     'requested_quantity' => $requested_quantity[$i],
    //                                     'supervisor_approved_quantity' => $accepted_quantity[$i],
    //                                     'fulfilled_quantity' => $accepted_quantity[$i],
    //                                     'status' => $status
    //                                 ]);
    //                             }

    //                             if (!$this->StockRequestItems->save($mapping)) {

    //                                 $this->Flash->error(__('Failed to approve the stock request.'));
    //                             }

    //                             // Update product stock reserved_quantity
    //                             $this->updateProductStocks(
    //                                 $stock_request_data->requestTo,
    //                                 $stock_request_data->to_showroomID ?? null,
    //                                 $stock_request_data->warehouse_id ?? null,
    //                                 $product_id[$i] ?? null,
    //                                 $product_variant_id[$i] ?? null,
    //                                 $product_attribute_id[$i] ?? null,
    //                                 $accepted_quantity[$i] ?? 0
    //                             );

    //                         }

    //                         // Successfully updated all items and modifications
    //                         $this->Flash->success(__('The stock request and items have been approved successfully.'));
    //                     } else {
    //                         $this->Flash->error(__('Failed to approve the stock request.'));
    //                     }

    //                     return $this->redirect(['action' => 'index']);
    //                 }
    //                 else
    //                 {
    //                     $data = $this->request->getData();

    //                     $manager_review_status = 'Approved';
    //                     $manager_reviewed_time = date('Y-m-d H:i:s');
    //                     $reviewed_by = $user_detail->id;
    //                     $request_status = 'Approved';

    //                     $product_id = $this->request->getData('product_id');
    //                     $product_variant_id = $this->request->getData('product_variant_id');
    //                     $product_attribute_id = $this->request->getData('product_attribute_id');
    //                     $requested_quantity = $this->request->getData('quantity');
    //                     $accepted_quantity = $this->request->getData('accepted_quantity');
    //                     $statuses = $this->request->getData('status');

    //                     // Fetch and update the stock request data
    //                     $stock_request_data = $this->StockRequests->get($id);

    //                     $stock_request_data->manager_review_status = $manager_review_status;
    //                     $stock_request_data->manager_reviewed_time = $manager_reviewed_time;
    //                     $stock_request_data->reviewed_by = $reviewed_by;
    //                     $stock_request_data->request_status = $request_status;
    //                     $stock_request_data->edit_lock_by = null;

    //                     if ($this->StockRequests->save($stock_request_data)) {

    //                         $this->sendStockRequestApprovalEmail($stock_request_data);

    //                         $this->StockRequestItems->deleteAll(['stock_request_id' => $id]);

    //                         for($i=0; $i < sizeof($product_id); $i++)
    //                         {
    //                             $status = (isset($statuses[$i]) && $statuses[$i] === 'Reject') ? 'Rejected' : 'Approved';

    //                             $conditions = [
    //                                 'stock_request_id' => $id,
    //                                 'product_id' => $product_id[$i],
    //                                 'requested_quantity' => $requested_quantity[$i]
    //                             ];

    //                             $variantId = !empty($product_variant_id[$i]) ? $product_variant_id[$i] : null;
    //                             if ($variantId === null) {
    //                                 $conditions['product_variant_id IS'] = null;
    //                             } else {
    //                                 $conditions['product_variant_id'] = $variantId;
    //                             }

    //                             $mapping = $this->StockRequestItems->find()
    //                                 ->where($conditions)
    //                                 ->first();

    //                             if ($mapping) {
    //                                 $mapping->product_id = $product_id[$i];
    //                             } else {
    //                                 $mapping = $this->StockRequestItems->newEntity([
    //                                     'stock_request_id' => $id,
    //                                     'product_id' => $product_id[$i],
    //                                     'product_variant_id' => $product_variant_id[$i],
    //                                     'product_attribute_id' => $product_attribute_id[$i],
    //                                     'requested_quantity' => $requested_quantity[$i],
    //                                     'manager_approved_quantity' => $accepted_quantity[$i],
    //                                     'fulfilled_quantity' => $accepted_quantity[$i],
    //                                     'status' => $status
    //                                 ]);
    //                             }

    //                             if (!$this->StockRequestItems->save($mapping)) {

    //                                 $this->Flash->error(__('Failed to approve the stock request.'));
    //                             }

    //                             $this->updateProductStocks(
    //                                 $stock_request_data->requestTo,
    //                                 $stock_request_data->to_showroomID ?? null,
    //                                 $stock_request_data->warehouse_id ?? null,
    //                                 $product_id[$i] ?? null,
    //                                 $product_variant_id[$i] ?? null,
    //                                 $product_attribute_id[$i] ?? null,
    //                                 $accepted_quantity[$i] ?? 0
    //                             );
    //                         }

    //                         // Successfully updated all items and modifications
    //                         $this->Flash->success(__('The stock request and items have been approved successfully.'));
    //                     } else {
    //                         $this->Flash->error(__('Failed to approve the stock request.'));
    //                     }

    //                     return $this->redirect(['action' => 'index']);
    //                 }
    //             }
    //             else
    //             {
    //                 $this->Flash->success(__('Failed to approve the stock request.'));

    //                 return $this->redirect(['action' => 'index']);
    //             }

    //         } catch (\Exception $e) {
    //             $response['message'] = $e->getMessage();
    //         }

    //     }

    //     $stockRequest = $this->StockRequests->find()
    //         ->select([
    //             'StockRequests.id',
    //             'StockRequests.request_date',
    //             'StockRequests.request_status',
    //             'StockRequests.supervisor_verified_time',
    //             'StockRequests.requested_by',
    //             'StockRequests.verified_by',
    //             'StockRequests.to_showroomID',
    //             'StockRequests.status',
    //             'Warehouses.name',
    //             'Showrooms.name',
    //             'Users.first_name',
    //             'Users.last_name',
    //             'ToShowroom.name'
    //         ])
    //         ->contain([
    //             'Warehouses',
    //             'Showrooms',
    //             'Users'
    //         ])
    //         ->leftJoin(
    //             ['ToShowroom' => 'showrooms'],
    //             ['ToShowroom.id = StockRequests.to_showroomID']
    //         )
    //         ->where(['StockRequests.id' => $id])
    //         ->first();

    //     $stockRequestItems = $this->StockRequests->StockRequestItems->find()
    //         ->select([
    //             'StockRequestItems.id',
    //             'StockRequestItems.requested_quantity',
    //             'StockRequestItems.product_id',
    //             'StockRequestItems.product_variant_id',
    //             'StockRequestItems.product_attribute_id',
    //             'StockRequestItems.stock_request_id',
    //             'StockRequestItems.manager_approved_quantity',
    //             'StockRequestItems.supervisor_approved_quantity',
    //             'StockRequestItems.fulfilled_quantity',
    //             'StockRequestItems.status',
    //             'Products.name',
    //             'Products.purchase_price', 
    //             'Products.sku', 
    //             'ProductVariants.id', 
    //             'ProductVariants.variant_name', 
    //             'ProductVariants.purchase_price', 
    //             'ProductVariants.sku', 
    //         ])
    //         ->leftJoinWith('Products')  
    //         ->leftJoinWith('ProductVariants')
    //         ->where(['StockRequestItems.stock_request_id' => $id])
    //         ->toArray();
                
    //     foreach ($stockRequestItems as &$item) {
    //         // Initialize an attributes array in each item
    //         $item->attributes = [];

    //         if ($item->product_attribute_id) {
    //             // Fetch attributes related to the product
    //             $attributes = $this->ProductAttributes->find()
    //                 ->where(['ProductAttributes.id' => $item->product_attribute_id])
    //                 ->contain([
    //                     'Attributes' => [
    //                         'fields' => ['Attributes.name']
    //                     ],
    //                     'AttributeValues' => [
    //                         'fields' => ['AttributeValues.value']
    //                     ]
    //                 ])
    //                 ->first();

    //             if ($attributes) {
    //                 // Add attribute details to the item if found
    //                 $item->attributes = [
    //                     'attribute_name' => $attributes->attribute->name ?? '',
    //                     'attribute_value' => $attributes->attribute_value->value ?? ''
    //                 ];
    //             }
    //         }
    //     }

    //     $products = $this->Products->find()
    //         ->where(['Products.status' => 'A'])
    //         ->order(['Products.name' => 'ASC'])
    //         ->toArray();

    //     $this->set(compact('stockRequest', 'stockRequestItems', 'products'));

    // }

    private function sendStockRequestApprovalEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Requesting Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);

        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Request Approval
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Request Approval
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail; // Send to warehouse manager
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for approved stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Approved',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'request_date' => $stock_request->created ? $stock_request->created->format('d-m-Y') : 'N/A',
        ];

        $subject = "Stock Request #{$stock_request->id} - Approved";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_request_approval',
            $emailData
        );
    }

    private function sendStockRequestRejectionEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Requesting Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);

        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if ($requestorEmail) {
            $toEmails[] = $requestorEmail; // Send only to requesting showroom manager
        }

        // Set destination info for display (only needed for email body context)
        if (!empty($stock_request->to_showroomID)) {
            $toShowroom = $this->Showrooms->get($stock_request->to_showroomID);
            $toLocation = "Showroom: {$toShowroom->name}";
        } elseif (!empty($stock_request->warehouse_id)) {
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id);
            $toLocation = "Warehouse: {$warehouse->name}";
        } else {
            $toLocation = "N/A";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipient found for rejected stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Rejected',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => "Dear Manager,",
            'request_date' => $stock_request->created ? $stock_request->created->format('d-m-Y') : 'N/A',
        ];

        $subject = "Stock Request #{$stock_request->id} - Rejected";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_request_rejection', // You can keep using 'stock_request_approval' if no separate template is needed
            $emailData
        );
    }

    public function getVariants($productId)
    {
        $this->request->allowMethod(['get']);

        $requestTo = $this->request->getQuery('request_to');
        $requestId = $this->request->getQuery('id');

        if (!$requestTo || !$requestId) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => 'Invalid request parameters']));
        }

        $response = [
            'variants' => [],
            'attributes' => [],
            'quantity' => 0
        ];

        $stockCondition = ['ProductStocks.product_id' => $productId];

        if ($requestTo === 'warehouse') {
            $stockCondition['ProductStocks.warehouse_id'] = $requestId;
        } elseif ($requestTo === 'showroom') {
            $stockCondition['ProductStocks.showroom_id'] = $requestId;
        }

        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku'])
            ->where(['ProductVariants.product_id' => $productId, 'ProductVariants.status' => 'A'])
            ->toArray();

        foreach ($variants as $variant) {
            $variantStockCondition = $stockCondition;
            $variantStockCondition['ProductStocks.product_variant_id'] = $variant->id;

            // $variantStock = $this->ProductStocks->find()
            //     ->select(['quantity'])
            //     ->where($variantStockCondition)
            //     ->first();

            $variantStock = $this->ProductStocks->find()
                ->select([
                    'available_stock' => $this->ProductStocks->find()->func()->coalesce([
                        new QueryExpression('quantity - reserved_stock'),
                        0
                    ])
                ])
                ->where($variantStockCondition)
                ->first();

            $quantity = $variantStock ? $variantStock->available_stock : 0;

            // Fetch attributes without filtering by `product_variant_id`
            $attributes = $this->ProductAttributes->find()
                ->contain(['Attributes', 'AttributeValues'])
                ->where([
                    'ProductAttributes.product_id' => $productId,
                    'ProductAttributes.status' => 'A'
                ])
                ->toArray();

            $attributeData = [];

            foreach ($attributes as $attribute) {
                $attributeStockCondition = $variantStockCondition;
                $attributeStockCondition['ProductStocks.product_attribute_id'] = $attribute->id;

                // $attributeStock = $this->ProductStocks->find()
                //     ->select(['quantity'])
                //     ->where($attributeStockCondition)
                //     ->first();

                $attributeStock = $this->ProductStocks->find()
                    ->select([
                        'available_stock' => new QueryExpression('quantity - reserved_stock')
                    ])
                    ->where($attributeStockCondition)
                    ->first();

                $attributeQuantity = $attributeStock ? $attributeStock->available_stock : 0;

                $attributeData[] = [
                    'attribute_id' => $attribute->id,
                    'attribute_name' => $attribute->attribute->name,
                    'attribute_value' => $attribute->attribute_value->value,
                    'quantity' => $attributeQuantity
                ];
            }

            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
                'quantity' => $quantity,
                'attributes' => $attributeData
            ];
        }

        if (empty($response['variants'])) {
            // $totalStock = $this->ProductStocks->find()
            //     ->select(['quantity'])
            //     ->where($stockCondition)
            //     ->first();

            $totalStock = $this->ProductStocks->find()
                ->select([
                    'available_stock' => new QueryExpression('quantity - reserved_stock')
                ])
                ->where($stockCondition)
                ->first();

            $response['quantity'] = $totalStock ? $totalStock->available_stock : 0;
        }

        $this->set([
            'response' => $response,
            '_serialize' => ['response'],
        ]);

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }

        
    public function getVariantsWithoutQuantity($productId)
    {
        $this->request->allowMethod(['get']);

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : ''; 

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku', 'promotion_price']) // Fetch ID, name, and SKU of the variants
            ->where(['ProductVariants.product_id' => $productId]) // Filter by product_id
            ->andWhere(['ProductVariants.status' => 'A'])
            ->toArray();

        // Fetch product attributes and their values
        $productAttributes = $this->ProductAttributes->find()
            ->contain(['Attributes', 'AttributeValues'])
            ->where(['ProductAttributes.product_id' => $productId])
            ->andWhere(['ProductAttributes.status' => 'A'])
            ->toArray();

        // Prepare the response
        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $variantData = [];
        foreach ($variants as $variant) {

            $promotionPrice = is_numeric($variant->promotion_price) ? (float)$variant->promotion_price : 0;

            $formattedPromotionPrice = number_format($promotionPrice, 0, '', $thousandSeparator) . ' ' . $currencySymbol;

            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
                'promotion_price' => $formattedPromotionPrice,
            ];
        }

        foreach ($productAttributes as $productAttribute) {
            $response['attributes'][] = [
                'attribute_id' => $productAttribute->id,
                'attribute_name' => $productAttribute->attribute->name,
                'attribute_value' => $productAttribute->attribute_value->value,
            ];
        }

        $this->set([
                    'response' => $response,
                    '_serialize' => ['response'],
                ]);

        // Return JSON response
        return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
    }

}
