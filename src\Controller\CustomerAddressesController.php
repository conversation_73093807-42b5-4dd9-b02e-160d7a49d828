<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;

/**
 * CustomerAddresses Controller
 *
 * @property \App\Model\Table\CustomerAddressesTable $CustomerAddresses
 */
class CustomerAddressesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->CustomerAddresses->find()
            ->contain(['Customers', 'States', 'Cities', 'Municipalities']);
        $customerAddresses = $this->paginate($query);

        $this->set(compact('customerAddresses'));
    }

    /**
     * View method
     *
     * @param string|null $id Customer Address id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $customerAddress = $this->CustomerAddresses->get($id, contain: ['Customers', 'States', 'Cities', 'Municipalities', 'Orders']);
        $this->set(compact('customerAddress'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $customerAddress = $this->CustomerAddresses->newEmptyEntity();
        if ($this->request->is('post')) {
            $customerAddress = $this->CustomerAddresses->patchEntity($customerAddress, $this->request->getData());
            if ($this->CustomerAddresses->save($customerAddress)) {
                $this->Flash->success(__('The customer address has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The customer address could not be saved. Please, try again.'));
        }
        $customers = $this->CustomerAddresses->Customers->find('list', limit: 200)->all();
        $states = $this->CustomerAddresses->States->find('list', limit: 200)->all();
        $cities = $this->CustomerAddresses->Cities->find('list', limit: 200)->all();
        $municipalities = $this->CustomerAddresses->Municipalities->find('list', limit: 200)->all();
        $this->set(compact('customerAddress', 'customers', 'states', 'cities', 'municipalities'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Customer Address id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $customerAddress = $this->CustomerAddresses->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $customerAddress = $this->CustomerAddresses->patchEntity($customerAddress, $this->request->getData());
            if ($this->CustomerAddresses->save($customerAddress)) {
                $this->Flash->success(__('The customer address has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The customer address could not be saved. Please, try again.'));
        }
        $customers = $this->CustomerAddresses->Customers->find('list', limit: 200)->all();
        $states = $this->CustomerAddresses->States->find('list', limit: 200)->all();
        $cities = $this->CustomerAddresses->Cities->find('list', limit: 200)->all();
        $municipalities = $this->CustomerAddresses->Municipalities->find('list', limit: 200)->all();
        $this->set(compact('customerAddress', 'customers', 'states', 'cities', 'municipalities'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Customer Address id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $customerAddress = $this->CustomerAddresses->get($id);
        if ($this->CustomerAddresses->delete($customerAddress)) {
            $this->Flash->success(__('The customer address has been deleted.'));
        } else {
            $this->Flash->error(__('The customer address could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function editAddress($id = null)
    {
        if ($id === null) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Invalid ID']));
        }
        $customerAddress = $this->CustomerAddresses->get($id, ['contain' => []]);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            $house_no = $data['house_no'] ?? '';
            $addressLine1 = $data['address_line1'] ?? '';
            $addressLine2 = $data['address_line2'] ?? '';
            $landmark = $data['landmark'] ?? '';
            $city_id = $data['city_id'] ?? null;
            $municipality_id = $data['municipality_id'] ?? null;
            $zipcode = $data['zipcode'] ?? '';

            $city_name = '';
            if ($city_id) {
                $city = $this->CustomerAddresses->Cities->find()->where(['id' => $city_id])->first();
                $city_name = $city ? $city->city_name : '';
            }

            $municipality_name = '';
            if ($municipality_id) {
                $municipality = $this->CustomerAddresses->Municipalities->find()->where(['id' => $municipality_id])->first();
                $municipality_name = $municipality ? $municipality->name : '';
            }

            $fullAddress = trim("$house_no $addressLine1 $addressLine2 $landmark $municipality_name $city_name $zipcode");
            $fullAddress = str_replace(' ', '+', $fullAddress);

            // Google Maps API Key
            $apiKey = Configure::read('Settings.GOOGLE_MAP_API_KEY');
            $geocodeUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=$fullAddress&key=$apiKey";

            // Get response from Google API
            $response = file_get_contents($geocodeUrl);
            $Mapdata = json_decode($response, true);

            // Check if response is OK
            if ($Mapdata['status'] === 'OK') {
                $location = $Mapdata['results'][0]['geometry']['location'];
                $latitude = $location['lat'];
                $longitude = $location['lng'];
                $data['latitude'] = $latitude;
                $data['longitude'] = $longitude;
            } 
            $customerAddress = $this->CustomerAddresses->patchEntity($customerAddress, $data);
            if ($this->CustomerAddresses->save($customerAddress)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'success', 'address' => $customerAddress, 'message' => 'Customer Address saved.']));
            }
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Could not save the customer address.']));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => 'success', 'address' => $customerAddress]));
    }

    public function addCustomerAddress()
    {
        $this->request->allowMethod(['post']);

        $address = $this->CustomerAddresses->newEmptyEntity();
        $data = $this->request->getData();
            $house_no = $data['house_no'] ?? '';
            $addressLine1 = $data['address_line1'] ?? '';
            $addressLine2 = $data['address_line2'] ?? '';
            $landmark = $data['landmark'] ?? '';
            $city_id = $data['city_id'] ?? null;
            $municipality_id = $data['municipality_id'] ?? null;
            $zipcode = $data['zipcode'] ?? '';

            $city_name = '';
            if ($city_id) {
                $city = $this->CustomerAddresses->Cities->find()->where(['id' => $city_id])->first();
                $city_name = $city ? $city->city_name : '';
            }

            $municipality_name = '';
            if ($municipality_id) {
                $municipality = $this->CustomerAddresses->Municipalities->find()->where(['id' => $municipality_id])->first();
                $municipality_name = $municipality ? $municipality->name : '';
            }

            $fullAddress = trim("$house_no $addressLine1 $addressLine2 $landmark $municipality_name $city_name $zipcode");
            $fullAddress = str_replace(' ', '+', $fullAddress);

            // Google Maps API Key
            $apiKey = Configure::read('Settings.GOOGLE_MAP_API_KEY');
            $geocodeUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=$fullAddress&key=$apiKey";

            // Get response from Google API
            $response = file_get_contents($geocodeUrl);
            $Mapdata = json_decode($response, true);

            // Check if response is OK
            if ($Mapdata['status'] === 'OK') {
                $location = $Mapdata['results'][0]['geometry']['location'];
                $latitude = $location['lat'];
                $longitude = $location['lng'];
                $data['latitude'] = $latitude;
                $data['longitude'] = $longitude;
            } 
        $address = $this->CustomerAddresses->patchEntity($address, $data);

        if ($this->CustomerAddresses->save($address)) {
            $response = ['status' => 'success', 'message' => 'Address added successfully!'];
        } else {
            $response = ['status' => 'error', 'message' => 'Failed to add address.'];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }
}
