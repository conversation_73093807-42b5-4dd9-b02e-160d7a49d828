<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;


/**
 * ComingSoon Controller
 *
 * @property \App\Model\Table\ComingSoonTable $ComingSoon
 */
class ComingSoonController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }

    public function index()
    {
        $query = $this->ComingSoon->find()->where(['status !=' => 'D'])->applyOptions(['order' => ['product_name' => 'ASC']]);
        $comingSoon = $query->all();
        foreach ($comingSoon as $item) {
            $item->web_image = $this->Media->getCloudFrontURL($item->web_image);
        }
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('comingSoon', 'dateFormat', 'timeFormat', 'status', 'statusMap'));
    }

    /**
     * View method
     *
     * @param string|null $id Coming Soon id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $comingSoon = $this->ComingSoon->get($id, contain: []);
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $statuses = Configure::read('Constants.STATUS');
        $web_image = $this->Media->getCloudFrontURL($comingSoon->web_image);
        $mob_image = $this->Media->getCloudFrontURL($comingSoon->mob_image);
        $this->set(compact('comingSoon', 'dateFormat', 'statuses', 'web_image', 'mob_image'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $comingSoon = $this->ComingSoon->newEmptyEntity();
        $this->set([
            'webImageSize' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        if ($this->request->is('post')) {
            $comingSoonData = $this->request->getData();
            $allowedFormats = Configure::read('Constants.COMING_SOON_WEB_IMAGE_TYPE');
            $maxWebImageSize = Configure::read('Constants.COMING_SOON_WEB_IMAGE_SIZE') * 1024 * 1024;
            $maxMobileImageSize = Configure::read('Constants.COMING_SOON_MOB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($comingSoonData['web_image_file']) && $comingSoonData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                $web_image = $comingSoonData['web_image_file'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.COMING_SOON_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $comingSoonData['web_image'] = $uploadFolder . $webImageFile;
                    }
                }
            }

            if (!empty($comingSoonData['mobile_image_file']) && $comingSoonData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                $mob_image = $comingSoonData['mobile_image_file'];
                $mobImageName = trim($mob_image->getClientFilename());
                $mobImageSize = $mob_image->getSize();
                $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                if (!in_array($mobImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobImageSize > $maxMobileImageSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($mobImageName)) {
                    $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.COMING_SOON_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                    $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $comingSoonData['mob_image'] = $uploadFolder . $mobImageFile;
                    }
                }
            }
            $comingSoon = $this->ComingSoon->patchEntity($comingSoon, $comingSoonData);
            if ($this->ComingSoon->save($comingSoon)) {
                $this->Flash->success(__('The coming soon has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            // $this->log('er'.json_encode($comingSoon->getErrors()),'debug');
            $this->Flash->error(__('The coming soon could not be saved. Please, try again.'));
        }
        $this->set(compact('comingSoon'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Coming Soon id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $comingSoon = $this->ComingSoon->get($id, contain: []);
        $this->set([
            'webImageSize' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $web_image = $this->Media->getCloudFrontURL($comingSoon->web_image);
        $mob_image = $this->Media->getCloudFrontURL($comingSoon->mob_image);
        $statuses = Configure::read('Constants.STATUS');
        if ($this->request->is(['patch', 'post', 'put'])) {
            $comingSoonData = $this->request->getData();
            $allowedFormats = Configure::read('Constants.COMING_SOON_WEB_IMAGE_TYPE');
            $maxWebImageSize = Configure::read('Constants.COMING_SOON_WEB_IMAGE_SIZE') * 1024 * 1024;
            $maxMobileImageSize = Configure::read('Constants.COMING_SOON_MOB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($comingSoonData['web_image_file']) && $comingSoonData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                $web_image = $comingSoonData['web_image_file'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.COMING_SOON_WEB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.COMING_SOON_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $comingSoonData['web_image'] = $uploadFolder . $webImageFile;
                    }
                }
            }

            if (!empty($comingSoonData['mobile_image_file']) && $comingSoonData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                $mob_image = $comingSoonData['mobile_image_file'];
                $mobImageName = trim($mob_image->getClientFilename());
                $mobImageSize = $mob_image->getSize();
                $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                if (!in_array($mobImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobImageSize > $maxMobileImageSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.COMING_SOON_MOB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($mobImageName)) {
                    $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.COMING_SOON_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                    $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $comingSoonData['mob_image'] = $uploadFolder . $mobImageFile;
                    }
                }
            }
            $comingSoon = $this->ComingSoon->patchEntity($comingSoon, $comingSoonData);
            if ($this->ComingSoon->save($comingSoon)) {
                $this->Flash->success(__('The coming soon has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The coming soon could not be saved. Please, try again.'));
        }
        $this->set(compact('comingSoon', 'web_image', 'mob_image', 'statuses'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Coming Soon id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $comingSoon = $this->ComingSoon->get($id);
        $response = ['success' => false, 'message' => 'The Coming Soon record could not be deleted. Please, try again.'];
        if ($comingSoon) {
            if ($this->ComingSoon->delete($comingSoon)) {
                $response = ['success' => true, 'message' => 'The Coming Soon record has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Coming Soon record could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Coming Soon record does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $comingSoonId = $this->request->getData('image_id');

        if (!$imageType || !$comingSoonId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $comingSoon = $this->ComingSoon->get($comingSoonId);

        if (!$comingSoon) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Coming Soon record not found']));
        }

        if ($imageType === 'web') {
            $uploadFolder = Configure::read('Constants.COMING_SOON_WEB_IMAGE');
        } else if ($imageType === 'mobile') {
            $uploadFolder = Configure::read('Constants.COMING_SOON_MOB_IMAGE');
        }

        $imageField = $imageType === 'web' ? 'web_image' : 'mob_image';
        $existingImagePath = $comingSoon->{$imageField};

        if ($existingImagePath) {
            $filePath = WWW_ROOT . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $comingSoon->{$imageField} = null;
            if ($this->ComingSoon->save($comingSoon)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update Ad Block']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }
}
