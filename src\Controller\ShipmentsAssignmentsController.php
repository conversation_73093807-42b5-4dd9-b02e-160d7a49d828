<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\I18n\FrozenDate;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ShipmentsAssignmentsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Cities;
    protected $Zones;
    protected $Orders;
    protected $OrderItems;
    protected $Users;
    protected $Drivers;
    protected $DeliveryPartners;
    protected $ProductStocks;
    protected $Shipments;
    protected $ShipmentItems;
    protected $Products;
    protected $ProductVariants;
    protected $ZoneMunicipalities;
    protected $Showrooms;
    protected $Warehouses;
    protected $Municipalities;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Cities = $this->fetchTable('Cities');
        $this->Zones = $this->fetchTable('Zones');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Users = $this->fetchTable('Users');
        $this->Drivers = $this->fetchTable('Drivers');
        $this->DeliveryPartners = $this->fetchTable('DeliveryPartners');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentItems = $this->fetchTable('ShipmentItems');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ZoneMunicipalities = $this->fetchTable('ZoneMunicipalities');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Municipalities = $this->fetchTable('Municipalities');
    }
    
    public function index()
    {
        $shipmentsQuery = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A'])
                             ->contain(['Cities', 'Zones', 'Municipalities', 'Orders']);
                },
                'Drivers' => function ($q) {
                    return $q->leftJoinWith('Users')
                        ->select([
                            'Drivers.id',
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name',
                            'full_name' => $q->func()->concat([
                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                            ])
                        ]);
                },
                'DeliveryPartners'
            ])
            ->select([
                'Shipments.id',
                'Shipments.sender_type',
                'Shipments.senderID',
                'Shipments.created',
                'Shipments.delivery_status',
                'Shipments.driver_id',
                'sender_name' => $this->Shipments->find()
                    ->newExpr()
                    ->add("(CASE 
                            WHEN Shipments.sender_type = 'showroom' 
                            THEN (SELECT name FROM showrooms WHERE showrooms.id = Shipments.senderID) 
                            WHEN Shipments.sender_type = 'warehouse' 
                            THEN (SELECT name FROM warehouses WHERE warehouses.id = Shipments.senderID) 
                            ELSE 'N/A' 
                        END)"),
                'partner_name' => $this->Shipments->find()
                ->newExpr()
                ->add("(CASE 
                        WHEN Shipments.delivery_partner_id IS NOT NULL 
                        THEN DeliveryPartners.partner_name 
                        ELSE NULL 
                    END)")
            ])
            ->where([
                'Shipments.shipment_status NOT IN' => ['Cancelled'],
                'Shipments.driver_id IS' => null,
                'Shipments.delivery_partner_id IS' => null,
                'Shipments.status' => 'A'
            ])
            ->order(['Shipments.created' => 'DESC']);

        $shipments = $shipmentsQuery->toArray();

        $cities = $this->Cities->find()
            ->order(['Cities.city_name' => 'ASC'])
            ->toArray();

        // Query the Drivers table and join with Users to get driver names
        $drivers = $this->Drivers->find()
            ->select(['Drivers.id', 'Users.first_name', 'Users.last_name'])
            ->where(['Users.status' => 'A', 'Users.user_type' => 'Driver'])
            ->contain(['Users'])->toArray();

        $delivery_partners = $this->DeliveryPartners->find('all')
            ->where(['DeliveryPartners.status' => 'A'])->toArray();

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();

        $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

        $zones = $this->Zones->find()
            ->where(['Zones.status' => 'A'])
            ->order(['Zones.name' => 'ASC'])
            ->toArray();

        $municipalities = $this->Municipalities->find()
            ->where(['Municipalities.status' => 'A'])
            ->order(['Municipalities.name' => 'ASC'])
            ->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('shipments', 'cities', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'drivers', 'delivery_partners', 'showrooms', 'warehouses', 'zones', 'municipalities'));
    }

    public function assignShipment()
    {
        $this->request->allowMethod(['post']);

        try {
            $data = $this->request->getData();

            if ($this->request->is('json')) {
                $data = json_decode(file_get_contents('php://input'), true);
            }

            $deliveryType = $data['delivery_type'] ?? null;
            $deliveryId = $data['deliveryId'] ?? null;
            $shipmentIds = $data['shipment_ids'] ?? [];

            if (empty($deliveryType) || empty($deliveryId) || empty($shipmentIds)) {
                return $this->response->withStatus(400)->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Missing delivery type, delivery ID, or shipment IDs.')
                ]));
            }

            $updatedCount = 0;
            $failedShipments = [];

            foreach ($shipmentIds as $shipmentId) {
                try {
                    $shipment = $this->Shipments->get($shipmentId);

                    $shipment->delivery_type = $deliveryType;

                    if ($deliveryType === 'Driver') {
                        $shipment->driver_id = $deliveryId;
                        $shipment->delivery_partner_id = null;
                    } else {
                        $shipment->delivery_partner_id = $deliveryId;
                        $shipment->driver_id = null;
                    }

                    if ($this->Shipments->save($shipment)) {
                        $updatedCount++;

                        // If assigned to driver, send shipment details notification
                        if ($deliveryType === 'Driver') {

                            // Update related shipment_orders
                            $shipmentOrders = $this->Shipments->ShipmentOrders->find()
                                ->where(['shipment_id' => $shipmentId])
                                ->all();

                            foreach ($shipmentOrders as $shipmentOrder) {
                                $shipmentOrder->driver_id = $deliveryId;
                                $shipmentOrder->expected_delivery_date = date('Y-m-d');
                                $this->Shipments->ShipmentOrders->save($shipmentOrder);
                            }
                            
                            $shipmentDetails = $this->getShipmentDetails($shipmentId);
                            $this->sendDriverShipmentNotification($shipmentDetails);
                        }

                    } else {
                        $failedShipments[] = $shipmentId;
                    }
                } catch (\Exception $e) {
                    $failedShipments[] = $shipmentId;
                }
            }

            if (!empty($failedShipments)) {
                return $this->response->withStatus(207)->withType('application/json')->withStringBody(json_encode([
                    'status' => 'partial',
                    'message' => __('Some shipments could not be updated.'),
                    'failed_ids' => $failedShipments
                ]));
            }

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'success',
                'message' => __('{0} shipments updated successfully.', $updatedCount)
            ]));

        } catch (\Exception $e) {
            return $this->response->withStatus(500)->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ]));
        }
    }

    private function getShipmentDetails($id)
    {
        $shipment = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A'])
                        ->contain([
                            'CustomerAddresses',
                            'ShipmentOrderItems' => function ($q) {
                                return $q->where(['ShipmentOrderItems.status' => 'A'])
                                    ->contain([
                                        'OrderItems' => function ($q) {
                                            return $q->contain([
                                                'Products',
                                                'ProductVariants',
                                                'Orders' => [
                                                    'Customers' => function ($q) {
                                                        return $q->contain(['Users'])->select([
                                                            'Customers.id',
                                                            'Users.first_name',
                                                            'Users.last_name',
                                                            'full_name' => $q->func()->concat([
                                                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                                                            ])
                                                        ]);
                                                    }
                                                ]
                                            ]);
                                        }
                                    ]);
                            }
                        ]);
                },
                'Drivers' => function ($q) {
                    return $q->contain(['Users'])->select([
                        'Drivers.id',
                        'Users.email',
                        'Users.first_name',
                        'Users.last_name',
                    ]);
                }
            ])
            ->where(['Shipments.id' => $id])
            ->first();

        // Resolve sender name
        $senderName = 'N/A';
        if ($shipment->sender_type === 'Showroom') {
            $showroom = $this->Showrooms->find()
                ->select(['name', 'address'])
                ->where(['id' => $shipment->senderID])
                ->first();
            $senderName = $showroom->name ?? 'N/A';
            $senderAddress = $showroom->address ?? 'N/A';
        } elseif ($shipment->sender_type === 'Warehouse') {

            $warehouse = $this->Warehouses->find()
                ->select(['Warehouses.name', 'Warehouses.warehouse_no_area', 'Warehouses.address_line1'])
                ->contain([
                    'Cities' => function ($q) {
                        return $q->select(['Cities.id', 'Cities.city_name']);
                    },
                    'Municipalities' => function ($q) {
                        return $q->select(['Municipalities.id', 'Municipalities.name']);
                    }
                ])
                ->where(['Warehouses.id' => $shipment->senderID])
                ->first();

            $senderName = $warehouse->name ?? 'N/A';

            // Build sender address with city and municipality
            $addressParts = [];
            if (!empty($warehouse->warehouse_no_area)) {
                $addressParts[] = $warehouse->warehouse_no_area;
            }
            if (!empty($warehouse->address_line1)) {
                $addressParts[] = $warehouse->address_line1;
            }
            if (!empty($warehouse->city->city_name)) {
                $addressParts[] = $warehouse->city->city_name;
            }
            if (!empty($warehouse->municipality->name)) {
                $addressParts[] = $warehouse->municipality->name;
            }

            $senderAddress = !empty($addressParts) ? implode(', ', $addressParts) : 'N/A';

        }

        $shipment->sender_name = $senderName;
        $shipment->sender_address = $senderAddress;

        return $shipment;
    }

    private function sendDriverShipmentNotification($shipment)
    {
        if (empty($shipment->driver) || empty($shipment->driver->user->email)) {
            \Cake\Log\Log::warning("Driver email not found for shipment ID: {$shipment->id}");
            return;
        }

        $toEmail = [$shipment->driver->user->email];

        $driverName = $shipment->driver->user->first_name . ' ' . $shipment->driver->user->last_name;

        $ordersData = [];
        foreach ($shipment->shipment_orders as $shipmentOrder) {
            $customerAddress = $shipmentOrder->customer_address ?? null;
            $addressParts = array_filter([
                $customerAddress->address_line1 ?? null,
                $customerAddress->address_line2 ?? null,
                $customerAddress->landmark ?? null,
                $customerAddress->zipcode ?? null
            ]);
            $deliveryAddress = implode(', ', $addressParts);

            foreach ($shipmentOrder->shipment_order_items as $shipmentOrderItem) {
                $orderItem = $shipmentOrderItem->order_item;
                $order = $orderItem->order ?? null;
                $product = $orderItem->product ?? null;
                $productVariant = $orderItem->product_variant ?? null;
                $customer = $order->customer ?? null;
                $user = $customer->user ?? null;

                $ordersData[] = [
                    'order_id' => $order->id ?? 'N/A',
                    'product_name' => $product->name ?? 'N/A',
                    'variant' => $productVariant->variant_name ?? '',
                    'quantity' => $shipmentOrderItem->quantity ?? 0,
                    'delivery_address' => $deliveryAddress ?? 'N/A',
                    'customer_name' => $user ? $user->first_name . ' ' . $user->last_name : 'N/A',
                    'estimated_time' => $shipmentOrder->expected_delivery_date ? $shipmentOrder->expected_delivery_date->format('d-m-Y') : 'N/A',
                ];
            }
        }

        $emailData = [
            'shipment_id' => $shipment->id,
            'driver_name' => $driverName,
            'sender_name' => $shipment->sender_name,
            'sender_address' => $shipment->sender_address,
            'orders' => $ordersData,
        ];

        $subject = "New Shipment Assignment - Shipment #{$shipment->id}";

        $this->Global->send_email(
            $toEmail,
            null,
            $subject,
            'driver_shipment_notification',
            $emailData
        );
    }

}
