<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;

/**
 * SupportTickets Model
 *
 * @property \App\Model\Table\SupportCategoriesTable&\Cake\ORM\Association\BelongsTo $SupportCategories
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\BelongsTo $Orders
 * @property \App\Model\Table\SupportTicketImagesTable&\Cake\ORM\Association\HasMany $SupportTicketImages
 * @property \App\Model\Table\SupportTicketUpdatesTable&\Cake\ORM\Association\HasMany $SupportTicketUpdates
 *
 * @method \App\Model\Entity\SupportTicket newEmptyEntity()
 * @method \App\Model\Entity\SupportTicket newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\SupportTicket> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\SupportTicket get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\SupportTicket findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\SupportTicket patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\SupportTicket> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\SupportTicket|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\SupportTicket saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\SupportTicket>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SupportTicket>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\SupportTicket>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SupportTicket> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\SupportTicket>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SupportTicket>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\SupportTicket>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SupportTicket> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class SupportTicketsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('support_tickets');
        $this->setDisplayField('issue_name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('SupportCategories', [
            'foreignKey' => 'support_category_id',
        ]);
        $this->belongsTo('Orders', [
            'foreignKey' => 'order_id',
        ]);

        $this->belongsTo('CreatedByUsers', [
            'className' => 'Users', // Specify the target table
            'foreignKey' => 'created_by', // The foreign key in the `expenses` table
            'joinType' => 'INNER',
        ]);

        $this->hasMany('SupportTicketImages', [
            'foreignKey' => 'support_ticket_id',
        ]);

        $this->hasMany('SupportTicketUpdates', [
            'foreignKey' => 'support_ticket_id'
        ]);

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id', // Ensure 'customer_id' exists in the SupportTickets table
            'joinType' => 'LEFT', // Optional: Use 'INNER' if customers are always required
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation.Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('ticketID')
            ->maxLength('ticketID', 255)
            ->allowEmptyString('ticketID');

        $validator
            ->scalar('issue_name')
            ->maxLength('issue_name', 500)
            ->requirePresence('issue_name', 'create')
            ->notEmptyString('issue_name');

        $validator
            ->integer('support_category_id')
            ->allowEmptyString('support_category_id');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('priority')
            ->notEmptyString('priority');

        $validator
            ->nonNegativeInteger('order_id')
            ->allowEmptyString('order_id');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->nonNegativeInteger('created_by')
            ->allowEmptyString('created_by');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add(
            function ($entity, $options) {
                $supportCategoryId = $entity->get('support_category_id');
                $supportSubcategory = TableRegistry::getTableLocator()->get('SupportSubcategories')
                    ->find()
                    ->where(['id' => $supportCategoryId])  // Make sure it's a subcategory ID
                    ->first();

                return $supportSubcategory ? true : false; // If a subcategory exists, return true
            },
            'validSupportSubcategory',
            ['errorField' => 'support_category_id']
        );
        $rules->add($rules->existsIn(['order_id'], 'Orders'), ['errorField' => 'order_id']);

        return $rules;
    }

    //S add ticket
    public function addTicket($data)
    {
        $save_ticket = $this->newEmptyEntity();
        $save_ticket->ticketID = $data['ticketID'];
        $save_ticket->issue_name = $data['issue_name'];
        $save_ticket->support_category_id = $data['support_category_id'];
        $save_ticket->description = $data['description'];
        if(isset($data['showroom_id'])){
            $save_ticket->showroom_id = $data['showroom_id'];
        }
        if(isset($data['priority'])){
            $save_ticket->priority = $data['priority'];
        }
        if(isset($data['status'])){
            $save_ticket->status = $data['status'];
        }
        $save_ticket->created_by = $data['created_by'];
        if(isset($data['order_id'])){
            $save_ticket->order_id = $data['order_id'];
        }
        if(isset($data['customer_id'])){
            $save_ticket->customer_id = $data['customer_id'];
        }
        if(isset($data['customer_email'])){
            $save_ticket->customer_email = $data['customer_email'];
        }
        if($this->save($save_ticket)) {
            return $save_ticket->id;
        } else {
            return false;
        }
    }

    //S
    public function listSupportTickets($userId, $showroom, $roleName, $filter_ticket_status, $filter_support_category, $search_str, $page, $limit) {

        // Build the query with pagination
        $ticketQuery = $this->find()
            ->select([
                'SupportTickets.id',
                'SupportTickets.ticketID',
                'SupportTickets.support_category_id',
                'SupportTickets.issue_name',
                'SupportTickets.description',
                'SupportTickets.customer_id',
                'SupportTickets.customer_email',
                'SupportTickets.copy_to',                
                'SupportTickets.phone_number',
                'SupportTickets.priority',
                'SupportTickets.status',
                'SupportTickets.created',
                'SupportTickets.modified',
                'SupportTickets.closed_at'
            ])
            ->contain([
                'Orders',
                'SupportCategories' => ['fields' => ['id', 'name']],
                'CreatedByUsers' => function ($q2) {
                    return $q2->select(['CreatedByUsers.id', 'CreatedByUsers.first_name', 'CreatedByUsers.last_name', 'CreatedByUsers.profile_pic']);
                },
                'Customers' => function ($q3) {
                    return $q3->select([
                        'Customers.id', 'Customers.user_id', 'Customers.country_code', 'Customers.phone_number', 'Customers.profile_photo'
                    ]);
                }
            ])
            ->order(['SupportTickets.status' => 'DESC'])
            ->page($page, $limit); // Pagination here

        if($roleName == 'Showroom Supervisor') {
           $ticketQuery->where(['SupportTickets.showroom_id IN' => $showroom]);
        } elseif($roleName == 'Customer') {
           $ticketQuery->where(['SupportTickets.customer_id' => $userId]);
        } else {
           $ticketQuery->where(['SupportTickets.created_by' => $userId]);
        }

        // Apply filters if present
        if (!empty($filter_support_category)) {
            $ticketQuery->where(['SupportTickets.support_category_id' => $filter_support_category]);
        }

        if (!empty($filter_ticket_status)) {
            $ticketQuery->where(['SupportTickets.status' => $filter_ticket_status]);
        }

        if (!empty($search_str)) {
            $ticketQuery->where([
            'OR' => [
                    'SupportTickets.ticketID LIKE' => '%' . $search_str . '%',
                    'SupportTickets.issue_name LIKE' => '%' . $search_str . '%'
                ]
            ]);
        }

        // Execute the query
        $tickets = $ticketQuery->all();
        return $tickets->toArray();
    }

    //S
    public function unreadSupportTickets() {

        // Build the query with pagination
        $ticketQuery = $this->find()
            ->select([
                'SupportTickets.id',
                'SupportTickets.ticketID',
                'SupportTickets.support_category_id',
                'SupportTickets.issue_name',
                'SupportTickets.description',
                'SupportTickets.priority',
                'SupportTickets.status',
                'SupportTickets.created',
                'SupportTickets.closed_at'
            ])
            ->contain([
                'Orders',
                'SupportCategories' => ['fields' => ['id', 'name']],
                'CreatedByUsers' => function ($q2) {
                    return $q2->select(['CreatedByUsers.id', 'CreatedByUsers.first_name', 'CreatedByUsers.last_name']);
                }
            ])
            ->where(['SupportTickets.is_read' => 0])
            ->order(['SupportTickets.created' => 'DESC']);

        // Execute the query
        $tickets = $ticketQuery->all();
        return $tickets->toArray();
    }

    //S
    public function viewTicketDetail($support_ticket_id) {

        $ticket = $this->find()
        ->where(['SupportTickets.id' => $support_ticket_id])
        ->contain([
            'CreatedByUsers' => function ($q2) {
                    return $q2->select(['CreatedByUsers.id', 'CreatedByUsers.first_name', 'CreatedByUsers.last_name', 'CreatedByUsers.profile_pic']);
            },
            'SupportTicketImages' => function ($q) {
                return $q->select(['id', 'support_ticket_id', 'support_ticket_update_id', 'image'])
                ->where(['SupportTicketImages.support_ticket_update_id IS' => null]);
            },
            'SupportTicketUpdates' => [
                'UpdatedByUsers' => function ($q3) {
                    return $q3->select([
                            'id', 'first_name', 'last_name', 'profile_pic'
                        ]); // Fetch only the necessary fields
                },
                'SupportTicketImages' => function ($q1) {
                    return $q1->select(['id', 'support_ticket_id', 'support_ticket_update_id', 'image'])
                    ->where(['SupportTicketImages.support_ticket_id IS' => null]);
                }
            ]
        ])
        ->first();
        return $ticket;
    }

    //S
    public function viewCustTicketDetail($support_ticket_id) {

        $ticket = $this->find()
        ->where(['SupportTickets.id' => $support_ticket_id])
        ->contain([
            'SupportCategories' => ['fields' => ['id', 'name']],
            'Orders' => ['fields' => ['id', 'order_number', 'status']],
            'Customers' => ['fields' => ['id', 'user_id', 'country_code', 'phone_number', 'profile_photo']],
            'Customers.Users' => ['fields' => ['id', 'first_name', 'last_name']],
            'CreatedByUsers' => function ($q2) {
                    return $q2->select(['CreatedByUsers.id', 'CreatedByUsers.first_name', 'CreatedByUsers.last_name', 'CreatedByUsers.profile_pic']);
            },
            'SupportTicketImages' => function ($q) {
                return $q->select(['id', 'support_ticket_id', 'support_ticket_update_id', 'image'])
                ->where(['SupportTicketImages.support_ticket_update_id IS' => null]);
            },
            'SupportTicketUpdates' => [
                'UpdatedByUsers' => function ($q3) {
                    return $q3->select([
                            'id', 'first_name', 'last_name', 'profile_pic'
                        ])
                        ->leftJoinWith('Customers', function ($q) {
                            return $q->select(['id', 'profile_photo']);
                        }) ; // Fetch only the necessary fields
                },
                'SupportTicketImages' => function ($q1) {
                    return $q1->select(['id', 'support_ticket_id', 'support_ticket_update_id', 'image'])
                    ->where(['SupportTicketImages.support_ticket_id IS' => null]);
                }
            ]

        ])
        ->first();
        return $ticket;
    }

    //S
    public function changeStatus ($ticket_id, $data)
    {
        $ticket_entity = $this->get($ticket_id);
        $statusupdate['status'] = $data['status'];
        $statusupdate['closed_at'] = date('Y-m-d H:i:s');
        $ticket_entity = $this->patchEntity($ticket_entity, $statusupdate);
        $res = $this->save($ticket_entity);
        if($res) {
            return true;
        } else {
            return false;
        }
    }

    public function listSupportTicketsData($filter_ticket_status, $filter_support_category, $search_str, $page, $limit, $priority = null, $customer_id = null, $is_read = null, $ticketID = null)
    {
// Base query
$ticketQuery = $this->find()
    ->select([
        'SupportTickets.id',
        'SupportTickets.is_read',
        'SupportTickets.ticketID',
        'SupportTickets.support_category_id',
        'SupportTickets.issue_name',
        'SupportTickets.customer_id',
        'SupportTickets.customer_email',
        'SupportTickets.copy_to',
        'SupportTickets.modified',
        'SupportTickets.phone_number',
        'SupportTickets.description',
        'SupportTickets.priority',
        'SupportTickets.status',
        'SupportTickets.created',
        'SupportTickets.closed_at',
        'Orders.id',
        'Orders.order_number',
    ])
    ->contain([
        'Orders',
        'SupportCategories' => [
            'fields' => ['id', 'name']
        ],
        'CreatedByUsers' => function ($q) {
            return $q->select(['id', 'first_name', 'last_name']);
        },
        'Customers' => function ($q) {
            return $q->select([
                'id', 'user_id', 'country_code', 'phone_number', 'profile_photo'
            ])
            ->leftJoinWith('Users', function ($q) {
                return $q->select(['id', 'first_name', 'last_name']);
            });
        },
        'SupportTicketUpdates' => function ($q) {
            return $q->select([
                    'support_ticket_id', 'updated_by', 'updated_at','comment'
                ])
                ->order(['updated_at' => 'DESC'])
                ->limit(1);
        }
    ])
    ->leftJoinWith('SupportTicketUpdates.UpdatedByUsers', function ($q) {
        return $q->order(['SupportTicketUpdates.updated_at' => 'DESC'])->limit(1);
    })
    ->group(['SupportTickets.ticketID'])
    ->order(['COALESCE(SupportTicketUpdates.updated_at, SupportTickets.created)' => 'DESC']);

// Apply filters
if (!empty($priority)) {
    $ticketQuery->where(['SupportTickets.priority' => $priority]);
}

if (!empty($filter_support_category)) {
    $ticketQuery->where(['SupportTickets.support_category_id' => $filter_support_category]);
}

if (!empty($filter_ticket_status)) {
    $ticketQuery->where(['SupportTickets.status' => $filter_ticket_status]);
}

if (!empty($customer_id)) {
    $ticketQuery->where(['SupportTickets.customer_id' => $customer_id]);
}

if ($is_read !== null) {
    $ticketQuery->where(['SupportTickets.is_read' => $is_read]);
}

if (!empty($ticketID)) {
    $ticketQuery->where(['SupportTickets.ticketID' => $ticketID]);
}

if (!empty($search_str)) {
    $ticketQuery->where([
        'OR' => [
            'SupportTickets.ticketID LIKE' => "%$search_str%",
            'SupportTickets.issue_name LIKE' => "%$search_str%"
        ]
    ]);
}

// Get total count before pagination
$totalTickets = $ticketQuery->count();

// Apply pagination
$tickets = $ticketQuery
    ->limit($limit)
    ->page((int)$page)
    ->all();

        // Calculate pagination metadata
        $totalPages = ceil($totalTickets / $limit);
        $nextPage = ($page < $totalPages) ? $page + 1 : null;
        $prevPage = ($page > 1) ? $page - 1 : null;

        return [
            'tickets' => $tickets->toArray(),
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'next_page' => $nextPage,
                'prev_page' => $prevPage,
                'total_records' => $totalTickets
            ]
        ];
    }
    //Ax
    public function webViewTicketDetail($support_ticket_id) {

        $supportTicket = $this->find()
        ->where(['SupportTickets.id' => $support_ticket_id])
        ->contain([
            'SupportCategories' => ['fields' => ['id', 'name']],
            'Orders' => ['fields' => ['id', 'order_number', 'status']],
            'CreatedByUsers' => ['fields' => ['id', 'first_name', 'last_name', 'email']],
            'Customers' => function ($q) {
                return $q->select(['id', 'user_id', 'country_code', 'phone_number', 'profile_photo'])
                         ->leftJoinWith('Users', function ($q) {
                             return $q->select(['id', 'first_name', 'last_name']);
                         });
            },
            'SupportTicketImages' => ['fields' => ['id', 'support_ticket_id', 'image', 'status', 'created']],
            'SupportTicketUpdates' => function ($q) {
                return $q->select(['id', 'support_ticket_id', 'status', 'comment', 'updated_by', 'updated_at'])
                    ->order(['SupportTicketUpdates.id' => 'ASC'])
                    ->contain([
                        'SupportTicketImages' => function ($q) {
                            return $q->select(['id', 'support_ticket_id', 'support_ticket_update_id', 'image'])
                                     ->where(['SupportTicketImages.support_ticket_update_id IS NOT' => null]);
                        },
                        'UpdatedByUsers' => function ($q) {
                            return $q->select(['id', 'first_name', 'last_name'])
                                     ->leftJoinWith('Customers', function ($q) {
                                         return $q->select(['id', 'profile_photo']);
                                     })
                                     ->leftJoinWith('Roles', function ($q) {
                                         return $q->select(['name']);
                                     });
                        }
                    ]);
            }
        ])
        ->first();
    
    

        return $supportTicket ;
    }

    public function addTicketData($data)
    {
        $ticket = $this->newEntity($data);
        if ($this->save($ticket)) {
            return $ticket->id;
        }
        return false;
    }

}
