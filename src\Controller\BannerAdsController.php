<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;


/**
 * BannerAds Controller
 *
 * @property \App\Model\Table\BannerAdsTable $BannerAds
 */
class BannerAdsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }

    public function index()
    {
        $query = $this->BannerAds->find()->where(['status !=' => 'D'])->applyOptions(['order' => ['title' => 'ASC']]);
        $bannerAds = $query->all();
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $adTypes = Configure::read('Constants.BANNER_AD_TYPES');
        $adLocs = Configure::read('Constants.BANNER_AD_LOC');

        $this->set(compact('bannerAds', 'status', 'statusMap', 'adTypes', 'adLocs'));
    }

    /**
     * View method
     *
     * @param string|null $id Banner Ad id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $bannerAd = $this->BannerAds->get($id, contain: ['Categories', 'Brands', 'Products']);
        $adTypes = Configure::read('Constants.BANNER_AD_TYPES');
        $adLocs = Configure::read('Constants.BANNER_AD_LOC');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $bannerAdviewVal = $bannerAd->display_in_web == 1 && $bannerAd->display_in_mobile == 1
            ? 'Both'
            : ($bannerAd->display_in_web == 1
                ? 'Web'
                : ($bannerAd->display_in_mobile == 1
                    ? 'Mobile'
                    : '')
            );
        $statuses = Configure::read('Constants.STATUS');
        $web_image = $this->Media->getCloudFrontURL($bannerAd->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($bannerAd->mobile_image);
        $this->set(compact('bannerAd', 'adTypes', 'adLocs', 'dateFormat', 'bannerAdviewVal', 'statuses','web_image','mobile_image'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $bannerAd = $this->BannerAds->newEmptyEntity();
        $maxId = $this->BannerAds->find()
            ->select(['max_id' => $this->BannerAds->find()->func()->max('id')])
            ->first()
            ->max_id;

        $nextId = $maxId + 1;
        $adTypes = Configure::read('Constants.BANNER_AD_TYPES');
        $adLocs = Configure::read('Constants.BANNER_AD_LOC');

        $this->set([
            'webImageSize' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_HEIGHT'),
        ]);

        $this->set(compact('bannerAd', 'adTypes', 'adLocs', 'nextId'));
        if ($this->request->is('post')) {
            $bannerAdData = $this->request->getData();
            $allowedFormats = Configure::read('Constants.ADBLOCK_WEB_IMAGE_TYPE');
            $maxWebImageSize = Configure::read('Constants.ADBLOCK_WEB_IMAGE_SIZE') * 1024 * 1024;
            $maxMobileImageSize = Configure::read('Constants.ADBLOCK_MOB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($bannerAdData['web_image_file']) && $bannerAdData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                $web_image = $bannerAdData['web_image_file'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_HEIGHT');

                // if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                //     return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                // }

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.ADBLOCK_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerAdData['web_image'] = $uploadFolder . $webImageFile;
                    }
                }
            }

            if (!empty($bannerAdData['mobile_image_file']) && $bannerAdData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                $mob_image = $bannerAdData['mobile_image_file'];
                $mobImageName = trim($mob_image->getClientFilename());
                $mobImageSize = $mob_image->getSize();
                $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                if (!in_array($mobImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobImageSize > $maxMobileImageSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_HEIGHT');

                // if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                //     return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                // }

                if (!empty($mobImageName)) {
                    $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.ADBLOCK_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                    $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerAdData['mobile_image'] = $uploadFolder . $mobImageFile;
                    }
                }
            }
            $bannerAdData['display_in_web'] = !empty($bannerAdData['adblock-web']) ? 1 : 0;
            $bannerAdData['display_in_mobile'] = !empty($bannerAdData['adblock-mobile']) ? 1 : 0;
            $bannerAd = $this->BannerAds->patchEntity($bannerAd, $bannerAdData);
            if ($this->BannerAds->save($bannerAd)) {
                $this->Flash->success(__('The banner ad has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The banner ad could not be saved. Please, try again.'));
        }
        // $categories = $this->BannerAds->Categories->find('list', limit: 200)->all();
        // $brands = $this->BannerAds->Brands->find('list', limit: 200)->all();
        // $products = $this->BannerAds->Products->find('list', limit: 200)->all();
        // $this->set(compact('bannerAd'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Banner Ad id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $bannerAd = $this->BannerAds->get($id, contain: []);
        $adTypes = Configure::read('Constants.BANNER_AD_TYPES');
        $adLocs = Configure::read('Constants.BANNER_AD_LOC');
        $nextId = $bannerAd['id'];
        $statuses = Configure::read('Constants.STATUS');
        $this->set([
            'webImageSize' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $web_image = $this->Media->getCloudFrontURL($bannerAd->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($bannerAd->mobile_image);
        $this->set(compact('bannerAd', 'adTypes', 'adLocs', 'nextId', 'statuses', 'web_image', 'mobile_image'));
        if ($this->request->is(['patch', 'post', 'put'])) {
            $bannerAdData = $this->request->getData();
            $allowedFormats = Configure::read('Constants.ADBLOCK_WEB_IMAGE_TYPE');
            $maxWebImageSize = Configure::read('Constants.ADBLOCK_WEB_IMAGE_SIZE') * 1024 * 1024;
            $maxMobileImageSize = Configure::read('Constants.ADBLOCK_MOB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($bannerAdData['web_image_file']) && $bannerAdData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                $web_image = $bannerAdData['web_image_file'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.ADBLOCK_WEB_IMAGE_MAX_HEIGHT');

                // if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                //     return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                // }

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.ADBLOCK_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerAdData['web_image'] = $uploadFolder . $webImageFile;
                    }
                }
            }

            if (!empty($bannerAdData['mobile_image_file']) && $bannerAdData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                $mob_image = $bannerAdData['mobile_image_file'];
                $mobImageName = trim($mob_image->getClientFilename());
                $mobImageSize = $mob_image->getSize();
                $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                if (!in_array($mobImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobImageSize > $maxMobileImageSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.ADBLOCK_MOB_IMAGE_MAX_HEIGHT');

                // if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                //     return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                // }

                if (!empty($mobImageName)) {
                    $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.ADBLOCK_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                    $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerAdData['mobile_image'] = $uploadFolder . $mobImageFile;
                    }
                }
            }
            $bannerAdData['display_in_web'] = !empty($bannerAdData['adblock-web']) ? 1 : 0;
            $bannerAdData['display_in_mobile'] = !empty($bannerAdData['adblock-mobile']) ? 1 : 0;
            $bannerAd = $this->BannerAds->patchEntity($bannerAd, $bannerAdData);
            if ($this->BannerAds->save($bannerAd)) {
                $this->Flash->success(__('The banner ad has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The banner ad could not be saved. Please, try again.'));
        }
        // $categories = $this->BannerAds->Categories->find('list', limit: 200)->all();
        // $brands = $this->BannerAds->Brands->find('list', limit: 200)->all();
        // $products = $this->BannerAds->Products->find('list', limit: 200)->all();

    }

    /**
     * Delete method
     *
     * @param string|null $id Banner Ad id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $bannerAd = $this->BannerAds->get($id);
        $response = ['success' => false, 'message' => 'The Ad Block could not be deleted. Please, try again.'];
        if ($bannerAd) {
            if ($this->BannerAds->delete($bannerAd)) {
                $response = ['success' => true, 'message' => 'The Ad Block has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Ad Block could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Ad Block does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $bannerAdId = $this->request->getData('image_id');

        if (!$imageType || !$bannerAdId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $bannerAd = $this->BannerAds->get($bannerAdId);

        if (!$bannerAd) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Ad Block not found']));
        }

        if ($imageType === 'web') {
            $uploadFolder = Configure::read('Constants.ADBLOCK_WEB_IMAGE');
        } else if ($imageType === 'mobile') {
            $uploadFolder = Configure::read('Constants.ADBLOCK_MOB_IMAGE');
        }

        $imageField = $imageType === 'web' ? 'web_image' : 'mobile_image';
        $existingImagePath = $bannerAd->{$imageField};

        if ($existingImagePath) {
            $filePath = WWW_ROOT . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $bannerAd->{$imageField} = null;
            if ($this->BannerAds->save($bannerAd)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update Ad Block']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }
}
