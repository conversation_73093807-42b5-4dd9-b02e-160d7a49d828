<!DOCTYPE html>
<html>
<head>
    <title><?= __('Stock Request Notification') ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        h2 {
            color: orange;
            text-align: center;
        }
        .details {
            padding: 10px;
            background: #0080803b;
            border-radius: 4px;
            border-left: 5px solid teal;
            margin-bottom: 20px;
        }
        .order-block {
            padding: 10px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://babiken.com360degree.com/webroot/img/logo.png" alt="Babiken Marketplace" width="150">
        </div>
        <h2><?= __('Stock Request Notification') ?></h2>
        <p><?= __('Dear '.h($supplier_name).',') ?></p>
        <p><?= __('A new stock request has been generated. Please find the details below:') ?></p>
        <div class="details">
            <p><strong><?= __('Request ID:') ?></strong> <?= h($request_id) ?></p>
            <p><strong><?= __('PO Number:') ?></strong> <?= h($bill_no) ?></p>
            <p><strong><?= __('Warehouse:') ?></strong> <?= h($warehouse_name) ?></p>
            <p><strong><?= __('Supplier:') ?></strong> <?= h($supplier_name) ?></p>
            <p><strong><?= __('Request Date:') ?></strong> <?= h($request_date) ?></p>
            <p><strong><?= __('Expected Delivery Date:') ?></strong> 
                <?= !empty($required_delivery_date) ? h((new \Cake\I18n\FrozenDate($required_delivery_date))->format('d-m-Y')) : 'N/A' ?>
            </p>
        </div>

        <?php foreach ($items as $item): ?>
            <div class="order-block">
                <p><strong><?= __('Product Name:') ?></strong> <?= h($item['product_name']) ?></p>
                <p><strong><?= __('Variant:') ?></strong> <?= h($item['variant_name']) ?></p>
                <p><strong><?= __('Attribute:') ?></strong> <?= h($item['attribute']) ?></p>
                <p><strong><?= __('Quantity:') ?></strong> <?= h($item['quantity']) ?></p>
            </div>
        <?php endforeach; ?>

        <p><?= __('Please review the request and take the necessary action at your earliest convenience.') ?></p>
        <div class="footer">
            <p><?= __('Best Regards,') ?><br><strong><?= __('Babiken Marketplace Admin') ?></strong></p>
        </div>
    </div>
</body>
</html>
