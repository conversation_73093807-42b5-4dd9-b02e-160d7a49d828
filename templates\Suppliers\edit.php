<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Supplier $supplier
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/intlTelInput/css/intlTelInput.min.css') ?>">
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/intlTelInput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"></script>
<style type="text/css">
    .is-invalid {
        border: 1px solid red !important;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 ********* 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    select.is-invalid {
        border-color: #dc3545 !important;
    }

    .error {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 4px;
    }
    .tel {
            width: 100%;
        }

    .iti {
        width: 100%;
    }

    .iti .iti-flag {
        width: 20px;
        height: 14px;
        margin-right: 5px;
    }

    .iti .iti-country {
        display: flex;
        align-items: center;
    }

    .iti .iti-country .iti-flag {
        margin-right: 5px;

        .iti .iti-country .iti-dial-code {
            font-size: 16px;
            margin-left: 5px;
        }

        .iti {
            width: 100%;
            box-sizing: border-box;
        }

        .iti__input {
            width: 100%;
            box-sizing: border-box;
        }

        .tel {
            width: 100% !important;
            box-sizing: border-box;
        }

    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Suppliers', 'action' => 'index']) ?>"><?= __('Suppliers') ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __('Edit') ?></li>
    </ul>
    <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
        <small class="p-10 fw-bold"><?= __('BACK') ?></small>
        <span class="rotate me-2">⤣</span>
    </button>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20"><?= __('Edit Supplier') ?></h6>
            <?php echo $this->Form->create($supplier, ['id' => 'edit', 'novalidate' => true]); ?>
            <?php if (!empty($supplier->getErrors())): ?>
                <div class="validation-errors">
                    <?php foreach ($supplier->getErrors() as $field => $errors): ?>
                        <div class="field-errors">
                            <strong><?php echo h(ucwords($field)); ?>:</strong>
                            <ul>
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo h($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label fw-bold"><?= __('Supplier Name/Business Name') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name',
                        'placeholder' => 'Enter supplier name/business name',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="contact_name" class="col-sm-2 col-form-label fw-bold"><?= __('Point of contact name') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('contact_name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'contact_name',
                        'placeholder' => 'Enter point of contact name',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="phone_number" class="col-sm-2 col-form-label fw-bold"><?= __('Point of contact phone (Ivory Coast default)') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <div class="input-group">
                        <span class="input-group-text">+225</span>
                        <input type="text" name="phone_number" id="phone_number" placeholder="Enter phone number" value="<?= $supplier->phone_number ?>" class="form-control">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="contact_email" class="col-sm-2 col-form-label fw-bold"><?= __('Point of contact email') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('contact_email', [
                        'type' => 'email',
                        'class' => 'form-control',
                        'id' => 'contact_email',
                        'placeholder' => 'Enter point of contact email',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="fax" class="col-sm-2 col-form-label fw-bold"><?= __('Fax') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <input id="faxCountryCode" type="hidden" value="<?= $supplier->fax_country_code ?>" name="fax_country_code">
                    <input id="fax" type="tel" value="<?= $supplier->fax ?>" name="fax" required>
                    <!-- < ?php echo $this->Form->control('fax', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'fax',
                        'placeholder' => 'Enter fax',
                        'label' => false
                    ]); ?> -->
                </div>
            </div>

            <div class="form-group row">
                <label for="supportPhone" class="col-sm-2 col-form-label fw-bold"><?= __('After Sale Support Number') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <input id="countryCode" type="hidden" value="<?= $supplier->support_country_code ?>" name="support_country_code">
                    <input id="supportPhone" type="tel" value="<?= $supplier->support_number ?>" name="support_number" required>
                </div>
            </div>

            <div class="form-group row">
                <label for="credit_period" class="col-sm-2 col-form-label fw-bold"><?= __('Credit Period') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('credit_period', [
                        'type' => 'number',
                        'min' => 1,
                        'class' => 'form-control',
                        'id' => 'credit_period',
                        'placeholder' => 'Enter credit period',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="address" class="col-sm-2 col-form-label fw-bold"><?= __('Address') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('address', [
                        'type' => 'textarea',
                        'class' => 'form-control',
                        'id' => 'address',
                        'placeholder' => 'Enter address',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="pincode" class="col-sm-2 col-form-label fw-bold"><?= __('PIN Code') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('pincode', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'pincode',
                        'placeholder' => 'Enter pincode',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="city" class="col-sm-2 col-form-label fw-bold"><?= __('City') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('city', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'city',
                        'placeholder' => 'Enter city',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __('Status') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'status',
                        'options' => [
                            'A' => 'Active',
                            'I' => 'Inactive'
                        ],
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" class="btn"><?= __('Save') ?></button>
                </div>
            </div>
        </div>
        </form>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>
    $(document).ready(function () {
        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });
    });

    $(document).ready(function () {

        //FAX
        var inputFax = document.querySelector("#fax");
        var itiFax = window.intlTelInput(inputFax, {
            // initialCountry: "ci",
            separateDialCode: true,
            preferredCountries: ["ci", "us", "gb"],
            utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
        });

        var dialCode = $('#faxCountryCode').val();
        itiFax.setNumber('+'+dialCode+inputFax.value);

        function updateFaxCountryCode() {

            var countryData = itiFax.getSelectedCountryData();
            var countryCode = countryData.dialCode;
            var countryCodeText = `+${countryCode} `;

            $('#faxCountryCode').val(itiFax.getSelectedCountryData().dialCode);
        }

        inputFax.addEventListener("countrychange", updateFaxCountryCode);

        //SALE SUPPORT NUMBER
        var input = document.querySelector("#supportPhone");
        var iti = window.intlTelInput(input, {
            // initialCountry: "ci",
            separateDialCode: true,
            preferredCountries: ["ci", "us", "gb"],
            utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
        });

        var dialCode = $('#countryCode').val();
        iti.setNumber('+'+dialCode+input.value);

        function updateCountryCode() {

            var countryData = iti.getSelectedCountryData();
            var countryCode = countryData.dialCode;
            var countryCodeText = `+${countryCode} `;

            $('#countryCode').val(iti.getSelectedCountryData().dialCode);
        }

        input.addEventListener("countrychange", updateCountryCode);

        $.validator.addMethod("pattern", function (value, element, param) {
            if (this.optional(element)) {
                return true;
            }
            if (typeof param === "string") {
                param = new RegExp("^(?:" + param + ")$");
            }
            return param.test(value);
        }, "<?= __('Invalid format.') ?>");

        $.validator.addMethod('customEmail', function (value, element) {
            return this.optional(element) || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        }, "<?= __('Please enter a valid email address.') ?>");

        // Add custom validation method for sales support number
        $.validator.addMethod("validPhone", function(value, element) {
            return iti.isValidNumber(); // Use intlTelInput's isValidNumber method
        }, "<?= __('Please enter a valid sale support number') ?>");

        // Add custom validation method for sales support number
        $.validator.addMethod("validPhoneFax", function(value, element) {
            return itiFax.isValidNumber(); // Use intlTelInput's isValidNumber method
        }, "<?= __('Please enter a valid fax') ?>");

        $("#edit").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true
                },
                'contact_name': {
                    required: true
                },
                'contact_email': {
                    required: true,
                    customEmail: true
                },
                'phone_number': {
                    required: true,
                    digits: true,
                    minlength: 10,
                    maxlength: 10
                },
                'fax': {
                    required: true,
                    validPhoneFax: true
                },
                'credit_period':
                {
                    required: true,
                    digits: true
                },
                'support_number': {
                    required: true,
                    validPhone: true
                }
            },
            messages: {
                'name': {
                    required: "<?= __('Please enter supplier name/business name') ?>"
                },
                'contact_name': {
                    required: "<?= __('Please enter point of contact name') ?>"
                },
                'contact_email': {
                    required: "<?= __('Please enter point of contact email') ?>"
                },
                'phone_number': {
                    required: "<?= __('Please enter phone') ?>",
                    digits: "<?= __('Please enter only digits (0-9)') ?>",
                    minlength: "<?= __('The phone number must be exactly 10 digits long') ?>",
                    maxlength: "<?= __('The phone number must be exactly 10 digits long') ?>"
                },
                'fax': {
                    required: "<?= __('Please enter fax') ?>"
                },
                'credit_period': {
                    required: "<?= __('Please enter credit period') ?>",
                    digits: "<?= __('Please enter only digits') ?>"
                },
                'support_number': {
                    required: "<?= __('Please enter sales support number') ?>"
                }
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');

                // If it's a select2 or select box, also add to the container
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').addClass('is-invalid');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');

                // Remove from select2 or select container too
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').removeClass('is-invalid');
                }
            },
            submitHandler: function (form) {
                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            }
        });
    });
</script>
<?php $this->end(); ?>
