<?php foreach ($orderList as $val): ?>
                <div class="my-a-my-o-main">
                    <div class="my-a-my-o-main-my-orders-one">

                        <div class="my-a-my-o-main-my-orders-header">

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label">Order Number</div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= $val['order_number']; ?></div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label">Order Placed</div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= $val['order_date']; ?></div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label">Order Value</div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= $val['total_amount']; ?> FCFA</div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label">No of Items</div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= count($val['order_items']); ?> Nos</div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label">Delivary Address</div>
                                <div
                                    class="my-a-my-o-main-my-orders-one-input">
                                    <?= h(
                                        $val['customer_address']['address_line1']
                                        ?? $val['showroom']['address']
                                        ?? 'N/A'
                                    ); ?>
                                </div>
                            </div>

                            <button class="my-a-my-o-main-my-orders-invoice-btn"><i class="fa fa-file-text-o"></i> Invoice
                            </button>

                        </div>

                        <?php if (isset($val['order_items']) && !empty($val['order_items'])) : ?>
                            <?php foreach ($val['order_items'] as $product): ?>
                                <div class="my-order-item-details-container">

                                    <div>

                                        <div class="my-order-item-details">
                                            <a href="/product/<?= $product['product']['url_key'] ?>">
                                                <img src="<?= $product['thumb_image'] ?>" class="my-order-item-icon">
                                            </a>
                                            <div class="my-order-items-details-tpc">
                                                <div class="my-order-item-details-title-container">
                                                    <div class="my-order-item-details-title">
                                                    <a href="/product/<?= $product['product']['url_key'] ?>">
                                                    <?php if (!empty($product['product_variant_id']) && !empty($product['product_variant']['variant_name'])): ?>
                                                            <?= $product['product_variant']['variant_name'] ?>
                                                        <?php else: ?>
                                                            <?= $product['product']['name'] ?>
                                                        <?php endif; ?>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="my-order-item-details-price">
                                                   Qty <?= $product['quantity']; ?>
                                                </div>
                                                <div class="my-order-item-details-price">
                                                    <?php if (!empty($product['product_variant_id'])): ?>
                                                        <?= $product['product_variant']['promotion_price'] ?>
                                                    <?php else: ?>
                                                        <?= $this->Price->getPriceById($product['product']['id']) ?>
                                                    <?php endif; ?>
                                                    FCFA
                                                </div>
                                                <div class="my-order-item-details-buttons">
                                                <?php if(!in_array($product['status'],['Pending','Pending Cancellation', 'Pending Return']) && empty($product['order_returns'])): ?>

                                                        <button class="my-order-item-details-return-btn return-btn" id="return-btn"
                                                                data-order-id="<?= $product['order_id']; ?>"
                                                                data-order-item-id="<?= $product['id']; ?>"
                                                                data-return-amount="<?= $product['product']['promotion_price']; ?>">
                                                            <i class="fas fa-repeat my-order-item-details-return-btn-icn"></i>
                                                            Return
                                                        </button>

                                                <?php else: ?>

                                                <?php endif; ?>


                                                <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'cart']) ?>" data-product-id="<?php echo $product['product']['id']; ?>" class="my-order-item-details-again-btn add-to-cart">
                                                <i class="fas fa-repeat my-order-item-details-again-btn-icn"></i>
                                                    Bug it again
                                                </a>

                                                    <?php if(!in_array($product['status'],  ['Cancel Pending'])): ?>
                                                    <button class="my-order-item-details-cancel-btn cancel-api"
                                                            data-order-id="<?= $product['order_id']; ?>"
                                                            data-product-id="<?= $product['product']['id']; ?>"
                                                            data-order-item-id="<?= $product['id']; ?>">
                                                        <i class="fas fa-times my-order-item-details-cancel-btn-icn"></i> Cancel
                                                    </button>
                                                    <?php else: ?>

                                                    <?php endif; ?>

                                                </div>


                                            </div>
                                        </div>
<!--                                        <div class="my-acnt-delivay">-->
<!--                                            <i class="fas fa-truck"></i>-->
<!--                                            Estimated Delivary Aug 16, 2027-->
<!--                                        </div>-->

                                    </div>

                                    <div class="body-background">
                                        <div class="my-acnt-tracking-items">
                                            <div class="progress-container">
                                                <div class="progress-bar">
                                                    <div class="progress-item">
                                                        <div class="progress-text">Order confirmed</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                    <div class="progress-item">
                                                        <div class="progress-text">Shipped</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                    <div class="progress-item">
                                                        <div class="progress-text">Out for Delivery</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                    <div class="progress-item">
                                                        <div class="progress-text">Delivered</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>


                    </div>
                </div>
            <?php endforeach; ?>
