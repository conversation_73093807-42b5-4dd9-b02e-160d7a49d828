<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Showroom Entity
 *
 * @property int $id
 * @property int $city_id
 * @property string $name
 * @property string $location
 * @property int $contact_number
 * @property string $extra_product_price
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\City $city
 * @property \App\Model\Entity\Inventory[] $inventories
 * @property \App\Model\Entity\OfferShowroom[] $offer_showrooms
 * @property \App\Model\Entity\Order[] $orders
 * @property \App\Model\Entity\ShowroomStock[] $showroom_stocks
 * @property \App\Model\Entity\ShowroomUser[] $showroom_users
 * @property \App\Model\Entity\SupplierShowroom[] $supplier_showrooms
 */
class RefundTransactions extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        '*' => true,
        'id' => false,
    ];
}
