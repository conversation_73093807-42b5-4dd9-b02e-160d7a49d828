<?php

/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 * @var \App\View\AppView $this
 */

$cakeDescription = 'Babiken';
?>
<!DOCTYPE html>
<html lang="en">
<head>

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-PS57PL56');</script>
    <!-- End Google Tag Manager -->

    <?= $this->Html->charset() ?>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>
        <?= $cakeDescription ?>:
        <?= isset($title) ? h($title) : $this->fetch('title') ?>
    </title>
    <link rel="icon" type="image/x-icon" href="<?= $this->Url->webroot('assets/logo.png') ?>"/>

    <meta property="og:url" content="https://babiken.com360degree.com/">
    <meta property="og:image" content="https://babiken.com360degree.com/assets/logo.png">
    <meta property="og:locale" content="en_US">

    <meta name="title" content="Babiken Côte d'Ivoire | Vente en Ligne">
    <meta name="description" content="Babiken Site d'E-Commerce N°1 de Vente en ligne au Côte d'Ivoire
✔ Large Sélection : Électroménager, Télévisions, Splits, Ventilateurs, Matelas ...
✔ Meilleures Marques...
✈ Livraison rapide partout : Abidjan, Anyama...
✔ Paiement à la livraison">

    <meta name="robots" content="index,follow">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <?= $this->fetch('meta') ?>
    <meta name="csrf-token" content="<?= $this->request->getAttribute('csrfToken') ?>">
    <?php echo $this->fetch('add_meta'); ?>
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/styles.css') ?>"/>
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/productViewPage.css') ?>"/>
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/product-view-responsive-fix.css') ?>"/>
    <script src="<?= $this->Url->webroot('javascript/productViewPage.js') ?>"></script>
<!-- Select2 CSS & JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />


    <?php echo $this->fetch('add_css'); ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet"/>

    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet"/>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <style>
        button#buy-now-button.disabled{
            color: #c2c2c2;
            border-color: #c2c2c2;
        }
        .color-gb {
            cursor: pointer;
        }
        span.text-danger {
            color: red;
        }
        button.add-to-cart-button.disabled {
            background: #c2c2c2;
        }
        input.search-input {
            height: 40px;
            margin-top: 1px;
            width: 75%;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            color: #000000 !important;
        }
        .select2-container--default .select2-selection--single {
            border: none;
        }
        .select2-container--default .select2-selection--single {
            border: none;
            font-size: 12px;
        }
        .for-mobile-res {
            display: none;
        }
        /* Loader container */
        .search-results {
            position: fixed;

            overflow-y: auto;
            display: none;
            z-index: 100000 !important;
        }
        .ax-loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8); /* Semi-transparent white */
            z-index: 9999; /* Ensure it appears on top of everything */
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(5px); /* Blurred background */
            transition: opacity 0.5s ease, visibility 0.5s ease;
            opacity: 1;
            visibility: visible;
        }
        .ax-loader-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

    </style>

    <style>
    </style>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Babiken",
          "url": "https://www.babiken.net",
          "logo": "<?= $this->Url->webroot('assets/logo.png') ?>",
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "<?= h($this->SiteSettings->getSettings()->customer_support_no) ?>",
            "contactType": "Customer Service",
            "areaServed": "Worldwide",
            "availableLanguage": ["English", "French"]
          },
          "sameAs": [
            "<?= h($this->SiteSettings->getSettings()->facebook_url) ?>",
            "<?= h($this->SiteSettings->getSettings()->instagram_url) ?>",
            "<?= h($this->SiteSettings->getSettings()->twitter_url) ?>",
            "<?= h($this->SiteSettings->getSettings()->linkedin_url) ?>"
          ],
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "<?= h($this->SiteSettings->getSettings()->address_line1) ?>",
            "addressLocality": "<?= h($this->SiteSettings->getSettings()->city) ?>",
            "postalCode": "<?= h($this->SiteSettings->getSettings()->zipcode) ?>",
            "addressCountry": "<?= h($this->SiteSettings->getSettings()->country) ?>"
          }
        }
    </script>

</head>
<body>

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PS57PL56"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<header>
    <div class="main-header">
        <div class="contents">
            <div class="welcome"><?= __('Welcome to BABIKEN!') ?></div>
        </div>
    </div>
    <div class="sub-header">
        <?php if (isset($scrollContentArr) && !empty($scrollContentArr)): ?>
            <?php foreach ($scrollContentArr as $val): ?>
                <div class="scrolling-text"><?php echo $val->content; ?></div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="scrolling-text"><?= __('No content available') ?></div>
        <?php endif; ?>
    </div>
    <navbar class="navbar">
        <?php if (!empty($settings['company_logo'])): ?>
            <a href="<?= $this->Url->webroot('/') ?>">
                <img src="<?= $this->Url->webroot($settings['company_logo']) ?>" class="nav-icon"/>
            </a>
        <?php else: ?>
            <a href="<?= $this->Url->webroot('/') ?>">
                <img src="<?= $this->Url->webroot('assets/logo.png') ?>" class="nav-icon"/>
            </a>
        <?php endif; ?>

        <div class="home-text"><a href="<?= $this->Url->webroot('/') ?>"><?= __('Home') ?></a></div>
        <div class="shopdown">
            <!-- <select
                style="font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;"
                id="shopbtn">
                <option value="shop">Shop</option>
                <option value="eur">Category 1</option>
                <option value="eur">Category 2</option>
            </select> -->
        </div>
        </div>
        <div class="search-container search-container-agn">
        <?php echo $this->GlobalSearch->render([
        'placeholder' => 'Search Products',
        'searchIconPath' => ''
    ]); ?>

        </div>



        <?php /*
               <div class="ax2-nav-icons">
                    <span class="ax2-cart icon black-outline-icon">
                        <img class="bag-icon" src="<?= $this->Url->webroot('assets/shopping-bag-of-normal-design-outline.png') ?>"/>
                        <sup class="superscript"><?= $this->CartListView->renderCartListCount(); ?></sup>
                    </span>
                    <div class="ax2-modal">
                         <?php echo $this->CartListView->renderCartList(); ?>
                    </div>
                </div>

                <div class="ax2-nav-icons">
                    <span class="ax2-wishlist icon black-outline-icon">
                        <img class="bag-icon" src="<?= $this->Url->webroot('assets/heart-icon.png') ?>"/>
                        <sup class="superscript"><?= $this->WishListView->renderWishListCount(); ?></sup>
                    </span>
                    <div class="ax2-modal">
                         <?php echo $this->WishListView->renderWishList(); ?>
                    </div>
                </div>
        */ ?>

        <div class="whishlist">
            <?php if ($this->request->getSession()->check('Auth.User')): ?>
            <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'favourite']) ?>" class="icon black-outline-icon" style="display: block;">
            <?php else: ?>
            <a href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'login']) ?>" class="icon black-outline-icon" style="display: block;" onclick="toastr.warning('<?= __('Login required to access favourites') ?>', '', {timeOut: 3000, progressBar: true}); return false;">
            <?php endif; ?>
            <img src="<?= $this->Url->webroot('assets/heart-icon.png') ?>" class='far fa-heart' id="heart">
            <sup class="cart-superscript"><?php echo $this->WishListView->renderWishListCount(); ?></sup>
            </a>
        </div>

        <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'cart']) ?>" class="icon black-outline-icon" style="display: block;">
            <img class="bag-icon" src="<?= $this->Url->webroot('assets/shopping-cart.png') ?>"/>
            <sup class="superscript"><?= $this->CartListView->renderCartListCount(); ?></sup>
        </a>



        <hr class="break"/>



        <?php if ($this->request->getSession()->check('Auth.User')): ?>
            <!-- Show "My Account" link if the user is logged in -->
            <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount']) ?>" class="icon black-outline-icon"><img src="<?= $this->Url->webroot('assets/profile-icon.png') ?>"
                                                         class="far fa-user"
                                                         id="profile-icon"></i></a>

            <a href="<?= $this->Url->build(['controller' => 'account', 'action' => 'myAccount']) ?>">
                <div class="login-button">My Account</div>
            </a>
        <?php else: ?>
            <a href="<?= $this->Url->build(['controller' => 'customer', 'action' => 'login']) ?>" class="icon black-outline-icon"><img src="<?= $this->Url->webroot('assets/profile-icon.png') ?>"
                                                         class="far fa-user"
                                                         id="profile-icon"></i></a>
            <!-- Show "Login/Register" link if the user is not logged in -->
            <a href="<?= $this->Url->build(['controller' => 'customer', 'action' => 'login']) ?>">
                <div class="login-button"><?= __('Login/Register') ?></div>
            </a>
        <?php endif; ?>

    </navbar>


    <div class="space-allocation"></div>

</header>

<?= $this->fetch('content') ?>

<footer class="footer">

    <div class="footer-header-containers">

        <div class="footer-headers">
            <img class="footer-icon" src="<?= $this->Url->webroot('assets/footer-icon.png') ?>"/>
        </div>

        <div class="footer-headers clone-footer-headers"><?= __('Need Help') ?></div>
        <div class="footer-headers clone-footer-headers"><?= __('Company') ?></div>
        <div class="footer-headers clone-footer-headers"><?= __('Top Category') ?></div>
    </div>
    <div class="footer-flex">


    <div class="image-below">
            <p class="cutomer"><?= __('Customer Supports:') ?></p>
            <p class="number"><?= h($this->SiteSettings->getSettings()->customer_support_no) ?></p>
            <p class="nothing">

            <?= h($this->SiteSettings->getSettings()->address_line1) ?><br>
            <?= h($this->SiteSettings->getSettings()->address_line2) ?><br>
            <?= h($this->SiteSettings->getSettings()->city) ?>, <?= h($this->SiteSettings->getSettings()->state) ?> <?= h($this->SiteSettings->getSettings()->zipcode) ?><br>
                <?= h($this->SiteSettings->getSettings()->country) ?>

            </p>
            <p class="email"><EMAIL></p>

            <div class="footer-icons-container">

        <span class="social-icon-footer-div">
          <a href="<?= h($this->SiteSettings->getSettings()->facebook_url) ?>" class="social-icon-footer">
            <i class="fab fa-facebook" id="footer-icon"></i>
          </a>
        </span>

                <span class="social-icon-footer-div">
          <a href="<?= h($this->SiteSettings->getSettings()->instagram_url) ?>" class="social-icon-footer">
            <i class="fab fa-instagram" id="footer-icon"></i>
          </a>
        </span>

                <span class="social-icon-footer-div">
          <a href="<?= h($this->SiteSettings->getSettings()->twitter_url) ?>" class="social-icon-footer">
            <i class="fab fa-twitter" id="footer-icon"></i>
          </a>
        </span>

                <span class="social-icon-footer-div">
          <a href="<?= h($this->SiteSettings->getSettings()->linkedin_url) ?>" class="social-icon-footer">
            <i class="fab fa-linkedin" id="footer-icon"></i>
          </a>
        </span>
            </div>
        </div>


        <div class="need-help clone-need-help">
            <a class="need-help-below" style="color: #ffffff"
               href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'contactUs']) ?>"><?= __('Contact Us') ?></a>
            <p class="need-help-below"><?= $this->Html->link(__('Track Order'), ['controller' => 'Website', 'action' => 'cmsPage', 'track order']) ?></p>
            <p class="need-help-below"><?= $this->Html->link(__('Returns & Refund'), ['controller' => 'Website', 'action' => 'cmsPage', 'returns and refund']) ?></p>
            <p class="need-help-below"><?= $this->Html->link(__('FAQ\'s'), ['controller' => 'Website', 'action' => 'cmsPage', 'faqs']) ?></p>
            <p class="need-help-below"><?= $this->Html->link(__('Career'), ['controller' => 'Website', 'action' => 'cmsPage', 'career']) ?></p>
        </div>


        <div class="need-help clone-need-help">
            <p class="need-help-below"><?= $this->Html->link(__('About Us'), ['controller' => 'Website', 'action' => 'cmsPage', 'about us']) ?></p>
            <p class="need-help-below"><?= $this->Html->link(__('Terms Of Use'), ['controller' => 'Website', 'action' => 'cmsPage', 'terms and conditions']) ?></p>
            <p class="need-help-below"><?= $this->Html->link(__('Privacy & Policy'), ['controller' => 'Website', 'action' => 'cmsPage', 'privacy and policy']) ?></p>
            <p class="need-help-below"><?= $this->Html->link(__('Shipping Policy'), ['controller' => 'Website', 'action' => 'cmsPage', 'shipping policy']) ?></p>
            <p class="need-help-below"><?= $this->Html->link(__('Sitemap'), ['controller' => 'Website', 'action' => 'cmsPage', 'sitemap']) ?></p>
        </div>

        <div class="need-help clone-need-help">
            <p class="need-help-below"> <a href="/product-list/tv-audio"><?= __('TV / Audio') ?></a></p>
            <p class="need-help-below"><a href="/product-list/phones"><?= __('Smartphone') ?></a></p>
            <p class="need-help-below"><a href="/product-list/air-conditioner-1"><?= __('Air Conditioner') ?></a></p>
            <p class="need-help-below"><a href="/product-list/small-appliances"><?= __('Small Appliances') ?></a></p>
            <p class="need-help-below"><a href="/product-list/mattress"><?= __('Mattress') ?></a></p>
        </div>

    </div>
    </div>

    </div>

    <div class="for-mobile-res">
    <div id="c-name-foo"><?= __('Customer Supports') ?></div>
    <div id="c-num-foo">(*************</div>
    </div>

    <div class="other-footer-text clone-other-footer-text">
        <p class="Download-The-App"><?= __('Download The App') ?></p>
        <p class="Payment-Methods"><?= __('Payment Methods & Delivery') ?></p>
    </div>
    <div class="other-footer-icon">
        <img src="<?= $this->Url->webroot('assets/google-play.png') ?>" class="other-footer-icon-footer"/>
        <img src="<?= $this->Url->webroot('assets/app-store.png') ?>" class="other-footer-icon-footer"/>
    </div>
    <div class="parent-Payment-Methods-img">
        <div class="Payment-Methods-img">
            <img src="<?= $this->Url->webroot('assets/alpha.png') ?>" class="alpha"/>
            <img src="<?= $this->Url->webroot('assets/orange.png') ?>" class="alpha"/>
            <img src="<?= $this->Url->webroot('assets/mtn.png') ?>" class="alpha"/>
        </div>
    </div>
    <hr class="footer-br">
    <div class="copyright">Copyright <?= (date('Y') - 1) . '-' . date('Y') ?> <?= __('Babiken.net All Rights Reserved') ?>, V2.0</div>
</footer>
<script src="<?= $this->Url->webroot('javascript/index.js') ?>"></script>
<!-- jQuery library -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- jQuery UI library -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.0/themes/base/jquery-ui.css">
<script src="https://code.jquery.com/ui/1.13.0/jquery-ui.min.js"></script>
<script src="<?= $this->Url->webroot('js/jquery.validate.min.js') ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    const searchAjaxUrl = "<?= $this->Url->build('/', ['fullBase' => true]); ?>"
</script>
<?php echo $this->fetch('add_js'); ?>
<!-- Loader Div
<div id="loader" class="ax-loader-overlay">
    <img src="<?= $this->Url->webroot('img/loaders.gif') ?>" alt="Loading...">
</div>
-->
<script>

    $(document).on('click', '.add-to-cart', function () {
        const productId = $(this).data('product-id');
        const attributes = $(this).data('attributes') || [];
        console.log(JSON.stringify(attributes));
        addToCart(productId, attributes)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    toastr.success(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });

                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                //  toastr.success(error, 'Error')
            });
    });

    function addToCart(productId, attributes) {
        return new Promise((resolve, reject) => {
            const product_variant_id = $('#product-variant').val() || null;

            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'addToCart']) ?>",
                type: 'POST',
                data: {
                    product_variant_id: product_variant_id,
                    product_id: productId,
                    product_attribute_id: attributes,
                    quantity: $(".cart-qty").text() ? parseInt($(".cart-qty").text()) : 1
                },
                success: function (response) {
                    if (response.status) {
                        window.dataLayer = window.dataLayer || [];
                        window.dataLayer.push({
                        event: 'add_to_cart',
                        ecommerce: {
                                items: [{
                                item_id: response.data.product_id,
                                item_name: response.name,
                                price: response.data.price,
                                item_category: response.category_name,
                                item_brand: response.brand_name,
                                item_variant: response.data.product_variant_id,
                                quantity: response.data.quantity
                            }]
                        }
                        });
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    $(document).on('click', '.closeCartItem', function () {
        const cart_item_id = $(this).data('item-id');

        deleteToCart(cart_item_id)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    toastr.success(res.message, '', {
                        timeOut: 1000,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });

                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 1000,  // 3 seconds before hiding the toastr message
                        onHidden: function () {

                            window.dataLayer = window.dataLayer || [];
                            window.dataLayer.push({
                            event: 'remove_from_cart',
                            ecommerce: {
                                items: [{
                                item_id: res.data.product_id,
                                item_name: res.data.product_name,
                                price: res.data.price,
                                item_category: res.category_name,
                                item_brand: res.brand_name,
                                quantity: res.data.quantity
                                }]
                            }
                            });

                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                //  toastr.success(error, 'Error')
            });
    });

    function deleteToCart(cart_item_id) {

        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'deleteCartItem']) ?>",
                type: 'POST',
                data: {cart_item_id: cart_item_id},
                success: function (response) {
                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });

    }

</script>
<script>
    $(document).on('click', '.add-to-wishlist-btn', function () {
        const productId = $(this).data('product-id');
        addToWishlist(productId)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    toastr.success(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });

                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                //  toastr.success(error, 'Error')
            });
    });
    $(document).on('click', '.remove-to-wishlist-btn', function () {
        const productId = $(this).data('product-id');
        removeToWishlist(productId)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    toastr.success(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });
                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                //  toastr.success(error, 'Error')
            });
    });

    function addToWishlist(productId) {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'addWishlist']) ?>",
                type: 'POST',
                data: {product_id: productId},
                success: function (response) {
                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    function removeToWishlist(productId) {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'removeWishlist']) ?>",
                type: 'POST',
                data: {product_id: productId},
                success: function (response) {
                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }
</script>

<script>
    window.addEventListener("load", () => {
        const loader = document.getElementById("loader");
        if (!loader) return;
        setTimeout(() => {
            loader.classList.add("hidden");
            loader.addEventListener("transitionend", () => loader.remove());
        }, 1000);
    });

</script>
<script>
  $(document).ready(function () {
    $('.searchable-select').select2({
      width: 'auto',
      dropdownAutoWidth: true,
      minimumResultsForSearch: 0,
      allowClear: false,
      placeholder: 'Select Country Code'
    });

    // Re-adjust width after selection (optional)
    $('.searchable-select').on('change', function () {
      document.getElementById('ax-country-select').dispatchEvent(new Event('change'));
    });
  });
</script>

<script>
  $(document).ready(function () {

    $('.searchable-select-search').select2({
        // add extra classes to the select2 container
        containerCssClass: 'searchable-select-container',
        dropdownCssClass: 'searchable-select-dropdown'
    });

  });
</script>


<script>
    $(document).on('click', '.updateCartItem', function () {
        const cart_item_id = $(this).data('item-id');
        const action = $(this).data('item-type'); // 'increase' or 'decrease'

        // Find the corresponding quantity element
        const qtyElement = $(this).closest('.counter').find('.cartItemQty');
        let currentQty = parseInt(qtyElement.text());

        // Update the quantity based on action
        if (action === 'increase') {
            currentQty += 1;
        } else if (action === 'decrease') {
            if (currentQty > 1) {
                currentQty -= 1;
            } else {
                // Optionally, show a message if the user tries to go below 1
                alert('Quantity cannot be less than 1');
                return;
            }
        }
        qtyElement.text(currentQty);

        updateToCart(cart_item_id, currentQty)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    toastr.success(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            window.dataLayer = window.dataLayer || [];
                            window.dataLayer.push({
                            event: 'update_from_cart',
                            ecommerce: {
                                items: [{
                                item_id: res.data.product_id,
                                item_name: res.name,
                                price: res.data.price,
                                item_category: res.category_name,
                                item_brand: res.brand_name,
                                quantity: res.data.quantity
                                }]
                            }
                            });

                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });

                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                //  toastr.success(error, 'Error')
            });
    });

    function updateToCart(cart_item_id, currentQty) {

        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'updateCartItem']) ?>",
                type: 'POST',
                data: {cart_item_id: cart_item_id, quantity: currentQty},
                success: function (response) {
                    console.log(JSON.stringify(response));
                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });

    }


</script>

</body>

 <!-- Modal div -->
        <div id="modal" class="modal">
            <div class="modal-content">
                <span class="close-btn" id="closeModal">&times;</span>
                <?php echo $this->WishListView->renderWishList(); ?>
            </div>
        </div>
        <div id="cart" class="modal">
            <div class="modal-content">
            <a href="<?= $this->Url->build(['controller' => 'account', 'action' => 'cart']) ?>">
                    <div class="login-button">View Cart</div>
                </a>
                <span class="close-btn" id="closeCart">&times;</span>
                <div class="">

                    <?php echo $this->CartListView->renderCartList(); ?>

                </div>

            </div>
        </div>

<style>
    /* Autocomplete container styles */
    .ui-autocomplete {
        width: 350px; /* Slightly wider for better text fitting */
        max-height: 300px; /* Limit height for overflow */
        overflow-y: auto; /* Enable scrolling when needed */
        background-color: #fff;
        border: 1px solid #ddd; /* Soft border */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Soft shadow for better visibility */
        z-index: 1000;
        border-radius: 8px; /* Rounded corners */
        padding: 5px 0;
        font-family: Arial, sans-serif; /* Font consistency */
    }

    /* List item styling */
    .ui-autocomplete li {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        cursor: pointer;
        transition: background-color 0.3s ease, padding-left 0.2s ease; /* Smooth transitions */
        font-size: 14px; /* Slightly bigger text for readability */
        border-bottom: 1px solid #f1f1f1; /* Light separator between items */
    }

    /* Hover effect on list item */
    .ui-autocomplete li:hover {
        background-color: #f7f7f7; /* Light background on hover */
        padding-left: 20px; /* Indentation effect */
    }

    /* Image styling */
    .autocomplete-item-img {
        width: 40px;
        height: 40px;
        border-radius: 5px;
        margin-right: 15px; /* Space between image and text */
        object-fit: cover; /* Ensure image is well-cropped */
    }

    /* Item name styling */
    .autocomplete-item-name {
        font-weight: 600; /* Bold for better text prominence */
        color: #333; /* Dark text color */
    }

    /* Hover effect for the autocomplete item text */
    .autocomplete-item-name:hover {
        text-decoration: underline;
    }

    /* Add a smooth transition for the dropdown */
    .ui-autocomplete li {
        transition: background-color 0.3s, padding-left 0.2s;
    }
</style>
<?= $this->Flash->render() ?>
</html>
