<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Database\Expression\IdentifierExpression;

/**
 * Showrooms Model
 *
 * @property \App\Model\Table\CitiesTable&\Cake\ORM\Association\BelongsTo $Cities
 * @property \App\Model\Table\InventoriesTable&\Cake\ORM\Association\HasMany $Inventories
 * @property \App\Model\Table\OfferShowroomsTable&\Cake\ORM\Association\HasMany $OfferShowrooms
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\HasMany $Orders
 * @property \App\Model\Table\ShowroomStocksTable&\Cake\ORM\Association\HasMany $ShowroomStocks
 * @property \App\Model\Table\ShowroomUsersTable&\Cake\ORM\Association\HasMany $ShowroomUsers
 * @property \App\Model\Table\SupplierShowroomsTable&\Cake\ORM\Association\HasMany $SupplierShowrooms
 *
 * @method \App\Model\Entity\Showroom newEmptyEntity()
 * @method \App\Model\Entity\Showroom newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Showroom> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Showroom get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Showroom findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Showroom patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Showroom> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Showroom|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Showroom saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Showroom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Showroom>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Showroom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Showroom> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Showroom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Showroom>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Showroom>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Showroom> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ShowroomsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('showrooms');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id',
            'joinType' => 'INNER',
        ]);

        $this->hasMany('ShowroomImages', [
            'foreignKey' => 'showroom_id',
        ]);

        // $this->belongsTo('Zones', [
        //     'foreignKey' => 'zone_id',
        //     'joinType' => 'LEFT',
        // ]);

        $this->belongsTo('ShowroomManager', [
            'className' => 'Users',
            'foreignKey' => 'showroom_manager',
            'propertyName' => 'manager',
            'joinType' => 'LEFT',
        ]);

        $this->belongsTo('ShowroomSupervisor', [
            'className' => 'Users',
            'foreignKey' => 'showroom_supervisor',
            'propertyName' => 'supervisor',
            'joinType' => 'LEFT',
        ]);

        $this->belongsToMany('Zones', [
            'joinTable' => 'zone_showrooms',
            'foreignKey' => 'showroom_id',
            'targetForeignKey' => 'zone_id',
            'className' => 'Zones'
        ]);

        // $this->belongsTo('Users', [
        //     'foreignKey' => 'showroom_manager',
        //     'joinType' => 'LEFT',
        // ]);

        // $this->belongsTo('Users', [
        //     'foreignKey' => 'showroom_supervisor',
        //     'joinType' => 'LEFT',
        // ]);

        $this->hasMany('PurchaseOrderRequest', [
            'foreignKey' => 'delivery_address',
        ]);

        $this->hasMany('SupplierPayment', [
            'foreignKey' => 'showroom_id',
        ]);

        $this->hasMany('Inventories', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->hasMany('OfferShowrooms', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->hasMany('Orders', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->hasMany('ProductStocks', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->hasMany('ShowroomUsers', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->hasMany('SupplierShowrooms', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->belongsToMany('Offers', [
            'joinTable' => 'offer_showrooms',
            'foreignKey' => 'showroom_id',
            'targetForeignKey' => 'offer_id',
            'through' => 'OfferShowrooms',
        ]);
        $this->hasMany('ZoneShowrooms', [
            'foreignKey' => 'showroom_id',
            'joinType' => 'LEFT'
        ]);
        $this->belongsTo('Municipalities', [
            'foreignKey' => 'municipality_id',
            'joinType' => 'LEFT'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('city_id')
            ->notEmptyString('city_id');

        $validator
            ->nonNegativeInteger('municipality_id')
            ->allowEmptyString('municipality_id');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('address')
            ->maxLength('address', 255)
            ->requirePresence('address', 'create')
            ->notEmptyString('address');

        $validator
            ->scalar('area_sq_mts')
            ->maxLength('area_sq_mts', 255)
            ->allowEmptyString('area_sq_mts');

        $validator
            ->scalar('email')
            ->maxLength('email', 255)
            ->requirePresence('email', 'create')
            ->notEmptyString('email');

        // $validator
        //     ->integer('contact_number')
        //     ->requirePresence('contact_number', 'create')
        //     ->notEmptyString('contact_number');

        $validator
            ->scalar('showroom_timing')
            ->maxLength('showroom_timing', 255)
            ->allowEmptyString('showroom_timing');

        $validator
            ->nonNegativeInteger('showroom_manager')
            ->notEmptyString('showroom_manager');

        $validator
            ->nonNegativeInteger('showroom_supervisor')
            ->notEmptyString('showroom_supervisor');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['city_id'], 'Cities'), ['errorField' => 'city_id']);

        return $rules;
    }

    //S all showrooms
    public function listShowroom()
    {
        $all_showrooms = $this->find()->select(['id','name'])->where(['status'=>'A'])->order(['name'=>'ASC'])->toArray();
        return $all_showrooms;
    }

    //S showrooms based on city id
    public function listShowroomByCity($city_id)
    {
        $showrooms = $this->find()->select(['id','name'])->where(['status'=>'A', 'city_id'=>$city_id])->order(['name'=>'ASC'])->toArray();
        return $showrooms;
    }

    //S showrooms based on user id (showroom manager/showroom supervisor)
    public function listShowroomByUser($user_id, $role_name)
    {
        if($role_name == 'Showroom Supervisor'){
            $cond = ['Showrooms.showroom_supervisor'=>$user_id];
        } else if($role_name == 'Showroom Manager') {
            $cond = ['Showrooms.showroom_manager'=>$user_id];
        } else if($role_name == 'Sales Person'){
            /*$showroom = $this->find()
                ->select(['Showrooms.id'])
                ->innerJoinWith('ShowroomUsers')
                ->where(['ShowroomUsers.user_id' => $user_id]);
            $cond = ['Showrooms.id IN'=>$showroom];*/   
            $cond = []; 
        } else {
            $cond = [];
        }
        $showrooms = $this->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->toArray();
        return $showrooms;
    }

    //S showrooms details based on city id
    public function showroomDetailByCity($city_id)
    {
        $showrooms = $this->find()->select(['Showrooms.id','Showrooms.name', 'Cities.city_name','Showrooms.address','Showrooms.contact_number'])->contain(['Cities'])->where(['Showrooms.status'=>'A', 'city_id'=>$city_id])->order(['Showrooms.name'=>'ASC'])->toArray();
        return $showrooms;
    }

    //S showrooms details based on id
    public function showroomDetailById($id)
    {
        $showroom = $this->find()->select(['Showrooms.id','Showrooms.name', 'Cities.city_name','Showrooms.address','Showrooms.contact_country_code','Showrooms.contact_number', 'Showrooms.area_sq_mts', 'Showrooms.image', 'Showrooms.email', 'Showrooms.min_product_quantity', 'Showrooms.showroom_timing', 'Showrooms.showroom_supervisor', 'ShowroomManager.first_name', 'ShowroomManager.last_name'])->contain(['Cities', 'ShowroomManager'])->where(['Showrooms.id'=>$id])->first();
        return $showroom;
    }

    //S all showrooms details
    public function showroom()
    {
        $showroom = $this->find()->select(['Showrooms.id','Showrooms.name', 'Cities.city_name','Showrooms.address','Showrooms.contact_country_code','Showrooms.contact_number', 'Showrooms.area_sq_mts', 'Showrooms.image', 'Showrooms.email', 'Showrooms.min_product_quantity', 'Showrooms.showroom_timing', 'ShowroomManager.first_name', 'ShowroomManager.last_name'])->contain(['Cities', 'ShowroomManager'])->where(['Showrooms.status'=>'A'])->order(['Showrooms.name'=>'ASC'])->toArray();
        return $showroom;
    }

    //S showrooms details by manager
    public function showroomByUser($user_id, $role_name, $search_str)
    {
        $search_cond = null;
        if($role_name == 'Showroom Supervisor'){
            $cond = ['Showrooms.showroom_supervisor'=>$user_id];
        } else if($role_name == 'Showroom Manager') {
            $cond = ['Showrooms.showroom_manager'=>$user_id];
        }
        if($search_str){
            $search_cond = [
                'OR' => [
                    'Showrooms.name LIKE' => '%' . $search_str . '%',
                    'Showrooms.address LIKE' => '%' . $search_str . '%'
                ]
            ];
        }
        $query = $this->find()->select(['Showrooms.id','Showrooms.name', 'Cities.city_name','Showrooms.address','Showrooms.contact_country_code','Showrooms.contact_number', 'Showrooms.area_sq_mts', 'Showrooms.image', 'Showrooms.email', 'Showrooms.min_product_quantity', 'Showrooms.showroom_timing', 'ShowroomManager.first_name', 'ShowroomManager.last_name', 'ShowroomSupervisor.first_name', 'ShowroomSupervisor.last_name'])->contain(['Cities', 'ShowroomManager', 'ShowroomSupervisor'])->where([$cond, 'Showrooms.status'=>'A', $search_cond])->order(['Showrooms.name'=>'ASC'])->enableAutoFields(false);

        // Execute the query and use map() to format the results
        /*$showrooms = $query->all()->map(function ($row) {

            // Directly return only the showroom ID and name
            return [
                'id' => $row->id ?? null,
                'name' => $row->name ?? null,
                'city_name' => $row->city->city_name ?? null,
                'address' => $row->address ?? null,
                'contact_country_code' => $row->contact_country_code ?? null,
                'contact_number' => $row->contact_number ?? null,
                'area_sq_mts' => $row->area_sq_mts ?? null,
                'image' => $row->image ?? null,
                'showroom_timing' => $row->showroom_timing ?? null,
                'manger_first_name' => $row->manager->first_name ?? null,
                'manager_last_name' => $row->manager->last_name ?? null,
                'supervisor_first_name' => $row->supervisor->first_name ?? null,
                'supervisor_last_name' => $row->supervisor->last_name ?? null
            ];
        })->toArray();*/

        return $query->toArray();
    }

    public function getShowrooms()
    {

        $query = $this->find('all')
            ->select(['id', 'name'])
            ->where(['Showrooms.status' => 'A'])
            ->order(['Showrooms.name' => 'ASC']);

        $showrooms = $query->toArray();

        // Loop through categories
        foreach ($showrooms as $showroom) {
            // Add the current category to the tree array
            $showroomTreeArray[$showroom->id] = $showroom->name;

        }

        return $showroomTreeArray;
    }

    public function getNotAssignedToZoneShowrooms()
    {
        // Subquery to get showroom IDs that are already mapped in ZoneShowrooms
        $subquery = $this->ZoneShowrooms->find()
            ->select(['showroom_id']);

        // Main query to get showrooms that are not in the subquery result
        $query = $this->find('all')
            ->select(['id', 'name'])
            ->where(['Showrooms.status' => 'A'])
            ->where(function ($exp) use ($subquery) {
                return $exp->notIn('Showrooms.id', $subquery);
            })
            ->order(['Showrooms.name' => 'ASC']);

        $showrooms = $query->toArray();

        // Prepare array of showrooms
        $showroomTreeArray = [];
        foreach ($showrooms as $showroom) {
            $showroomTreeArray[$showroom->id] = $showroom->name;
        }

        return $showroomTreeArray;
    }

    public function getUnmappedShowrooms($zoneId = null)
    {
        // Start by querying for active showrooms
        $query = $this->find()
            ->select(['id', 'name'])
            ->where(['Showrooms.status' => 'A']);

        // Exclude showrooms already mapped in ZoneShowrooms
        if ($zoneId === null) {
            // For add action, exclude all mapped showrooms
            $query->where(function ($exp) {
                return $exp->notIn('id', $this->ZoneShowrooms->find()->select(['showroom_id']));
            });
        } else {
            // For edit action, allow currently mapped showrooms of this zone
            $query->where(function ($exp) use ($zoneId) {
                return $exp->notIn('id', $this->ZoneShowrooms->find()
                    ->select(['showroom_id'])
                    ->where(['zone_id !=' => $zoneId])
                );
            });
        }

        // Order by showroom name and return as an array
        $query->order(['Showrooms.name' => 'ASC']);
        $showrooms = $query->toArray();

        // Prepare array of showrooms
        $showroomTreeArray = [];
        foreach ($showrooms as $showroom) {
            $showroomTreeArray[$showroom->id] = $showroom->name;
        }

        return $showroomTreeArray;
    }

    public function searchCartShowRoom($search_str, $city_name = null)
    {
        // Base condition for active showrooms
        $base_cond = ['Showrooms.status' => 'A'];

        // Search condition initialization
        $search_cond = [];

        // If search string is provided, create the search condition
        if ($search_str) {
            $search_cond = [
                'OR' => [
                    'Showrooms.name LIKE' => '%' . $search_str . '%',
                    'Showrooms.address LIKE' => '%' . $search_str . '%'
                ]
            ];
        }

        // If city name is provided and no search string is present, filter by city
        if ($city_name && !$search_str) {
            $base_cond['Cities.city_name LIKE'] = '%' . $city_name . '%';
        }

        // Construct the query with the necessary conditions
        $query = $this->find()
            ->select([
                'Showrooms.id',
                'Showrooms.city_id',
                'Showrooms.name',
                'Cities.city_name',
                'Showrooms.address',
                'Showrooms.contact_country_code',
                'Showrooms.contact_number'
            ])
            ->contain(['Cities', 'ShowroomManager', 'ShowroomSupervisor'])
            ->where(array_merge($base_cond, $search_cond))  // Merging base and search conditions
            ->order(['Showrooms.name' => 'ASC'])
           // ->limit(5)  // Limiting to 5 showrooms
            ->enableAutoFields(false);

        // Return the results as an array
        return $query->toArray();
    }

    public function showroomlist()
    {
        $showroom = $this->find()
            ->select([
                'Showrooms.id',
                'Showrooms.name',
                'Cities.city_name',
                'Showrooms.address',
                'Showrooms.contact_country_code',
                'Showrooms.contact_number',
                'Showrooms.area_sq_mts',
                'Showrooms.image',
                'Showrooms.email',
                'Showrooms.showroom_timing',
                'ShowroomManager.first_name',
                'ShowroomManager.last_name',
              //  'Municipalities.municipality_name' // ✅ Correct municipality name
            ])
            ->contain([
                'Cities',
                'ShowroomManager',
            ])
            ->where(['Showrooms.status' => 'A'])
            ->order(['Showrooms.name' => 'ASC'])
            ->toArray();

        return $showroom;
    }

    //Z
    public function showroomStocks($showroomId)
    {
        return $this->ProductStocks->find()
                ->select([
                    'ProductStocks.id',
                    'ProductStocks.product_attribute_id',
                    'ProductStocks.quantity',
                    'ProductStocks.reserved_stock',
                    'ProductStocks.defective_stock',
                    'ProductStocks.service_center_stock',
                    'ProductStocks.purchased_stock',
                    'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),
                    'Products.name',
                    'Products.reference_name',
                    'ProductVariants.variant_name',
                    'SKU' => $this->ProductStocks->find()->func()->coalesce([
                        new IdentifierExpression('ProductVariants.sku'),
                        new IdentifierExpression('Products.sku')
                    ]),
                    'Attributes.name',
                    'AttributeValues.value'
                ])
                ->contain([
                    'Showrooms' => ['fields' => ['Showrooms.id', 'Showrooms.name']],
                    'Warehouses' => ['fields' => ['Warehouses.id', 'Warehouses.name']],
                    'Products' => ['fields' => ['Products.id', 'Products.name', 'Products.purchase_price', 'Products.sku']],
                    'ProductVariants' => ['fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.purchase_price', 'ProductVariants.sku']],
                    // Always fetch attributes when product_attribute_id is available
                    'ProductAttributes' => function ($q) {
                        return $q->select([
                            'ProductAttributes.id',
                            'ProductAttributes.product_id',
                            'ProductAttributes.attribute_id',
                            'ProductAttributes.attribute_value_id'
                        ])
                        ->leftJoinWith('Attributes') // Ensures attributes are fetched when available
                        ->leftJoinWith('AttributeValues'); // Ensures attribute values are fetched when available
                    }
                ])
                ->where(['ProductStocks.showroom_id' => $showroomId])
                ->order(['ProductStocks.id' => 'DESC']);
    }

    //Z
    public function getWarehouseStocks()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['warehouse_id'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Warehouse ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $warehouseId = $data['warehouse_id'];

                // Calling the method directly
                $warehouseStocks = $this->Warehouses->warehouseStocks($warehouseId)->all();

                if (!$warehouseStocks->isEmpty()) {
                    $result = [
                        'status' => __('success'),
                        'data' => $warehouseStocks->toArray()
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => __('success'),
                        'data' => [],
                        'message' => __('No stock data found for the given warehouse')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }


}
