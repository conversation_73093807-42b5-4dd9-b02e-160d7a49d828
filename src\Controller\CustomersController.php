<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\Database\Expression\CaseExpression;



/**
 * Customers Controller
 *
 * @property \App\Model\Table\CustomersTable $Customers
 */
class CustomersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\CustomerAddressesTable $CustomerAddresses;
    protected \App\Model\Table\CitiesTable $Cities;
    protected \App\Model\Table\OrdersTable $Orders;
    protected \App\Model\Table\MunicipalitiesTable $Municipalities;
    protected \App\Model\Table\CustomerGroupsTable $CustomerGroups;
    protected \App\Model\Table\LoyaltySettingsTable $LoyaltySettings;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->Cities = $this->fetchTable('Cities');
        $this->Orders = $this->fetchTable('Orders');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->CustomerGroups = $this->fetchTable('CustomerGroups');
        $this->LoyaltySettings = $this->fetchTable('LoyaltySettings');
    }

    public function index()
    {
        $callcentreAgent = (int) Configure::read('Constants.CALL_CENTRE_AGENT_ROLE_ID');
        $callcentreSupervisor = (int) Configure::read('Constants.CALL_CENTRE_SUPERVISOR_ROLE_ID');

        $query = $this->Customers->find()
            ->contain([
                'Users',
                'Wallets' => function ($q) {
                    return $q->select([
                        'Wallets.customer_id',
                        'wallet_balance_sum' => $q->func()->sum('Wallets.balance')
                    ])->group('Wallets.customer_id');
                },
                'CustomerAddresses' => [
                    'conditions' => ['CustomerAddresses.status' => 'A'],
                    'Cities',
                    'Municipalities'
                ],
                'Orders' => function ($q) use ($callcentreAgent,$callcentreSupervisor) {
                    $exp = new QueryExpression();

                    return $q->select([
                        'Orders.customer_id',
                        'order_count' => $q->func()->count('*'),
                        'total_amount_sum' => $q->func()->sum('Orders.total_amount'),
                        'showroom_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.order_type = 'Showroom' THEN 1 ELSE 0 END")
                        ),
                        'online_web_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.order_type = 'Online' AND Orders.order_online_source = 'Web' THEN 1 ELSE 0 END")
                        ),
                        'online_mobile_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.order_type = 'Online' AND Orders.order_online_source = 'Mobile' THEN 1 ELSE 0 END")
                        ),
                        'delivered_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.delivery_mode = 'delivery' THEN 1 ELSE 0 END")
                        ),
                        'pickedup_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.delivery_mode = 'pickup' THEN 1 ELSE 0 END")
                        ),
                        'sales_mng_person_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.order_type = 'Showroom' AND (Orders.sales_person_id IS NOT NULL AND Orders.sales_person_id != '' OR Orders.showroom_id IS NOT NULL AND Orders.showroom_id != '') THEN 1 ELSE 0 END")
                        ),
                        'customer_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.order_type = 'Online' THEN 1 ELSE 0 END")
                        ),
                        'callcentre_order_count' => $q->func()->sum(
                            $q->newExpr("CASE WHEN Orders.created_by_role = {$callcentreAgent} OR Orders.created_by_role = {$callcentreSupervisor} THEN 1 ELSE 0 END")
                        ),
                    ])->group('Orders.customer_id');
                },
                'CustomerGroupMappings' => [
                    'conditions' => ['CustomerGroupMappings.status' => 'A'],
                    'CustomerGroups'
                ],
                'Loyalty' => function ($q) {
                    return $q->where(['Loyalty.status' => 'A']);
                }
            ])
            ->where(['Users.status !=' => 'D'])
            ->order(['Users.first_name' => 'ASC'])
            ->group('Customers.id');


        $customers = $query->all();
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])->all()->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No City available'];
        }

        $customerGroups = $this->CustomerGroups->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($customerGroups)) {
            $customerGroups = ['' => 'No Customer Groups available'];
        }
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';
        $this->set(compact('customers', 'status', 'statusMap', 'currencySymbol', 'dateFormat', 'cities', 'decimalSeparator', 'thousandSeparator', 'customerGroups'));
    }

    /**
     * View method
     *
     * @param string|null $id Customer id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $customer = $this->Customers->get($id, [
            'contain' => [
                'Users',
                'Carts',
                'CustomerAddresses' => function ($q) {
                    return $q->where(['CustomerAddresses.status' => 'A'])
                        ->contain(['Cities', 'Municipalities']);
                },
                'CustomerGroupMappings' => function ($q) {
                    return $q->where(['CustomerGroupMappings.status' => 'A'])
                        ->contain(['CustomerGroups']);
                },
                'Orders',
                'Reviews',
                'Wishlists',
                'Wallets',
                'Loyalty' => function ($q) {
                    return $q->where(['Loyalty.status' => 'A']);
                }
            ]
        ]);
        $profile_photo = $this->Media->getCloudFrontURL($customer->profile_photo);
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $orderstatusesMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $paymentstatusesMap = Configure::read('Constants.PAYMENT_STATUSES_MAP');
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('customer', 'profile_photo', 'currencySymbol', 'orderstatuses', 'paymentstatuses', 'orderstatusesMap', 'paymentstatusesMap', 'thousandSeparator', 'decimalSeparator'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */

    public function addCustomer()
    {

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            $user = $this->Customers->Users->newEmptyEntity();
            $user = $this->Customers->Users->patchEntity($user, $data);
            $email = $user->email;
            if ($email !== '') {
                $exists = $this->Customers->Users->exists([
                    'email' => $email,
                    'status' => 'A',
                    'user_type' => 'Customer'
                ]);
                if ($exists) {
                    $response = ['status' => 'error', 'message' => __('Email Already exists.')];
                    $this->response = $this->response->withType('application/json');
                    $this->response = $this->response->withStringBody(json_encode($response));
                    return $this->response;
                }
            }

            $mobile_no = $user->mobile_no;
            $country_code = $user->country_code;
            if ($mobile_no == '') {
                $user['mobile_no'] = null;
                $user['country_code'] = null;
            } else {
                $existsmob = $this->Customers->Users->exists(['Users.mobile_no' => $mobile_no, 'country_code' => $country_code, 'status' => 'A', 'user_type' => 'Customer']);
                if ($existsmob) {
                    $response = ['status' => 'error', 'message' => __('Mobile Number Already exists.')];
                    $this->response = $this->response->withType('application/json');
                    $this->response = $this->response->withStringBody(json_encode($response));
                    return $this->response;
                }
            }
            $password = $this->Global->randomPassword();
            $user['password'] = $password;
            $user['user_type'] = 'Customer';

            if ($this->Customers->Users->save($user)) {
                $userId = $user->id;

                $customer = $this->Customers->newEmptyEntity();
                $data['user_id'] = $userId;
                $customer = $this->Customers->patchEntity($customer, $data);

                if ($this->Customers->save($customer)) {
                    if ($email !== '') {
                        $to = trim($email);
                        $subject = "Welcome Email";
                        $template = "welcome_user";


                        $viewVars = ['username' => $user->first_name . ' ' . $user->last_name, 'email' => $email, 'password' => $password];
                        $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                    }

                    if ($mobile_no != '') {
                        $smsResult = $this->Global->sendWelcomeMsg($country_code . $mobile_no, $password);
                    }
                    $customers = $this->Customers->find()
                        ->contain(['Users'])
                        ->select([
                            'id' => 'Customers.id',
                            'full_name' => $this->Customers->Users->find()
                                ->func()
                                ->concat(['Users.first_name' => 'identifier', ' ', 'Users.last_name' => 'identifier']),
                            'phone_number' => $this->Customers->Users->find()
                                ->func()
                                ->concat(['+', 'Users.country_code' => 'identifier', 'Users.mobile_no' => 'identifier']),
                            'email' => 'Users.email'
                        ])
                        ->all()
                        ->map(function ($row) {
                            return [
                                'id' => $row->id,
                                'display' => $row->full_name . ' (' . $row->phone_number . ')' . '(' . $row->email . ')'
                            ];
                        })
                        ->toArray();

                    $response = [
                        'status' => 'success',
                        'message' => __('The customer has been saved.'),
                        'customers' => $customers
                    ];
                } else {
                    $this->log('Customer could not be saved. Errors: ' . json_encode($customer->getErrors()), 'debug');
                    $response = ['status' => 'error', 'message' => __('The customer could not be saved. Please try again with another email ID.')];
                }
            } else {
                $this->log('User could not be saved. Errors: ' . json_encode($user->getErrors()), 'debug');
                $response = ['status' => 'error', 'message' => __('The customer could not be saved. Please try again with another email ID.')];
            }
        } else {
            $response = ['status' => 'error', 'message' => __('Invalid request method.')];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    public function checkEmailExists()
    {
        $this->request->allowMethod(['post']);

        $email = $this->request->getData('email');
        $exists = $this->Customers->Users->exists(['email' => $email, 'status' => 'A', 'user_type' => 'Customer']);
        if (!$exists) {
            $response = ['status' => 'success', 'message' => __('Doenot exists.')];
        } else {
            $response = ['status' => 'error', 'message' => __('Exists.')];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }




    public function getCustomerDetails()
    {
        $this->request->allowMethod(['post']);

        $customerId = $this->request->getData('id');
        $customer = $this->Customers->find()
            ->contain([
                'Users',
                'CustomerAddresses' => function ($q) {
                    return $q->where(['CustomerAddresses.status' => 'A']);
                },
                'Loyalty' => function ($q) {
                    return $q->where(['Loyalty.status' => 'A']);
                },
                'CustomerGroupMappings' => function ($q) {
                    return $q->contain([
                        'CustomerGroups' => function ($q) {
                            return $q->where(['CustomerGroups.status' => 'A']);
                        }
                    ])
                        ->where(['CustomerGroupMappings.status' => 'A']);
                }

            ])
            ->where(['Customers.id' => $customerId])
            ->first();

        if ($customer) {
            $user = $customer->user ?? null;
            $address = !empty($customer->customer_addresses) ? $customer->customer_addresses[0] : null;
            $loyalty = !empty($customer->loyalty) ? $customer->loyalty[0] : null;
            $customerGroupIdsArray = [];
            if (!empty($customer->customer_group_mappings)) {
                $customerGroupIdsArray = array_map(function ($mapping) {
                    return $mapping->customer_group_id;
                }, $customer->customer_group_mappings);
            }
            
            $loyalty_redemption = null;
            if (!empty($customerGroupIdsArray)) {
                $loyalty_redemption = $this->LoyaltySettings->find()
                    ->select(['redeem_point_value', 'redeem_points'])
                    ->where([
                        'customer_group_id IN' => $customerGroupIdsArray,
                        'status' => 'A'
                    ])
                    ->first();
            }
            $response = [
                'status' => 'success',
                'customer' => [
                    'first_name' => $user->first_name ?? '',
                    'last_name' => $user->last_name ?? '',
                    'customer_address_id' => $address->id ?? '',
                    'email' => $user->email ?? '',
                    'phone_number' => $user->mobile_no ? '+' . $user->country_code . ' ' . $user->mobile_no : '',
                    'loyalty_category' => $loyalty->loyalty_category ?? '',
                    'loyalty_redeem_point_value' => $loyalty_redemption->redeem_point_value ?? '',
                    'loyalty_redeem_points' => $loyalty_redemption->redeem_points ?? '',
                    'spent_amount' => number_format((float)($loyalty->spent_amount ?? 0), 2),
                    'points' => (float)($loyalty->points ?? 0),
                    'validity' => $loyalty->validity ?? 'indefinitely',
                    'house_no' => $address->house_no ?? '',
                    'address_line1' => $address->address_line1 ?? '',
                    'address_line2' => $address->address_line2 ?? '',
                    'city_id' => $address->city_id ?? '',
                    'municipality_id' => $address->municipality_id ?? '',
                    'zipcode' => $address->zipcode ?? '',
                    'customer_groups' => $customer->customer_group_mappings ? json_encode(array_map(function ($mapping) {
                        return $mapping->customer_group_id;
                    }, $customer->customer_group_mappings)) : ''
                ]
            ];
        } else {
            $response = ['status' => 'error', 'message' => __('Customer not found.')];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }


    /**
     * Edit method
     *
     * @param string|null $id Customer id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $customer = $this->Customers->get($id, ['contain' => ['Users', 'CustomerAddresses' => function ($q) {
            return $q->where(['CustomerAddresses.status' => 'A'])
                ->contain(['Cities', 'Municipalities']);
        }, 'CustomerGroupMappings' => function ($q) {
            return $q->where(['CustomerGroupMappings.status !=' => 'D'])
                ->contain(['CustomerGroups']);
        }]]);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            if (!empty($data['user']['mobile_no'])) {
                $data['user']['mobile_no'] = str_replace(' ', '', $data['user']['mobile_no']);
            }
            $customer = $this->Customers->patchEntity($customer, $data, ['associated' => ['Users']]);
            $this->log(json_encode($customer), 'debug');
            if ($this->Customers->save($customer)) {
                $customerId = $customer->id;
                $CustGroupIds = $this->request->getData('customer_group_mappings._ids');

                $this->saveCustomerGroups($customerId, $CustGroupIds);
                $this->Flash->success(__('The customer has been saved.'));
                return $this->redirect(['action' => 'index']);
            }

            $this->Flash->error(__('The customer could not be saved. Please, try again.'));
        }
        $statuses = Configure::read('Constants.STATUS');
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])->all()->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No City available'];
        }

        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($municipalities)) {
            $municipalities = ['' => 'No Municipality available'];
        }

        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');

        $customergroupoptions = $this->CustomerGroups->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($customergroupoptions)) {
            $cities = ['' => 'No Customer Groups available'];
        }
        $selectedCustGroups = array_column($customer->customer_group_mappings, 'customer_group_id');

        $this->set(compact('customer', 'statuses', 'cities', 'municipalities', 'ABIDJAN_CITY_ID', 'customergroupoptions', 'selectedCustGroups'));
    }


    /**
     * Delete method
     *
     * @param string|null $id Customer id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function deleteCustomer($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $customer = $this->Customers->get($id, ['contain' => ['Users']]);
        $response = ['success' => false, 'message' => 'The customer could not be deleted. Please, try again.'];

        if ($customer) {
            if ($this->Orders->exists(['customer_id' => $customer->id])) {
                $response = ['success' => false, 'message' => 'Cannot delete this customer as orders are linked to this account, try again.'];
            } else {
                if ($this->Customers->Users->delete($customer->user)) {
                    $response = ['success' => true, 'message' => 'The customer has been deleted.'];
                } else {
                    $response = ['success' => false, 'message' => 'The customer could not be deleted. Please, try again.'];
                }
            }
        } else {
            $response = ['success' => false, 'message' => 'The customer does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function getCustomerAddresses($customerId)
    {
        $this->request->allowMethod(['get']);
        $addresses = $this->Customers->CustomerAddresses->find('all', [
            'conditions' => ['customer_id' => $customerId, 'CustomerAddresses.status =' => 'A'],
            'contain' => ['Municipalities', 'Cities']
        ])->toArray();

        $response = [
            'status' => 'success',
            'addresses' => []
        ];

        if (!empty($addresses)) {
            foreach ($addresses as $address) {
                $response['addresses'][] = [
                    'id' => $address->id,
                    'house_no' => $address->house_no,
                    'address_line1' => $address->address_line1,
                    'address_line2' => $address->address_line2,
                    'landmark' => $address->landmark,
                    'municipality_id' => $address->municipality_id ?? '',
                    'municipality_name' => $address->municipality->name ?? '',
                    'city_id' => $address->city_id ?? '',
                    'city_name' => $address->city->city_name ?? '',
                    'zipcode' => $address->zipcode
                ];
            }
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    protected function saveCustomerGroups($customerId, $CustGroupIds)
    {
        $customerGroupsMappingsTable = $this->Customers->CustomerGroupMappings;

        $customerGroupsMappingsTable->updateAll(
            ['status' => 'D'],
            ['customer_id' => $customerId]
        );

        $CustGroupIds = is_array($CustGroupIds) ? array_filter($CustGroupIds) : [];

        if (!empty($CustGroupIds)) {

            $data = [];
            foreach ($CustGroupIds as $groupId) {
                $data[] = [
                    'customer_id' => $customerId,
                    'customer_group_id' => $groupId,
                    'status' => 'A'
                ];
            }

            $entities = $customerGroupsMappingsTable->newEntities($data);
            if (!$customerGroupsMappingsTable->saveMany($entities)) {
                $this->Flash->error(__('The customer group associations could not be saved. Please, try again.'));
            }
        }
    }
}
