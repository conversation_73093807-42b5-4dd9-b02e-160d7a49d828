<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.4.6/css/flag-icon.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flag-icon-css/css/flag-icon.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">

<style>
    .remember-login .remember {
        margin-right: 70px !important;
    }
    .forget-password {
        font-size: 15px !important;
        font-weight: 400 !important;
        text-decoration: none !important;
    }
    #email-error, #password-error, #mobile-error{
        display: block;
        text-align: left;
        color: red;
        margin-left: 44px;
        font-weight: 600;
        font-size: 14px;
    }

    #terms_agree-error {
        display: block;
        text-align: left;
        color: red;
        margin-left: 50px;
        font-weight: 600;
        font-size: 14px;
    }

    #account-body-subcontainer {
        padding-right: 146px !important;
        padding-bottom: 20px;
        /* padding-top: 20px; */
        padding-top: 50px;
        padding-left: 20px;
        position: relative;
    }

    .sign-up-links-input-container {
        .input-selection {
            margin: 30px 0px 0px 0px;
            label {
               margin: 0px 20px 0px 0px;
            }
        }
    }

    #country_code {
        color: black;
        /* background-color: white; */
        background-color: transparent;
        outline: none;
        border: none;
        width: 75px;
        position: absolute;
        margin: 16px 0px 0px 4px;
    }

    .clone-sign-up-links-input-mail {
        padding-left: 100px;
        width: 205px;
    }

    .terms-container {
        align-items: flex-start;
        gap: 10px;
        margin-top: 24px;
    }

    .terms-container input[type="radio"] {
        margin-top: 0px;
    }

    #loginForm {
        width: 390px;
    }

    #togglePassword {
        cursor: pointer;
        font-size: 1.2rem;
        position: absolute;
        margin: 15px 0px 0px 276px;
    }
    #pasyatxt{
        width: 291px;
    }
    #pasyatxt::placeholder{
        padding-left:0px;
    }
    #pasyatxt-error {
        text-align: left;
        color: red;
        margin-left: 44px;
        font-weight: 600;
        font-size: 14px;
    }
    .remember-login{
        display: flex;
        align-items: center;
        margin-left: 39px;
        margin-top: 10px;
    }
    .remember-login .remember{
        /* margin-right: 90px; */
        margin-right: 56px;
    }
    .remember-login .forget-password{
        top:0;
    }
    #loginForm .log-action-container{
        margin-left: 29px;
    }
    #loginForm .log-action-container .request-otp{
        width: 304px;
    }
    #loginForm .log-action-container .sign-up-links-container .sign-up-with-google{
        width: 304px;
    }
    #loginForm .log-action-container .sign-up-links-container .sign-up-with-faebook{
        width: 304px;
    }
    #loginForm .log-action-container .new-to-babiken{
        width: 304px;
    }
    .log-home{
        position: absolute;
        top: 10px;
        left: 22px;
        color: #0d839b;
        font-weight: 700;
    }
    .log-home > i{
        color: #f77f00;
    }
    .sign-up-links-input-mail{
        width: 293px;
    }
    #mobile-container{
        position: relative;
    }
    .select2-container {
        position: absolute;
        left: 47px;
        top: 14px;
    }
    .select2-dropdown{
        width: 250px !important;
    }
    .select2-results__option img{
        width: 20px;
        position: relative;
        top: 1px;
    }
    .select2-container .select2-selection--single .select2-selection__rendered{
        padding-right: 1px;
        width: 54px;
        display: inline-block; 
        text-overflow: inherit; 
    }
    .select2-container .select2-selection--single .select2-selection__rendered img{
        width: 20px;
        position: relative;
        top: 2px;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow{
        right: -6px;
    }
    @media only screen and (max-width: 1024px) {
        #account-body-subcontainer{
            padding-right: 55px !important;
            padding-bottom: 20px;
            padding-top: 15px;
            padding-left: 15px;
            margin-left: 5px;
        }

        .camera-img-container {
            margin-right: 35px;
        }
    }
    @media (max-width:991px) {
        .log-home{
            display:none;
        }
    }
    @media (max-width: 769px) {
        #account-body-subcontainer {
        padding-right: 75px !important;
        padding-bottom: 20px;
        padding-top: 15px;
        padding-left: 0px;
        margin-left: -53px;
    }

    #country_code {
        margin: 20px 0px 0px 4px;
    }
}

    @media (max-width: 426px) {

        #account-body-subcontainer {
            padding-right: 48px !important;
            padding-bottom: 20px;
            padding-top: 15px;
            padding-left: 0px;
            margin-left: 30px;
            width: 300px;
        }

        .account-body {
            width: 400px;
            margin-left: 0px;
        }

        #country_code {
            width: 75px;
            margin: 19px 0px 0px 19px;
        }
    }

    @media (max-width: 376px){
        .account-body {
         width: 400px;
         margin-left: -25px;
    }

         #loginForm {
         width: 350px;
    }

    .terms-container {
    margin-left: -14px;
    }

    #togglePassword {
        margin: 15px 0px 0px 272px;
    }

    }

    @media (max-width: 321px) {
        .account-body {
            width: 328px;
            margin-left: -11px;
        }

        #request-div-containers {
            margin-left: 5px;
            width: 280px;
        }

        .sign-up-links-input-mail {
            font-size: 20px;
            width: 233px;
            margin-left: 7px;
        }

        .sign-up-with-your-mobile {
            font-size: 14px;
            font-weight: 600;
        }

        #mobile {
            padding-left: 85px;
            width: 157px;
        }


        #togglePassword {
            margin: 15px 0px 0px 226px;
        }

        #loginForm {
            width: 295px;
        }

        .sign-terms-stuffs {
            font-size: 10px;
            margin-left: 22px;
        }

        .new-to-babiken {
            font-size: 14px;
            width: 246px;
            margin-left: -18px;
            margin-top: 35px;
        }

        .request-otp {
            font-size: 15px;
            width: 252px;
            margin-left: -13px;
        }

        .sign-up-with-google {
            font-size: 18px;
            padding: 2px 0px;
            width: 250px;
            margin: 5px 0px 0px -5px;
        }

        .sign-up-with-faebook {
            font-size: 18px;
            padding: 2px 0px;
            width: 249px;
            margin: 27px 0px 0px 1px;
        }

        #sign-up-links-container {
        margin-left: -13px;
        }

        .account-body-texts {
            width: 300px;
        }

        .or {
            background-color: white;
            position: relative;
            top: 17px;
            padding: 10px;
            left: -3px;
        }

    }

</style>
<?php $this->end(); ?>
<accountbody class="account-body">
    <div class="account-body-subcontainer" id="account-body-subcontainer">
        <a href="/" class="log-home"><i class="fas fa-home"></i><?= __('Back to Home') ?></a>
        <div class="camera-img-container">
            <img src="<?= $loginPageImage['web_image']; ?>" class="camera-img">
        </div>
        <div class=" account-body-texts">
            <?= $this->Form->create(null, ['id' => 'loginForm']) ?>
            <?= $this->Flash->render() ?>
            <p class="looks-like-you-new"><?= __('Login') ?></p>
            <p class="sign-up-with-your-mobile"><?= __('Get access to your Orders, Wishlist and Recommendations') ?></p>

            <div class="sign-up-links-input-container">
            <?php
                $loginType = $this->request->getData('login_type') ?? 'mobile';
            ?>

            <div class="input-selection">
                <label>
                    <input type="radio" name="login_type" value="mobile" id="mobile-option"
                        <?= $loginType === 'mobile' ? 'checked' : '' ?>> <?= __("Mobile Number") ?>
                </label>
                <label>
                    <input type="radio" name="login_type" value="email" id="email-option"
                        <?= $loginType === 'email' ? 'checked' : '' ?>> <?= __("Email") ?>
                </label>
            </div>

            <br />
            <div id="email-container" class="toggle-input" style="<?= $loginType === 'email' ? '' : 'display: none;' ?>">
                <input type="email" class="sign-up-links-input-mail" value="<?= h($this->request->getData('email')) ?>" name="email" id="email" placeholder="Enter your email">
            </div>
                <div id="mobile-container" class="toggle-input" style="<?= $loginType === 'mobile' ? '' : 'display: none;' ?>">
                    <div>
                        <select name="country_code" id="country_code" class="country-code-dropdown">
                            <option value="1">+1 <small>(USA)</small></option>
                            <option value="1">+1 <small>(Canada)</small></option>
                            <option value="7">+7 <small>(Russia)</small></option>
                            <option value="7">+7 <small>(Kazakhstan)</small></option>
                            <option value="20">+20 <small>(Egypt)</small></option>
                            <option value="27">+27 <small>(South Africa)</small></option>
                            <option value="30">+30 <small>(Greece)</small></option>
                            <option value="31">+31 <small>(Netherlands)</small></option>
                            <option value="32">+32 <small>(Belgium)</small></option>
                            <option value="33">+33 <small>(France)</small></option>
                            <option value="34">+34 <small>(Spain)</small></option>
                            <option value="36">+36 <small>(Hungary)</small></option>
                            <option value="39">+39 <small>(Italy)</small></option>
                            <option value="40">+40 <small>(Romania)</small></option>
                            <option value="41">+41 <small>(Switzerland)</small></option>
                            <option value="43">+43 <small>(Austria)</small></option>
                            <option value="44">+44 <small>(United Kingdom)</small></option>
                            <option value="45">+45 <small>(Denmark)</small></option>
                            <option value="46">+46 <small>(Sweden)</small></option>
                            <option value="47">+47 <small>(Norway)</small></option>
                            <option value="48">+48 <small>(Poland)</small></option>
                            <option value="49">+49 <small>(Germany)</small></option>
                            <option value="51">+51 <small>(Peru)</small></option>
                            <option value="52">+52 <small>(Mexico)</small></option>
                            <option value="53">+53 <small>(Cuba)</small></option>
                            <option value="54">+54 <small>(Argentina)</small></option>
                            <option value="55">+55 <small>(Brazil)</small></option>
                            <option value="56">+56 <small>(Chile)</small></option>
                            <option value="57">+57 <small>(Colombia)</small></option>
                            <option value="58">+58 <small>(Venezuela)</small></option>
                            <option value="60">+60 <small>(Malaysia)</small></option>
                            <option value="61">+61 <small>(Australia)</small></option>
                            <option value="62">+62 <small>(Indonesia)</small></option>
                            <option value="63">+63 <small>(Philippines)</small></option>
                            <option value="64">+64 <small>(New Zealand)</small></option>
                            <option value="65">+65 <small>(Singapore)</small></option>
                            <option value="66">+66 <small>(Thailand)</small></option>
                            <option value="81">+81 <small>(Japan)</small></option>
                            <option value="82">+82 <small>(South Korea)</small></option>
                            <option value="84">+84 <small>(Vietnam)</small></option>
                            <option value="86">+86 <small>(China)</small></option>
                            <option value="90">+90 <small>(Turkey)</small></option>
                            <option value="91">+91 <small>(India)</small></option>
                            <option value="92">+92 <small>(Pakistan)</small></option>
                            <option value="93">+93 <small>(Afghanistan)</small></option>
                            <option value="94">+94 <small>(Sri Lanka)</small></option>
                            <option value="95">+95 <small>(Myanmar)</small></option>
                            <option value="98">+98 <small>(Iran)</small></option>
                            <option value="211">+211 <small>(South Sudan)</small></option>
                            <option value="212">+212 <small>(Morocco)</small></option>
                            <option value="213">+213 <small>(Algeria)</small></option>
                            <option value="216">+216 <small>(Tunisia)</small></option>
                            <option value="218">+218 <small>(Libya)</small></option>
                            <option value="220">+220 <small>(Gambia)</small></option>
                            <option value="221">+221 <small>(Senegal)</small></option>
                            <option value="222">+222 <small>(Mauritania)</small></option>
                            <option value="223">+223 <small>(Mali)</small></option>
                            <option value="224">+224 <small>(Guinea)</small></option>
                            <option value="225" selected="selected">+225 <small>(Ivory Coast)</small></option>
                            <option value="226">+226 <small>(Burkina Faso)</small></option>
                            <option value="227">+227 <small>(Niger)</small></option>
                            <option value="228">+228 <small>(Togo)</small></option>
                            <option value="229">+229 <small>(Benin)</small></option>
                            <option value="230">+230 <small>(Mauritius)</small></option>
                            <option value="231">+231 <small>(Liberia)</small></option>
                            <option value="232">+232 <small>(Sierra Leone)</small></option>
                            <option value="233">+233 <small>(Ghana)</small></option>
                            <option value="234">+234 <small>(Nigeria)</small></option>
                            <option value="235">+235 <small>(Chad)</small></option>
                            <option value="236">+236 <small>(Central African Republic)</small></option>
                            <option value="237">+237 <small>(Cameroon)</small></option>
                            <option value="238">+238 <small>(Cape Verde)</small></option>
                            <option value="239">+239 <small>(Sao Tome and Principe)</small></option>
                            <option value="240">+240 <small>(Equatorial Guinea)</small></option>
                            <option value="241">+241 <small>(Gabon)</small></option>
                            <option value="242">+242 <small>(Congo - Brazzaville)</small></option>
                            <option value="243">+243 <small>(Congo - Kinshasa)</small></option>
                            <option value="244">+244 <small>(Angola)</small></option>
                            <option value="245">+245 <small>(Guinea-Bissau)</small></option>
                            <option value="246">+246 <small>(British Indian Ocean Territory)</small></option>
                            <option value="248">+248 <small>(Seychelles)</small></option>
                            <option value="249">+249 <small>(Sudan)</small></option>
                            <option value="250">+250 <small>(Rwanda)</small></option>
                            <option value="251">+251 <small>(Ethiopia)</small></option>
                            <option value="252">+252 <small>(Somalia)</small></option>
                            <option value="253">+253 <small>(Djibouti)</small></option>
                            <option value="254">+254 <small>(Kenya)</small></option>
                            <option value="255">+255 <small>(Tanzania)</small></option>
                            <option value="256">+256 <small>(Uganda)</small></option>
                            <option value="257">+257 <small>(Burundi)</small></option>
                            <option value="258">+258 <small>(Mozambique)</small></option>
                            <option value="260">+260 <small>(Zambia)</small></option>
                            <option value="261">+261 <small>(Madagascar)</small></option>
                            <option value="262">+262 <small>(Réunion)</small></option>
                            <option value="262">+262 <small>(Mayotte)</small></option>
                            <option value="263">+263 <small>(Zimbabwe)</small></option>
                            <option value="264">+264 <small>(Namibia)</small></option>
                            <option value="265">+265 <small>(Malawi)</small></option>
                            <option value="266">+266 <small>(Lesotho)</small></option>
                            <option value="267">+267 <small>(Botswana)</small></option>
                            <option value="268">+268 <small>(Eswatini)</small></option>
                            <option value="269">+269 <small>(Comoros)</small></option>
                            <option value="290">+290 <small>(Saint Helena)</small></option>
                            <option value="291">+291 <small>(Eritrea)</small></option>
                            <option value="297">+297 <small>(Aruba)</small></option>
                            <option value="298">+298 <small>(Faroe Islands)</small></option>
                            <option value="299">+299 <small>(Greenland)</small></option>

                        </select>

                        <input type="tel" name="mobile" value="<?= h($this->request->getData('mobile')) ?>" id="mobile" placeholder="<?= __("Enter mobile number") ?>" class="mobile-input sign-up-links-input-mail clone-sign-up-links-input-mail">
                    </div>
                </div>

                <i id="togglePassword" class="bi bi-eye-slash" ></i>
                <input name="password" id="pasyatxt" type="password" class="sign-up-links-input-mail" placeholder="Enter Password" required>

                <script>
                    document.getElementById("togglePassword").addEventListener("click", function () {
                        const passwordInput = document.getElementById("pasyatxt");

                        // Check current type and toggle accordingly
                        if (passwordInput.type === "password") {
                            passwordInput.type = "text"; // make password visible
                            this.classList.remove("bi-eye-slash");
                            this.classList.add("bi-eye"); // eye icon becomes open
                        } else {
                            passwordInput.type = "password"; // hide password (dots)
                            this.classList.remove("bi-eye");
                            this.classList.add("bi-eye-slash"); // eye icon becomes closed
                        }
                    });
                </script>
                <div class="remember-login">
                    <div class="remember">
                        <input type="checkbox" /> <?= __('Remember Me') ?>
                    </div>
                    <div class="forget-link">
                        <a class="forget-password" id="forget-password-link" href="#"><?= __('Forgot Password') ?></a>
                        <script>
                            document.addEventListener("DOMContentLoaded", function () {
                                function updateForgetPasswordLink() {
                                    var isEmail = document.getElementById("email-option").checked;
                                    var baseUrl = "<?= $this->Url->build(['controller' => 'customer', 'action' => 'forget']) ?>";
                                    var param = isEmail ? "email" : "mobile";
                                    document.getElementById("forget-password-link").href = baseUrl + "?type=" + param;
                                }
                                document.getElementById("email-option").addEventListener("change", updateForgetPasswordLink);
                                document.getElementById("mobile-option").addEventListener("change", updateForgetPasswordLink);
                                updateForgetPasswordLink();
                            });
                        </script>
                    </div>
                </div>
            </div>
            <div class="terms-container">


            </div>
            <div class="request-div-containers log-action-container" id="request-div-containers">
                <button type="submit" class="request-otp"><?= __('LOGIN') ?></button>
                <div class="sign-up-links-container" id="sign-up-links-container">
                    <div class="sign-up-with-google" id="google-singUp"><img src="<?= $this->Url->webroot('assets/google-icon.png') ?>"
                                                                             class="goggle-icon"> <?= __('Login with Google') ?>
                    </div>
                    <div class="sign-up-with-faebook" id="fb-singUp"><img src="<?= $this->Url->webroot('assets/facebook-icon.png') ?>"
                                                                          class="facebook-icon"><?= __("Login with Facebook") ?>
                    </div>
                    <hr class="or-break">
                    <span class="or"><?= __('OR') ?></span>
                </div>
                <button type="button"  onclick="window.location.href='<?= $this->Url->build(['controller' => 'customer', 'action' => 'signup']) ?>'" class="new-to-babiken">New to Babiken?
                    <?= __("Create an Account") ?>
                </button>
            </div>
            <?= $this->Form->end() ?>
        </div>
    </div>
</accountbody>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.19.5/jquery.validate.min.js"></script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const emailOption = document.getElementById("email-option");
        const mobileOption = document.getElementById("mobile-option");
        const emailContainer = document.getElementById("email-container");
        const mobileContainer = document.getElementById("mobile-container");

        emailOption.addEventListener("change", function () {
            if (this.checked) {
                emailContainer.style.display = "block";
                mobileContainer.style.display = "none";
            }
        });

        mobileOption.addEventListener("change", function () {
            if (this.checked) {
                mobileContainer.style.display = "block";
                emailContainer.style.display = "none";
            }
        });

        // jQuery validation
        $("#loginForm").validate({
            rules: {
                email: {
                    required: function () {
                        return $("#email-option").is(":checked");
                    },
                    email: true
                },
                mobile: {
                    required: function () {
                        return $("#mobile-option").is(":checked");
                    },
                    digits: true,
                    minlength: 10,
                    maxlength: 15
                },
                password: {
                    required: true,
                    minlength: 6
                },
                terms_agree: {
                    required: true
                }
            },
            messages: {
                email: {
                    required: "Please enter your email.",
                    email: "Please enter a valid email address."
                },
                mobile: {
                    required: "Please enter your mobile number.",
                    digits: "Please enter only numbers.",
                    minlength: "Mobile number must be at least 10 digits.",
                    maxlength: "Mobile number must not exceed 15 digits."
                },
                password: {
                    required: "Please enter your password.",
                    minlength: "Password must be at least 6 characters long."
                },
                terms_agree: {
                    required: "Please accept the Terms of Use and Privacy Policy."
                }
            },
            errorElement: "div",
            errorPlacement: function (error, element) {
                error.addClass("error-message");
                if (element.attr("name") === "mobile") {
                    error.insertAfter(element.closest("#mobile-container"));
                } else if (element.attr("name") === "terms_agree") {
                    error.insertAfter(element.closest(".terms-container"));
                } else {
                    error.insertAfter(element);
                }
            }
        });
    });
</script>
<!-- This script written by Karthi on 27-03-2025 -->
<script>
    $(function(){
        $(".account-body").siblings("header").hide();
        $(".country-code-dropdown").select2({
            templateResult: formatState,
            templateSelection: formatState
        });
    });
    function formatState (state) {
        if (!state.id) {
            return state.text;
        }
        var baseUrl = "/assets";
        var fileName = state.text
        .replace(/[()]/g, '')           // Remove parentheses
        .replace(/[0-9+]/g, "")
        .trim()
        .toLowerCase()
        .replace(/\s+/g, '-');
        var $state = $(
            '<span><img src="' + baseUrl + '/' + fileName + '.png" class="img-flag" /> ' + state.text + '</span>'
        );
        // Use .text() instead of HTML string concatenation to avoid script injection issues
        $state.find("span").text(state.text);
        $state.find("img").attr("src", baseUrl + "/" + fileName + ".png");

        return $state;
    };
</script>
<?php $this->start('add_js'); ?>

    <script>
        <?php $loginUrl = $this->Url->build(['controller' => 'customer', 'action' => 'socialLogin','google']); ?>
        document.addEventListener("DOMContentLoaded", function() {
            document.getElementById("google-singUp").onclick = function() {
             alert("Social login is disabled here. It's available in the live (production) site.");
              // uncomment below line in production
              //  window.location.href = "<?= $loginUrl ?>";
            };

            <?php if (isset($showLoginToast) && $showLoginToast): ?>
            // Direct toastr call for login success
            if (typeof toastr !== 'undefined') {
                toastr.success('<?= isset($loginToastMessage) ? $loginToastMessage : __('You are logged in successfully!') ?>');
            } else {
                // If toastr is not loaded yet, try again after a short delay
                setTimeout(function() {
                    if (typeof toastr !== 'undefined') {
                        toastr.success('<?= isset($loginToastMessage) ? $loginToastMessage : __('You are logged in successfully!') ?>');
                    }
                }, 500);
            }
            <?php endif; ?>
        });
    </script>

<?php $this->end(); ?>
