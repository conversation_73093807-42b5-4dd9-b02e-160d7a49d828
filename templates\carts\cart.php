<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">

<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkOutPayment.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkoutNewCards.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkOrderSummary.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/cartDeliverToAddress.css') ?>">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<style>
    .Special-Offers {
        margin-right: 820px !important;
    }
    .custom-card-name {
        margin-left: 12px;
    }
    .custom-name-container{
        height: 58px;
    }
    .my-carousel-inner-div {
        height: 448px;
    }
    .description-price-offer {
        margin-left: 12px;
    }
    .card-t-name{
        height: 36px;
    }
    .clone-custom-parent-carousel .custom-card-subname{
        height: 33px;
    }
    .clone-custom-parent-carousel .custom-carousel {
        height: 470px;
    }
    .clone-custom-parent-carousel .custom-carousel-inner {
        height: 454px;
    }
    .p-v-p-item-description-image-description-price-offer {
        margin-left: 0px;
    }
    .add-to-cart {
        padding: 10px 7px;
    }
    button[data-item-type="increase"]{
        padding: 5px 10px;
    }
    .my-order-item-details {
        width: auto;
    }
</style>
<?php $this->end(); ?>

<div class="productCategoryListingC">
    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span class="productCategoryListing-home-span">Home</span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span">Cart</span>
    </div>
</div>
<div style="text-align: center;">
        <?= $this->Flash->render() ?>
</div>
<div class="productCategoryListingC back-transparent">
    <div class="productCategoryListing">
        <?php if($checkCartCount > 0): ?>
        <div class="checkout-payment-title">CART <span>(<?= $total_items ?>)</span></div>
        <?php endif; ?>
    </div>
</div>

<div class="check-out-container">


    <div class="p-v-p-item-description-container">
        <?php if($checkCartCount > 0): ?>
        <div class="show-order-summary-container">

            <?php foreach ($cartItems as $item): ?>
            <div class="my-order-item-details">
                <a href="/product/<?= $item['url_key'] ?>">
                <img src="<?= $item['product_image'] ?>" class="my-order-item-icon">
                </a>
                <div class="my-order-items-details-tpc">
               
                    <a href="/product/<?= $item['url_key'] ?>">
                    <div class="my-order-item-details-title-container">
                        <div class="my-order-item-details-title"><?= $item['product_name'] ?></div>
                    </div>
                    <div class="my-order-item-details-price"><?= $this->Price->setPriceFormat($item['price']) ?> </div>
                    <div class="my-order-item-details-price"><?= __("Reference: "). $item['reference_name'] ?>  </div>
                    <div class="units-left"><?= $item['get_available_status'] ?></div>
                    <div  style="padding-top: 6%;">  <span class="strikethrough-text" style="margin-left:0;"><?= $this->Price->setPriceFormat($item['sale']) ?></span></div>
                    <div>
                        <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $item['discount'] ?></span>% 0ff</span>
                    </div>
                    <div class="custom-card-star-cart">
                            <?php echo $this->Rating->renderStars($item['rating']); ?>
                    </div>
                    </a>
                </div>
                <div class="add-to-cart-ctnr">
                    <div class="counter">
                        <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                data-item-type="decrease">-
                        </button>
                        <div class="counter-value cartItemQty id=" value
                         value="<?= $item['quantity'] ?>"><?= $item['quantity'] ?></div>
                        <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                data-item-type="increase">+
                        </button>
                    </div>
                    <div class="p-v-p-item-description-add-to-wishlist-share">

                        <?php if ($item['whishlist']): ?>
                        <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                            data-product-id="<?= $item['product_id'] ?>"><span
                                class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                    src="/assets/heart-background.png" class="wishlist"> </span>
                        </div>
                        <?php else: ?>
                                <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                    data-product-id="<?= $item['product_id'] ?>"><span
                                        class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                            src="/assets/heart-nobackgrounddark.png" class="wishlist"> </span>
                                </div>
                        <?php endif; ?>
                    </div>
                <button class="close-btn-btn closeCartItem" data-item-id="<?= $item['cart_item_id'] ?>">✖</button>
            </div>
        </div>
        <?php endforeach; ?>
            <!-- <div class="checkout-payment-title">Not Included <span>(<?= count($cartOutItems); ?>)</span></div> -->
        <?php foreach ($cartOutItems as $item): ?>
            <div class="new-my-order-item-details my-order-item-details">
                <a href="/product/<?= $item['url_key'] ?>">
                <img src="<?= $item['product_image'] ?>" class="my-order-item-icon">
                </a>
                <div class="my-order-items-details-tpc">
                <a href="/product/<?= $item['url_key'] ?>">
                    <div class="my-order-item-details-title-container">
                        <div class="my-order-item-details-title"><?= $item['product_name'] ?></div>
                    </div>
                   
                    <div class="my-order-item-details-price"><?= $this->Price->setPriceFormat($item['price']) ?></div>

                    <div class="units-left out-of-stock"><?= $item['get_available_status'] ?></div>
                    <div class="custom-card-star-cart">
                        <?php echo $this->Rating->renderStars($item['rating']); ?>
                    </div>
                  </a>  
                </div>
                <div class="add-to-cart-ctnr">
                    <div class="counter">
                        <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                data-item-type="decrease">-
                        </button>
                        <div class="counter-value cartItemQty id=" value
                        " value="<?= $item['quantity'] ?>"><?= $item['quantity'] ?></div>
                        <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                data-item-type="increase">+
                        </button>
                    </div>
                    <div class="p-v-p-item-description-add-to-wishlist-share">

                             <?php if ($item['whishlist']): ?>
                                <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                    data-product-id="<?= $item['product_id'] ?>"><span
                                        class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                            src="/assets/heart-background.png" class="wishlist"> </span>
                                </div>
                            <?php else: ?>
                                    <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                        data-product-id="<?= $item['product_id'] ?>"><span
                                            class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                                src="/assets/heart-nobackgrounddark.png" class="wishlist"> </span>
                                    </div>
                            <?php endif; ?>
                        </div>
                    <button class="close-btn-btn closeCartItem" data-item-id="<?= $item['cart_item_id'] ?>">✖</button>
                </div>
            </div>
        <?php endforeach; ?>
        </div>

        <div class="checkout-order-summary">
            <div class="checkout-order-summary-title">
               <?= __("PRICE DETAILS") ?>
            </div>

            <div class="checkout-order-summary-price">
            <div>Price <span>(<?= $total_items ?> items)</span> :</div>
            <div></div>
            <div><?= $this->Price->setPriceFormat($totalPrice) ?> </div>
            </div>
            <hr class="checkout-order-summary-hr">

            <div class="for-padding">

                <div class="checkout-order-discount-price-label">
                    <div>Discount :</div>
                    <div style="text-decoration: line-through;"><?= $this->Price->setPriceFormat($totalDiscountedPrice) ?></div>
                </div>

            </div>

            <hr class="checkout-order-summary-hr">

            <div class="checkout-order-summary-price">
                <div>Total</div>
                <div><span class="totalAmount"></span> FCFA</div>
            </div>


            <hr class="checkout-order-summary-hr">

            <button class="PROCEED-CHECKOUT"
                    onclick="window.location.href='<?= $this->Url->build(['controller' => 'account', 'action' => 'checkout']) ?>';">
                PROCEED TO CHECKOUT
            </button>

        </div>
        <?php else: ?>
            <div class="no-cart-items">
                <img src="/assets/empty-cart.png" alt="Empty Cart image" />
                <p class="no-cart-item-text"><?= __("Your cart is empty!") ?></p>
            </div>
        <?php endif; ?>
    </div>


</div>

<?php if($checkCartCount <= 0): ?>
    <div class="my-carousel-controls">
        <button class="carousel-button left-btn" onclick="moveLeftMyCarouselMy()">🠔</button>
        <button class="carousel-button right-btn" onclick="moveRightMyCarouselMy()">🠖</button>
    </div>
    <div class="my-carousel-container">
        <p class="promo-text"><?= __("Top Selling Items") ?> </p>
        <div class="my-parent-carousel">
            <div class="my-carousel">
                <div class="my-carousel-inner-div">

                    <?php foreach ($topSellCarEmptyItemShow as $k => $val): ?>
                    <?php //print_r($val); ?>
                        <div class="custom-card-div">
                            <div class="custom-box-2">

                                <?php if ($val->whishlist): ?>
                                    <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                        data-product-id="<?= $val->id ?>"><span
                                            class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                    </div>
                                <?php else: ?>
                                    <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                        data-product-id="<?= $val->id ?>"><span
                                            class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <span class="snd-crsl-img">
                                    <?php if (!empty($val['product_image'])): ?>
                                        <img src="<?= $val['product_image'] ?>" class="snd-crsl-img" alt="Product Image"/>
                                    <?php else: ?>
                                        <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="snd-crsl-img"
                                            alt="Product Image"/>
                                    </span>
                                    <?php endif; ?>
                            <div class="custom-card-star">
                                <?php echo $this->Rating->renderStars($val['rating']); ?>
                            </div>
                            <div class="custom-name-container">
                                <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val->url_key]) ?>">
                                    <div class="custom-card-name"><?= $val['name'] ?></div>
                                </a>
                                <div class="custom-card-subname"><?= __("Reference: ") ?> <?= $val['reference_name'] ?></div>
                            </div>
                            <div class="custom-card-price"><?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                                <button class="custom-card-add-to-cart-button  add-to-cart"
                                        data-product-id="<?= $val['id'] ?>">
                                                <span class="custom-card-cart-icon"><i
                                                        class="fas fa-cart-arrow-down"></i></span>
                                    <span class="custom-card-add-to-cart">ADD TO CART</span>
                                </button></div>

                            <span class="strikethrough-text"><?= $this->Price->setPriceFormat($val['sales_price']) ?></span>

                        </div>
                    <?php endforeach; ?>


                </div>
            </div>
        </div>
    </div>

    <?php if (isset($userId) && $userId !== null): ?>
            <div class="custom-carousel-container" style="margin-top: 3%;">
                <p class="Special-Offers">Recently Viewed Products</p>
                <div class="custom-carousel-controls Recently-reviewed-for-mob">
                    <button class="carousel-button left-btn-s" onclick="slideLeftRecentlyViewed()">🡠</button>
                    <button class="carousel-button right-btn-s" onclick="slideRightRecentlyViewed()">🡢</button>
                </div>
                <div class="custom-parent-carousel clone-custom-parent-carousel">
                    <div class="custom-carousel">
                        <div class="custom-carousel-inner">
                            <?= $this->RecentViewProduct->renderSlider($userId) ?>
                        </div>
                    </div>
                </div>
            </div>
    <?php endif; ?>
<?php endif; ?>

<style>

    .rating-stars .star {
        color: rgb(55, 205, 55);
        font-size: 20px;
        margin-right: 2px;
    }

    .rating-stars .star:before {
        content: '\2605';
    }

    .rating-stars .star.half:after {
        /*color: #ffc107; /* A slightly lighter gold for half-stars */
        content: '\2605';
        color: rgb(55, 205, 55);
        position: absolute;
        margin-left: -17px;
        width: 9px;
        overflow: hidden;
    }

    .rating-stars .star.empty {
        color: #ddd; /* Gray for empty stars */
    }

    .clone-other-footer-text {
        margin: -205px 0px 0px 0px;
    }

</style>

<?php $this->start('add_js'); ?>

<script>
    let lytPoint = 0;
    $(document).on('click', '.loyaltyPointsCheck', function () {
        var customerId = <?= json_encode($customerId) ?>;
        if (customerId == null) {

            toastr.warning("<?= __('Login required to add loyalty points') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            checkLoyaltyPoint()
                .then((res) => {
                    if (res.status == 'success' || res.status == 200) {
                        toastr.success(res.message, '', {
                            timeOut: 3000,  // 3 seconds before hiding the toastr message
                            progressBar: true,
                            onHidden: function () {
                                document.getElementById('popup').style.display = 'block';
                                document.getElementById('overlay').style.display = 'block';
                            }
                        });
                    } else {
                        toastr.warning(res.message, '', {
                            timeOut: 3000,  // 3 seconds before hiding the toastr message
                            progressBar: true,
                            onHidden: function () {
                                // Reload the page after the toastr message disappears
                                //    location.reload();
                            }
                        });
                    }
                })
                .catch((error) => {
                    toastr.error(error, 'Error')
                });
        }
    });


    function checkLoyaltyPoint() {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkLoyaltyPoint']) ?>",
                type: 'POST',
                success: function (response) {
                    lytPoint = response.data.points_converted;
                    $(".setDateAjax").html(new Date(response.data.validity_end_date.date).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                    }));
                    $(".loyalty-price").html(response.data.points_converted);

                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    $(".radeem-btn").on("click", function () {
        let inputPoint = parseFloat($(".radeem-input").val());

        if (inputPoint > parseFloat(lytPoint)) {
            toastr.warning("<?= __('Your entered point must be less than available points.') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            $(".lyt-reedem-point").html(inputPoint);
            $(".lytPoints").val(inputPoint);

            closePopup();
            toastr.success("<?= __('Point Applied Successfully!') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });

            // Store the input value in sessionStorage
            sessionStorage.setItem('redeemPoint', inputPoint);
            updateTotalAmount();
        }
    });

    // On page load, retrieve the value from sessionStorage
    $(document).ready(function () {
        let redeemPoint = sessionStorage.getItem('redeemPoint');
        if (redeemPoint) {
            $(".lyt-reedem-point").html(redeemPoint);
            $(".lytPoints").val(redeemPoint);
            $(".radeem-input").val(redeemPoint);
        }
        updateTotalAmount();
    });

    var customerId = <?= json_encode($customerId) ?>;
    if (customerId) {
        document.getElementById('loyaltyPoints').onclick = function () {
            document.getElementById('popup').style.display = 'block';
            document.getElementById('overlay').style.display = 'block';
        };
    }

    function closePopup() {
        document.getElementById('popup').style.display = 'none';
        document.getElementById('overlay').style.display = 'none';
    }
</script>
<script>

    $(document).on('click', '.checkCouponCode', function () {
        let couponName = $(".couponName").val();

        applyOffers(couponName, <?= $totalPrice ?>)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    $(".appliedCoupon").html(couponName);
                    $(".offerApplied").html(res.data.coupon_amount);

                    sessionStorage.setItem('coupon_amount', res.data.coupon_amount);
                    sessionStorage.setItem('appliedCoupon', couponName);
                    updateTotalAmount();
                    toastr.success(res.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                        }
                    });
                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                            sessionStorage.removeItem('coupon_amount');
                            sessionStorage.removeItem('appliedCoupon');
                            updateTotalAmount();
                            window.location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                toastr.error(error, 'Error')
            });
    });

    function applyOffers(couponName, amt) {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'applyoffer']) ?>",
                type: 'POST',
                data: {'coupon_code': couponName, 'subTotal': amt},
                success: function (response) {
                    if (response.status) {


                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    $(document).ready(function () {
        // Check sessionStorage for appliedCoupon and coupon_amount
        let appliedCoupon = sessionStorage.getItem('appliedCoupon');
        let couponAmount = sessionStorage.getItem('coupon_amount');

        if (appliedCoupon && couponAmount) {
            // Update UI with stored values
            $(".appliedCoupon").html(appliedCoupon);
            $(".couponName").val(appliedCoupon);
            $(".offerApplied").html(couponAmount);
        } else {
            // Reset UI if no values in sessionStorage
            $(".appliedCoupon").html('');
            $(".offerApplied").html('0.00');
        }
    });
</script>

<script>
    $(document).ready(function () {
        updateTotalAmount();

        // On click of removeCoupon button
        $('.removeCoupon').on('click', function () {
            // Remove coupon amount from sessionStorage
            sessionStorage.removeItem('coupon_amount');
            sessionStorage.removeItem('appliedCoupon');

            // Reset applied coupon UI (optional)
            $(".appliedCoupon").html('N/A');
            $(".offerApplied").html('0.00');
            $(".couponName").val('');

            // Update total amount
            updateTotalAmount();

            // Optionally show a message
            toastr.success("Coupon removed successfully!", '', {
                timeOut: 3000,
                progressBar: true,
            });
        });
    });

    function updateTotalAmount() {
        // Retrieve stored values
        let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
        let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
        let subAmount = <?= $totalPrice ?>;

        let totalAmount = subAmount - couponAmount - redeemPoint;
        $('.totalAmount').html(formatAmount(totalAmount));
    }
    function formatAmount(amount){
        return amount.toString().split('.')[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    }
</script>

<script>
    $(document).ready(function () {
        var search_str = '';  // Default empty search string
        var city_name = '';  // Default city name (can be adjusted as needed)
        var debounceTimeout;

        // Initial call to fetch showroom data when the page loads
        fetchShowroomData(search_str, city_name);

        // Listen for input changes and call fetchShowroomData with the updated search term
        $('#showroom-search').on('input', function () {
            var updatedSearchStr = $(this).val();  // Get the value from the search input field

            // Clear the previous timeout to reset the delay
            clearTimeout(debounceTimeout);

            // Set a new timeout to call fetchShowroomData after 500ms delay
            debounceTimeout = setTimeout(function () {
                fetchShowroomData(updatedSearchStr, city_name);  // Call the function with the updated search string
            }, 500); // 500ms delay (can be adjusted as needed)
        });
    });
    $(document).ready(function () {
        // When the radio button for "Pickup from showroom" is selected
        $('#pickup-from-showroom').on('change', function () {
            if ($(this).prop('checked')) {
                $('#pickup-div').show();  // Show Pickup div
                $('#delivery-div').hide();  // Hide Delivery div
            }
        });

        // When the radio button for "Deliver to address" is selected
        $('#deliver-to-address').on('change', function () {
            if ($(this).prop('checked')) {
                $('#delivery-div').show();  // Show Delivery div
                $('#pickup-div').hide();  // Hide Pickup div
            }
        });
    });

    // Function to fetch showroom data via AJAX
    function fetchShowroomData(search_str, city_name) {
        // Show loading message
        $('#loading-message').show();

        $.ajax({
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'getCartShowRoomAddress']) ?>",
            method: 'post',  // Assuming it's a POST request
            data: {
                search_str: search_str,
                city_name: city_name
            },
            dataType: 'json',  // Expecting JSON response
            success: function (response) {
                // Hide loading message
                $('#loading-message').hide();

                // Handle the successful response
                console.log('API Response:', response);
                if (response.status === 'success') {
                    // Clear the previous showroom list
                    $(".showroom-list").html("");

                    // Create new showroom list based on the response
                    let showroomHTML = response.data.map(showroom => {
                        return `
                            <div class="Order-s-nameandaddress">
                                <div class="Order-s-nameandaddress-internal-ctn">
                                    <input type="radio" name="showroom" id="showroom-${showroom.id}" value="${showroom.id}">
                                    <label for="showroom-${showroom.id}" class="pop-up-name">${showroom.name}</label>
                                </div>

                                <div class="pop-up-address">
                                    <div>Address: ${showroom.address}</div>
                                    <div>City: <span>${showroom.city.city_name}</span></div>
                                    <div><img src="../assets/phone.256x254.png" class="phone"> <span>${showroom.contact_number}</span></div>
                                </div>
                            </div>
                        `;
                    }).join(''); // Join the array of HTML strings into one string

                    // Update the content in the "showroom-list" container
                    document.getElementById('showroom-list').innerHTML = showroomHTML;
                } else {
                    console.error('Error:', response.message);
                }
            },
            error: function (xhr, status, error) {
                // Hide loading message in case of error
                $('#loading-message').hide();

                // Handle error
                console.error('AJAX Error:', error);
            }
        });
    }

    sessionStorage.removeItem('coupon_amount');
    sessionStorage.removeItem('redeemPoint');
    sessionStorage.removeItem('delivery_charge');
    sessionStorage.removeItem('delivery_mode_type');
    sessionStorage.removeItem('showroom_id');
    sessionStorage.removeItem('customer_address_id');
    sessionStorage.removeItem('city_id');
    sessionStorage.removeItem('appliedCoupon');
</script>
<?php $this->end(); ?>
