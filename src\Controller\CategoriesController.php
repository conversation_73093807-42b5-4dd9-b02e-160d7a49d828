<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;
use Cake\ORM\Behavior\TreeBehavior;

/**
 * Categories Controller
 *
 * @property \App\Model\Table\CategoriesTable $Categories
 */
class CategoriesController extends AppController
{


    protected $Attributes;
    protected $AttributeValues;
    protected $CategoryAttributes;
    protected $ProductCategories;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Attributes = $this->fetchTable('Attributes');
        $this->AttributeValues = $this->fetchTable('AttributeValues');
        $this->CategoryAttributes = $this->fetchTable('CategoryAttributes');
        $this->ProductCategories = $this->fetchTable('ProductCategories');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    public function index()
    {
        $query = $this->Categories
            ->find('threaded')
            ->select(['id', 'name', 'parent_id', 'status'])
            ->contain([
                'ParentCategories' => function ($q) {
                    return $q->select(['id', 'name']);
                },
                'ChildCategories' => function ($q) {
                    return $q->select(['id', 'name', 'parent_id', 'status'])
                        ->contain([
                            'ParentCategories' => function ($q) {
                                return $q->select(['id', 'name']);
                            },
                            'ChildCategories' => function ($q) {
                                return $q->select(['id', 'name', 'parent_id', 'status'])
                                    ->contain([
                                        'ParentCategories' => function ($q) {
                                            return $q->select(['id', 'name']);
                                        }
                                    ]);
                            }
                        ]);
                }
            ])->order(['Categories.name' => 'ASC']);

        $query->where(['Categories.status !=' => 'D']);

        $categories = $query->toArray();

        $title = 'Manage Categories';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        // $categories = $this->Categories->getCategoriesWithHierarchy();
        $this->set(compact('categories', 'title', 'status', 'statusMap'));
    }

    public function view($id = null)
    {
        $category = $this->Categories->get($id, [
            'contain' => [
                'ParentCategories',
                'BannerAds',
                'BrandCategoryMappings',
                'ChildCategories',
                'CategoryAttributes' => function ($q) {
                    return $q->where(['CategoryAttributes.status' => 'A']);
                },
                'ProductCategories',
                'WidgetCategoryMappings'
            ]
        ]);


        $category_icon = $this->Media->getCloudFrontURL($category->category_icon);
        $web_banner = $this->Media->getCloudFrontURL($category->web_banner);
        $mobile_banner = $this->Media->getCloudFrontURL($category->mobile_banner);

        $categoryAttributes = $this->CategoryAttributes->find()
            ->contain([
                'Attributes',
                'AttributeValues' => function ($q) {
                    return $q->where(['AttributeValues.status' => 'A']);
                }
            ])
            ->where([
                'CategoryAttributes.category_id' => $id,
                'CategoryAttributes.status' => 'A'
            ])
            ->all();

        $attributeList = [];

        foreach ($categoryAttributes as $categoryAttribute) {
            $attribute = $categoryAttribute->attribute;
            $attributeValue = $categoryAttribute->attribute_value;

            if (!$attribute || !$attributeValue) {
                continue;
            }

            $attrId = $attribute->id;
            $attrName = $attribute->name;

            if (!isset($attributeList[$attrId])) {
                $attributeList[$attrId] = [
                    'attribute_id' => $attrId,
                    'attribute_name' => $attrName,
                    'attribute_values' => []
                ];
            }

            if (!in_array($attributeValue->value, $attributeList[$attrId]['attribute_values'])) {
                $attributeList[$attrId]['attribute_values'][] = $attributeValue->value;
            }
        }

        $attributeList = array_values($attributeList);



        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');

        $title = 'Category | View';
        $this->set(compact('category', 'category_icon', 'web_banner', 'mobile_banner', 'title', 'status', 'statusMap', 'attributeList'));
    }

    public function add()
    {

        $this->set([
            'iconSize' => Configure::read('Constants.CATEGORY_ICON_SIZE'),
            'webBannerSize' => Configure::read('Constants.CATEGORY_WEB_BANNER_SIZE'),
            'mobileBannerSize' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_SIZE'),
            'iconType' => Configure::read('Constants.CATEGORY_ICON_JS_TYPE'),
            'webBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_JS_TYPE'),
            'mobileBannerType' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_JS_TYPE'),
            'iconMinWidth' => Configure::read('Constants.CATEGORY_ICON_MIN_WIDTH'),
            'iconMaxWidth' => Configure::read('Constants.CATEGORY_ICON_MAX_WIDTH'),
            'iconMinHeight' => Configure::read('Constants.CATEGORY_ICON_MIN_HEIGHT'),
            'iconMaxHeight' => Configure::read('Constants.CATEGORY_ICON_MAX_HEIGHT'),
            'webBannerMinWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_WIDTH'),
            'webBannerMaxWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_WIDTH'),
            'webBannerMinHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_HEIGHT'),
            'webBannerMaxHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_HEIGHT'),
            'mobileBannerMinWidth' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MIN_WIDTH'),
            'mobileBannerMaxWidth' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MAX_WIDTH'),
            'mobileBannerMinHeight' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MIN_HEIGHT'),
            'mobileBannerMaxHeight' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MAX_HEIGHT'),
            'categoryIconType' => Configure::read('Constants.CATEGORY_ICON_TYPE'),
            'categoryWebBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_TYPE'),
            'categoryMobileBannerType' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_TYPE'),
        ]);

        $category = $this->Categories->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $attribute_name_id = isset($data['attribute_name_id']) ? $data['attribute_name_id'] : '';
            $attribute_source = isset($data['attribute_source']) ? $data['attribute_source'] : '';
            $attribute_names = isset($data['attribute_name']) ? $data['attribute_name'] : '';
            $attribute_values = isset($data['attribute_value']) ? $data['attribute_value'] : '';

            $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Categories');

            if (isset($data['category_icon']) && $data['category_icon']->getError() === UPLOAD_ERR_OK) {


                $category_icon = $data['category_icon'];
                $fileName = trim($category_icon->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $category_icon->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Category Icon could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['category_icon'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['category_icon'] = '';
            }

            if (isset($data['web_banner']) && $data['web_banner']->getError() === UPLOAD_ERR_OK) {
                $web_banner = $data['web_banner'];
                $fileName = trim($web_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $web_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Web banner could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['web_banner'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['web_banner'] = '';
            }


            if (isset($data['mobile_banner']) && $data['mobile_banner']->getError() === UPLOAD_ERR_OK) {
                $mobile_banner = $data['mobile_banner'];
                $fileName = trim($mobile_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $mobile_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Mobile banner could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['mobile_banner'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['mobile_banner'] = '';
            }

            $categorydata = $this->Categories->patchEntity($category, $data);
            $categorysave = $this->Categories->save($categorydata);
            if ($categorysave) {

                $catId = $categorysave->id;

                foreach ($attribute_source as $index => $source) {
                    $name = trim($attribute_names[$index]);
                    $values = array_map('trim', explode(',', $attribute_values[$index]));
                    $attributeId = null;

                    if ($source === 'manual') {
                        $attribute = $this->Attributes->newEntity([
                            'name' => $name,
                            'key_name' => 'category',
                            'status' => 'A'
                        ]);
                        if ($this->Attributes->save($attribute)) {
                            $attributeId = $attribute->id;

                            foreach ($values as $value) {
                                $attributeValue = $this->AttributeValues->newEntity([
                                    'attribute_id' => $attributeId,
                                    'value' => $value,
                                    'status' => 'A'
                                ]);
                                if ($this->AttributeValues->save($attributeValue)) {
                                    $categoryAttribute = $this->CategoryAttributes->newEntity([
                                        'attribute_id' => $attributeId,
                                        'attribute_value_id' => $attributeValue->id,
                                        'category_id' => $catId
                                    ]);
                                    $this->CategoryAttributes->save($categoryAttribute);
                                }
                            }
                        }
                    } elseif ($source === 'datalist') {
                        $attributeId = $attribute_name_id[$index] ?? null;
                        if ($attributeId) {
                            foreach ($values as $value) {
                                // Check if value already exists for this attribute_id
                                $existingValue = $this->AttributeValues->find()
                                    ->where(['attribute_id' => $attributeId, 'value' => $value])
                                    ->first();

                                if (!$existingValue) {
                                    $newValue = $this->AttributeValues->newEntity([
                                        'attribute_id' => $attributeId,
                                        'value' => $value,
                                        'status' => 'A'
                                    ]);
                                    if ($this->AttributeValues->save($newValue)) {
                                        $valueId = $newValue->id;
                                    } else {
                                        continue;
                                    }
                                } else {
                                    $valueId = $existingValue->id;
                                }

                                $categoryAttribute = $this->CategoryAttributes->newEntity([
                                    'attribute_id' => $attributeId,
                                    'attribute_value_id' => $valueId,
                                    'category_id' => $catId
                                ]);
                                $this->CategoryAttributes->save($categoryAttribute);
                            }
                        }
                    }
                }

                $this->Flash->success(__('The category has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The category could not be saved. Please, try again.'));
        }

        $parentCategories = $this->Categories->ParentCategories->find('list')->all();

        $categories = $this->Categories->find('treeList', keyPath: 'id', valuePath: 'name')
            ->where(['status' => 'A'])
            ->toArray();

        // $categories = $this->Categories->find('all')->where(['status' => 'A'])
        //     ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
        //     ->toArray();

        // $formattedCategories = $this->formatCategories($categories);

        $attributes = $this->Attributes->find('all')
            ->where(['key_name' => 'category'])
            ->toArray();

        $title = 'Category | Add';
        $this->set(compact('category', 'parentCategories', 'categories', 'attributes', 'title'));
    }

    function isArrayIndexEmpty($array)
    {
        return !isset($array[0]) || trim($array[0]) === '';
    }
    public function edit($id = null)
    {
        $this->set([
            'iconSize' => Configure::read('Constants.CATEGORY_ICON_SIZE'),
            'webBannerSize' => Configure::read('Constants.CATEGORY_WEB_BANNER_SIZE'),
            'mobileBannerSize' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_SIZE'),
            'iconType' => Configure::read('Constants.CATEGORY_ICON_JS_TYPE'),
            'webBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_JS_TYPE'),
            'mobileBannerType' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_JS_TYPE'),
            'iconMinWidth' => Configure::read('Constants.CATEGORY_ICON_MIN_WIDTH'),
            'iconMaxWidth' => Configure::read('Constants.CATEGORY_ICON_MAX_WIDTH'),
            'iconMinHeight' => Configure::read('Constants.CATEGORY_ICON_MIN_HEIGHT'),
            'iconMaxHeight' => Configure::read('Constants.CATEGORY_ICON_MAX_HEIGHT'),
            'webBannerMinWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_WIDTH'),
            'webBannerMaxWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_WIDTH'),
            'webBannerMinHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_HEIGHT'),
            'webBannerMaxHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_HEIGHT'),
            'mobileBannerMinWidth' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MIN_WIDTH'),
            'mobileBannerMaxWidth' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MAX_WIDTH'),
            'mobileBannerMinHeight' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MIN_HEIGHT'),
            'mobileBannerMaxHeight' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_MAX_HEIGHT'),
            'categoryIconType' => Configure::read('Constants.CATEGORY_ICON_TYPE'),
            'categoryWebBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_TYPE'),
            'categoryMobileBannerType' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_TYPE'),
        ]);

        $category = $this->Categories->get($id, [
            'contain' => [
                'CategoryAttributes' => function ($q) {
                    return $q->where(['CategoryAttributes.status' => 'A']);
                },
                'CategoryAttributes.Attributes.AttributeValues'
            ]
        ]);

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $attribute_name_id = isset($data['attribute_name_id']) ? $data['attribute_name_id'] : '';
            $attribute_source = isset($data['attribute_source']) ? $data['attribute_source'] : '';
            $attribute_names = isset($data['attribute_name']) ? $data['attribute_name'] : '';
            $attribute_values = isset($data['attribute_value']) ? $data['attribute_value'] : '';


            // echo "<pre>";
            // print_r($data);
            // die;
            $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Categories', $id);

            if (isset($data['category_icon']) && $data['category_icon']->getError() === UPLOAD_ERR_OK) {
                $category_icon = $data['category_icon'];
                $fileName = trim($category_icon->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $category_icon->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Category Icon could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['category_icon'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['category_icon'] = $category->category_icon;
            }

            if (isset($data['web_banner']) && $data['web_banner']->getError() === UPLOAD_ERR_OK) {
                $web_banner = $data['web_banner'];
                $fileName = trim($web_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $web_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Web banner could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['web_banner'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['web_banner'] = $category->web_banner;
            }

            if (isset($data['mobile_banner']) && $data['mobile_banner']->getError() === UPLOAD_ERR_OK) {
                $mobile_banner = $data['mobile_banner'];
                $fileName = trim($mobile_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $mobile_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Mobile banner could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['mobile_banner'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['mobile_banner'] = $category->mobile_banner;
            }

            $categorydata = $this->Categories->patchEntity($category, $data);
            $categorysave = $this->Categories->save($categorydata);
            if ($categorysave) {

                $catId = $categorysave->id;

                $CategoryAttributesTable = $this->CategoryAttributes;

                $CategoryAttributesTable->updateAll(
                    ['status' => 'D'],
                    ['category_id' => $catId]
                );

                foreach ($attribute_source as $index => $source) {
                    $name = trim($attribute_names[$index]);
                    $values = array_map('trim', explode(',', $attribute_values[$index]));
                    $attributeId = null;

                    if ($source === 'manual') {
                        $attribute = $this->Attributes->newEntity([
                            'name' => $name,
                            'key_name' => 'category',
                            'status' => 'A'
                        ]);
                        if ($this->Attributes->save($attribute)) {
                            $attributeId = $attribute->id;

                            foreach ($values as $value) {
                                $attributeValue = $this->AttributeValues->newEntity([
                                    'attribute_id' => $attributeId,
                                    'value' => $value,
                                    'status' => 'A'
                                ]);
                                if ($this->AttributeValues->save($attributeValue)) {
                                    $categoryAttribute = $this->CategoryAttributes->newEntity([
                                        'attribute_id' => $attributeId,
                                        'attribute_value_id' => $attributeValue->id,
                                        'category_id' => $catId
                                    ]);
                                    $this->CategoryAttributes->save($categoryAttribute);
                                }
                            }
                        }
                    } elseif ($source === 'datalist') {
                        $attributeId = $attribute_name_id[$index] ?? null;
                        if ($attributeId) {
                            foreach ($values as $value) {
                                // Check if value already exists for this attribute_id
                                $existingValue = $this->AttributeValues->find()
                                    ->where(['attribute_id' => $attributeId, 'value' => $value])
                                    ->first();

                                if (!$existingValue) {
                                    $newValue = $this->AttributeValues->newEntity([
                                        'attribute_id' => $attributeId,
                                        'value' => $value,
                                        'status' => 'A'
                                    ]);
                                    if ($this->AttributeValues->save($newValue)) {
                                        $valueId = $newValue->id;
                                    } else {
                                        continue;
                                    }
                                } else {
                                    $valueId = $existingValue->id;
                                }

                                $categoryAttribute = $this->CategoryAttributes->newEntity([
                                    'attribute_id' => $attributeId,
                                    'attribute_value_id' => $valueId,
                                    'category_id' => $catId
                                ]);
                                $this->CategoryAttributes->save($categoryAttribute);
                            }
                        }
                    }
                }

                $this->updateChildCategoriesStatus($category->id, $category->status);

                $this->Flash->success(__('The category has been updated.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The category could not be saved. Please, try again.'));
        }

        $aws_url = Configure::read('Settings.AWS_URL');

        $parentCategories = $this->Categories->ParentCategories->find('list')->all();

        $categories = $this->Categories->find('treeList', keyPath: 'id', valuePath: 'name')
            ->where(['status' => 'A'])
            ->toArray();

        $category_icon = $this->Media->getCloudFrontURL($category->category_icon);
        $web_banner = $this->Media->getCloudFrontURL($category->web_banner);
        $mobile_banner = $this->Media->getCloudFrontURL($category->mobile_banner);

        // echo "<pre>"; print_r($category); die;
        $attributes = $this->Attributes->find('all')
            ->where(['key_name' => 'category'])
            ->toArray();

        $title = 'Category | Edit';
        $this->set(compact('category', 'parentCategories', 'aws_url', 'categories', 'category_icon', 'web_banner', 'mobile_banner', 'attributes', 'title'));
    }

    private function updateChildCategoriesStatus($categoryId, $status)
    {



        $category = $this->Categories->get($categoryId);

        if ($status == 'D') {
            if ($category->brand_logo) {
                $this->Media->awsDelete($category->category_icon);
            }
            if ($category->web_banner) {
                $this->Media->awsDelete($category->web_banner);
            }
            if ($category->mobile_banner) {
                $this->Media->awsDelete($category->mobile_banner);
            }

            $category->category_icon = null;
            $category->web_banner = null;
            $category->mobile_banner = null;
        }

        $category->status = $status;
        $this->Categories->save($category);

        $childCategories = $this->Categories->find('all')
            ->where(['parent_id' => $categoryId])
            ->all();

        foreach ($childCategories as $child) {
            $child->status = $status;
            $this->Categories->save($child);
            $this->updateChildCategoriesStatus($child->id, $status);
        }
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The category could not be deleted. Please, try again.'];

        try {

            $activeProductsCount = $this->ProductCategories->find()
                ->matching('Products', function ($q) {
                    return $q->where(['Products.status' => 'A']);
                })
                ->where(['ProductCategories.category_id' => $id])
                ->count();

            if ($activeProductsCount > 0) {
                $response = ['success' => false, 'message' => "There are $activeProductsCount active products under this category. Please delete the products first."];
            } else {
                $this->updateChildCategoriesStatus($id, 'D');
                $response = ['success' => true, 'message' => 'The category has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function subcategories($categoryId = null)
    {

        $this->autoRender = false;

        $this->request->allowMethod(['get']);
        $subcategories = [];

        if ($categoryId) {

            $categories = $this->Categories
                ->find('children', ['for' => $categoryId])
                ->find('threaded')
                ->toArray();

            $dropdownData = $this->formatCategoriesForDropdown($categories);

            $result = ['status' => __('success'), 'data' => $dropdownData];
        } else {
            $result = ['status' => __('success'), 'data' => []];
        }

        $this->response->getBody()->write(json_encode($result));
        $this->response = $this->response->withType('json');
        return $this->response;
    }

    function formatCategoriesForDropdown($categories, $prefix = '')
    {
        $dropdown = [];

        foreach ($categories as $category) {
            $dropdown[$prefix . $category->name] = $category->id;

            if (!empty($category->children)) {
                $dropdown += $this->formatCategoriesForDropdown($category->children, $prefix . '-- ');
            }
        }

        return $dropdown;
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $categoryId = $this->request->getData('image_id');

        if (!$categoryId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $category = $this->Categories->get($categoryId);

        if (!$category) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Category not found']));
        }

        $existingImagePath = $category->category_icon;

        if ($existingImagePath) {
            $filePath = WWW_ROOT . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $category->category_icon = null;
            if ($this->Categories->save($category)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update category']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }
}
