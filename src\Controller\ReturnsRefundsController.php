<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ReturnsRefundsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $OrderReturns;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->OrderReturns = $this->fetchTable('OrderReturns');
    }

    public function index()
    {

        $orderRefunds = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => ['fields' => ['id', 'status']],
                'Orders' => [
                    'Customers' => [
                        'Users'
                    ]
                ]
            ])
            ->where(['OrderReturns.status IN' => ['Refund Pending', 'Refunded']])
            ->order(['OrderReturns.id' => 'DESC'])
            ->enableHydration(false)
            ->all();

        $orderReturns = $this->OrderReturns->find()
            // ->contain([
            //     'OrderItems' => ['fields' => ['id', 'status']],
            //     'Orders' => [
            //         'Customers' => [
            //             'Users'
            //         ]
            //     ]
            // ])
            ->contain([
                'OrderItems' => ['fields' => ['id', 'status']],
                'Orders' => [
                    'fields' => ['id', 'customer_id', 'order_date', 'status'],
                    'Customers' => [
                        'Users' => [
                            'fields' => ['id', 'first_name', 'last_name', 'email']
                        ]
                    ],
                    'Transactions' => [
                        'fields' => ['id', 'order_id', 'payment_method', 'payment_status']
                    ]
                ]
            ])
            ->order(['OrderReturns.id' => 'DESC']) // sort by latest request_id
            ->enableHydration(false) // optional: returns raw arrays
            ->all();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');

        $this->set(compact('orderReturns', 'orderRefunds', 'orderstatuses', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));


    }

}
