<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\SupportTicket> $supportTickets
 */

?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
      href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>">
<style>
    .expand-icon {
        cursor: pointer;
    }
    label {
        color: #6c757d !important;
    }
</style>
<?php $this->end(); ?>

<div class="section-header">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item active"><?= __('View Support Tickets') ?></li>
    </ul>
</div>

<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>

<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("View Support Tickets") ?></h4>
                <div class="card-header-form">
                <form class="d-flex align-items-center" method="get" action="<?= $this->Url->build(['controller' => 'SupportTickets', 'action' => 'supportTicketList']) ?>">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" name="search" placeholder="<?= __('Search Ticket ID, Order ID, Customer Name, Phone No') ?>" value="<?= $this->request->getQuery('search') ?>">
                        <div class="input-group-btn">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>

                        <button class="btn menu-toggle fw-bold" type="button" id="filterToggle">
                            <i class="fas fa-filter"></i>
                            <?= __('Filter') ?>
                        </button>

                        <?php if ($canAdd): ?>
                            <a href="/support-tickets/add" class="btn m-r-15">
                                <i class="fas fa-plus"></i>
                                <?= __("Add Support Ticket") ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </form>


                </div>

            </div>

            <div class="filters m-4" id="filterSection" style="display: none;">
                <form method="get" action="<?= $this->Url->build(['controller' => 'SupportTickets', 'action' => 'supportTicketList']) ?>">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <label class="fw-bold"><?= __('Priority :') ?></label>
                            <select class="form-select" id="priorityFilter" name="priority">
                                <option value=""><?= __('-- Priority --') ?></option>
                                <option value="Low" <?= $this->request->getQuery('priority') == 'Low' ? 'selected' : '' ?>><?= __('Low') ?></option>
                                <option value="Medium" <?= $this->request->getQuery('priority') == 'Medium' ? 'selected' : '' ?>><?= __('Medium') ?></option>
                                <option value="High" <?= $this->request->getQuery('priority') == 'High' ? 'selected' : '' ?>><?= __('High') ?></option>
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <label class="fw-bold"><?= __('Status :') ?></label>
                            <select class="form-select" id="statusFilter" name="status">
                                <option value=""><?= __('-- Status --') ?></option>
                                <option value="Open" <?= $this->request->getQuery('status') == 'Open' ? 'selected' : '' ?>><?= __('Open') ?></option>
                                <option value="In Progress" <?= $this->request->getQuery('status') == 'In Progress' ? 'selected' : '' ?>><?= __('In Progress') ?></option>
                                <option value="Resolved" <?= $this->request->getQuery('status') == 'Resolved' ? 'selected' : '' ?>><?= __('Resolved') ?></option>
                                <option value="Closed" <?= $this->request->getQuery('status') == 'Closed' ? 'selected' : '' ?>><?= __('Closed') ?></option>
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <label class="fw-bold"><?= __('Category :') ?></label>
                            <select class="form-select" id="categoryFilter" name="category">
                                <option value=""><?= __('-- Category --') ?></option>
                                <?php foreach ($allCategory as $k => $val): ?>
                                    <option value="<?= $val->id ?>" <?= $this->request->getQuery('category') == $val->id ? 'selected' : '' ?>><?= $val->name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <button class="btn btn-primary mt-4" type="submit"><?= __('Apply Filters') ?></button>
                        </div>
                    </div>
                </form>
            </div>



            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="supportCategoryTable">
                        <thead>
                        <tr>
                            <th><?= __('Ticket ID') ?></th>
                            <th><?= __('Order Number') ?></th>
                            <th><?= __('Customer Name') ?></th>
                            <th><?= __('Description') ?></th>
                            <th><?= __('Opened') ?></th>
                            <th><?= __('Last Replied On') ?></th>
                            <th><?= __('Category') ?></th>
                            <th><?= __('Owner') ?></th>
                            <th><?= __('Priority') ?></th>
                            <th><?= __('Status') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($supportTickets['tickets'] as $ticket): ?>
                            <tr>
                                <td><?= $ticket->ticketID ?></td>
                                <td>
                                    <?php 
                                        if(isset($ticket->order->order_number) && !empty($ticket->order->order_number)){
                                            echo h($ticket->order->order_number);
                                        } else {
                                            echo __('N/A');
                                        }
                                    ?></td>
                                <td>
                                 <?php 
                                     if(isset($ticket->customer['_matchingData']['Users']['first_name'])){
                                         echo h($ticket->customer['_matchingData']['Users']['first_name']) . ' ' . h($ticket->customer['_matchingData']['Users']['last_name']);
                                     } else {
                                         echo h($ticket->issue_name) . 'aaa';
                                         echo '<br /><small>' . h($ticket->phone_number) . '</small>';
                                     }
                                 ?>
                                   
                                 
                                 </td>
                                <td><?= h($ticket->description) ?></td>
                                <td><?= $ticket->created->format($dateFormat . ' ' . $timeFormat) ?></td>
                                <td>
                                    <?php 
                                        if(isset($ticket['support_ticket_updates'][0]['support_ticket_id'])){
                                            echo h($ticket['support_ticket_updates'][0]['updated_at']);
                                        }
                                    ?>
                                </td>
                                <td><?= h($ticket->support_category?->name) ?></td>
                                <td><?= h($ticket->created_by_user?->first_name) ?> &nbsp; <?= h($ticket->created_by_user?->last_name) ?></td>
                                <td>
                                    <?php
                                    $priority = h($ticket->priority);
                                    $badgeClass = '';

                                    switch ($priority) {
                                        case 'Low':
                                            $badgeClass = 'bg-success';
                                            break;
                                        case 'Medium':
                                            $badgeClass = 'bg-warning';
                                            break;
                                        case 'High':
                                            $badgeClass = 'bg-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $badgeClass; ?>"><?= $priority; ?></span>
                                </td>

                                <td><?= h($ticket->status) ?></td>
                                <td class="actions">
                                    <a href="<?= '/support-tickets/view/'.$ticket->id ?>"
                                       class="" data-toggle="tooltip" title="View">
                                        <i class="far fa-eye m-r-10"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>


                            <div class="row mt-3 mb-5 align-items-center">
                                <div class="col-md-6 text-md-start text-center mb-md-0 mb-4">
                                    <div class="dataTables_info" id="table-info" role="status" aria-live="polite">
                                        <?= __('Showing') ?> <?= $supportTickets['pagination']['current_page'] ?> <?= __('to') ?> <?= $supportTickets['pagination']['total_pages'] ?> <?= __('of') ?> <?= $supportTickets['pagination']['total_records'] ?> <?= __('entries') ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="dataTables_paginate paging_simple_numbers" id="table-paginate">
                                        <ul class="pagination justify-content-md-end justify-content-center">
                                        <li class="paginate_button page-item previous <?= ($supportTickets['pagination']['prev_page'] === null) ? 'disabled' : '' ?>" id="previous">
                                                <a href="<?= ($supportTickets['pagination']['prev_page'] !== null)
                                                    ? $this->Url->build([
                                                        'controller' => 'SupportTickets',
                                                        'action' => 'supportTicketList',
                                                        '?' => array_merge($this->request->getQuery(), ['page' => $supportTickets['pagination']['prev_page']])
                                                    ])
                                                    : '#' ?>" class="page-link"><?= __('Previous') ?></a>
                                            </li>

                                            <li class="paginate_button page-item active">
                                                <a href="#" class="page-link"><?= $supportTickets['pagination']['current_page'] ?></a>
                                            </li>

                                            <li class="paginate_button page-item next <?= ($supportTickets['pagination']['next_page'] === null) ? 'disabled' : '' ?>" id="next">
                                                <a href="<?= ($supportTickets['pagination']['next_page'] !== null)
                                                    ? $this->Url->build([
                                                        'controller' => 'SupportTickets',
                                                        'action' => 'supportTicketList',
                                                        '?' => array_merge($this->request->getQuery(), ['page' => $supportTickets['pagination']['next_page']])
                                                    ])
                                                    : '#' ?>" class="page-link"><?= __('Next') ?></a>
                                            </li>

                                        </ul>
                                    </div>
                                </div>
                            </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const priorityFilter = document.getElementById("priorityFilter");
        const statusFilter = document.getElementById("statusFilter");
        const categoryFilter = document.getElementById("categoryFilter");
        const filterToggle = document.getElementById("filterToggle");
        const filterSection = document.getElementById("filterSection");

        // Function to update URL without reloading
        function updateUrlParams() {
            const params = new URLSearchParams(window.location.search);

            if (priorityFilter.value) {
                params.set("priority", priorityFilter.value);
            } else {
                params.delete("priority");
            }

            if (statusFilter.value) {
                params.set("status", statusFilter.value);
            } else {
                params.delete("status");
            }

            if (categoryFilter.value) {
                params.set("category", categoryFilter.value);
            } else {
                params.delete("category");
            }

            // Update URL without refreshing the page
            window.history.replaceState({}, "", `${window.location.pathname}?${params.toString()}`);
        }

        // Event listeners for dropdown changes
        priorityFilter.addEventListener("change", updateUrlParams);
        statusFilter.addEventListener("change", updateUrlParams);
        categoryFilter.addEventListener("change", updateUrlParams);

        // Restore selected values from URL
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has("priority")) {
            priorityFilter.value = urlParams.get("priority");
        }
        if (urlParams.has("status")) {
            statusFilter.value = urlParams.get("status");
        }
        if (urlParams.has("category")) {
            categoryFilter.value = urlParams.get("category");
        }

        // Toggle filter visibility and remember state
        if (localStorage.getItem("filterVisible") === "true") {
            filterSection.style.display = "block";
        } else {
            filterSection.style.display = "none";
        }

        filterToggle.addEventListener("click", function () {
            if (filterSection.style.display === "none") {
                filterSection.style.display = "block";
                localStorage.setItem("filterVisible", "true");
            } else {
                filterSection.style.display = "none";
                localStorage.setItem("filterVisible", "false");
            }
        });
    });
</script>


<?php $this->end(); ?>
