<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Datasource\EntityInterface;
use Cake\I18n\FrozenTime;

/**
 * Orders Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\CustomerAddressesTable&\Cake\ORM\Association\BelongsTo $CustomerAddresses
 * @property \App\Model\Table\OffersTable&\Cake\ORM\Association\BelongsTo $Offers
 * @property \App\Model\Table\ShowroomsTable&\Cake\ORM\Association\BelongsTo $Showrooms
 * @property \App\Model\Table\OrderItemsTable&\Cake\ORM\Association\HasMany $OrderItems
 * @property \App\Model\Table\ReturnsTable&\Cake\ORM\Association\HasMany $Returns
 * @property \App\Model\Table\ShipmentsTable&\Cake\ORM\Association\HasMany $Shipments
 * @property \App\Model\Table\TransactionsTable&\Cake\ORM\Association\HasMany $Transactions
 *
 * @method \App\Model\Entity\Order newEmptyEntity()
 * @method \App\Model\Entity\Order newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Order> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Order get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Order findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Order patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Order> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Order|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Order saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class OrdersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('orders');
        $this->setDisplayField('order_number');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        //        $this->hasMany('SupportTickets', [
        //            'foreignKey' => 'order_id',
        //        ]);
        $this->hasMany('SupportTickets', [
            'foreignKey' => 'order_id',
            'dependent' => true,
        ]);
        $this->belongsTo('CustomerAddresses', [
            'foreignKey' => 'customer_address_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('Offers', [
            'foreignKey' => 'offer_id',
        ]);
        $this->belongsTo('Showrooms', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->hasMany('OrderItems', [
            'foreignKey' => 'order_id',
            'dependent' => true,
            'cascadeCallbacks' => true,
        ]);

        $this->OrderItems->hasMany('OrderReturns', [
            'foreignKey' => 'order_item_id',
            'dependent' => true,
        ]);

        $this->OrderItems->hasMany('OrderCancellations', [
            'foreignKey' => 'order_item_id',
            'dependent' => true,
        ]);

        $this->hasMany('Returns', [
            'foreignKey' => 'order_id',
        ]);
        $this->hasMany('Shipments', [
            'foreignKey' => 'order_id',
        ]);
        $this->hasMany('Transactions', [
            'foreignKey' => 'order_id'
        ]);

        $this->belongsTo('SalesPersons', [
            'className' => 'Users',
            'foreignKey' => 'sales_person_id',
            'joinType' => 'LEFT',
        ]);

        $this->belongsTo('CreatedByRoles', [
            'className' => 'Roles',
            'foreignKey' => 'created_by_role'
        ]);

        $this->belongsTo('CreatedBy', [
            'className' => 'Users',
            'foreignKey' => 'created_by'
        ]);

        $this->hasMany('OrderTrackingHistories', [
            'foreignKey' => 'order_id'
        ]);

        $this->belongsTo('CreditApplications', [
            'foreignKey' => 'credit_application_id',
            'joinType' => 'LEFT',
        ]);

        $this->belongsTo('CartCreditPayments', [
            'foreignKey' => 'cart_credit_payment_id',
            'joinType' => 'LEFT',
        ]);

        $this->hasMany('ShipmentOrders', [
            'foreignKey' => 'order_id',
            'joinType' => 'LEFT',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->nonNegativeInteger('customer_address_id')
            ->allowEmptyString('customer_address_id');

        $validator
            ->nonNegativeInteger('offer_id')
            ->allowEmptyString('offer_id');

        $validator
            ->scalar('order_number')
            ->maxLength('order_number', 255)
            ->requirePresence('order_number', 'create')
            ->notEmptyString('order_number');

        $validator
            ->dateTime('order_date')
            ->requirePresence('order_date', 'create')
            ->notEmptyDateTime('order_date');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->scalar('payment_method')
            ->maxLength('payment_method', 255)
            ->requirePresence('payment_method', 'create')
            ->notEmptyString('payment_method');

        $validator
            ->scalar('shipping_method')
            ->maxLength('shipping_method', 255);
        // ->requirePresence('shipping_method', 'create')
        // ->notEmptyString('shipping_method');

        $validator
            ->decimal('subtotal_amount')
            ->requirePresence('subtotal_amount', 'create')
            ->notEmptyString('subtotal_amount');

        $validator
            ->decimal('delivery_charge')
            ->notEmptyString('delivery_charge');

        $validator
            ->decimal('discount_amount')
            ->notEmptyString('discount_amount');

        $validator
            ->decimal('offer_amount')
            ->allowEmptyString('offer_amount');

        $validator
            ->decimal('loyalty_points_redeemed')
            ->allowEmptyString('loyalty_points_redeemed');

        $validator
            ->decimal('loyalty_amount')
            ->allowEmptyString('loyalty_amount');

        $validator
            ->decimal('total_amount')
            ->requirePresence('total_amount', 'create')
            ->notEmptyString('total_amount');

        $validator
            ->scalar('delivery_mode')
            ->requirePresence('delivery_mode', 'create')
            ->notEmptyString('delivery_mode');

        $validator
            ->scalar('delivery_mode_type')
            ->allowEmptyString('delivery_mode_type');

        $validator
            ->nonNegativeInteger('showroom_id')
            ->allowEmptyString('showroom_id');

        $validator
            ->integer('sales_person_id')
            ->allowEmptyString('sales_person_id');
        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);
        $rules->add($rules->existsIn(['customer_address_id'], 'CustomerAddresses'), ['errorField' => 'customer_address_id']);
        $rules->add($rules->existsIn(['offer_id'], 'Offers'), ['errorField' => 'offer_id']);
        $rules->add($rules->existsIn(['showroom_id'], 'Showrooms'), ['errorField' => 'showroom_id']);

        return $rules;
    }

    public function getMyOrder($customerId)
    {
        $query = $this->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'Orders.order_date',
                'Orders.total_amount',
                'Orders.status',
                'Orders.payment_method',
                'Orders.delivery_mode',
            ])
            ->where(['Orders.customer_id' => $customerId])
            ->order(['Orders.order_date' => 'DESC'])->all();
        return $query;
    }

    public function generateUniqueOrderNum($PrefixSource = '')
    {
        $orderNum = $this->generateRandomNumber(10);

        while ($this->exists(['order_number' => $orderNum])) {
            $orderNum = $this->generateRandomNumber(10);
        }
        if ($PrefixSource == 'Supervisor') {
            $orderNum = 'SA-' . $orderNum;
        } else if ($PrefixSource == 'Customer') {
            $orderNum = 'CA-' . $orderNum;
        } else if ($PrefixSource == 'Website') {
            $orderNum = 'WS-' . $orderNum;
        } else if ($PrefixSource == 'Admin') {
            $orderNum = 'AP-' . $orderNum;
        }

        return $orderNum;
    }

    private function generateRandomNumber($length)
    {
        $number = '';
        for ($i = 0; $i < $length; $i++) {
            $number .= mt_rand(0, 9);
        }
        return $number;
    }

    public function getOrderDetails($user_id = '', $Role = '')
    {
        $query = $this->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'full_name' => $this->Customers->Users->find()->func()->concat([
                    "Users.first_name" => 'identifier',
                    ' ',
                    "Users.last_name" => 'identifier',
                ]),
                'phone_number' => $this->Customers->Users->find()->func()->concat([
                    "Users.country_code" => 'identifier',
                    ' ',
                    "Users.mobile_no" => 'identifier',
                ]),
                'Orders.total_amount',
                'Orders.status',
                'Orders.payment_method',
                'Orders.order_date',
                'quantity' => $this->OrderItems->find()->func()->sum('OrderItems.quantity'),
                'transaction_status' => 'Transactions.payment_status',
                'Orders.delivery_mode',
                'Orders.showroom_id',
            ])
            ->innerJoinWith('Customers.Users')
            ->leftJoinWith('OrderItems')
            ->leftJoinWith('Transactions')
            ->leftJoinWith('Showrooms')
            ->group(['Orders.id'])
            ->order(['Orders.id' => 'DESC']);

        if (!empty($user_id) && $Role == 'Showroom_Manager') {
            $query->where(['Showrooms.showroom_manager' => $user_id]);
        }
        if (!empty($user_id) && $Role == 'Showroom_Supervisor') {
            $query->where(['Showrooms.showroom_supervisor' => $user_id]);
        }

        if (!empty($user_id) && $Role == 'SalesPerson') {
            $query->where(['Orders.sales_person_id' => $user_id]);
        }

        return $query;
    }


    public function delete(EntityInterface $order, array $options = []): bool
    {
        $order->status = 'Cancelled';
        if ($this->save($order)) {
            return true;
        }
        return false;
    }

    public function delete_order(EntityInterface $order, array $options = []): bool
    {
        $order->status = 'Deleted';
        $order->status_date = date('Y-m-d');
        $order->last_modified_by = $options['last_modified_by'];

        if ($this->save($order)) {
            return true;
        }
        return false;
    }

    //M
    public function add_record($attributes)
    {

        $new = $this->newEmptyEntity();
        foreach ($attributes as $key => $value) {
            $new->$key = $value;
        }

        if ($this->save($new)) {
            return $new->id;
        } else {
            return false;
        }
    }

    //M
    public function update_record($id, $attributes)
    {
        $old_attr = $this->get($id);
        $old_attr = $this->patchEntity($old_attr, $attributes);
        if ($this->save($old_attr)) {
            return $old_attr;
        } else {
            return false;
        }
    }


    //S
    public function getCurrentYearOrders($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $totalOrdersQuery = $this->find()
            ->select(['total_orders' => $this->find()->func()->count('*')])
            ->where(function ($exp) use ($currentYear, $showroomId) {
                return $exp->eq('YEAR(order_date)', $currentYear)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalOrders = $totalOrdersQuery ? $totalOrdersQuery->total_orders : 0;
    }

    //S
    public function getCurrentMonthOrders($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $currentMonth = FrozenTime::now()->month;
        $totalOrdersQuery = $this->find()
            ->select(['total_orders' => $this->find()->func()->count('*')])
            ->where(function ($exp) use ($currentYear, $currentMonth, $showroomId) {
                return $exp
                    ->eq('YEAR(order_date)', $currentYear)
                    ->eq('MONTH(order_date)', $currentMonth)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalOrders = $totalOrdersQuery ? $totalOrdersQuery->total_orders : 0;
    }

    //S
    public function getCurrentDayOrders($showroomId)
    {
        $today = FrozenTime::now()->format('Y-m-d');
        $totalOrdersQuery = $this->find()
            ->select(['total_orders' => $this->find()->func()->count('*')])
            ->where(function ($exp) use ($today, $showroomId) {
                return $exp->eq('DATE(order_date)', $today)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalOrders = $totalOrdersQuery ? $totalOrdersQuery->total_orders : 0;
    }

    //S
    public function getCurrentYearSales($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $totalSalesQuery = $this->find()
            ->select(['total_sales' => $this->find()->func()->sum('total_amount')])
            ->where(function ($exp) use ($currentYear, $showroomId) {
                return $exp->eq('YEAR(order_date)', $currentYear)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalSales = $totalSalesQuery->total_sales ? $totalSalesQuery->total_sales : 0;
    }

    //S
    public function getCurrentMonthSales($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $currentMonth = FrozenTime::now()->month;
        $totalSalesQuery = $this->find()
            ->select(['total_sales' => $this->find()->func()->sum('total_amount')])
            ->where(function ($exp) use ($currentYear, $currentMonth, $showroomId) {
                return $exp
                    ->eq('YEAR(order_date)', $currentYear)
                    ->eq('MONTH(order_date)', $currentMonth)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalSales = $totalSalesQuery->total_sales ? $totalSalesQuery->total_sales : 0;
    }

    //S
    public function getCurrentDaySales($showroomId)
    {
        $today = FrozenTime::now()->format('Y-m-d');
        $totalSalesQuery = $this->find()
            ->select(['total_sales' => $this->find()->func()->sum('total_amount')])
            ->where(function ($exp) use ($today, $showroomId) {
                return $exp->eq('DATE(order_date)', $today)
                    ->notIn('status', ['Cancelled', 'Returned', 'Deleted'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalSales = $totalSalesQuery->total_sales ? $totalSalesQuery->total_sales : 0;
    }

    //S
    public function getRecentOrders($showroomId)
    {
        return $this->find()
            ->select(['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.order_date', 'Orders.status', 'Customers.id', 'Users.id', 'Users.first_name', 'Users.last_name'])
            ->contain([
                'Customers' => [
                    'Users' => [
                        'fields' => ['Users.id', 'Users.first_name', 'Users.last_name']
                    ]
                ]
            ])
            ->where(['Orders.showroom_id' => $showroomId, 'Orders.status' => 'Pending'])
            ->order(['Orders.order_date' => 'DESC'])
            ->all();
    }

    //S
    public function listOrderByShowroom($showroom, $filter_order_status, $filter_payment_status, $filter_sdate, $time_period, $startDate, $endDate, $search_str, $page, $limit)
    {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'Orders.order_date',
                'Orders.total_amount',
                'Orders.status',
                /* 'quantity' => $this->OrderItems->find()->func()->sum('OrderItems.quantity'),*/
                'Showrooms.id',
                'Showrooms.name',
                'customer_name' => $this->Customers->Users->find()->func()->concat([
                    "Users.first_name" => 'identifier',
                    ' ',
                    "Users.last_name" => 'identifier',
                ]),
                'payment_method' => 'Transactions.payment_method',
                'payment_status' => 'Transactions.payment_status'
            ])
            ->contain([
                'OrderItems' => function ($q) {
                    return $q->select([
                        'order_id',
                        'total_quantity' => $q->func()->sum('OrderItems.quantity')
                    ])
                        ->group('OrderItems.order_id'); // Group by order_id
                },
                'Customers' => [
                    'Users' => function ($q1) {
                        return $q1->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                    }
                ]
            ])
            ->join([
                'Showrooms' => [
                    'table' => 'showrooms',
                    'type' => 'INNER',
                    'conditions' => 'Showrooms.id = Orders.showroom_id'
                ]
            ])
            ->join([
                'Transactions' => [
                    'table' => 'transactions',
                    'type' => 'INNER',
                    'conditions' => 'Transactions.order_id = Orders.id'
                ]
            ])
            /*->join(['OrderItems' => [
                'table' => 'order_items',
                'type' => 'INNER',
                'conditions' => 'OrderItems.order_id = Orders.id'
                ]
            ])*/
            ->where([
                'Orders.showroom_id IN' => $showroom
                /*'OR' => [
                    'Showrooms.showroom_manager' => $userId,
                    'Showrooms.showroom_supervisor' => $userId
                ]*/
            ])
            ->order(['Orders.created' => 'DESC'])
            ->page($page, $limit); // Pagination here

        // Apply filters if present
        if (!empty($filter_order_status)) {
            $ordersQuery->where(['Orders.status' => $filter_order_status]);
        }

        if (!empty($filter_payment_status)) {
            $ordersQuery->where(['Transactions.payment_status' => $filter_payment_status]);
        }

        if (!empty($search_str)) {
            $ordersQuery->where(['Orders.order_number LIKE' => '%' . $search_str . '%']);
        }

        // Filter by month
        /* if (!empty($filter_month)) {
            $ordersQuery->where(function ($exp) use ($filter_month) {
                $startOfMonth = $filter_month . '-01 00:00:00';
                $endOfMonth = date('Y-m-t 23:59:59', strtotime($startOfMonth));
                return $exp->between('Orders.order_date', $startOfMonth, $endOfMonth);
            });
        }*/

        // Filter by custom date range
        /*if (!empty($filter_sdate) && !empty($filter_edate)) {
            $ordersQuery->where(function ($exp) use ($filter_sdate, $filter_edate) {
                $start = $filter_sdate . ' 00:00:00';
                $end = $filter_edate . ' 23:59:59';
                return $exp->between('Orders.order_date', $start, $end);
            });
        }*/

        //Filter by date
        if (!empty($filter_sdate)) {
            $ordersQuery->where(['DATE(Orders.order_date)' => $filter_sdate]);
        }

        //Filter by time period
        if (!empty($time_period)) {
            $ordersQuery->where([
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
            ]);
        }

        // Execute the query
        $orders = $ordersQuery->all();
        return $orders->toArray();
    }

    //S
    public function orderDetail($orderId)
    {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([])
            ->leftJoinWith('Showrooms') // Ensures we still fetch data if showroom_id is NULL
            ->leftJoinWith('CustomerAddresses')
            ->contain([
                'OrderItems' => [
                    'Products' => ['fields' => ['id', 'name', 'reference_name']],
                    'ProductVariants',
                    'OrderTrackingHistories' => [
                        'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                    ]
                ],
                'Customers' => [
                    'fields' => ['id', 'profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']],
                    /* 'CustomerAddresses' => [
                        'fields' => ['customer_id', 'city_id', 'name', 'type', 'address_line1', 'address_line2', 'house_no', 'landmark', 'zipcode', 'phone_no1', 'phone_no2'],
                        'Cities' => ['fields' => ['id', 'city_name']],
                        'Municipalities' => ['fields' => ['id', 'name']]
                    ]*/
                ],
                /*'CustomerAddresses' => [
                    'fields' => ['city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode','phone_no1', 'phone_no2'],
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ],*/
                'CustomerAddresses' => function ($q2) {
                    return $q2->select([
                        'CustomerAddresses.id',
                        'CustomerAddresses.city_id',
                        'CustomerAddresses.name',
                        'CustomerAddresses.type',
                        'CustomerAddresses.address_line1',
                        'CustomerAddresses.address_line2',
                        'CustomerAddresses.house_no',
                        'CustomerAddresses.landmark',
                        'CustomerAddresses.zipcode',
                        'CustomerAddresses.phone_no1',
                        'CustomerAddresses.phone_no2'
                    ])
                        ->contain([
                            'Cities' => ['fields' => ['id', 'city_name']],
                            'Municipalities' => ['fields' => ['id', 'name']]
                        ]);
                },
                'Transactions',
                'Offers' => ['fields' => ['offer_name', 'offer_code', 'offer_type', 'discount']],
                /*'Showrooms' => [
                    'fields' => ['city_id', 'name', 'address', 'area_sq_mts', 'image', 'email', 'contact_country_code', 'contact_number', 'showroom_timing'],
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'ShowroomManager' => ['fields' => ['first_name', 'last_name']],
                    'ShowroomSupervisor' => ['fields' => ['first_name', 'last_name']],
                ],*/
                'Showrooms' => function ($q1) {
                    return $q1->select([
                        'Showrooms.id',
                        'Showrooms.city_id',
                        'Showrooms.name',
                        'Showrooms.address',
                        'Showrooms.area_sq_mts',
                        'Showrooms.image',
                        'Showrooms.email',
                        'Showrooms.contact_country_code',
                        'Showrooms.contact_number',
                        'Showrooms.showroom_timing'
                    ])
                        ->contain([
                            'Cities' => ['fields' => ['id', 'city_name']],
                            'ShowroomManager' => ['fields' => ['first_name', 'last_name']],
                            'ShowroomSupervisor' => ['fields' => ['first_name', 'last_name']]
                        ]);
                },
                'OrderTrackingHistories'
            ])
            ->where(['Orders.id' => $orderId])
            ->first();

        return $ordersQuery;
    }

    //S
    public function changeStatus($order_id, $data)
    {
        $order_entity = $this->get($order_id);
        $statusupdate['status'] = $data['status'];
        $statusupdate['last_modified_by'] = $data['last_modified_by'];
        $order_entity = $this->patchEntity($order_entity, $statusupdate);
        $res = $this->save($order_entity);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }

    public function orderListByUser($customerId, $page, $limit)
    {
        $ordersQuery = $this->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'Orders.order_date',
                'Orders.total_amount',
                'Orders.status',
                'payment_method' => 'Transactions.payment_method',
                'payment_status' => 'Transactions.payment_status'
            ])
            ->contain([
                'OrderItems' => [
                    'OrderReturns' => function ($q) {
                        return $q->select(['id', 'order_item_id', 'reason', 'status']);
                    },
                    'OrderCancellations' => function ($q) {
                        return $q->select(['id', 'order_id', 'order_item_id', 'reason', 'status']);
                    },
                    'Products' => ['fields' => ['id', 'name', 'sales_price', 'promotion_price', 'url_key']],
                    'ProductVariants',
                    'OrderTrackingHistories' => [
                        'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                    ]
                ],
                'CustomerAddresses' => [
                    'fields' => [
                        'id',
                        'customer_id',
                        'name',
                        'address_line1',
                        'address_line2',
                        'city_id',
                        'zipcode'
                    ],
                ],
                'Showrooms' => function ($q1) {
                    return $q1->select([
                        'Showrooms.id',
                        'Showrooms.city_id',
                        'Showrooms.name',
                        'Showrooms.address',
                        'Showrooms.area_sq_mts',
                        'Showrooms.image',
                        'Showrooms.email',
                        'Showrooms.contact_country_code',
                        'Showrooms.contact_number',
                        'Showrooms.showroom_timing'
                    ])
                        ->contain([
                            'ShowroomManager' => ['fields' => ['first_name', 'last_name']],
                            'ShowroomSupervisor' => ['fields' => ['first_name', 'last_name']]
                        ]);
                },
                'Offers' => ['fields' => ['offer_name', 'offer_code']],
                'Customers' => [
                    'fields' => ['id', 'profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'mobile_no']],
                    'CustomerAddresses' => [
                        'fields' => ['customer_id', 'city_id', 'name', 'type', 'address_line1', 'address_line2', 'house_no', 'landmark', 'zipcode', 'phone_no1', 'phone_no2'],
                        'Cities' => ['fields' => ['id', 'city_name']],
                        'Municipalities' => ['fields' => ['id', 'name']]
                    ]
                ]
            ])
            ->join([
                'Transactions' => [
                    'table' => 'transactions',
                    'type' => 'INNER',
                    'conditions' => 'Transactions.order_id = Orders.id'
                ]
            ])
            ->where([
                'Orders.customer_id' => $customerId
            ])
            ->order(['Orders.created' => 'DESC'])
            ->page($page, $limit);

        $orders = $ordersQuery->all();
        return $orders->toArray();
    }

    //S
    public function salesPersonOrder($showroomId)
    {
        $today_date = date('Y-m-d');
        return $this->find()
            ->select(['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.order_date', 'Orders.status', 'Orders.sales_person_id', 'Orders.sales_person_bonus',/* 'Customers.id',*/ 'SalesPersons.id', 'SalesPersons.first_name', 'SalesPersons.last_name', 'CreatedBy.id', 'CreatedBy.first_name',  'CreatedBy.last_name', 'CreatedByRoles.name'])
            ->contain([
                'SalesPersons',
                'CreatedBy',
                'CreatedByRoles'
                /*'Customers' => [
                    'Users' => [
                        'fields' => ['Users.id', 'Users.first_name', 'Users.last_name']
                    ]
                ]*/
            ])
            ->where(['Orders.showroom_id' => $showroomId, 'DATE(Orders.order_date)' => $today_date, 'Orders.sales_person_id IS NOT' => NULL, 'Orders.status NOT IN' => ['Deleted', 'Cancelled']])
            ->order(['Orders.order_date' => 'DESC'])
            ->all();
    }

    //S
    public function salesOrder($showroomId, $all_roles)
    {
        $today_date = date('Y-m-d');
        return $this->find()
            ->select(['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.order_date', 'Orders.status', 'Orders.sales_person_id', 'Orders.sales_person_bonus',/* 'Customers.id',*/ 'SalesPersons.id', 'SalesPersons.first_name', 'SalesPersons.last_name', 'CreatedBy.id', 'CreatedBy.first_name',  'CreatedBy.last_name', 'CreatedByRoles.name'])
            ->contain([
                'SalesPersons',
                'CreatedBy',
                'CreatedByRoles'
                /*'Customers' => [
                    'Users' => [
                        'fields' => ['Users.id', 'Users.first_name', 'Users.last_name']
                    ]
                ]*/
            ])
            ->where(['Orders.showroom_id' => $showroomId, 'DATE(Orders.order_date)' => $today_date, 'Orders.created_by_role IN' => $all_roles])
            ->order(['Orders.order_date' => 'DESC'])
            ->all();
    }

    //S
    public function getManagerSales($showroomId, $userId)
    {
        $today = FrozenTime::now()->format('Y-m-d');
        $totalSalesQuery = $this->find()
            ->select(['total_sales' => $this->find()->func()->sum('total_amount')])
            ->where(function ($exp) use ($today, $showroomId, $userId) {
                return $exp->eq('DATE(order_date)', $today)
                    ->notIn('status', ['Cancelled', 'Returned', 'Deleted'])
                    ->eq('showroom_id', $showroomId)
                    ->isNull('sales_person_id')
                    ->eq('created_by', $userId);
            })
            ->first();
        return $totalSales = $totalSalesQuery->total_sales ? $totalSalesQuery->total_sales : 0;
    }

    public function WalletOrderTransactions($customerId){
        // Deprecated: use WalletOrderTransactionsPaginated for new code
        return $this->find()
            ->select(['Orders.wallet_payment_type','Orders.wallet_redeem_amount','Orders.id', 'Orders.order_number', 'Orders.order_date', 'Orders.total_amount', 'Orders.status'])
            // ->contain(['Transactions'])
            ->where(['Orders.customer_id' => $customerId, 'Orders.wallet_redeem_amount >' => 0])
            ->order(['Orders.order_date' => 'DESC'])
            ->all();
    }

    // New: Paginated and filterable wallet transactions
    public function WalletOrderTransactionsPaginated($customerId, $filter = 'all', $page = 1, $limit = 10)
    {
        $query = $this->find()
            ->select([
                'Orders.wallet_payment_type',
                'Orders.wallet_redeem_amount',
                'Orders.id',
                'Orders.order_number',
                'Orders.order_date',
                'Orders.total_amount',
                'Orders.status',
                //'transaction_type' => 'Transactions.type'
            ])
            // ->contain(['Transactions'])
            ->where(['Orders.customer_id' => $customerId, 'Orders.wallet_redeem_amount >' => 0])
            ->order(['Orders.order_date' => 'DESC']);

        // Apply filter if specified
        if ($filter === 'credit') {
            $query->where(['Orders.wallet_payment_type' => 'credit']);
        } elseif ($filter === 'debit') {
            $query->where(['Orders.wallet_payment_type' => 'debit']);
        }

        $query->page($page, $limit);

        // Return as array for easy JSON encoding
        return $query->toArray();
    }
}
