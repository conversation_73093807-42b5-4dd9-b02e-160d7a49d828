<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    .select2-container--default .select2-selection--single.is-invalid {
        border-color: #dc3545 !important;
        border-width: 1px;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    .error-msg {
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 4px;
        display: block;
    }

    #toRemovePadding {
        margin-bottom: 0px !important;
        padding-bottom: 0px !important;
    }

    #remove-border {
        border: none;
        background-color: transparent;
    }

    .modal-backdrop {
        background-color : transparent !important;
        position: relative !important;
    }

    .admin_btn:hover{
        background-color: #f77f00 !important;
        color: white !important;
    }

    .admin_btn
    {
        color: white !important;
        background-color: #0d839b !important;
    }

    label {
    width: 115px !important;
    }

    #filter-body-refund-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #filter-body-refund-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #filter-body-refund-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #filter-body-refund-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #filter-body-refund-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #filter-body-refund-container .input-group .btn {
        box-shadow: unset !important;
    }

    #filter-body-refund-container .btn:focus,
    #filter-body-refund-container .btn:hover,
    #filter-body-refund-container .btn.active {
        background-color: #f77f00 !important;
    }

    #filter-body-return-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #filter-body-return-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #filter-body-return-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #filter-body-return-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #filter-body-return-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #filter-body-return-container .input-group .btn {
        box-shadow: unset !important;
    }

    #filter-body-return-container .btn:focus,
    #filter-body-return-container .btn:hover,
    #filter-body-return-container .btn.active {
        background-color: #f77f00 !important;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div
        class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item active">
                <?= __('Returns and Refunds') ?>
            </li>
        </ul>
    </div>
    <div class="section-body1">
        <div class="container-fluid">
            <?= $this->Flash->render() ?>
        </div>
    </div>
    <div class="card" id="toRemovePadding">

            <div class="form-group row">
                <label for="supplier_name" class="col-sm-2 col-form-label fw-bold"><?= __("Showroom") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php if (strtolower($role->name) === 'admin' || strtolower($role->name) == 'showroom supervisor'): ?>
                        <!-- Dropdown for admin/supervisor -->
                        <select class="form-control form-select select2" id="cashDeskShowroomSelect" name="showroom_id">
                            <option value=""><?= __('Select Showroom') ?></option>
                            <?php foreach ($showrooms as $showroom): ?>
                                <option value="<?= $showroom->id; ?>"><?= h($showroom->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                    <?php else: ?>
                        <?php 
                            $userShowroom = null;
                            foreach ($showrooms as $showroom) {
                                if ($showroom->showroom_manager == $requested_user->id) {
                                    $userShowroom = $showroom;
                                    break;
                                }
                            }
                        ?>
                        <?php if ($userShowroom): ?>
                            <input type="text" class="form-control" name="showroom_id_display" value="<?= h($userShowroom->name); ?>" readonly>
                            <input type="hidden" id="cashDeskShowroomSelect" name="showroom_id" value="<?= $userShowroom->id; ?>">
                        <?php else: ?>
                            <span class="text-danger"><?= __('No showroom assigned to you.') ?></span>
                        <?php endif; ?>
                    <?php endif; ?>
                    <span style="display:none;color: #dc3545;" id="select_showroom_error"><?= __('Please select showroom') ?></span>
                </div>
            </div>

        <!-- Button to trigger the AJAX -->
        <div class="col-sm-3 mt-3">
            <button type="button" class="btn" id="loadCashDeskBtn">
                <?= __('Load Cash Desk') ?>
            </button>
        </div>

        <div id="cashDeskSummary" class="mt-4" style="display: none;">
            <div class="card p-3 bg-light">
                <p><strong><?= __('Available Cash') ?>:</strong> <span id="availableCash" class="text-success"></span></p>
                <p><strong><?= __('Last Cash Desk Closed At') ?>:</strong> <span id="lastCashDeskClose" class="text-primary"></span></p>

                <button type="button" class="btn btn-danger mt-2" id="cashDeskCloseBtn">
                    <?= __('Close Cash Desk') ?>
                </button>
            </div>
        </div>


        <!-- Area to render result -->
        <div id="cashDeskResult" class="mt-4"></div>
            </div>

    

    <div class="card" id="toRemovePadding">
            <div class="card-header p-0">
                    <h4><?= __('Supplier Payments') ?></h4>
                    <div class="card-header-form">
                            <div class="input-group">
                                <input
                                    type="text"
                                    class="form-control search-control"
                                    placeholder="Search"
                                    id="customSearchBoxReturnCancellation"
                                />
                                <div class="input-group-btn">
                                    <button class="btn">
                                        <i
                                            class="fas fa-search"
                                        ></i>
                                    </button>
                                </div>
                                <?php if ($canAdd): ?>
                                    <a class="btn view-add-supplier-payment-modal btn-primary admin_btn m-r-15">
                                        <i class="fas fa-plus"></i>
                                        <?= __('Add Supplier Payment') ?>
                                    </a>
                                <?php endif; ?>
                                <button
                                    class="btn return-menu-toggle fw-bold"
                                    type="submit"
                                    style="height:31px"
                                >
                                    <i
                                        class="fas fa-filter"
                                    ></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                    </div>
                </div>
                <div id="filter-body-return-container">
                    <div
                        class="input-group m-l-10"
                    >
                        <form
                            method="get"
                            accept-charset="utf-8"
                            class="form-inline filter-rating attribute d-flex"
                            id="filter-search"
                            action=""
                        >

                            <div class="form-group m-r-20">
                                <label class="m-r-5" for="status" style="width: max-content;"><?= __('Status:') ?></label>
                                <select name="payment_status" id="filterSupplierPaymentStatus" class="form-control form-select">
                                    <option value=""><?= __("Filter By Payment Status") ?></option>
                                    <option value="Paid"><?= __("Paid") ?>
                                    </option>
                                    <option value="Partially Paid"><?= __("Partially Paid") ?>
                                    </option>
                                    <option value="Pending"><?= __("Pending") ?>
                                    </option>
                                </select>
                            </div>

                            <div class="form-group d-flex m-t-10">
                                <button class="btn btn-primary supplier_payment_filter" id="return_filter">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                                </button>
                                <button type="reset" class="btn btn-primary reset_supplier_payment_filter"><i
                                        class="fas fa-redo-alt"></i></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        <div class="section-body">
            <div class="container-fluid">
                <div class="card-body">
                    <div class="table-responsive">
                        <div
                            id="table-3_wrapper"
                            class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer"
                        >
                            <table
                                class="table table-striped dataTable no-footer"
                                id="supplierPaymentTable"
                                role="grid"
                                aria-describedby="table-3_info"
                            >
                                <thead>
                                    <tr role="row">
                                        <th
                                        class="sorting_asc"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        aria-sort="ascending"
                                        aria-label="Request ID: activate to sort column descending"
                                        style="width: 100px"
                                    >
                                        <?= __('Supplier') ?></th>   
                                        <th
                                        class="sorting_asc"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        aria-sort="ascending"
                                        aria-label="Request ID: activate to sort column descending"
                                        style="width: 100px"
                                    >
                                        <?= __('Showroom') ?></th>             
                                        <th
                                            class="sorting_asc"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            aria-sort="ascending"
                                            style="width: 100px"
                                        >
                                            <?= __('Bill No.') ?>
                                        </th>
                                        <th
                                            class="sorting_asc"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            aria-sort="ascending"
                                            style="width: 100px"
                                        >
                                            <?= __('Payment Date') ?>
                                        </th>
                                        <th
                                            class="sorting_asc"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            aria-sort="ascending"
                                            style="width: 100px"
                                        >
                                            <?= __('Amount') ?>
                                        </th>
                                        <th
                                            class="sorting"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            style="width: 200px"
                                        >
                                            <?= __('Due Date') ?>
                                        </th>
                                        <th
                                            class="sorting"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            style="width: 200px"
                                        >
                                            <?= __('Payment Status') ?>
                                        </th>
                                        <th
                                            class="sorting_disabled"
                                            rowspan="1"
                                            colspan="1"
                                            aria-label="Actions"
                                            style="width: 150px"
                                        >
                                            <?= __('Actions') ?>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($supplier_payment as $payment): ?>
                                    <tr>
                                        <td><?php echo !empty($payment->supplier->name) ? h($payment->supplier->name) : 'N/A'; ?></td>

                                        <td><?php echo !empty($payment->showroom->name) ? h($payment->showroom->name) : 'N/A'; ?></td>

                                        <td><?php echo !empty($payment->supplier_purchase_order->bill_no) ? h($payment->supplier_purchase_order->bill_no) : 'N/A'; ?></td>

                                        <td><?php echo !empty($payment->payment_date) ? $payment->payment_date->format($dateFormat . ' ' . $timeFormat) : 'N/A'; ?></td>

                                        <td><?php echo !empty($payment->amount) ? h(number_format($payment->amount, 0, '', $thousandSeparator)).' '.$currencySymbol : 'N/A'; ?></td>


                                        <!-- <td>< ?php 

                                            $date = $payment->supplier_purchase_order->payment_due_date->format($dateFormat . ' ' . $timeFormat);

                                                echo !empty($date) ? date('Y-m-d', strtotime($date. ' + '.$supplier->credit_period.' days')) : 'N/A';
                                            ?>
                                        </td> -->

                                        <td>
                                        <?php 
                                            if (!empty($payment->supplier_purchase_order) && !empty($payment->supplier_purchase_order->payment_due_date)) {
                                                $dueDate = $payment->supplier_purchase_order->payment_due_date;

                                                if ($dueDate instanceof \Cake\I18n\FrozenTime) {
                                                    $formattedDate = $dueDate->format($dateFormat . ' ' . $timeFormat);
                                                    $adjustedDate = date('Y-m-d', strtotime($formattedDate . ' + ' . ($supplier->credit_period ?? 0) . ' days'));
                                                    echo $adjustedDate;
                                                } else {
                                                    echo 'N/A';
                                                }
                                            } else {
                                                echo 'N/A';
                                            }
                                        ?>
                                        </td>

                                        <td>
                                            <?php
                                                $paymentStatusMap = [
                                                    __('Paid') => ['label' => __('Paid'), 'class' => 'col-green'],
                                                    __('Partially Paid') => ['label' => __('Partially Paid'), 'class' => 'col-orange'],
                                                    __('Pending') => ['label' => __('Pending'), 'class' => 'col-blue']
                                                ];

                                                $payment_status = $paymentStatusMap[$payment->supplier_purchase_order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $payment_status['class'] ?>">
                                                    <?= h($payment_status['label']) ?>
                                                </div>
                                        </td>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a onclick="openViewSupplierPaymentModal(<?= $payment->id ?>)" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>    
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</section>

<!-- Add Supplier Payment Modal -->
<div id="add-supplier-payment-modal" style="margin-top: 50px;" class="modal" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Add Supplier Payment") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container">
                    <form id="add_supplier_payment_form" action="<?= $this->Url->build(['controller' => 'SupplierPayment', 'action' => 'add']) ?>" method="post" enctype="multipart/form-data">

                        <div class="form-group row">
                            <label for="supplierSelect" class="col-sm-2 col-form-label fw-bold">
                                <?= __("Supplier Name") ?> <sup class="text-danger font-11">*</sup>
                            </label>
                            <div class="col-sm-5 main-field">
                                <select class="form-control form-select select2" name="supplier_id" id="supplierSelect">
                                    <option value=""><?= __('-- Select Supplier --') ?></option>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <option value="<?= h($supplier->id) ?>"><?= h($supplier->name) ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span id="supplier_error" style="color: red; display: none;"><?= __('Please select a supplier.') ?></span>
                            </div>
                        </div>


                        <div class="form-group row">
                            <label for="supplier_name" class="col-sm-2 col-form-label fw-bold"><?= __("Collection Showroom") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                            <?php if (strtolower($role->name) === 'admin' || strtolower($role->name) == 'showroom supervisor'): ?>
                                    <!-- Admin: Show dropdown with all showrooms -->
                                    <select class="form-control form-select select2 d-flex align-items-center" id="showroomSelect" name="showroom_id">
                                        <option><?= __('Select Showroom') ?></option>
                                        <?php foreach ($showrooms as $showroom): ?>
                                            <option value="<?= $showroom->id; ?>">
                                                <?= htmlspecialchars($showroom->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <span id="showroom_error" style="color: #dc3545;display: none;"><?= __('Select Showroom') ?></span>
                                <?php else: ?>
                                    <!-- Showroom Manager: Show the specific showroom -->
                                    <?php 
                                        $userShowroom = null;
                                        foreach ($showrooms as $showroom) {
                                            if ($showroom->showroom_manager == $requested_user->id) {
                                                $userShowroom = $showroom;
                                                break;
                                            }
                                        }
                                    ?>
                                    <?php if ($userShowroom): ?>
                                        <input type="text" class="form-control" id="showroomSelect" name="showroom_id_display" value="<?= htmlspecialchars($userShowroom->name); ?>" readonly>
                                        <input type="hidden" name="showroom_id" value="<?= $userShowroom->id; ?>">
                                    <?php else: ?>
                                        <span class="text-danger"><?= __('No showroom assigned to you.') ?></span>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <span style="display:none;color: #dc3545;" id="payment_showroom_error"><?= __('Please select showroom') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="payment_mode" class="col-sm-2 col-form-label fw-bold"><?= __("Mode of Payment") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <select name="payment_mode" id="payment-mode" title="Select Payment Mode" data-live-search="true" class="form-control">
                                    <option value=""><?= __("Select Payment Mode") ?></option>
                                    <option value="cheque"><?= __("Cheque") ?></option>
                                    <option value="cash"><?= __("Cash") ?></option>
                                </select>
                                <span style="display:none;color: #dc3545;" id="payment_mode_error"><?= __('Please select payment mode') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="amount" class="col-sm-2 col-form-label fw-bold"><?= __("Amount") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('amount', [
                                    'type' => 'number',
                                    'class' => 'form-control',
                                    'id' => 'amount',
                                    'placeholder' => __('Amount'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="payment_amount_error"><?= __('Please enter amount') ?></span>
                            </div>
                        </div>

                        <!-- Hidden container to store bill amounts dynamically -->
                        <div id="billAmountsContainer"></div>


                        <div class="form-group row">
                            <label for="payment_date" class="col-sm-2 col-form-label fw-bold"><?= __("Payment Date and Time") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('payment_date', [
                                    'type' => 'datetime',
                                    'class' => 'form-control',
                                    'id' => 'payment_date',
                                    'value' => date('Y-m-d'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="payment_date_error"><?= __('Please choose a payment date') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="receipt_number" class="col-sm-2 col-form-label fw-bold"><?= __("Receipt Number") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('receipt_number', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'receipt_number',
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="receipt_number_error"><?= __('Please enter a receipt') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                                <label for="receipt" class="col-sm-2 col-form-label fw-bold"><?= __("Upload Receipt") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('receipt', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'id' => 'receipt',
                                        'accept' => 'image/*',
                                        'onchange' => "previewReceipt(event)",
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="receipt_error"><?= __('Please upload a receipt.') ?></span>
                                    <div class="receipt-preview m-t-20"></div>
                                </div>
                            </div>

                        <div id="cheque_div" style="display: none;">
                            <div class="form-group row">
                                <label for="cheque_no" class="col-sm-2 col-form-label fw-bold"><?= __("Cheque No") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('cheque_no', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'cheque_no',
                                        'placeholder' => __('Cheque No'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="cheque_no_error"><?= __('Please enter cheque number.') ?></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="cheque_date" class="col-sm-2 col-form-label fw-bold"><?= __("Date of Cheque") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('cheque_date', [
                                        'type' => 'date',
                                        'class' => 'form-control',
                                        'id' => 'cheque_date',
                                        'min' => date('Y-m-d'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="cheque_date_error"><?= __('Please choose cheque date.') ?></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="bank_name" class="col-sm-2 col-form-label fw-bold"><?= __("Bank Name") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('bank_name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'bank_name',
                                        'placeholder' => __('Bank Name'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="bank_name_error"><?= __('Please enter bank name.') ?></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="cheque_copy" class="col-sm-2 col-form-label fw-bold"><?= __("Upload Cheque Copy") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('cheque_copy', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'id' => 'cheque_copy',
                                        'accept' => 'image/*',
                                        'onchange' => "previewChequeCopy(event)",
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="cheque_copy_error"><?= __('Please upload a cheque copy.') ?></span>
                                    <div class="cheque-preview m-t-20"></div>
                                </div>
                            </div>
                        </div>

                        <div id="cash_div" style="display: none;">
                            <div class="form-group row">
                                <label for="payee_name" class="col-sm-2 col-form-label fw-bold"><?= __("Payee Name") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('payee_name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'payee_name',
                                        'placeholder' => __('Payee Name'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="payee_name_error"><?= __('Please enter payee name.') ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-12">
                                <button style="background-color: #0d839b !important;color: white !important;float: right;position: relative;right: 30px;" type="submit" class="btn add_supplier_payment_btn admin_btn"><?= __('Save') ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>

    <div id="view-supplier-payment-modal" style="margin-top: 50px;" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("View Supplier Payment") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container view_supplier_payment_container">
                    
                </div>
            </div>
        </div>
      </div>  
    </div>


<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/filter.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>

    const currencySymbol = '<?= h($currencySymbol) ?>';
    var paginationCount = <?= json_encode($paginationCount) ?>;

    $('#loadCashDeskBtn').on('click', function () {

        const showroomId = $('#cashDeskShowroomSelect').val();

        if (!showroomId) {
            $('#select_showroom_error').show();
            return;
        } else {
            $('#select_showroom_error').hide();
        }

        $.ajax({
            url: '<?= $this->Url->build(["controller" => "CashDesk", "action" => "cashDesk"]) ?>',
            type: 'POST',
            dataType: 'json',
            data: {
                showroom_id: showroomId
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function (response) {
                if (response.status === 'success') {
                    const data = response.data;

                    // Format amounts with FCFA
                    const formatCurrency = (amount) => parseFloat(amount).toLocaleString('fr-FR', { maximumFractionDigits: 0 }) + ' ' + currencySymbol;

                    // Show cash summary
                    $('#availableCash').text(data.available_cash).data('cash',data.available_cash);

                    $('#lastCashDeskClose').text(data.last_cash_desk_close || '<?= __('Not closed yet') ?>');
                    $('#cashDeskSummary').show();

                    // Optionally show raw data for dev/debug
                    // $('#cashDeskResult').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    $('#cashDeskResult').html('<div class="text-danger">' + response.message + '</div>');
                    $('#cashDeskSummary').hide();
                }
            },
            error: function () {
                $('#cashDeskResult').html('<div class="text-danger">Error while fetching cash desk data.</div>');
                $('#cashDeskSummary').hide();
            }
        });
    });

    $('#cashDeskCloseBtn').on('click', function () {
        const showroomId = $('#cashDeskShowroomSelect').val(); // assuming the showroom ID is selected or available
        const totalCash = $('#availableCash').data('cash'); // e.g. "55 FCFA"
        const numericTotalCash = totalCash.replace(/[^\d.]/g, '');

        if (!showroomId) {
            swal("<?= __('Error') ?>", "<?= __('Showroom ID is missing.') ?>", "error");
            return;
        }

        $.ajax({
            url: '<?= $this->Url->build(["controller" => "CashDesk", "action" => "cashDeskClose"]) ?>',
            type: 'POST',
            data: {
                showroom_id: showroomId,
                total_cash: numericTotalCash
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function (response) {
                if (response.status === 'success') {
                    swal("<?= __('Success') ?>", response.message, "success")
                        .then(() => {
                            location.reload();  // Refresh the page after user closes the alert
                        });
                    // $('#cashDeskCloseBtn').hide();
                } else {
                    swal("<?= __('Error') ?>", response.message, "error");
                }
            },
            error: function () {
                swal("<?= __('Error') ?>", "<?= __('Something went wrong') ?>", "error");
            }
        });
    });

    /** SUPPLIER PAYMENT **/

    document.addEventListener('DOMContentLoaded', function () {
        const filterReturnButton = document.querySelector('.btn.return-menu-toggle');
        const filterReturnBodyContainer = document.getElementById('filter-body-return-container');

        filterReturnButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (filterReturnBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                filterReturnBodyContainer.classList.remove('showing');
                filterReturnBodyContainer.classList.add('hiding');
                filterReturnBodyContainer.addEventListener('animationend', function () {
                    filterReturnBodyContainer.classList.remove('hiding');
                    filterReturnBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                filterReturnBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                filterReturnBodyContainer.classList.add('showing');
            }
        });
    });

    var supplier_payment_table = $("#supplierPaymentTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": [
            { "data": "supplier" },
            { "data": "showroom" },
            { "data": "bill_no" },
            { "data": "payment_date" },
            { "data": "amount" },
            { "data": "due_date" },
            { "data": "payment_status" },
            {
                "data": "actions",
                "render": function (data, type, row) {
                    return data;
                }
            }
        ]
    });

    $('#customSearchBoxReturnCancellation').on('keyup', function () {
        supplier_payment_table.search(this.value).draw();
    });

    $('.view-add-supplier-payment-modal').on('click', function() {

        $('#add_supplier_payment_form')[0].reset();
        $('#cheque_div').hide();
        $('#cash_div').hide();
        $('#add-supplier-payment-modal').modal('show');
    });

    $('#payment-mode').on('change', function () {

        payment_mode = this.value;
        if(payment_mode == '<?= __('cheque') ?>')
        {
            $('#payee_name_error').hide();
            $('#payment_mode_error').hide();
            $('#cheque_div').show();
            $('#cash_div').hide();

            $('#payment-mode').removeClass('is-invalid');
        }
        else if(payment_mode == '<?= __('cash') ?>')
        {
            $('#cheque_no_error').hide();
            $('#cheque_date_error').hide();
            $('#bank_name_error').hide();
            $('#cheque_copy_error').hide();
            $('#payment_mode_error').hide();

            $('input[name="cheque_copy"]').val('');
            $('#cheque_div').hide();
            $('#cash_div').show();

            $('#payment-mode').removeClass('is-invalid');
        }
        else
        {
            $('#payment_mode_error').show();
            $('#payment-mode').addClass('is-invalid');
            $('input[name="cheque_copy"]').val('');
            $('#cheque_div').hide();
            $('#cash_div').hide();
        }

    });

    function previewChequeCopy(event) {
        const input = event.target;
        const previewContainer = document.querySelector('.cheque-preview');
        const file = input.files[0]; // Get the first file

        // Clear any previous content
        previewContainer.innerHTML = '';

        if (file) {

            $('#cheque_copy_error').hide();
            $('#cheque_copy').removeClass('is-invalid');

            const reader = new FileReader();

            reader.onload = function (e) {
                // Create an image element
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.width = '100px'; // Set width of the image
                img.style.height = '100px'; // Set height of the image
                img.style.marginRight = '10px'; // Space between images

                // Create the remove button
                const removeButton = document.createElement('button');
                removeButton.innerHTML = '&times;'; // Use HTML entity for "X"
                removeButton.style.color = 'red'; // Set color for the "X"
                removeButton.style.background = 'none'; // Remove background
                removeButton.style.border = 'none'; // Remove border
                removeButton.style.cursor = 'pointer'; // Change cursor to pointer
                removeButton.style.backgroundColor = 'lightgray'; // Change cursor to pointer
                removeButton.type = 'button'; // Set button type
                removeButton.className = 'cheque-remove-button';
                removeButton.onclick = function () {
                    previewContainer.innerHTML = ''; // Clear preview
                    input.value = ''; // Clear input field
                };

                // Append image and remove button to the preview container
                previewContainer.appendChild(img);
                previewContainer.appendChild(removeButton);
            };

            reader.readAsDataURL(file); // Read the file as a data URL
        }
    }

    function previewReceipt(event) {
        const input = event.target;
        const previewContainer = document.querySelector('.receipt-preview');
        const file = input.files[0]; // Get the first file

        // Clear any previous content
        previewContainer.innerHTML = '';

        if (file) {

            $('#receipt_error').hide();
            $('#receipt').removeClass('is-invalid');

            const reader = new FileReader();

            reader.onload = function (e) {
                // Create an image element
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.width = '100px'; // Set width of the image
                img.style.height = '100px'; // Set height of the image
                img.style.marginRight = '10px'; // Space between images

                // Create the remove button
                const removeButton = document.createElement('button');
                removeButton.innerHTML = '&times;'; // Use HTML entity for "X"
                removeButton.style.color = 'red'; // Set color for the "X"
                removeButton.style.background = 'none'; // Remove background
                removeButton.style.border = 'none'; // Remove border
                removeButton.style.cursor = 'pointer'; // Change cursor to pointer
                removeButton.style.backgroundColor = 'lightgray'; // Change cursor to pointer
                removeButton.type = 'button'; // Set button type
                removeButton.className = 'receipt-remove-button';
                removeButton.onclick = function () {
                    previewContainer.innerHTML = ''; // Clear preview
                    input.value = ''; // Clear input field
                };

                // Append image and remove button to the preview container
                previewContainer.appendChild(img);
                previewContainer.appendChild(removeButton);
            };

            reader.readAsDataURL(file); // Read the file as a data URL
        }
    }

    $('#add_supplier_payment_form').on('submit', function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var error = 0;

        if ($("#supplierSelect").val() == '' || $("#supplierSelect").val() == null) {
            error = 1;
            $('#supplier_error').show();
            $('#supplierSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');
        }

        // if ($("#payment-showrooms-select").val() == '' || $("#payment-showrooms-select").val() == null) {
        //     error = 1;
        //     $('#payment_showroom_error').show();
        //     $('#payment-showrooms-select').next('.select2-container').find('.select2-selection').addClass('is-invalid');
        // }

        <?php if (strtolower($role->name) === 'admin' || strtolower($role->name) === 'showroom supervisor'): ?>
            const selectedShowroom = $("#showroomSelect").val();

            if (!selectedShowroom || selectedShowroom === '<?= __('Select Showroom') ?>') {
                error = 1;
                $('#payment_showroom_error').show();
                $('#showroomSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');

            } else {
                $('#showroom_error').hide();
            }
        <?php endif; ?>

        if ($("#payment-mode").val() == '' || $("#payment-mode").val() == null) {
            error = 1;
            $('#payment_mode_error').show();
            $('#payment-mode').addClass('is-invalid');
        }
        if ($("#amount").val() == '' || $("#amount").val() == null) {
            error = 1;
            $('#payment_amount_error').show();
            $('#amount').addClass('is-invalid');
        }
        if ($("#payment_date").val() == '' || $("#payment_date").val() == null) {
            error = 1;
            $('#payment_date_error').show();
            $('#payment_date').addClass('is-invalid');
        }

        if ($("#payment-mode").val() == '<?= __('cheque') ?>') {
            if ($("#cheque_no").val() == '' || $("#cheque_no").val() == null) {
                error = 1;
                $('#cheque_no_error').show();
                $('#cheque_no').addClass('is-invalid');
            }
            if ($("#cheque_date").val() == '' || $("#cheque_date").val() == null) {
                error = 1;
                $('#cheque_date_error').show();
                $('#cheque_date').addClass('is-invalid');
            }
            if ($("#bank_name").val() == '' || $("#bank_name").val() == null) {
                error = 1;
                $('#bank_name_error').show();
                $('#bank_name').addClass('is-invalid');
            }
            if ($("#cheque_copy").val() == '' || $("#cheque_copy").val() == null) {
                error = 1;
                $('#cheque_copy_error').show();
                $('#cheque_copy').addClass('is-invalid');
            }
        } else if ($("#payment-mode").val() == '<?= __('cash') ?>') {
            if ($("#payee_name").val() == '' || $("#payee_name").val() == null) {
                error = 1;
                $('#payee_name_error').show();
                $('#payee_name').addClass('is-invalid');
            }
        }

        if ($("#receipt_number").val() == '' || $("#receipt_number").val() == null) {
            error = 1;
            $('#receipt_number_error').show();
            $('#receipt_number').addClass('is-invalid');
        }

        if ($("#receipt").val() == '' || $("#receipt").val() == null) {
            error = 1;
            $('#receipt_error').show();
            $('#receipt').addClass('is-invalid');
        }

        if (error == 0) {
            $('#payment_bill_select_error').hide();
            $('#payment_showroom_error').hide();
            $('#payment_mode_error').hide();
            $('#payment_amount_error').hide();
            $('#payment_date_error').hide();
            $('.add_supplier_payment_btn').attr('disabled', 'disabled');

            // Append multiple bill data
            // selectedBills.forEach((billId, index) => {
            //     formData.append(`bills[${index}]`, billId);
            // });

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,  
                processData: false,  
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status == '<?= __('success') ?>') {
                        
                        swal('<?= __('Success') ?>', '<?= __('Supplier payment requests have been saved.') ?>', 'success')
                        .then(() => {
                            location.reload();
                        });

                    } else {
                        $('.add_supplier_payment_btn').removeAttr('disabled');
                        swal('<?= __('Failed') ?>', '<?= __('Failed to save supplier payment. Please try again.') ?>', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    $('.add_supplier_payment_btn').removeAttr('disabled');
                    swal('<?= __('Failed') ?>', '<?= __('Failed to save supplier payment. Please try again.') ?>', 'error');
                }
            });
        }
    });


    function openViewSupplierPaymentModal(supplierPaymentId)
    {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierPayment', 'action' => 'view']) ?>"+"/"+supplierPaymentId,
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                if(data.status == 'success')
                {   

                    $('.view_supplier_payment_container').html('');

                    if(data.supplier_payment.payment_mode) {

                        if(data.supplier_payment.payment_mode == '<?= __('cheque') ?>')
                        {
                            var payment_method = `

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Cheque No.') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;">${data.supplier_payment.cheque_no ? data.supplier_payment.cheque_no : '-' }</p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Date of Cheque') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;">${data.supplier_payment.cheque_date ? data.supplier_payment.cheque_date : '-' }</p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Bank Name') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;">${data.supplier_payment.bank_name ? data.supplier_payment.bank_name : '-' }</p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Cheque Copy') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <img style="width: 200px;height: 100px;" src="${data.supplier_payment.cheque_copy}"></img>
                                    </div>
                                </div>

                                `;

                        }
                        else if (data.supplier_payment.payment_mode == '<?= __('cash') ?>') {
                             
                            var payment_method = `<div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Payee Name') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.payee_name ? data.supplier_payment.payee_name : '-' }</p>
                                </div>
                            </div>`;
                        } 
                    }

                    const receiptHtml = data.supplier_payment.receipt
                      ? `<img style="width: 200px;height: 100px;" src="${data.supplier_payment.receipt}" alt="Receipt Image">`
                      : `<p class="ps-5" style="color: black;">-</p>`;

                    var html = `

                            <div class="form-group row">
                                <label for="category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Bill No') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.supplier_purchase_order.bill_no ? data.supplier_payment.supplier_purchase_order.bill_no : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="sub-category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Supplier Name') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.supplier.name ? data.supplier_payment.supplier.name : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-size" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Showroom') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.showroom.name ? data.supplier_payment.showroom.name : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="brand" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Mode of Payment') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.payment_mode ? data.supplier_payment.payment_mode : '-' }
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Amount') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.amount ? data.supplier_payment.amount : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Payment Date') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.payment_date ? data.supplier_payment.payment_date : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Payment Status') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.supplier_purchase_order.payment_status ? data.supplier_payment.supplier_purchase_order.payment_status : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Receipt Number') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.receipt_number ? data.supplier_payment.receipt_number : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Receipt') ?></label>
                                <div class="col-sm-5 ps-5">
                                  ${receiptHtml}
                                </div>
                            </div>

                            ${payment_method}

                    `;

                    $('.view_supplier_payment_container').append(html);
                    $('#view-supplier-payment-modal').modal('show');
                }

            },
            error: function() {
                swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
            }
        });
    }

    $('.supplier_payment_filter').on('click', function (e) {

        e.preventDefault();

        var payment = $("#filterSupplierPaymentStatus option:selected").val();

        supplier_payment_table.search('').columns().search('');

        if (payment) {
            supplier_payment_table.column(6).search(payment, true, false, false).draw();
        }

    });

    $('.reset_supplier_payment_filter').on('click', function (e) {

        e.preventDefault();

        supplier_payment_table.search('').columns().search('').draw();

    });

</script>

<?php $this->end(); ?>