<?php

declare(strict_types=1);

namespace App\Controller;
use Cake\ORM\TableRegistry;

/**
 * WarehouseUsers Controller
 *
 */
class WarehouseUsersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->WarehouseUsers->find();
        $warehouseUsers = $this->paginate($query);

        $this->set(compact('warehouseUsers'));
    }

    /**
     * View method
     *
     * @param string|null $id Warehouse User id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $warehouseUser = $this->WarehouseUsers->get($id, contain: []);
        $this->set(compact('warehouseUser'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function addWarehouseUsers()
    {
        $warehouseUser = $this->WarehouseUsers->newEmptyEntity();
        if ($this->request->is('post')) {
            $warehouseUser = $this->WarehouseUsers->patchEntity($warehouseUser, $this->request->getData());
            $this->log('saved' . json_encode($warehouseUser), 'debug');
            if ($this->WarehouseUsers->save($warehouseUser)) {
                $response = ['status' => 'success', 'message' => __('The warehouse user has been saved.')];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            }
            $this->log('saved' . json_encode($warehouseUser->getErrors()), 'debug');
            $response = ['status' => 'error', 'message' => __('The warehouse user could not be saved. Please, try again.')];
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Warehouse User id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function editWarehouseUsers($id = null)
    {

        if ($id === null) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Invalid ID']));
        }

        $warehouseUser = $this->WarehouseUsers->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $warehouseUser = $this->WarehouseUsers->patchEntity($warehouseUser, $this->request->getData());
            if ($this->WarehouseUsers->save($warehouseUser)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'success', 'warehouseUser' => $warehouseUser, 'message' => 'Warehouse user saved.']));
            }
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Could not save the warehouse user.']));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => 'success', 'warehouseUser' => $warehouseUser]));
    }

    /**
     * Delete method
     *
     * @param string|null $id Warehouse User id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function deleteWarehouseUser($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $warehouseuser = $this->WarehouseUsers->get($id);
        $response = ['success' => false, 'message' => 'The warehouse user could not be deleted. Please, try again.'];
        if ($warehouseuser) {
            if ($this->WarehouseUsers->delete($warehouseuser)) {
                $response = ['success' => true, 'message' => 'The warehouse user has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The warehouse user could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The warehouse user does not exist.'];
        }

        
        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    // public function getWarehouseUsers()
    // {
    //     $warehouse_id = $this->request->getQuery('warehouse_id');

    //     $users = $this->WarehouseUsers->find()
    //         ->where([
    //             'WarehouseUsers.warehouse_id' => $warehouse_id,
    //             'WarehouseUsers.status !=' => 'D'
    //         ])
    //         ->contain(['Users', 'Roles'])
    //         ->select([
    //             'id' => 'WarehouseUsers.id',
    //             'name' => 'CONCAT(Users.first_name, " ", Users.last_name)',
    //             'role' => 'Roles.name',
    //             'status' => 'WarehouseUsers.status'
    //         ])
    //         ->enableAutoFields(false)
    //         ->toArray();

    //     $response = ['users' => $users];

    //     $this->response = $this->response->withType('application/json');
    //     $this->response = $this->response->withStringBody(json_encode($response));

    //     return $this->response;
    // }

    public function getWarehouseUsers()
    {
        $warehouseId = $this->request->getQuery('warehouse_id');

        $users = [];

        if ($warehouseId) {
            $warehousesTable = TableRegistry::getTableLocator()->get('Warehouses');
            $usersTable = TableRegistry::getTableLocator()->get('Users');
            $rolesTable = TableRegistry::getTableLocator()->get('Roles');

            $warehouse = $warehousesTable->find()
                ->select(['id', 'manager_id', 'assistant_id'])
                ->where(['id' => $warehouseId])
                ->first();

            if ($warehouse) {
                $userIds = array_filter([$warehouse->manager_id, $warehouse->assistant_id]);

                if (!empty($userIds)) {
                    $users = $usersTable->find()
                        ->where(['Users.id IN' => $userIds])
                        ->contain(['Roles'])
                        ->select([
                            'Users.id',
                            'name' => $usersTable->find()->func()->concat([
                                'Users.first_name' => 'identifier',
                                ' ',
                                'Users.last_name' => 'identifier'
                            ]),
                            'role' => 'Roles.name',
                            'Users.status'
                        ])
                        ->enableAutoFields(false)
                        ->toArray();
                }
            }
        }

        $response = ['users' => $users];

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

}
