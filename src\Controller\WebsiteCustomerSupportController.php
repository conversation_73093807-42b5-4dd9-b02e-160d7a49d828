<?php
declare(strict_types=1);

namespace App\Controller;

use Cake\Controller\Controller;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\Time;
use Cake\I18n\FrozenTime;
use App\Mailer\UserMailer;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Http\Client;
use Cake\Http\Exception\BadRequestException;
use Cake\Http\Response;
use Cake\Utility\Text;

class WebsiteCustomerSupportController extends Controller
{
    protected $Users;
    protected $Customers;

    protected $SupportCategories;
    protected $SupportTickets;
    protected $SupportTicketUpdates;
    protected $SupportTicketImages;

    public function initialize(): void
    {
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');

        $this->SupportCategories = $this->fetchTable('SupportCategories');        
        $this->SupportTickets = $this->fetchTable('SupportTickets');
        $this->SupportTicketUpdates = $this->fetchTable('SupportTicketUpdates');
        $this->SupportTicketImages = $this->fetchTable('SupportTicketImages'); 

        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('Website');
        $this->loadComponent('Flash');
        $this->loadComponent('Mtn');
        $this->loadComponent('Wave');
        $this->loadComponent('WebsiteFunction');
        $this->viewBuilder()->setLayout('web');
    }
    public function beforeRender(\Cake\Event\EventInterface $event)
    {
        parent::beforeRender($event);
        $topSellCarEmptyItemShow = $this->WebsiteFunction->getTopSellItems(10);
        $scrollContentArr = $this->WebsiteFunction->getHomeScrollText();

        // Pass it to all views
        $this->set(compact('topSellCarEmptyItemShow','scrollContentArr'));
    }

    public function supportDesk()
    {
        $identity = $this->request->getSession()->read('Auth.User');

        if (empty($this->request->getSession()->read('Auth.User'))) {
            $this->Flash->toast(__("Login required to access user account."), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
            return $this->redirect(['controller' => 'customer', 'action' => 'login']);
        }

        $users = $this->Users->find()
        ->contain([
            'Customers' => function ($q) {
                return $q->select(['id']); // Select only the Customer.id field
            }
        ])
        ->select(['Users.id']) // Select the necessary fields from Users
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $identity->id])
        ->first();

        $customer_id = $users->customer->id;

        $title = 'Support Tickets Add';

        $allCategory = $this->SupportCategories->getAllActiveSupportCategories()->all();

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        if ($this->request->is('post')) {

            $identity = $this->request->getSession()->read('Auth.User');
            if($identity) {
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }
            $data = $this->request->getData();
            $data['created_by'] = $userId;
            $data['updated_by'] = $userId;
            $data['support_category_id'] = $data['category'];
            $timestamp = FrozenTime::now()->format('YmdHis'); // Current date and time in YYYYMMDDHHMMSS format
            $randomNumber = mt_rand(1000, 9999); // Random 4-digit number
            $data['ticketID'] = "$timestamp-$randomNumber";

            $addTicket = $this->SupportTickets->addTicketData($data);

            if($addTicket){
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_id', $addTicket) : [];
            }
            if ($addTicket) {
                $this->Flash->success(__('Ticket created successfully.'));
                return $this->redirect(['controller' => 'WebsiteCustomerSupport', 'action' => 'supportDesk']);
            } else {
                $this->Flash->error(__('Unable to create ticket. Please try again.'));
            }
        }
           
        // Define the required parameters
        $filter_ticket_status = $this->request->getQuery('status', null);
        $filter_support_category = $this->request->getQuery('category', null);
        $search_str = $this->request->getQuery('search', '');
        $page = $this->request->getQuery('page', 1);
        $limit = $this->request->getQuery('limit', 3);
        $priority = $this->request->getQuery('priority', null);
        $is_read = $this->request->getQuery('is_read', null); // New filter parameter
           
       
        // Call listSupportTicketsData method with required parameters
        $supportTicketsData = $this->SupportTickets->listSupportTicketsData($filter_ticket_status, $filter_support_category, $search_str, $page, $limit, $priority, $customer_id, $is_read);
           
        $supportCategories = $this->SupportCategories->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();

        $this->set(compact('identity', 'supportTicketsData', 'filter_ticket_status', 'filter_support_category', 'search_str', 'page', 'limit', 'priority', 'is_read'));
        $this->set('supportCategories', $supportCategories);

        $this->viewBuilder()->setTemplatePath('SupportDesk');
        $this->render('index');
    }
    public function reply()
    {
        $identity = $this->request->getSession()->read('Auth.User');
        $ticketId = $this->request->getData('id');
      
        if (!$identity) {
            return $this->redirect(['controller' => 'Website', 'action' => 'login'])->withFlash(__('Please login to access this page.'), ['element' => 'error']);
        }

        $users = $this->Users->find()
        ->contain([
            'Customers' => function ($q) {
                return $q->select(['id']); // Select only the Customer.id field
            }
        ])
        ->select(['Users.id']) // Select the necessary fields from Users
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $identity->id])
        ->first();

        $customer_id = $users->customer->id;
        $user_id = $users->id;

        if ($this->request->is(['post', 'put'])) {
           
            $data = $this->request->getData();
         
            $support_ticket_id = $data['id'];           
            $data['updated_by'] = $user_id;                
           
            $addUpdate = $this->SupportTicketUpdates->addUpdate($support_ticket_id, $data); 

            if ($addUpdate) {
                  
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_update_id', $addUpdate) : [];
              
                $this->Flash->websiteSuccess(__('Your reply has been submitted.'));
                return $this->redirect([
                    'controller' => 'WebsiteCustomerSupport',
                    'action' => 'supportDeskManage',
                    $ticketId
                ]);
            }
            $this->Flash->websiteError(__('Unable to submit your reply. Please try again.'));
        }
        return $this->redirect([
            'controller' => 'WebsiteCustomerSupport',
            'action' => 'supportDeskManage',
            $ticketId
        ]);
    }

    public function supportDeskManage($ticketId = null)
    {
         
        if (!$ticketId) {
            throw new NotFoundException(__('Invalid ticket ID'));
        }
        $identity = $this->request->getSession()->read('Auth.User');
      
        if (!$identity) {
            return $this->redirect(['controller' => 'Website', 'action' => 'login'])->withFlash(__('Please login to access this page.'), ['element' => 'error']);
        }

        $users = $this->Users->find()
        ->contain([
            'Customers' => function ($q) {
                return $q->select(['id']); // Select only the Customer.id field
            }
        ])
        ->select(['Users.id']) // Select the necessary fields from Users
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $identity->id])
        ->first();

        $customer_id = $users->customer->id;

       
        // Call listSupportTicketsData method with required parameters
        $supportTicketsData = $this->SupportTickets->webViewTicketDetail($ticketId);
          
        // Check if customer profile has an image and call Media component for S3 image
        if (!empty($supportTicketsData['updated_by_user']['_matchingData']['Customers']['profile_photo'])) {
            $supportTicketsData['updated_by_user']['_matchingData']['Customers']['profile_photo'] = $this->Media->getCloudFrontURL("babiken_uploads\/Kim Yoo Jung_27855.jpg");
        }

        $supportCategories = $this->SupportCategories->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();
       
 //      dd($supportTicketsData->created_by_user->first_name);
        $this->set(compact('identity', 'supportTicketsData'));
        $this->set('supportCategories', $supportCategories);

        $this->viewBuilder()->setTemplatePath('SupportDesk');
        $this->render('view');
    }

        /***  Private functions start  ****/
        private function supportImageUpload($flag, $id)
        {
            $files =  $this->request->getData('images');
            $uploadedImages = [];
            $i = 0;
            foreach ($files as $file) {
                if ($file->getError() === UPLOAD_ERR_OK) {
                    $fileName = trim($file->getClientFilename());
                    if (!empty($fileName)) {
                        $imageTmpName = $file->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Settings.SUPPORT_DESK');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                        } else {
                            $supportImage = $this->SupportTicketImages->newEmptyEntity();
                            if($flag == 'ticket_update_id'){
                                $supportImage->support_ticket_update_id = $id;
                            }else{
                                $supportImage->support_ticket_id = $id;
                            }
                            $supportImage->image = $folderPath . $imageFile;
                            $i++;
                            if ($this->SupportTicketImages->save($supportImage)) {
                                $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                            } else {
                                $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                            }
                        }
                    }
                }
            }
            return $uploadedImages;
        }
    

    /****  Private functions end   ***/
}
