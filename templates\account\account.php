<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<?php $this->end(); ?>
<style>
.p-s-img {
    margin-right: 85px;
}
.loyalty-points-earned {
    text-align: center;
    background: #ffa5005e;
    width: 70%;
    margin: 0 auto;
    padding: 4px 2px;
    border-radius: 10px;
    margin-top: 10px;
    font-weight: bold;
    color: black;
    font-size: 16px;
}
.delete-account-btn {
    color: black;
}
.change-password{
    position: relative;
    width: 100% !important;
}
.change-password .p-s-img-btn-edit-clone{
    position: absolute;
    right: 75px;
}
.error-message {
    color: red;
    font-size: 14px;
    margin-top: 5px;
    display: block;
    text-align: left;
}
.password-field input.error {
    border: 1px solid red;
}

.password-input-container {
    position: relative;
    width: 100%;
}
.toggle-password {
    position: absolute;
    right: 8px;
    /* top: 0%; */
    bottom: 9px;
    /*transform: translateY(-50%);*/
    cursor: pointer;
    color: #999;
    font-size: 13px;
}
.toggle-password:hover {
    color: #333;
}
form.password-field #label-input{
    margin-right:0;
}
form.password-field #label-input input {
    width: 186px;
}
</style>
<div class="productCategoryListingC">
    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span class="productCategoryListing-home-span"><?= __('Home') ?></span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span Electronic-devices"><?= __('My Account') ?></span>
    </div>
</div>

<div class="my-a-my-o-c">

<?php echo $this->element('web_sidebar'); ?>

    <div class="my-a-my-o-main" id="my-a-my-o-main">
        <div class="p-s-img-btn-c">
            <form id="photoForm" method="post" action="<?= $this->Url->build([
                'controller' => 'Account',
                'action' => 'updateProfilePhoto'
            ]); ?>" enctype="multipart/form-data">
            <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

            <input type="hidden" name="_csrfToken" value="<?= $this->request->getAttribute('csrfToken') ?>">

            <img src="<?= $users->customer->profile_photo ? $users->customer->profile_photo : '../assets/dp.png' ?>"
                 class="p-s-img" id="profileImagePreview">
              

            </form>

          
                <button type="button" id="edit-save-button" class="p-s-img-btn-edit"><?= __('Edit') ?></button>
                <button type="button" id="cancel-button" class="p-s-img-btn-edit" style="display: none;"><?= __('Cancel') ?></button>
                <div class="loyalty-points-earned">
                    <p><?= __('Loyalty Points :'); ?>
                    <?= $this->Price->setPriceFormat($loyaltyDetails['data']['points_converted']) ?>
                    </p>
                    <p><?= __('Wallet:'); ?>
                    <?= $this->Price->setPriceFormat($walletAmount) ?>
                    </p>
                </div>
        </div>


        <div class="p-d-i-g-c-c">
            <?= $this->Flash->render() ?>
            <form class="p-d-i-g" method="post" action="<?= $this->Url->build([
                'controller' => 'Account',
                'action' => 'updateMyAccount'
            ]); ?>" enctype="multipart/form-data">
                <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                <div id="label-input">
                    <label for="name"><?= __('First Name') ?></label>
                    <input type="text" id="name" name="name" value="<?= $users->first_name ?>" required disabled>
                </div>

                <div id="label-input">
                    <label for="last_name"><?= __('Last Name') ?></label>
                    <input type="text" id="last_name" name="last_name" value="<?= $users->last_name ?>" required disabled>
                </div>

                <div id="label-input">
                    <label for="email"><?= __('Email') ?></label>
                    <input type="email" id="email" name="email" required value="<?= $users->email ?>" disabled>
                </div>

                <div id="label-input">
                    <label for="phone"><?= __('Phone Number') ?></label>
                    <input type="tel" id="phone" name="phone" required value="<?= $users->mobile_no ?>" disabled>
                </div>

                <div id="label-input">
                    <label for="dob"><?= __('Date of Birth') ?></label>
                    <input type="date" id="dob" name="dob" required
                           value="<?= isset($users->customer->date_of_birth) ? date("Y-m-d", strtotime($users->customer->date_of_birth)) : '' ?>"
                           disabled>
                </div>

                <div id="label-input" class="gender-label-input">
                    <label><?= __('Gender:') ?></label>
                    <div class="gender-input">
                        <input id="male-radio-input" type="radio" name="gender"
                               value="M" <?= isset($users->customer->gender) && $users->customer->gender === 'M' ? 'checked' : '' ?>
                               disabled>
                        <label for="male-radio-input"><?= __('Male') ?></label>

                        <input id="female-radio-input" type="radio" name="gender"
                               value="F" <?= isset($users->customer->gender) && $users->customer->gender === 'F' ? 'checked' : '' ?>
                               disabled>
                        <label for="female-radio-input"><?= __('Female') ?></label>
                    </div>
                </div>
                <div id="label-input">
                    <label for="name"><?= __('Choose Profile Photo') ?></label>
                    <input type="file" name="profile_photo" accept="image/*" style="margin: 0;" class="user-profile-pic"/>
                </div>
                
                <div id="label-input" >
                    
                    <button id="changePasswordBtn" class="p-s-img-btn-edit p-s-img-btn-edit-clone" type="button"><?= __('Change Password') ?></button>    
                </div>

            </form>
        </div>


        <form class="password-field" method="post" id="popupChange" action="<?= $this->Url->build([
            'controller' => 'Account',
            'action' => 'changePassword'
        ]); ?>">
    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

    <!-- Close Button -->
    <button type="button" id="closePasswordPopup" class="close-btn">&times;</button>

    <div id="label-input">
        <label for="current_password"><?= __('Old Password') ?></label>
        <div class="password-input-container">
            <input type="password" id="old_password" name="old_password">
            <i class="toggle-password fa fa-eye-slash" data-target="old_password"></i>
        </div>
    </div>
    <div id="label-input">
        <label for="new_password"><?= __('New Password') ?></label>
        <div class="password-input-container">
            <input type="password" id="new_password" name="new_password">
            <i class="toggle-password fa fa-eye-slash" data-target="new_password"></i>
        </div>
    </div>
    <div id="label-input">
        <label for="confirm_password"><?= __('Confirm Password') ?></label>
        <div class="password-input-container">
            <input type="password" id="confirm_password" name="confirm_password">
            <i class="toggle-password fa fa-eye-slash" data-target="confirm_password"></i>
        </div>
    </div>
    <button class="p-s-img-btn-edit" type="submit"><?= __('Update Password') ?></button>
</form>

<!-- JavaScript -->
<script>
    document.getElementById("changePasswordBtn").addEventListener("click", function() {
        document.querySelector(".password-field").style.display = "block";
    });

    document.getElementById("closePasswordPopup").addEventListener("click", function() {
        document.querySelector(".password-field").style.display = "none";
    });

    // Password toggle functionality
    document.querySelectorAll('.toggle-password').forEach(function(toggle) {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const passwordInput = document.getElementById(targetId);

            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.classList.remove('fa-eye-slash');
                this.classList.add('fa-eye');
            } else {
                passwordInput.type = 'password';
                this.classList.remove('fa-eye');
                this.classList.add('fa-eye-slash');
            }
        });
    });
</script>

    </div>
</div>


<div class="delete-account">
    <form method="post" action="<?= $this->Url->build(['controller' => 'Account', 'action' => 'deleteAccount']); ?>" id="deleteAccountForm">
        <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
        <button type="button" class="delete-account-btn" onclick="confirmDelete()"><?= __('Delete account') ?></button>
    </form>
</div>

<?php $this->start('add_js'); ?>
<script>
    const changePhoto = document.getElementById('change-photo');
    const editSaveButton = document.getElementById('edit-save-button');
    const cancelButton = document.getElementById('cancel-button');
    const form = document.querySelector('.p-d-i-g');
    const inputs = form.querySelectorAll('input');

    // Initial state
    const disableInputs = () => {
        inputs.forEach(input => input.setAttribute('disabled', 'disabled'));
        editSaveButton.textContent = 'Edit';
        editSaveButton.setAttribute('type', 'button');


        cancelButton.style.display = 'none';
    };

    const enableInputs = () => {
        inputs.forEach(input => input.removeAttribute('disabled'));
        editSaveButton.textContent = 'Save';
        editSaveButton.setAttribute('type', 'submit');
        cancelButton.style.display = 'inline-block';
    };

    // Toggle Edit/Save
    editSaveButton.addEventListener('click', function () {
        if (editSaveButton.textContent === 'Edit') {
            enableInputs();
        } else {
            // Form submission handled by submit button
            form.submit();
        }
    });

    // Cancel action
    cancelButton.addEventListener('click', function () {
        // Reset inputs and disable again
        disableInputs();
    });

    // Initialize the form in disabled state
    disableInputs();
</script>
<script>
    document.getElementById("change-photo").addEventListener("click", function() {
        document.getElementById("photoForm").submit(); // Trigger form submission
    });
</script>
<script>
    function confirmDelete() {
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            document.getElementById('deleteAccountForm').submit();
        }
    }
</script>
<script>
$(document).ready(function() {

    // Form validation
    $("#popupChange").validate({
        rules: {
            old_password: {
                required: true,
                minlength: 6
            },
            new_password: {
                required: true,
                minlength: 6
            },
            confirm_password: {
                required: true,
                equalTo: "#new_password"
            }
        },
        messages: {
            old_password: {
                required: "Please enter your current password.",
                minlength: "Password must be at least 6 characters long."
            },
            new_password: {
                required: "Please enter a new password.",
                minlength: "Password must be at least 8 characters long."
            },
            confirm_password: {
                required: "Please confirm your new password.",
                equalTo: "Passwords do not match."
            }
        },
        errorElement: "div",
        errorPlacement: function (error, element) {
            error.addClass("error-message");
            error.insertAfter(element.closest('.password-input-container'));
        },
        highlight: function(element) {
            $(element).addClass("error");
        },
        unhighlight: function(element) {
            $(element).removeClass("error");
        },
        submitHandler: function(form) {
            // Show loading indicator or disable button if needed
            $('button[type="submit"]').attr('disabled', 'disabled').text('Updating...');
            form.submit();
        }
    });
});
</script>
<?php $this->end(); ?>
