<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Wallets Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 *
 * @method \App\Model\Entity\Wallet newEmptyEntity()
 * @method \App\Model\Entity\Wallet newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Wallet> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Wallet get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Wallet findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Wallet patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Wallet> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Wallet|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Wallet saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Wallet>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wallet>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wallet>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wallet> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wallet>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wallet>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wallet>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wallet> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class WalletsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('wallets');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id')
            ->add('customer_id', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->decimal('balance')
            ->notEmptyString('balance');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['customer_id']), ['errorField' => 'customer_id']);
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);

        return $rules;
    }

    /**
     * Custom finder to find wallets by customer ID and balance.
     *
     * @param \Cake\ORM\Query\SelectQuery $query The query object.
     * @param array<string, mixed> $options The options for the finder.
     * @return \Cake\ORM\Query\SelectQuery
     */
    public function getMyWalletAmount($customerId){
        return $this->find()
            ->select(['balance'])
            ->where(['customer_id' => $customerId])
            ->first();
    }

    public function updateWalletBalance($customerId, $amount, $type): bool
    {
        $wallet = $this->find()
            ->where(['customer_id' => $customerId])
            ->first();

        if ($wallet && $type === 'debit' && $wallet->balance >= $amount) {
            // Debit the wallet
            $wallet->balance -= $amount;
            return $this->save($wallet) !== false;
        } elseif ($wallet && $type === 'credit') {
            // Credit the wallet
            $wallet->balance += $amount;
            return $this->save($wallet) !== false;
        }

        return false;
    }
}