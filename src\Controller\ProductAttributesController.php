<?php

declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\QueryExpression;

/**
 * ProductAttributes Controller
 *
 * @property \App\Model\Table\ProductAttributesTable $ProductAttributes
 */
class ProductAttributesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

     protected $ProductStocks;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->ProductStocks = $this->fetchTable('ProductStocks');
    }

    public function index()
    {
        $query = $this->ProductAttributes->find()
            ->contain(['Products', 'Attributes', 'AttributeValues']);
        $productAttributes = $this->paginate($query);

        $this->set(compact('productAttributes'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Attribute id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function viewProductAttribute($productId, $id = null)
    {
        $this->request->allowMethod(['get']);

        $productAttributes = $this->ProductAttributes->find()
            ->contain(['Attributes', 'AttributeValues'])
            ->where([
                'product_id' => $productId,
                'ProductAttributes.attribute_id' => $id,
                'ProductAttributes.status' => 'A'
            ])
            ->all()
            ->toArray();

        if (!empty($productAttributes)) {
            $response = [
                'status' => 'success',
                'data' => $productAttributes
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Product Attribute not found'
            ];
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function addProductAttribute()
    {
        $productAttribute = $this->ProductAttributes->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $product_id = $data['product_id'];
            $attribute_id = $data['attribute_id'];
            $attribute_value_ids = $data['attribute_value_id'];

            $existingAttribute = $this->ProductAttributes->find()
                ->where([
                    'product_id' => $product_id,
                    'attribute_id' => $attribute_id,
                    'status' => 'A'
                ])
                ->first();

            if ($existingAttribute) {
                $response = [
                    'status' => 'error',
                    'message' => __('The product attribute could not be saved. An existing attribute already exists. Please, try again.')
                ];
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

            foreach ($attribute_value_ids as $attribute_value_id) {
                $productAttribute = $this->ProductAttributes->newEmptyEntity();
                $productAttribute = $this->ProductAttributes->patchEntity($productAttribute, [
                    'product_id' => $product_id,
                    'attribute_id' => $attribute_id,
                    'attribute_value_id' => $attribute_value_id,
                    'status' => 'A'
                ]);

                if (!$this->ProductAttributes->save($productAttribute)) {
                    $response = [
                        'status' => 'error',
                        'message' => __('The product attribute could not be saved for value ID {0}. Please, try again.', $attribute_value_id)
                    ];
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                }
            }

            $response = [
                'status' => 'success',
                'message' => __('The product attribute(s) have been saved.')
            ];
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Product Attribute id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function editProductAttribute($product_id = null, $id = null)
    {
        $productId = $this->request->getQuery('product_id') ?? $product_id;
        $attr_id = $this->request->getQuery('attr_id') ?? $id;

        $productAttribute = $this->ProductAttributes->find()
            ->where([
                'product_id' => $productId,
                'attribute_id' => $attr_id,
                'status' => 'A'
            ])
            ->all()
            ->toArray();

        if ($this->request->is(['patch', 'post', 'put'])) {
            if ($id === null || $product_id === null) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'error', 'message' => 'Invalid ID']));
            }
            $data = $this->request->getData();
            $product_id = $product_id;
            $attribute_id = $id;
            $attribute_value_ids = $data['attribute_value_id'];

            $this->ProductAttributes->updateAll(
                ['status' => 'D'],
                ['attribute_id' => $attribute_id, 'product_id' => $product_id]
            );
            // $existingAttribute = $this->ProductAttributes->find()
            //     ->where([
            //         'product_id' => $product_id,
            //         'attribute_id' => $attribute_id,
            //         'id <>' => $id,  
            //         'status' => 'A'
            //     ])
            //     ->first();
            // if ($existingAttribute) {
            //     $response = [
            //         'status' => 'error',
            //         'message' => __('The product attribute could not be saved. An existing attribute already exists. Please, try again.')
            //     ];
            //     return $this->response->withType('application/json')
            //         ->withStringBody(json_encode($response));
            // }

            foreach ($attribute_value_ids as $attribute_value_id) {

                $existingAttributeValue = $this->ProductAttributes->find()
                    ->where([
                        'product_id' => $product_id,
                        'attribute_id' => $attribute_id,
                        'attribute_value_id' => $attribute_value_id,
                        'status' => 'A'
                    ])
                    ->first();
                if (!$existingAttributeValue) {
                    $productAttribute = $this->ProductAttributes->newEmptyEntity();
                    $productAttribute->product_id = $product_id;
                    $productAttribute->attribute_id = $attribute_id;
                    $productAttribute->attribute_value_id = $attribute_value_id;
                    $productAttribute->status = 'A';

                    $this->ProductAttributes->save($productAttribute);
                }
            }

            $response = ['status' => 'success', 'message' => __('Product attributes have been updated.')];
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => 'success', 'productAttribute' => $productAttribute]));
    }


    /**
     * Delete method
     *
     * @param string|null $id Product Attribute id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function deleteProductAttributes()
    {
        $this->request->allowMethod(['post', 'delete']);
        $product_id = $this->request->getQuery('product_id');
        $attribute_id = $this->request->getQuery('attribute_id');

        $response = ['success' => false, 'message' => 'The product attributes could not be deleted. Please, try again.'];

        $productAttributes = $this->ProductAttributes->find()
            ->where(['product_id' => $product_id, 'attribute_id' => $attribute_id, 'status !=' => 'D'])
            ->all();

        if (!$productAttributes->isEmpty()) {
            $success = true;

            foreach ($productAttributes as $productAttribute) {
                if (!$this->ProductAttributes->delete($productAttribute)) {
                    $success = false;
                    break;
                }
            }

            if ($success) {
                $response = ['success' => true, 'message' => 'The product attributes have been marked as deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'An error occurred while updating the product attributes.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'No product attributes found to delete.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function getAttributesData()
    {
        $product_id = $this->request->getQuery('product_id');

        if (!$product_id) {
            $response = ['status' => 'error', 'message' => 'Product ID is required'];
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        $attributes = $this->ProductAttributes->find('all')
            ->where(['ProductAttributes.product_id' => $product_id, 'ProductAttributes.status !=' => 'D'])
            ->contain(['Attributes', 'AttributeValues'])
            ->select([
                'attribute_id' => 'ProductAttributes.attribute_id',
                'attribute_name' => 'Attributes.name',
                'id' => 'ProductAttributes.id',
                'value' => 'AttributeValues.value',
                'status' => 'ProductAttributes.status'
            ])
            ->order(['Attributes.name' => 'ASC'])
            ->toArray();

        if (empty($attributes)) {
            $response = ['status' => 'error', 'message' => 'No attributes found for this product'];
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        $groupedAttributes = [];
        foreach ($attributes as $attribute) {
            $attributeId = $attribute['attribute_id'];
            if (!isset($groupedAttributes[$attributeId])) {
                $groupedAttributes[$attributeId] = [
                    'attribute_id' => $attributeId,
                    'attribute_name' => $attribute['attribute_name'],
                    'ids' => [],
                    'values' => [],
                    'status' => $attribute['status']
                ];
            }
            $groupedAttributes[$attributeId]['ids'][] = $attribute['id'];
            $groupedAttributes[$attributeId]['values'][] = $attribute['value'];
        }

        foreach ($groupedAttributes as &$data) {
            $data['ids'] = implode(',', $data['ids']);
            $data['values'] = implode(', ', $data['values']);
        }

        $response = ['status' => 'success', 'attributes' => $groupedAttributes];

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    public function getProductAttributes()
    {
        $this->request->allowMethod(['post']); 

        $productId = $this->request->getData('product_id');

        if (!$productId) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Invalid product ID'
                ]));
        }

        $attributes = $this->ProductAttributes->find()
            ->select([
                'ProductAttributes.id',
                'Attributes.name',
                'AttributeValues.value'
            ])
            ->contain(['Attributes', 'AttributeValues'])
            ->where([
                'ProductAttributes.product_id' => $productId,
                'ProductAttributes.status' => 'A'
            ])
            ->all();

        if ($attributes->isEmpty()) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'No attributes found for this product.'
                ]));
        }

        $formattedAttributes = [];
        foreach ($attributes as $attribute) {
            $formattedAttributes[] = [
                'id' => $attribute->id,
                'name' => $attribute->attribute->name . ' : ' . $attribute->attribute_value->value
            ];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'attributes' => $formattedAttributes
            ]));
    }

    public function getProductAttrPrices()
    {
        $this->autoRender = false;
        $this->request->allowMethod(['post']);

        $productAttrId = $this->request->getData('id');
        $productId = $this->request->getData('productId');
        $productVarId = $this->request->getData('productVarId');
        $showrooms = $this->request->getData('showrooms') ?? [];

       
        if ($showrooms) {
            $avlQuantity = $this->ProductStocks->find()
                ->select([
                    'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
                ])
                ->where(['showroom_id IN' => $showrooms, 'product_id' => $productId, 'product_variant_id is' => $productVarId, 'product_attribute_id is' => $productAttrId])
                ->first();
        } else {
            $avlQuantity = $this->ProductStocks->find()
                ->select([
                    'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
                ])
                ->where(['product_id' => $productId, 'product_variant_id is' => $productVarId, 'product_attribute_id is' => $productAttrId])
                ->first();
        }

        $availableQuantity = $avlQuantity ? max(0, (int) $avlQuantity->available_quantity) : 0;

        if ($productAttrId) {
            $response = [
                'status' => 'success',
                'availableQuantity' => $availableQuantity ?? 0
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Product not found.'
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }
}
