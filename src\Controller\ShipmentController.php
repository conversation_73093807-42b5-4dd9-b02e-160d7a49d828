<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\I18n\FrozenDate;
use Cake\Database\Expression\QueryExpression;
use Cake\ORM\Query;
use Cake\Core\Configure;
use Cake\I18n\FrozenTime;
use Cake\ORM\Query\Expression\IdentifierExpression;
use Cake\Routing\Router;
use Cake\ORM\TableRegistry;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ShipmentController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Zones;
    protected $Showrooms;
    protected $ZoneShowrooms;
    protected $Orders;
    protected $OrderItems;
    protected $OrderItemAttributes;
    protected $Customers;
    protected $CustomerAddresses;
    protected $Users;
    protected $Drivers;
    protected $ProductStocks;
    protected $Shipments;
    protected $ShipmentItems;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $Warehouses;
    protected $DeliveryPartners;
    protected $ShipmentOrders;
    protected $ShipmentOrderItems;
    protected $Municipalities;
    protected $Cities;
    protected $CustomerGroups;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Zones = $this->fetchTable('Zones');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->ZoneShowrooms = $this->fetchTable('ZoneShowrooms');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->OrderItemAttributes = $this->fetchTable('OrderItemAttributes');
        $this->Customers = $this->fetchTable('Customers');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->Users = $this->fetchTable('Users');
        $this->Drivers = $this->fetchTable('Drivers');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentItems = $this->fetchTable('ShipmentItems');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->DeliveryPartners = $this->fetchTable('DeliveryPartners');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->ShipmentOrderItems = $this->fetchTable('ShipmentOrderItems');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->Cities = $this->fetchTable('Cities');
        $this->CustomerGroups = $this->fetchTable('CustomerGroups');
    }
    
    public function index()
    {
        $shipmentsQuery = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => function ($q) {
                    return $q
                     ->where(['ShipmentOrders.status' => 'A'])
                     ->contain([
                        'Cities',
                        'Zones',
                        'Municipalities',
                        'Orders' => function ($q2) {
                            return $q2->select(['id', 'delivery_mode_type']);
                        }
                    ]);
                },
                'Drivers' => function ($q) {
                    return $q->leftJoinWith('Users')
                        ->select([
                            'Drivers.id',
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name',
                            'full_name' => $q->func()->concat([
                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                            ])
                        ]);
                },
                'DeliveryPartners'
            ])
            ->select([
                'Shipments.id',
                'Shipments.sender_type',
                'Shipments.senderID',
                'Shipments.created',
                'Shipments.delivery_status',
                'Shipments.driver_id',
                'Shipments.delivery_partner_id',
                'sender_name' => $this->Shipments->find()
                    ->newExpr()
                    ->add("(CASE 
                        WHEN Shipments.sender_type = 'showroom' 
                        THEN (SELECT name FROM showrooms WHERE showrooms.id = Shipments.senderID) 
                        WHEN Shipments.sender_type = 'warehouse' 
                        THEN (SELECT name FROM warehouses WHERE warehouses.id = Shipments.senderID) 
                        ELSE 'N/A' 
                    END)"),
                'partner_name' => $this->Shipments->find()
                    ->newExpr()
                    ->add("(CASE 
                        WHEN Shipments.delivery_partner_id IS NOT NULL 
                        THEN DeliveryPartners.partner_name 
                        ELSE NULL 
                    END)")
            ])
            ->where(['Shipments.status' => 'A'])
            ->enableAutoFields(true)
            ->leftJoinWith('ShipmentOrders.Orders')
            ->group(['Shipments.id']) // ✅ prevents duplicates
            ->order([
                '(CASE WHEN Orders.delivery_mode_type = "express" THEN 0 ELSE 1 END)' => 'ASC',
                'Shipments.created' => 'DESC'
            ]);

        $shipments = $shipmentsQuery->toArray();

        foreach ($shipments as $shipment) {
            $shipment->can_delete = (strtolower($shipment->shipment_status) === 'pending'
            ) &&
            empty($shipment->driver_id) &&
            empty($shipment->delivery_partner_id);
        }

        $cities = $this->Cities->find()
            ->order(['Cities.city_name' => 'ASC'])
            ->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('shipments', 'cities', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }

    // public function fetchOrderItemsByShipmentId()
    // {
    //     $this->request->allowMethod(['post']);
    //     $shipmentId = $this->request->getData('shipment_id');

    //     $subQuery = "(SELECT product_attribute_id 
    //           FROM order_item_attributes 
    //           WHERE order_item_attributes.order_item_id = OrderItems.id 
    //             AND order_item_attributes.status = 'A' 
    //           LIMIT 1)";

    //     $shipment = $this->Shipments->find()
    //         ->contain([
    //             'ShipmentOrders' => [
    //                 'CustomerAddresses',
    //                 'ShipmentOrderItems' => function ($q) {
    //                     return $q->where(['ShipmentOrderItems.status' => 'A']);
    //                 },
    //                 'ShipmentOrderItems.OrderItems' => function (Query $q) use ($subQuery) {
    //                     return $q->select([
    //                             'OrderItems.id',
    //                             'OrderItems.product_id',
    //                             'OrderItems.product_variant_id',
    //                             'OrderItems.quantity',
    //                             'product_attribute_id' => $q->newExpr($subQuery)
    //                         ])
    //                         ->contain([
    //                             'Products',
    //                             'ProductVariants',
    //                             'Orders' => [
    //                                 'Customers' => function ($q) {
    //                                     return $q->contain(['Users'])->select([
    //                                         'Customers.id',
    //                                         'Users.first_name',
    //                                         'Users.last_name',
    //                                         'full_name' => $q->func()->concat([
    //                                             'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
    //                                         ])
    //                                     ]);
    //                                 }
    //                             ]
    //                         ]);
    //                 }
    //             ]
    //         ])
    //         ->where(['Shipments.id' => $shipmentId])
    //         ->first();


    //     if (!$shipment) {
    //         return $this->response->withType('application/json')
    //             ->withStringBody(json_encode(['success' => false, 'message' => 'Shipment not found']));
    //     }

    //     $orderItemsData = [];
    //     foreach ($shipment->shipment_orders as $shipmentOrder) {
    //         foreach ($shipmentOrder->shipment_order_items as $shipmentOrderItem) {
    //             $orderItem = $shipmentOrderItem->order_item ?? null;
    //             if ($orderItem) {
    //                 $order = $orderItem->order ?? null;
    //                 $product = $orderItem->product ?? null;
    //                 $productVariant = $orderItem->product_variant ?? null;
    //                 $customer = $order->customer ?? null;
    //                 $user = $customer->user ?? null;

    //                 // Initialize attribute info
    //                 $productAttribute = ['attribute_name' => 'N/A', 'attribute_value' => 'N/A'];

    //                 // Fetch product attribute name and value from shipment item
    //                 if (!empty($shipmentOrderItem->product_attribute_id)) {
    //                     $attribute = $this->ProductAttributes->find()
    //                         ->where(['ProductAttributes.id' => $shipmentOrderItem->product_attribute_id])
    //                         ->contain([
    //                             'Attributes' => [
    //                                 'fields' => ['Attributes.name']
    //                             ],
    //                             'AttributeValues' => [
    //                                 'fields' => ['AttributeValues.value']
    //                             ]
    //                         ])
    //                         ->first();

    //                     if ($attribute) {
    //                         $productAttribute = [
    //                             'attribute_name' => $attribute->attribute->name ?? 'N/A',
    //                             'attribute_value' => $attribute->attribute_value->value ?? 'N/A'
    //                         ];
    //                     }
    //                 }

    //                 $orderItemsData[] = [
    //                     'order_id' => $order->id ?? 'N/A',
    //                     'customer_name' => ($user->first_name ?? '') . ' ' . ($user->last_name ?? ''),
    //                     'sku' => $orderItem->product_variant_id ? ($productVariant->sku ?? 'N/A') : ($product->sku ?? 'N/A'),
    //                     'product_name' => $product->name ?? 'N/A',
    //                     'variants' => $orderItem->product_variant_id ? ($productVariant->variant_name ?? 'N/A') : 'N/A',
    //                     'attribute_name' => $productAttribute['attribute_name'],
    //                     'attribute_value' => $productAttribute['attribute_value'],
    //                     'ordered_quantity' => $shipmentOrderItem->quantity ?? 0,
    //                     'shipped_quantity' => $shipmentOrderItem->shipped_quantity ?? 0,
    //                     'delivery_status' => $shipmentOrderItem->item_delivery_status ?? 'Pending'
    //                 ];
    //             }
    //         }
    //     }

    //     return $this->response->withType('application/json')
    //         ->withStringBody(json_encode(['success' => true, 'data' => $orderItemsData]));
    // }

    public function fetchOrderItemsByShipmentId()
    {
        $this->request->allowMethod(['post']);
        $shipmentId = $this->request->getData('shipment_id');

        $shipment = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => [
                    'CustomerAddresses',
                    'ShipmentOrderItems' => function ($q) {
                        return $q->where(['ShipmentOrderItems.status' => 'A']);
                    },
                    'ShipmentOrderItems.OrderItems' => function (Query $q) {
                        return $q->select([
                                'OrderItems.id',
                                'OrderItems.product_id',
                                'OrderItems.product_variant_id',
                                'OrderItems.product_attribute_id', // direct from OrderItems
                                'OrderItems.quantity'
                            ])
                            ->contain([
                                'Products',
                                'ProductVariants',
                                'Orders' => [
                                    'Customers' => function ($q) {
                                        return $q->contain(['Users'])->select([
                                            'Customers.id',
                                            'Users.first_name',
                                            'Users.last_name',
                                            'full_name' => $q->func()->concat([
                                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                                            ])
                                        ]);
                                    }
                                ]
                            ]);
                    }
                ]
            ])
            ->where(['Shipments.id' => $shipmentId])
            ->first();

        if (!$shipment) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => false, 'message' => 'Shipment not found']));
        }

        $orderItemsData = [];
        foreach ($shipment->shipment_orders as $shipmentOrder) {
            foreach ($shipmentOrder->shipment_order_items as $shipmentOrderItem) {
                $orderItem = $shipmentOrderItem->order_item ?? null;
                if ($orderItem) {
                    $order = $orderItem->order ?? null;
                    $product = $orderItem->product ?? null;
                    $productVariant = $orderItem->product_variant ?? null;
                    $customer = $order->customer ?? null;
                    $user = $customer->user ?? null;

                    // Initialize attribute info
                    $productAttribute = ['attribute_name' => 'N/A', 'attribute_value' => 'N/A'];

                    // Fetch product attribute name and value from OrderItem's product_attribute_id
                    if (!empty($orderItem->product_attribute_id)) {
                        $attribute = $this->ProductAttributes->find()
                            ->where(['ProductAttributes.id' => $orderItem->product_attribute_id])
                            ->contain([
                                'Attributes' => ['fields' => ['Attributes.name']],
                                'AttributeValues' => ['fields' => ['AttributeValues.value']]
                            ])
                            ->first();

                        if ($attribute) {
                            $productAttribute = [
                                'attribute_name' => $attribute->attribute->name ?? 'N/A',
                                'attribute_value' => $attribute->attribute_value->value ?? 'N/A'
                            ];
                        }
                    }

                    $orderItemsData[] = [
                        'order_id' => $order->id ?? 'N/A',
                        'customer_name' => ($user->first_name ?? '') . ' ' . ($user->last_name ?? ''),
                        'sku' => $orderItem->product_variant_id ? ($productVariant->sku ?? 'N/A') : ($product->sku ?? 'N/A'),
                        'product_name' => $product->name ?? 'N/A',
                        'variants' => $orderItem->product_variant_id ? ($productVariant->variant_name ?? 'N/A') : 'N/A',
                        'attribute_name' => $productAttribute['attribute_name'],
                        'attribute_value' => $productAttribute['attribute_value'],
                        'ordered_quantity' => $shipmentOrderItem->quantity ?? 0,
                        'shipped_quantity' => $shipmentOrderItem->shipped_quantity ?? 0,
                        'delivery_status' => $shipmentOrderItem->item_delivery_status ?? 'Pending',
                        'delivery_status_date' => $shipmentOrderItem->delivery_status_date->format('Y-m-d') ?? 'N/A'
                    ];
                }
            }
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'data' => $orderItemsData]));
    }

    public function add()
    {
        $today = FrozenDate::today(); // Get today's date

        $orders = $this->Orders->find()
            ->select([
                'order_id' => 'Orders.id',
                'customer_id' => 'Orders.customer_id',
                'customer_address_id' => 'Orders.customer_address_id',
                'city_id' => 'CustomerAddresses.city_id',
                'municipality_id' => 'CustomerAddresses.municipality_id',
                'zone_id' => 'Zones.id',
                'express_delivery' => $this->Orders->find()->newExpr()->add([
                    "CASE 
                        WHEN Orders.delivery_mode_type = 'express' THEN 'Yes' 
                        ELSE 'No' 
                    END"
                ]),
                'delivery_mode_type' => 'Orders.delivery_mode_type',
                'customer_name' => $this->Orders->find()->func()->concat([
                    'Users.first_name' => 'identifier',
                    ' ',
                    'Users.last_name' => 'identifier'
                ])->setReturnType('string'),
                'city' => 'Cities.city_name',
                'zone' => 'Zones.name',
                'municipality' => 'Municipalities.name',
                'order_date' => 'Orders.order_date',
                'expected_delivery_date' => 'Orders.delivery_date',
                'showroom_name' => 'Showrooms.name',
                'customer_groups' => $this->Orders->find()->func()->group_concat([
                    'DISTINCT CustomerGroups.name' => 'identifier'
                ], ['separator' => ', '])
            ])
            ->leftJoinWith('Customers')
            ->leftJoinWith('Customers.Users')
            ->leftJoinWith('CustomerAddresses')
            ->leftJoinWith('CustomerAddresses.Cities')
            ->leftJoinWith('CustomerAddresses.Municipalities')
            ->leftJoinWith('CustomerAddresses.Municipalities.ZoneMunicipalities')
            ->leftJoinWith('CustomerAddresses.Municipalities.ZoneMunicipalities.Zones')
            ->leftJoinWith('Showrooms')
            ->leftJoinWith('Customers.CustomerGroupMappings.CustomerGroups', function ($q) {
                return $q->where(['CustomerGroupMappings.status' => 'A']);
            })
            ->leftJoinWith('Transactions') // Join with transactions table
            ->where([
                'Orders.shipment_status IN' => ['Unassigned', 'Partially Assigned'],
                'Orders.delivery_mode' => 'delivery',
                'Orders.status' => 'Approved',
                'Orders.delivery_date IS NOT' => null,
                'OR' => [
                    'Transactions.payment_method' => 'Cash on Delivery',
                    [
                        'Transactions.payment_method !=' => 'Cash on Delivery',
                        'Transactions.payment_status' => 'Paid'
                    ]
                ]
            ])
            ->group(['Orders.id'])
            ->distinct(['Orders.id'])
            ->order([
                $this->Orders->find()->newExpr()->add([
                    "CASE 
                        WHEN Orders.delivery_mode_type = 'express' THEN 1 
                        ELSE 2 
                    END"
                ]),
                'Orders.delivery_date' => 'ASC'
            ])
            ->toArray();

        $zones = $this->Zones->find()
            ->where(['Zones.status' => 'A'])
            ->order(['Zones.name' => 'ASC'])
            ->toArray();

        $municipalities = $this->Municipalities->find()
            ->where(['Municipalities.status' => 'A'])
            ->order(['Municipalities.name' => 'ASC'])
            ->toArray();

        $cities = $this->Cities->find()
            ->order(['Cities.city_name' => 'ASC'])
            ->toArray();

        $customer_groups = $this->CustomerGroups->find()
            ->where(['CustomerGroups.status' => 'A'])
            ->order(['CustomerGroups.name' => 'ASC'])
            ->toArray();

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();

        $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

        $this->set(compact('showrooms', 'warehouses', 'orders', 'zones', 'municipalities', 'cities', 'customer_groups'));
    }

    public function fetchOrderItems()
    {
        $this->request->allowMethod(['post', 'ajax']);

        $orderId = $this->request->getData('order_id');
        $senderType = $this->request->getData('sender_type');
        $senderId = $this->request->getData('sender_id');

        if (!$orderId || !$senderId) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => false, 'message' => 'Invalid request']));
        }

        // Fetch Order and check shipment status
        $order = $this->Orders->find()
            ->select(['shipment_status'])
            ->where(['id' => $orderId])
            ->first();

        // Fetch order items directly including product_attribute_id from order_items table
        $orderItems = $this->OrderItems->find()
            ->select([
                'OrderItems.id',
                'OrderItems.product_id',
                'OrderItems.quantity',
                'OrderItems.product_variant_id',
                'OrderItems.product_attribute_id', // Now fetched directly
                'Products.id',
                'Products.name',
                'Products.sku',
                'ProductVariants.id',
                'ProductVariants.variant_name',
                'ProductVariants.sku'
            ])
            ->leftJoinWith('Products')
            ->leftJoinWith('ProductVariants')
            ->where(['OrderItems.order_id' => $orderId])
            ->toArray();

        $data = [];
        foreach ($orderItems as $item) {
            // Define stock conditions
            $stockConditions = [
                'product_id' => $item->product_id,
                ($senderType === 'warehouse' ? 'warehouse_id' : 'showroom_id') => $senderId,
            ];

            if (!empty($item->product_variant_id)) {
                $stockConditions['product_variant_id'] = $item->product_variant_id;
            }

            if (!empty($item->product_attribute_id)) {
                $stockConditions['product_attribute_id'] = $item->product_attribute_id;
            }

            // Fetch stock
            $stock = $this->ProductStocks->find()
                ->where($stockConditions)
                ->first();

            // Get shipped quantity if order is partially assigned
            $shippedQuantity = 0;
            if ($order->shipment_status === 'Partially Assigned') {
                $shippedQuantityResult = $this->ShipmentOrderItems->find()
                    ->select(['total_shipped' => $this->ShipmentOrderItems->find()->func()->sum('quantity')])
                    ->where(['order_item_id' => $item->id, 'status' => 'A'])
                    ->first();

                $shippedQuantity = $shippedQuantityResult ? $shippedQuantityResult->total_shipped : 0;
            }

            // Get attribute name & value if product_attribute_id is present
            $attributeName = '';
            $attributeValue = '';
            if (!empty($item->product_attribute_id)) {
                $attribute = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                if ($attribute) {
                    $attributeName = $attribute->attribute->name ?? '';
                    $attributeValue = $attribute->attribute_value->value ?? '';
                }
            }

            $data[] = [
                'id' => $item->id,
                'sku' => $item->product_variant_id ? $item->_matchingData['ProductVariants']['sku'] : $item->_matchingData['Products']['sku'],
                'product_name' => $item->_matchingData['Products']['name'],
                'variants' => $item->product_variant_id ? $item->_matchingData['ProductVariants']['variant_name'] : 'N/A',
                'attribute_name' => $attributeName,
                'attribute_value' => $attributeValue,
                'ordered_quantity' => $item->quantity,
                'shipped_quantity' => $shippedQuantity ?? 0,
                'product_stock_id' => $stock->id ?? null,
                'in_stock' => $stock ? intval($stock->quantity) - intval($stock->reserved_stock) : 0
            ];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'data' => $data]));
    }


    /** SINGLE SHIPMENT FOR MULTIPLE ORDER **/
    // public function createShipment()
    // {
    //     $this->request->allowMethod(['post']);

    //     $data = $this->request->getData();

    //     if (empty($data['orders'])) {
    //         return $this->response->withType('application/json')
    //             ->withStringBody(json_encode(['error' => 'No orders selected']));
    //     }

    //     $shipment = $this->Shipments->newEmptyEntity();
        
    //     $shipment->sender_type = $data['sender']; // showroom or warehouse
    //     $shipment->senderID = $data['senderId'];
    //     $shipment->shipment_status = 'Pending';
    //     $shipment->delivery_status = 'Pending';

    //     if (!$this->Shipments->save($shipment)) {

    //         return $this->response->withType('application/json')
    //             ->withStringBody(json_encode(['error' => 'Failed to create shipment']));
    //     }

    //     foreach ($data['orders'] as $order) {
    //         $shipmentOrder = $this->ShipmentOrders->newEmptyEntity();
    //         $shipmentOrder->shipment_id = $shipment->id;
    //         $shipmentOrder->order_id = $order['order_id'];
    //         $shipmentOrder->customer_id = $order['customer_id'] ?? null;
    //         $shipmentOrder->customer_address_id = $order['customer_address_id'] ?? null;
    //         $shipmentOrder->city_id = $order['city_id'] ?? null;
    //         $shipmentOrder->municipality_id = $order['municipality_id'] ?? null;
    //         $shipmentOrder->zone_id = $order['zone_id'] ?? null;

    //         if ($this->ShipmentOrders->save($shipmentOrder)) {
    //             foreach ($order['items'] as $item) {
    //                 $shipmentOrderItem = $this->ShipmentOrderItems->newEmptyEntity();
    //                 $shipmentOrderItem->shipment_order_id = $shipmentOrder->id;
    //                 $shipmentOrderItem->order_item_id = $item['order_item_id'];
    //                 $shipmentOrderItem->quantity = $item['quantity'];

    //                 if ($this->ShipmentOrderItems->save($shipmentOrderItem)) {
    //                     // Update the reserved stock in product_stocks table
    //                     $this->ProductStocks->updateAll(
    //                         ['reserved_stock = reserved_stock + ' . (int)$item['quantity']], // Increment reserved stock
    //                         ['id' => $item['product_stock_id']] // Update specific product stock
    //                     );
    //                 }
    //             }

    //             // Check if the order status needs to be updated
    //             $this->updateOrderShipmentStatus($order['order_id']);
    //         }
    //     }

    //     // Send Email Notification
    //     $this->sendShipmentNotificationEmail($shipment);

    //     return $this->response->withType('application/json')
    //         ->withStringBody(json_encode(['success' => 'Shipment created successfully']));
    // }

    public function createShipment()
    {
        $this->request->allowMethod(['post']);

        $data = $this->request->getData();

        if (empty($data['orders'])) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => 'No orders selected']));
        }

        $ordersWithInsufficientStock = [];
        $ordersToProcess = [];

        // Temporary map of reserved stocks during loop (simulate reservation before DB update)
        $tempReservedStock = [];

        foreach ($data['orders'] as $order) {
            $hasStock = true;

            foreach ($order['items'] as $item) {
                $stockId = $item['product_stock_id'];
                $requiredQty = $item['quantity'];

                if (!isset($tempReservedStock[$stockId])) {
                    // Fetch initial stock from DB only once
                    $stock = $this->ProductStocks->get($stockId);
                    $tempReservedStock[$stockId] = $stock->reserved_stock;
                    $totalAvailable[$stockId] = $stock->quantity;
                }

                $currentReserved = $tempReservedStock[$stockId];
                $available = $totalAvailable[$stockId] - $currentReserved;

                if ($available < $requiredQty) {
                    $hasStock = false;
                    break;
                }

                // Simulate reservation
                $tempReservedStock[$stockId] += $requiredQty;
            }

            if ($hasStock) {
                $ordersToProcess[] = $order;
            } else {
                $ordersWithInsufficientStock[] = $order['order_id'];
            }
        }

        if (empty($ordersToProcess)) {
            $orderIdsText = implode(', ', $ordersWithInsufficientStock);
            $sender = strtolower($data['sender']) === 'warehouse' ? __('selected warehouse') : __('selected showroom');
            
            $message = __('There are not enough stocks to create shipment for Order ID(s): {0} with the {1}.', $orderIdsText, $sender);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => $message]));
        }

        // Proceed to create shipment for valid orders
        $shipment = $this->Shipments->newEmptyEntity();
        
        $shipment->sender_type = $data['sender']; // showroom or warehouse
        $shipment->senderID = $data['senderId'];
        $shipment->shipment_status = 'Pending';
        $shipment->delivery_status = 'Pending';

        if (!$this->Shipments->save($shipment)) {

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => 'Failed to create shipment']));
        }

        foreach ($ordersToProcess as $order) {
            $shipmentOrder = $this->ShipmentOrders->newEmptyEntity();
            $shipmentOrder->shipment_id = $shipment->id;
            $shipmentOrder->order_id = $order['order_id'];
            $shipmentOrder->customer_id = $order['customer_id'] ?? null;
            $shipmentOrder->customer_address_id = $order['customer_address_id'] ?? null;
            $shipmentOrder->city_id = $order['city_id'] ?? null;
            $shipmentOrder->municipality_id = $order['municipality_id'] ?? null;
            $shipmentOrder->zone_id = $order['zone_id'] ?? null;

            if ($this->ShipmentOrders->save($shipmentOrder)) {
                foreach ($order['items'] as $item) {
                    $shipmentOrderItem = $this->ShipmentOrderItems->newEmptyEntity();
                    $shipmentOrderItem->shipment_order_id = $shipmentOrder->id;
                    $shipmentOrderItem->order_item_id = $item['order_item_id'];
                    $shipmentOrderItem->quantity = $item['quantity'];

                    if ($this->ShipmentOrderItems->save($shipmentOrderItem)) {
                        // Update the reserved stock in product_stocks table
                        $this->ProductStocks->updateAll(
                            ['reserved_stock = reserved_stock + ' . (int)$item['quantity']], // Increment reserved stock
                            ['id' => $item['product_stock_id']] // Update specific product stock
                        );
                    }
                }

                // Check if the order status needs to be updated
                $this->updateOrderShipmentStatus($order['order_id']);
            }
        }

        // Send Email Notification
        $this->sendShipmentNotificationEmail($shipment);

        $responseMessage = __('Shipment created successfully.');
        if (!empty($ordersWithInsufficientStock)) {
            $skippedOrders = implode(', ', $ordersWithInsufficientStock);
            $stockWarning = __('There are not enough stocks to create shipment for Order IDs: {0} with selected warehouse or showroom.', $skippedOrders);
            $responseMessage .= ' ' . $stockWarning;
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => $responseMessage]));

    }

    private function updateOrderShipmentStatus($orderId)
    {
        // Fetch total quantities from order_items
        $orderItems = $this->OrderItems->find()
            ->select(['order_id', 'id', 'quantity'])
            ->where(['order_id' => $orderId])
            ->toArray();

        $totalOrderQuantities = [];
        foreach ($orderItems as $orderItem) {
            $totalOrderQuantities[$orderItem->id] = $orderItem->quantity;
        }

        // Fetch total quantities already assigned in shipment_order_items
        $shippedItems = $this->ShipmentOrderItems->find()
            ->select(['order_item_id', 'quantity'])
            ->where(['order_item_id IN' => array_keys($totalOrderQuantities)])
            ->toArray();

        $shippedQuantities = [];
        foreach ($shippedItems as $shippedItem) {
            if (!isset($shippedQuantities[$shippedItem->order_item_id])) {
                $shippedQuantities[$shippedItem->order_item_id] = 0;
            }
            $shippedQuantities[$shippedItem->order_item_id] += $shippedItem->quantity;
        }

        // Determine the new order shipment status
        $allAssigned = true;
        foreach ($totalOrderQuantities as $orderItemId => $orderQuantity) {
            $currentShippedQuantity = $shippedQuantities[$orderItemId] ?? 0;

            if ($currentShippedQuantity < $orderQuantity) {
                $allAssigned = false;
                break;
            }
        }

        if ($allAssigned) {
            $this->Orders->updateAll(['shipment_status' => 'Assigned'], ['id' => $orderId]);
        } else {
            $this->Orders->updateAll(['shipment_status' => 'Partially Assigned'], ['id' => $orderId]);
        }
    }

    public function updateStatus()
    {
        $this->request->allowMethod(['post']);

        $response = [
            'success' => false,
            'message' => 'Unable to update shipment status. Please try again.'
        ];

        try {
            $shipmentId = $this->request->getData('shipment_id');
            $status = $this->request->getData('status');

            $shipment = $this->Shipments->get($shipmentId);
            $shipment->delivery_status = $status;

            if ($this->Shipments->save($shipment)) {

                // Get shipment orders for this shipment
                $shipmentOrders = $this->ShipmentOrders->find()
                    ->where(['shipment_id' => $shipmentId])
                    ->all();

                foreach ($shipmentOrders as $order) {
                    $order->order_delivery_status = $status;
                    $this->ShipmentOrders->save($order);

                    // For each shipment order, update items
                    $shipmentOrderItems = $this->ShipmentOrderItems->find()
                        ->where(['shipment_order_id' => $order->id])
                        ->all();

                    foreach ($shipmentOrderItems as $item) {
                        $item->item_delivery_status = $status;
                        $this->ShipmentOrderItems->save($item);
                    }
                }

                $response = [
                    'success' => true,
                    'message' => 'Shipment status updated successfully.'
                ];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($response));
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }

            return $this->redirect(['action' => 'index']);
        }
    }

    private function sendShipmentNotificationEmail($shipment)
    {
        $toEmails = [];

        if ($shipment->sender_type === 'Showroom') {
            // Fetch Showroom Manager & Supervisor
            $showroom = $this->Showrooms->get($shipment->senderID, [
                'contain' => ['ShowroomManager', 'ShowroomSupervisor']
            ]);

            $managerEmail = $showroom->manager->email ?? null;
            $supervisorEmail = $showroom->supervisor->email ?? null;

            if ($managerEmail) {
                $toEmails[] = $managerEmail;
            }

            if ($supervisorEmail) {
                $toEmails[] = $supervisorEmail;
            }

            $locationType = "Showroom";
            $locationName = $showroom->name ?? 'N/A';

        } elseif ($shipment->sender_type === 'Warehouse') {
            // Fetch Warehouse Manager
            $warehouse = $this->Warehouses->get($shipment->senderID, [
                'contain' => ['Managers']
            ]);

            $warehouseManagerName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            $toEmails = array_filter([$warehouseManagerEmail]);

            $locationType = "Warehouse";
            $locationName = $warehouse->name ?? 'N/A';
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for shipment ID: " . $shipment->id);
            return;
        }

        $adminViewUrl = Router::url([
            'controller' => 'Shipment',
            'action' => 'view',
            $shipment->id,
            '_full' => true
        ]);

        $emailData = [
            'shipment_id' => $shipment->id,
            'shipment_status' => ucfirst($shipment->shipment_status),
            'from_location' => "{$locationType}: {$locationName}",
            'greeting' => "Dear {$warehouseManagerName},",
            'shipment_date' => $shipment->created ? $shipment->created->format('d-m-Y') : 'N/A',
            'admin_view_url' => $adminViewUrl,
        ];

        $subject = "New Shipment Created - #{$shipment->id}";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'shipment_notification',
            $emailData
        );
    }

    public function view($id = null)
    {
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $shipment = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => ['Cities', 'Zones', 'Municipalities'],
                'Drivers' => function (Query $q) {
                    return $q->leftJoinWith('Users')
                        ->select([
                            'Drivers.id',
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name',
                            'full_name' => $q->func()->concat([
                                'Users.first_name' => 'identifier',
                                ' ',
                                'Users.last_name' => 'identifier',
                            ]),
                        ]);
                },
                'DeliveryPartners',
            ])
            ->select([
                'Shipments.id',
                'Shipments.sender_type',
                'Shipments.senderID',
                'Shipments.created',
                'Shipments.shipment_status',
                'Shipments.delivery_status',
                'Shipments.driver_id',
                'Shipments.delivery_partner_id',
                'sender_name' => $this->Shipments->find()
                    ->newExpr()
                    ->add("(CASE 
                            WHEN Shipments.sender_type = 'showroom' 
                            THEN (SELECT name FROM showrooms WHERE showrooms.id = Shipments.senderID) 
                            WHEN Shipments.sender_type = 'warehouse' 
                            THEN (SELECT name FROM warehouses WHERE warehouses.id = Shipments.senderID) 
                            ELSE 'N/A' 
                        END)"),
                'partner_name' => $this->Shipments->find()
                    ->newExpr()
                    ->add("(CASE 
                            WHEN Shipments.delivery_partner_id IS NOT NULL 
                            THEN DeliveryPartners.partner_name 
                            ELSE NULL 
                        END)"),
            ])
            ->where(['Shipments.id' => $id])
            ->first();

        $shipment_items = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => [
                    'CustomerAddresses',
                    'ShipmentOrderItems' => function ($q) {
                        return $q->where(['ShipmentOrderItems.status' => 'A']);
                    },
                    'ShipmentOrderItems.OrderItems' => function (Query $q) {
                        return $q->select([
                                'OrderItems.id',
                                'OrderItems.product_id',
                                'OrderItems.product_variant_id',
                                'OrderItems.quantity',
                                'OrderItems.product_attribute_id'
                            ])
                            ->contain([
                                'Products',
                                'ProductVariants',
                                'Orders' => function ($q) {
                                    return $q->contain(['Customers' => function ($q) {
                                        return $q->contain(['Users'])->select([
                                            'Customers.id',
                                            'Users.first_name',
                                            'Users.last_name',
                                            'full_name' => $q->func()->concat([
                                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                                            ])
                                        ]);
                                    }]);
                                }
                            ]);
                    }
                ]
            ])
            ->where(['Shipments.id' => $id])
            ->first();

        $orderItemsData = [];
        foreach ($shipment_items->shipment_orders as $shipmentOrder) {

            $customerAddress = $shipmentOrder->customer_address ?? null;
            if ($customerAddress) {
                $addressParts = array_filter([
                    isset($customerAddress->address_line1) && !ctype_space($customerAddress->address_line1) ? $customerAddress->address_line1 : null,
                    isset($customerAddress->address_line2) && !ctype_space($customerAddress->address_line2) ? $customerAddress->address_line2 : null,
                    isset($customerAddress->landmark) && !ctype_space($customerAddress->landmark) ? $customerAddress->landmark : null,
                    isset($customerAddress->zipcode) && !ctype_space($customerAddress->zipcode) ? $customerAddress->zipcode : null,
                ]);

                $fullAddress = !empty($addressParts) ? implode(', ', $addressParts) : 'N/A';
            } else {
                $fullAddress = 'N/A';
            }

            foreach ($shipmentOrder->shipment_order_items as $shipmentOrderItem) {
                $orderItem = $shipmentOrderItem->order_item ?? null;
                if ($orderItem) {
                    $order = $orderItem->order ?? null;
                    $product = $orderItem->product ?? null;
                    $productVariant = $orderItem->product_variant ?? null;
                    $customer = $order->customer ?? null;
                    $user = $customer->user ?? null;

                    // Initialize attribute info
                    $productAttribute = ['attribute_name' => 'N/A', 'attribute_value' => 'N/A'];

                    // Fetch product attribute name and value if product_attribute_id is available
                    if (!empty($orderItem->product_attribute_id)) {
                        $attribute = $this->ProductAttributes->find()
                            ->where(['ProductAttributes.id' => $orderItem->product_attribute_id])
                            ->contain([
                                'Attributes' => [
                                    'fields' => ['Attributes.name']
                                ],
                                'AttributeValues' => [
                                    'fields' => ['AttributeValues.value']
                                ]
                            ])
                            ->first();

                        if ($attribute) {
                            $productAttribute = [
                                'attribute_name' => $attribute->attribute->name ?? 'N/A',
                                'attribute_value' => $attribute->attribute_value->value ?? 'N/A'
                            ];
                        }
                    }

                    $orderItemsData[] = [
                        'order_id' => $order->id ?? 'N/A',
                        'express_delivery' => $order->delivery_mode_type ?? 'N/A',
                        'customer_name' => ($user->first_name ?? '') . ' ' . ($user->last_name ?? ''),
                        'customer_address' => $fullAddress,
                        'sku' => $orderItem->product_variant_id ? ($productVariant->sku ?? 'N/A') : ($product->sku ?? 'N/A'),
                        'product_name' => $product->name ?? 'N/A',
                        'variants' => $orderItem->product_variant_id ? ($productVariant->variant_name ?? 'N/A') : 'N/A',
                        'attribute_name' => $productAttribute['attribute_name'],
                        'attribute_value' => $productAttribute['attribute_value'],
                        'shipped_quantity' => $shipmentOrderItem->quantity ?? 0,
                        'delivery_status' => $shipmentOrderItem->item_delivery_status ?? 'Pending',
                        'delivery_time' => $shipmentOrderItem->delivery_status_date ?? 'N/A',
                        'delivery_proof' => $shipmentOrderItem->proof_of_delivery ?? 'N/A',
                        'comments' => $shipmentOrderItem->driver_comments ?? 'N/A',
                        'cash_collected' => number_format((float)$shipmentOrder->cash_collected, 0, '', $thousandSeparator) . ' ' . $currencySymbol ?? 'N/A',
                    ];
                }
            }
        }

        $this->set(compact('shipment', 'orderItemsData'));
    }

    public function edit($id = null)
    {

        $shipment_data = $this->Shipments->get($id, []);

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            // Normalize delivery_type and set IDs accordingly
            if (!empty($data['delivery_type']) && strtolower($data['delivery_type']) === 'driver') {
                $data['delivery_type'] = 'Driver';

                // If driver_id is '-- Select Driver --', set it to null
                if (isset($data['driver_id']) && $data['driver_id'] === '-- Select Driver --') {
                    $data['driver_id'] = null;
                }

                $data['delivery_partner_id'] = null;
            } else {
                $data['delivery_type'] = 'Delivery Partner';

                // If delivery_partner_id is '-- Select Delivery Partner --', set it to null
                if (isset($data['delivery_partner_id']) && $data['delivery_partner_id'] === '-- Select Delivery Partner --') {
                    $data['delivery_partner_id'] = null;
                }

                $data['driver_id'] = null;
            }

            $previousDriverId = $shipment_data->driver_id ?? null;

            // Prevent delivery_status_date from being patched into the Shipments entity
            $shipment = $this->Shipments->patchEntity($shipment_data, $data, [
                'accessibleFields' => ['delivery_status_date' => false]
            ]);

            // Save the updated shipment
            if ($this->Shipments->save($shipment)) {

                // Process each shipment_order_item
                foreach ($data['shipment_order_item_id'] as $index => $itemId) {
                    $productId = $data['product_id'][$index] ?? null;
                    $variantId = $data['product_variant_id'][$index] ?? null;
                    $attributeId = $data['product_attribute_id'][$index] ?? null;
                    $quantity = $data['quantity'][$index] ?? 0;
                    $remove = !empty($data['remove'][$index]);

                    if ($remove) {

                        // Fetch the shipment_order_item
                        $shipmentOrderItem = $this->ShipmentOrderItems->get($itemId);

                        $previousQuantity = $shipmentOrderItem->quantity;

                        // Build base conditions
                        $conditions = [
                            'product_id' => $productId,
                        ];

                        // Add location-specific condition
                        if ($data['sender_type'] === 'Showroom' && !empty($data['showroom_id'])) {
                            $conditions['showroom_id'] = $data['showroom_id'];
                        } elseif ($data['sender_type'] === 'Warehouse' && !empty($data['warehouse_id'])) {
                            $conditions['warehouse_id'] = $data['warehouse_id'];
                        } else {
                            return; // Invalid sender type or missing ID
                        }

                        // Add variant condition if applicable
                        if (!empty($variantId)) {
                            $conditions['product_variant_id'] = $variantId;
                        }

                        // Add attribute condition if applicable
                        if (!empty($attributeId)) {
                            $conditions['product_attribute_id'] = $attributeId;
                        }

                        // Find and update reserved stock before deleting
                        $stock = $this->ProductStocks->find()->where($conditions)->first();
                        if ($stock) {
                            $stock->reserved_stock = max(0, $stock->reserved_stock - $previousQuantity);
                            $this->ProductStocks->save($stock);
                        }

                        // Delete the shipment_order_item
                        // $this->ShipmentOrderItems->delete($shipmentOrderItem);
                        $shipmentOrderItem->status = 'D';
                        $this->ShipmentOrderItems->save($shipmentOrderItem);

                        // ✅ Update the associated order's shipment_status to "Partially Assigned"
                        $shipmentOrder = $this->Shipments->ShipmentOrders->get($shipmentOrderItem->shipment_order_id);
                        if ($shipmentOrder && $shipmentOrder->order_id) {
                            $order = $this->Shipments->ShipmentOrders->Orders->get($shipmentOrder->order_id);
                            if ($order) {
                                $order->shipment_status = 'Partially Assigned';
                                $this->Shipments->ShipmentOrders->Orders->save($order);
                            }
                        }

                    } else {

                        // Update shipment_order_item details if necessary
                        $shipmentOrderItem = $this->ShipmentOrderItems->get($itemId);

                        // Convert delivery_status_date if available
                        $deliveryDate = !empty($data['delivery_status_date'][$index])
                            ? FrozenTime::createFromFormat('Y-m-d', $data['delivery_status_date'][$index])
                            : null;

                        $previousQuantity = $shipmentOrderItem->quantity;

                        $shipmentOrderItem = $this->ShipmentOrderItems->patchEntity($shipmentOrderItem, [
                            'quantity' => $quantity,
                            'product_id' => $productId,
                            'product_variant_id' => $variantId,
                            'delivery_status_date' => $deliveryDate
                        ]);

                        if ($this->ShipmentOrderItems->save($shipmentOrderItem))
                        {
                            // Build base conditions
                            $conditions = [
                                'product_id' => $productId,
                            ];

                            // Add location-specific condition
                            if ($data['sender_type'] === 'Showroom' && !empty($data['showroom_id'])) {
                                $conditions['showroom_id'] = $data['showroom_id'];
                            } elseif ($data['sender_type'] === 'Warehouse' && !empty($data['warehouse_id'])) {
                                $conditions['warehouse_id'] = $data['warehouse_id'];
                            } else {
                                return; // Invalid sender type or missing ID
                            }

                            // Add variant condition if applicable
                            if (!empty($variantId)) {
                                $conditions['product_variant_id'] = $variantId;
                            }

                            // Find and update reserved stock
                            $stock = $this->ProductStocks->find()->where($conditions)->first();
                            if ($stock) {
                                $stock->reserved_stock = max(0, ($stock->reserved_stock - $previousQuantity) + $quantity);
                                $this->ProductStocks->save($stock);
                            }
                        }
                    }
                }

                // ✅ Now check if any shipment_order has all items removed (status = 'D')
                $shipmentOrderIds = $this->ShipmentOrderItems->find()
                    ->select(['ShipmentOrderItems.shipment_order_id'])
                    ->distinct()
                    ->matching('ShipmentOrders', function ($q) use ($shipment) {
                        return $q->where(['ShipmentOrders.shipment_id' => $shipment->id]);
                    })
                    ->all()
                    ->map(fn($row) => $row->shipment_order_id)
                    ->toList();

                foreach ($shipmentOrderIds as $shipmentOrderId) {
                    $activeItemsCount = $this->ShipmentOrderItems->find()
                        ->where([
                            'shipment_order_id' => $shipmentOrderId,
                            'status' => 'A'
                        ])
                        ->count();

                    if ($activeItemsCount === 0) {
                        $shipmentOrder = $this->Shipments->ShipmentOrders->get($shipmentOrderId);
                        $shipmentOrder->status = 'D';
                        $this->Shipments->ShipmentOrders->save($shipmentOrder);
                    }
                }

                // ✅ Check if all shipment_orders under this shipment are marked D
                $activeShipmentOrders = $this->Shipments->ShipmentOrders->find()
                    ->where([
                        'shipment_id' => $shipment->id,
                        'status' => 'A'
                    ])
                    ->count();

                if ($activeShipmentOrders === 0) {
                    $shipment->status = 'D';
                    $this->Shipments->save($shipment);

                    // ✅ Send deletion email
                    $this->sendShipmentDeletedNotificationEmail($shipment);
                }

                if (strtolower($shipment->delivery_type) === 'driver') {
                    $newDriverId = $shipment->driver_id;

                    if ($previousDriverId === null && $newDriverId !== null) {

                        // Update related shipment_orders
                        $shipmentOrders = $this->Shipments->ShipmentOrders->find()
                            ->where(['shipment_id' => $id])
                            ->all();

                        foreach ($shipmentOrders as $shipmentOrder) {
                            $shipmentOrder->driver_id = $newDriverId;
                            $shipmentOrder->expected_delivery_date = date('Y-m-d');
                            $this->Shipments->ShipmentOrders->save($shipmentOrder);
                        }

                        $shipmentDetails = $this->getShipmentDetails($id);
                        $this->sendDriverShipmentNotification($shipmentDetails, 'assigned');

                    } elseif ($previousDriverId !== null && $previousDriverId != $newDriverId) {

                        // Update related shipment_orders
                        $shipmentOrders = $this->Shipments->ShipmentOrders->find()
                            ->where(['shipment_id' => $id])
                            ->all();

                        foreach ($shipmentOrders as $shipmentOrder) {
                            $shipmentOrder->driver_id = $newDriverId;
                            $shipmentOrder->expected_delivery_date = date('Y-m-d');
                            $this->Shipments->ShipmentOrders->save($shipmentOrder);
                        }
                        
                        $shipmentWithNewDriver = $this->getShipmentDetails($id);
                        $this->sendDriverShipmentNotification($shipmentWithNewDriver, 'assigned');

                        // Notify previous driver too
                        $oldDriver = $this->Shipments->Drivers->get($previousDriverId, ['contain' => ['Users']]);

                        if (!empty($oldDriver->user->email)) {
                            $oldShipment = clone $shipmentWithNewDriver;
                            $oldShipment->driver = $oldDriver;

                            $this->sendDriverShipmentNotification($oldShipment, 'unassigned'); // Notify old driver with same data
                        }
                    }
                } elseif (strtolower($shipment->delivery_type) === 'delivery partner') {

                    // Update related shipment_orders
                    $shipmentOrders = $this->Shipments->ShipmentOrders->find()
                        ->where(['shipment_id' => $id])
                        ->all();

                    foreach ($shipmentOrders as $shipmentOrder) {
                        $shipmentOrder->driver_id = null;
                        $shipmentOrder->expected_delivery_date = null;
                        $this->Shipments->ShipmentOrders->save($shipmentOrder);
                    }

                    // Check if a driver was previously assigned
                    if ($previousDriverId !== null) {
                        $oldDriver = $this->Shipments->Drivers->get($previousDriverId, ['contain' => ['Users']]);

                        if (!empty($oldDriver->user->email)) {
                            $shipmentDetails = $this->getDriverShipmentDetails($id, $previousDriverId);
                            $shipmentDetails->driver = $oldDriver;

                            $this->sendDriverShipmentNotification($shipmentDetails, 'unassigned');
                        }
                    }
                }

                $this->Flash->success(__('The shipment has been updated.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The shipment could not be updated. Please, try again.'));
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $shipment = $this->Shipments->find()
        ->contain([
            'ShipmentOrders' => ['Cities', 'Zones', 'Municipalities'],
            'Drivers' => function (Query $q) {
                return $q->leftJoinWith('Users')
                    ->select([
                        'Drivers.id',
                        'Users.id',
                        'Users.first_name',
                        'Users.last_name',
                        'full_name' => $q->func()->concat([
                            'Users.first_name' => 'identifier',
                            ' ',
                            'Users.last_name' => 'identifier',
                        ]),
                    ]);
            },
            'DeliveryPartners',
        ])
        ->select([
            'Shipments.id',
            'Shipments.sender_type',
            'Shipments.senderID',
            'Shipments.created',
            'Shipments.shipment_status',
            'Shipments.delivery_status',
            'Shipments.delivery_type',
            'Shipments.driver_id',
            'Shipments.delivery_partner_id',
            'sender_name' => $this->Shipments->find()
                ->newExpr()
                ->add("(CASE 
                        WHEN Shipments.sender_type = 'showroom' 
                        THEN (SELECT name FROM showrooms WHERE showrooms.id = Shipments.senderID) 
                        WHEN Shipments.sender_type = 'warehouse' 
                        THEN (SELECT name FROM warehouses WHERE warehouses.id = Shipments.senderID) 
                        ELSE 'N/A' 
                    END)"),
            'partner_name' => $this->Shipments->find()
                ->newExpr()
                ->add("(CASE 
                        WHEN Shipments.delivery_partner_id IS NOT NULL 
                        THEN DeliveryPartners.partner_name 
                        ELSE NULL 
                    END)"),
        ])
        ->where(['Shipments.id' => $id])
        ->first();

        $shipment_items = $this->Shipments->find()
        ->contain([
            'ShipmentOrders' => [
                'CustomerAddresses',
                'ShipmentOrderItems' => function ($q) {
                    return $q->where(['ShipmentOrderItems.status' => 'A']);
                },
                'ShipmentOrderItems.OrderItems' => function (Query $q) {
                    return $q->select([
                            'OrderItems.id',
                            'OrderItems.product_id',
                            'OrderItems.product_variant_id',
                            'OrderItems.quantity',
                            'OrderItems.product_attribute_id'
                        ])
                        ->contain([
                            'Products',
                            'ProductVariants',
                            'Orders' => function ($q) {
                                return $q->contain(['Customers' => function ($q) {
                                    return $q->contain(['Users'])->select([
                                        'Customers.id',
                                        'Users.first_name',
                                        'Users.last_name',
                                        'full_name' => $q->func()->concat([
                                            'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                                        ])
                                    ]);
                                }]);
                            }
                        ]);
                }
            ]
        ])
        ->where(['Shipments.id' => $id])
        ->first();

        $orderItemsData = [];
        foreach ($shipment_items->shipment_orders as $shipmentOrder) {

            $customerAddress = $shipmentOrder->customer_address ?? null;
            if ($customerAddress) {
                $addressParts = array_filter([
                    isset($customerAddress->address_line1) && !ctype_space($customerAddress->address_line1) ? $customerAddress->address_line1 : null,
                    isset($customerAddress->address_line2) && !ctype_space($customerAddress->address_line2) ? $customerAddress->address_line2 : null,
                    isset($customerAddress->landmark) && !ctype_space($customerAddress->landmark) ? $customerAddress->landmark : null,
                    isset($customerAddress->zipcode) && !ctype_space($customerAddress->zipcode) ? $customerAddress->zipcode : null,
                ]);

                $fullAddress = !empty($addressParts) ? implode(', ', $addressParts) : 'N/A';
            } else {
                $fullAddress = 'N/A';
            }

            foreach ($shipmentOrder->shipment_order_items as $shipmentOrderItem) {
                $orderItem = $shipmentOrderItem->order_item ?? null;
                if ($orderItem) {
                    $order = $orderItem->order ?? null;
                    $product = $orderItem->product ?? null;
                    $productVariant = $orderItem->product_variant ?? null;
                    $customer = $order->customer ?? null;
                    $user = $customer->user ?? null;

                    // Initialize attribute info
                    $productAttribute = ['attribute_name' => 'N/A', 'attribute_value' => 'N/A'];

                    // Fetch product attribute name and value if product_attribute_id is available
                    if (!empty($orderItem->product_attribute_id)) {
                        $attribute = $this->ProductAttributes->find()
                            ->where(['ProductAttributes.id' => $orderItem->product_attribute_id])
                            ->contain([
                                'Attributes' => [
                                    'fields' => ['Attributes.name']
                                ],
                                'AttributeValues' => [
                                    'fields' => ['AttributeValues.value']
                                ]
                            ])
                            ->first();

                        if ($attribute) {
                            $productAttribute = [
                                'attribute_name' => $attribute->attribute->name ?? 'N/A',
                                'attribute_value' => $attribute->attribute_value->value ?? 'N/A'
                            ];
                        }
                    }

                    $orderItemsData[] = [
                        'order_id' => $order->id ?? 'N/A',
                        'shipment_order_item_id' => $shipmentOrderItem->id ?? 'N/A',
                        'express_delivery' => $order->delivery_mode_type ?? 'N/A',
                        'customer_name' => ($user->first_name ?? '') . ' ' . ($user->last_name ?? ''),
                        'customer_address' => $fullAddress,
                        'sku' => $orderItem->product_variant_id ? ($productVariant->sku ?? 'N/A') : ($product->sku ?? 'N/A'),
                        'product_id' => $product->id ?? 'N/A',
                        'product_variant_id' => $orderItem->product_variant_id ? ($orderItem->product_variant_id ?? null) : null,
                        'product_attribute_id' => $orderItem->product_attribute_id ? ($orderItem->product_attribute_id ?? null) : null,
                        'product_name' => $product->name ?? 'N/A',
                        'variants' => $orderItem->product_variant_id ? ($productVariant->variant_name ?? 'N/A') : 'N/A',
                        'attribute_name' => $productAttribute['attribute_name'],
                        'attribute_value' => $productAttribute['attribute_value'],
                        'shipped_quantity' => $shipmentOrderItem->quantity ?? 0,
                        'delivery_status' => $shipmentOrderItem->item_delivery_status ?? 'Pending',
                        'delivery_time' => $shipmentOrderItem->delivery_status_date ?? 'N/A',
                        'delivery_proof' => $shipmentOrderItem->proof_of_delivery ?? 'N/A',
                        'comments' => $shipmentOrderItem->driver_comments ?? 'N/A',
                        'cash_collected' => number_format((float)$shipmentOrder->cash_collected, 0, '', $thousandSeparator) . ' ' . $currencySymbol ?? 'N/A',
                    ];
                }
            }
        }

        // Query the Drivers table and join with Users to get driver names
        $drivers = $this->Drivers->find()
            ->select(['Drivers.id', 'Users.first_name', 'Users.last_name'])
            ->where(['Users.status' => 'A', 'Users.user_type' => 'Driver'])
            ->contain(['Users'])->toArray();

        $delivery_partners = $this->DeliveryPartners->find('all')
            ->where(['DeliveryPartners.status' => 'A'])->toArray();

        $this->set(compact('shipment', 'orderItemsData', 'drivers', 'delivery_partners'));
    }

    private function getDriverShipmentDetails($id, $includeOldDriverId = null)
    {
        $shipment = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A'])
                        ->contain([
                            'CustomerAddresses',
                            'ShipmentOrderItems' => function ($q) {
                                return $q->where(['ShipmentOrderItems.status' => 'A'])
                                    ->contain([
                                        'OrderItems' => function ($q) {
                                            return $q->contain([
                                                'Products',
                                                'ProductVariants',
                                                'Orders' => [
                                                    'Customers' => function ($q) {
                                                        return $q->contain(['Users'])->select([
                                                            'Customers.id',
                                                            'Users.first_name',
                                                            'Users.last_name',
                                                            'full_name' => $q->func()->concat([
                                                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                                                            ])
                                                        ]);
                                                    }
                                                ]
                                            ]);
                                        }
                                    ]);
                            }
                        ]);
                }
            ])
            ->where(['Shipments.id' => $id])
            ->first();

        // If driver_id is null but we want to include the old driver manually
        if ($shipment && $shipment->driver_id === null && $includeOldDriverId !== null) {
            $oldDriver = $this->Shipments->Drivers->get($includeOldDriverId, [
                'contain' => ['Users']
            ]);
            $shipment->driver = $oldDriver;
        }

        // Dynamically resolve sender_name (pickup location)
        $senderName = 'N/A';
        if ($shipment->sender_type === 'Showroom') {
            $showroom = $this->Showrooms->find()
                ->select(['name', 'address'])
                ->where(['id' => $shipment->senderID])
                ->first();
            $senderName = $showroom->name ?? 'N/A';
            $senderAddress = $showroom->address ?? 'N/A';
        } elseif ($shipment->sender_type === 'Warehouse') {
        
            $warehouse = $this->Warehouses->find()
                ->select(['Warehouses.name', 'Warehouses.warehouse_no_area', 'Warehouses.address_line1'])
                ->contain([
                    'Cities' => function ($q) {
                        return $q->select(['Cities.id', 'Cities.city_name']);
                    },
                    'Municipalities' => function ($q) {
                        return $q->select(['Municipalities.id', 'Municipalities.name']);
                    }
                ])
                ->where(['Warehouses.id' => $shipment->senderID])
                ->first();

            $senderName = $warehouse->name ?? 'N/A';

            // Build sender address with city and municipality
            $addressParts = [];
            if (!empty($warehouse->warehouse_no_area)) {
                $addressParts[] = $warehouse->warehouse_no_area;
            }
            if (!empty($warehouse->address_line1)) {
                $addressParts[] = $warehouse->address_line1;
            }
            if (!empty($warehouse->city->city_name)) {
                $addressParts[] = $warehouse->city->city_name;
            }
            if (!empty($warehouse->municipality->name)) {
                $addressParts[] = $warehouse->municipality->name;
            }

            $senderAddress = !empty($addressParts) ? implode(', ', $addressParts) : 'N/A';

        }

        $shipment->sender_name = $senderName;
        $shipment->sender_address = $senderAddress;

        return $shipment;
    }

    private function getShipmentDetails($id)
    {
        $shipment = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A'])
                        ->contain([
                            'CustomerAddresses',
                            'ShipmentOrderItems' => function ($q) {
                                return $q->where(['ShipmentOrderItems.status' => 'A'])
                                    ->contain([
                                        'OrderItems' => function ($q) {
                                            return $q->contain([
                                                'Products',
                                                'ProductVariants',
                                                'Orders' => [
                                                    'Customers' => function ($q) {
                                                        return $q->contain(['Users'])->select([
                                                            'Customers.id',
                                                            'Users.first_name',
                                                            'Users.last_name',
                                                            'full_name' => $q->func()->concat([
                                                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                                                            ])
                                                        ]);
                                                    }
                                                ]
                                            ]);
                                        }
                                    ]);
                            }
                        ]);
                },
                'Drivers' => function ($q) {
                    return $q->contain(['Users'])->select([
                        'Drivers.id',
                        'Users.email',
                        'Users.first_name',
                        'Users.last_name',
                    ]);
                }
            ])
            ->where(['Shipments.id' => $id])
            ->first();

        // Resolve sender name
        $senderName = 'N/A';
        if ($shipment->sender_type === 'Showroom') {
            $showroom = $this->Showrooms->find()
                ->select(['name', 'address'])
                ->where(['id' => $shipment->senderID])
                ->first();
            $senderName = $showroom->name ?? 'N/A';
            $senderAddress = $showroom->address ?? 'N/A';
        } elseif ($shipment->sender_type === 'Warehouse') {
            
            $warehouse = $this->Warehouses->find()
                ->select(['Warehouses.name', 'Warehouses.warehouse_no_area', 'Warehouses.address_line1'])
                ->contain([
                    'Cities' => function ($q) {
                        return $q->select(['Cities.id', 'Cities.city_name']);
                    },
                    'Municipalities' => function ($q) {
                        return $q->select(['Municipalities.id', 'Municipalities.name']);
                    }
                ])
                ->where(['Warehouses.id' => $shipment->senderID])
                ->first();

            $senderName = $warehouse->name ?? 'N/A';

            // Build sender address with city and municipality
            $addressParts = [];
            if (!empty($warehouse->warehouse_no_area)) {
                $addressParts[] = $warehouse->warehouse_no_area;
            }
            if (!empty($warehouse->address_line1)) {
                $addressParts[] = $warehouse->address_line1;
            }
            if (!empty($warehouse->city->city_name)) {
                $addressParts[] = $warehouse->city->city_name;
            }
            if (!empty($warehouse->municipality->name)) {
                $addressParts[] = $warehouse->municipality->name;
            }

            $senderAddress = !empty($addressParts) ? implode(', ', $addressParts) : 'N/A';

        }

        $shipment->sender_name = $senderName;
        $shipment->sender_address = $senderAddress;

        return $shipment;
    }

    // private function getShipmentDetails($id)
    // {
    //     $shipment = $this->Shipments->find()
    //         ->contain([
    //             'ShipmentOrders' => [
    //                 'CustomerAddresses',
    //                 'ShipmentOrderItems' => [
    //                     'OrderItems' => function ($q) {
    //                         return $q->contain([
    //                             'Products',
    //                             'ProductVariants',
    //                             'Orders' => [
    //                                 'Customers' => function ($q) {
    //                                     return $q->contain(['Users'])->select([
    //                                         'Customers.id',
    //                                         'Users.first_name',
    //                                         'Users.last_name',
    //                                         'full_name' => $q->func()->concat([
    //                                             'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
    //                                         ])
    //                                     ]);
    //                                 }
    //                             ]
    //                         ]);
    //                     }
    //                 ]
    //             ],
    //             'Drivers' => function ($q) {
    //                 return $q->contain(['Users'])->select([
    //                     'Drivers.id',
    //                     'Users.email',
    //                     'Users.first_name',
    //                     'Users.last_name',
    //                 ]);
    //             }
    //         ])
    //         ->where(['Shipments.id' => $id])
    //         ->first();

    //     // Dynamically resolve sender_name (pickup location)
    //     $senderName = 'N/A';
    //     if ($shipment->sender_type === 'Showroom') {
            
    //         $showroom = $this->Showrooms->find()
    //             ->select(['name'])
    //             ->where(['id' => $shipment->senderID])
    //             ->first();
    //         $senderName = $showroom->name ?? 'N/A';
    //     } elseif ($shipment->sender_type === 'Warehouse') {

    //         $warehouse = $this->Warehouses->find()
    //             ->select(['name'])
    //             ->where(['id' => $shipment->senderID])
    //             ->first();
    //         $senderName = $warehouse->name ?? 'N/A';
    //     }

    //     $shipment->sender_name = $senderName;

    //     return $shipment;
    // }

    private function sendDriverShipmentNotification($shipment, $type)
    {
        if (empty($shipment->driver) || empty($shipment->driver->user->email)) {
            \Cake\Log\Log::warning("Driver email not found for shipment ID: {$shipment->id}");
            return;
        }

        $toEmail = [$shipment->driver->user->email];

        $driverName = $shipment->driver->user->first_name . ' ' . $shipment->driver->user->last_name;

        $ordersData = [];
        foreach ($shipment->shipment_orders as $shipmentOrder) {
            $customerAddress = $shipmentOrder->customer_address ?? null;
            $addressParts = array_filter([
                $customerAddress->address_line1 ?? null,
                $customerAddress->address_line2 ?? null,
                $customerAddress->landmark ?? null,
                $customerAddress->zipcode ?? null
            ]);
            $deliveryAddress = implode(', ', $addressParts);

            foreach ($shipmentOrder->shipment_order_items as $shipmentOrderItem) {
                $orderItem = $shipmentOrderItem->order_item;
                $order = $orderItem->order ?? null;
                $product = $orderItem->product ?? null;
                $productVariant = $orderItem->product_variant ?? null;
                $customer = $order->customer ?? null;
                $user = $customer->user ?? null;

                $ordersData[] = [
                    'order_id' => $order->id ?? 'N/A',
                    'product_name' => $product->name ?? 'N/A',
                    'variant' => $productVariant->variant_name ?? '',
                    'quantity' => $shipmentOrderItem->quantity ?? 0,
                    'delivery_address' => $deliveryAddress ?? 'N/A',
                    'customer_name' => $user ? $user->first_name . ' ' . $user->last_name : 'N/A',
                    'estimated_time' => $shipmentOrder->expected_delivery_date ? $shipmentOrder->expected_delivery_date->format('d-m-Y') : 'N/A',
                ];
            }
        }

        $emailData = [
            'shipment_id' => $shipment->id,
            'driver_name' => $driverName,
            'sender_name' => $shipment->sender_name,
            'sender_address' => $shipment->sender_address,
            'orders' => $ordersData,
        ];

        $subject = $type === 'assigned'
        ? "New Shipment Assignment - Shipment #{$shipment->id}"
        : "Shipment Unassigned - Shipment #{$shipment->id}";

        $template = $type === 'assigned'
            ? 'driver_shipment_notification'
            : 'driver_shipment_unassigned';

        $this->Global->send_email(
            $toEmail,
            null,
            $subject,
            $template,
            $emailData
        );
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The shipment could not be deleted. Please, try again.'];

        try {
            $shipment = $this->Shipments->get($id);
            $shipment->status = 'D';

            if ($this->Shipments->save($shipment)) {

                // ✅ Send deletion email
                $this->sendShipmentDeletedNotificationEmail($shipment);

                $shipmentOrders = $this->Shipments->ShipmentOrders->find()
                    ->where(['shipment_id' => $id])
                    ->all();

                $affectedOrderIds = [];

                foreach ($shipmentOrders as $shipmentOrder) {
                    $shipmentOrder->status = 'D';
                    $this->Shipments->ShipmentOrders->save($shipmentOrder);

                    $shipmentOrderItems = $this->Shipments->ShipmentOrders->ShipmentOrderItems->find()
                        ->where(['shipment_order_id' => $shipmentOrder->id])
                        ->all();

                    foreach ($shipmentOrderItems as $item) {
                        $item->status = 'D';
                        $this->Shipments->ShipmentOrders->ShipmentOrderItems->save($item);

                        // Get related order item
                        $orderItem = $this->OrderItems->find()
                            ->where(['id' => $item->order_item_id])
                            ->first();

                        if ($orderItem) {
                            $affectedOrderIds[$orderItem->order_id] = true; // Track affected orders

                            $order = $this->Orders->find()
                                ->where(['id' => $orderItem->order_id])
                                ->first();

                            if ($order) {
                                // Get one active attribute
                                $attribute = $this->OrderItemAttributes->find()
                                    ->where([
                                        'order_item_id' => $orderItem->id,
                                        'status' => 'A'
                                    ])
                                    ->first();

                                // Build stock query conditions
                                $conditions = [];

                                if (!empty($orderItem->product_id)) {
                                    $conditions['product_id'] = $orderItem->product_id;
                                }

                                if (!empty($order->product_variant_id)) {
                                    $conditions['product_variant_id'] = $order->product_variant_id;
                                }

                                if (!empty($attribute->product_attribute_id)) {
                                    $conditions['product_attribute_id'] = $attribute->product_attribute_id;
                                }

                                $senderType = strtolower((string)$shipment->sender_type);
                                if ($senderType === 'warehouse') {
                                    $conditions['warehouse_id'] = $shipment->senderID;
                                } elseif ($senderType === 'showroom') {
                                    $conditions['showroom_id'] = $shipment->senderID;
                                }

                                if (!empty($conditions['product_id'])) {
                                    $stock = $this->ProductStocks->find()
                                        ->where($conditions)
                                        ->first();

                                    if ($stock) {
                                        $stock->reserved_stock = max(0, $stock->reserved_stock - $item->quantity);
                                        $this->ProductStocks->save($stock);
                                    }
                                }
                            }
                        }
                    }
                }

                // Update shipment_status for affected orders
                foreach (array_keys($affectedOrderIds) as $orderId) {
                    $activeShipmentsExist = $this->Shipments->ShipmentOrders->find()
                        ->where([
                            'order_id' => $orderId,
                            'status' => 'A'
                        ])
                        ->count();

                    $order = $this->Orders->get($orderId);
                    $order->shipment_status = $activeShipmentsExist > 0 ? 'Partially Assigned' : 'Unassigned';
                    $this->Orders->save($order);
                }

                $response = ['success' => true, 'message' => 'The shipment has been marked as deleted along with related orders, items, stock unreserved, and order shipment statuses updated.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($response));
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    private function sendShipmentDeletedNotificationEmail($shipment)
    {
        $toEmails = [];

        if ($shipment->sender_type === 'Showroom') {
            // Fetch Showroom Manager & Supervisor
            $showroom = $this->Showrooms->get($shipment->senderID, [
                'contain' => ['ShowroomManager', 'ShowroomSupervisor']
            ]);

            $managerEmail = $showroom->manager->email ?? null;
            $supervisorEmail = $showroom->supervisor->email ?? null;

            if ($managerEmail) {
                $toEmails[] = $managerEmail;
            }

            if ($supervisorEmail) {
                $toEmails[] = $supervisorEmail;
            }

            $locationType = "Showroom";
            $locationName = $showroom->name ?? 'N/A';

        } elseif ($shipment->sender_type === 'Warehouse') {
            // Fetch Warehouse Manager
            $warehouse = $this->Warehouses->get($shipment->senderID, [
                'contain' => ['Managers']
            ]);

            $warehouseManagerName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            $toEmails = array_filter([$warehouseManagerEmail]);

            $locationType = "Warehouse";
            $locationName = $warehouse->name ?? 'N/A';
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for deleted shipment ID: " . $shipment->id);
            return;
        }

        $emailData = [
            'shipment_id' => $shipment->id,
            'shipment_status' => 'Deleted',
            'from_location' => "{$locationType}: {$locationName}",
            'greeting' => "Dear {$warehouseManagerName},",
            'shipment_date' => $shipment->created ? $shipment->created->format('d-m-Y') : 'N/A',
        ];

        $subject = "Shipment Deleted - #{$shipment->id}";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'shipment_deleted_notification',
            $emailData
        );
    }

    public function assignShipment()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $shipmentId = $this->request->getData('shipment_id');

        $shipment = $this->Shipments->get($shipmentId, [
            'contain' => [
                'ShipmentOrders' => function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A'])
                             ->contain([
                                 'ShipmentOrderItems' => function ($q2) {
                                     return $q2->where(['ShipmentOrderItems.status' => 'A']);
                                 }
                             ]);
                }
            ]
        ]);

        if ($shipment->delivery_status !== 'Pending') {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Stock already marked as outgoing.'
            ]));
        }

        $shipment->delivery_status = 'Picked up';

        $shipmentOrderItemsTable = TableRegistry::getTableLocator()->get('ShipmentOrderItems');
        $shipmentOrdersTable = TableRegistry::getTableLocator()->get('ShipmentOrders');
        $productStocksTable = TableRegistry::getTableLocator()->get('ProductStocks');

        foreach ($shipment->shipment_orders as $order) {
            $order->order_delivery_status = 'Picked up';

            // Save ShipmentOrder
            if (!$shipmentOrdersTable->save($order)) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Failed to update shipment order.',
                    'errors' => $order->getErrors()
                ]));
            }

            foreach ($order->shipment_order_items as $item) {
                $item->item_delivery_status = 'Picked up';

                // Save ShipmentOrderItem
                if (!$shipmentOrderItemsTable->save($item)) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'message' => 'Failed to update shipment order item.',
                        'errors' => $item->getErrors()
                    ]));
                }

                // Get product details from OrderItems
                $orderItem = $this->Orders->OrderItems->get($item->order_item_id, [
                    'contain' => ['Products', 'ProductVariants', 'ProductAttributes']
                ]);

                $product_id = $orderItem->product_id;
                $product_variant_id = $orderItem->product_variant_id;
                $product_attribute_id = $orderItem->product_attribute_id;
                $quantity = $item->quantity;

                // Check if sender is Warehouse or Showroom
                $conditions = ['product_id' => $product_id];

                if (strtolower($shipment->sender_type) == 'warehouse') {
                    $conditions['warehouse_id'] = $shipment->senderID;
                } elseif (strtolower($shipment->sender_type) == 'showroom') {
                    $conditions['showroom_id'] = $shipment->senderID;
                }

                // Conditionally include variant and attribute IDs
                if (!empty($product_variant_id)) {
                    $conditions['product_variant_id'] = $product_variant_id;
                }
                if (!empty($product_attribute_id)) {
                    $conditions['product_attribute_id'] = $product_attribute_id;
                }

                $productStock = $productStocksTable->find()
                    ->where($conditions)
                    ->first();

                if ($productStock) {
                    // Deduct quantity and reserved_stock
                    $productStock->quantity -= $quantity;
                    $productStock->reserved_stock -= $quantity;

                    // Save the updated product stock
                    if (!$productStocksTable->save($productStock)) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => false,
                            'message' => 'Failed to update product stock.',
                            'errors' => $productStock->getErrors()
                        ]));
                    }
                } else {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'message' => 'Product stock not found for the specified warehouse/showroom.'
                    ]));
                }

            }
        }

        // Save the shipment record itself last
        if ($this->Shipments->save($shipment)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => 'Shipment marked as picked up.'
            ]));
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => false,
            'message' => 'Failed to update shipment.'
        ]));
    }



}
