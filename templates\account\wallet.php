<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myFav.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myWallet.css') ?>" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<?php $this->end(); ?>
<style>
    div#wallet-transactions-list {
        max-height: 400px;
        overflow-y: auto;
        /* Remove if you want full page scroll instead of div scroll */
    }
</style>

<div class="productCategoryListingC">
    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span
            class="productCategoryListing-home-span"><?= $this->Html->link('Home', ['controller' => 'Website', 'action' => 'home', 'prefix' => false]) ?></span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span Electronic-devices">My Wallet</span>
    </div>
</div>

<div class="my-a-my-o-c">
<?php echo $this->element('web_sidebar'); ?>

    <div class="my-a-my-o-main">

        <div class="greetings-container">
            <div class="hello-greetings"><?= __('Hello') ?>, <span><?= ucfirst($users->first_name); ?></span></div>
            <div class="avail-balance"><?= __('Your Available Wallet Balance') ?></div>
            <div class="avail-balance-amount"><?= $this->Price->setPriceFormat($walletAmount) ?></div>
        </div>

        <div class="TransactionHistoryTitle"><?= __('Transaction History') ?></div>

        <div class="walletabsContainer">
            <div class="wallet-tabs">
                <button class="wallet-tab<?= $filter === 'all' ? ' active' : '' ?>" data-filter="all"><?= __('All') ?></button>
                <button class="wallet-tab<?= $filter === 'credit' ? ' active' : '' ?>" data-filter="credit"><?= __('Credit') ?></button>
                <button class="wallet-tab<?= $filter === 'debit' ? ' active' : '' ?>" data-filter="debit"><?= __('Debit') ?></button>
            </div>
            <div id="wallet-transactions-list">
                <?php foreach ($transactions as $transaction): ?>
                    <div id="t-c-cs" class="transaction-item">
                        <div class="transaction-icon">
                            <span class="<?= isset($transaction->wallet_payment_type) ? ($transaction->wallet_payment_type === 'credit' ? 'up-image' : 'down-image') : ($transaction->wallet_payment_type === 'credit' ? 'up-image' : 'down-image') ?>">
                               <?= isset($transaction->wallet_payment_type) ? ($transaction->wallet_payment_type === 'credit' ? '↗' : '↘') : '↗' ?>
                            </span>
                        </div>
                        <div class="order-date-date-time" style="display: flex; flex-direction: column;">
                            <span class="order-date"><?= __('Order Number') ?> #<?= h($transaction->order_number) ?></span>
                            <span class="date-time">
                                <span><?= h($transaction->order_date->i18nFormat('dd.MM.yyyy')) ?> </span>
                                <?= h($transaction->order_date->i18nFormat('HH:mm')) ?>
                            </span>
                        </div>
                        <div>
                            <span class="<?= (isset($transaction->wallet_payment_type) ? $transaction->wallet_payment_type : $transaction->wallet_payment_type) === 'credit' ? 'green-price' : 'red-price' ?>">
                                <?= (isset($transaction->wallet_payment_type) ? ($transaction->wallet_payment_type === 'credit' ? '+' : '-') : '+') ?>
                                <?= h($this->Price->setPriceFormat($transaction->wallet_redeem_amount)) ?>
                            </span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div id="wallet-transactions-loader" style="text-align:center;display:none;">
                <span><?= __('Loading...') ?></span>
            </div>
            <div id="wallet-transactions-end" style="text-align:center;display:none;">
                <span><?= __('No more transactions.') ?></span>
            </div>
        </div>

        <script>
        // JS for wallet tab filtering and lazy loading (page scroll only)
        (function() {
            let currentPage = <?= (int)$page ?>;
            let currentFilter = '<?= h($filter) ?>';
            const limit = <?= (int)$limit ?>;
            let loading = false;
            let endReached = false;

            function renderTransactions(transactions, append = false) {
                const list = document.getElementById('wallet-transactions-list');
                if (!append) list.innerHTML = '';
                transactions.forEach(function(transaction) {
                    const type = transaction.wallet_payment_type || transaction.wallet_payment_type;
                    const icon = type === 'credit' ? '↗' : '↘';
                    const priceClass = type === 'credit' ? 'green-price' : 'red-price';
                    const sign = type === 'credit' ? '+' : '-';
                    // Format date/time
                    let dateStr = '';
                    let timeStr = '';
                    if (transaction.order_date) {
                        const dateObj = new Date(transaction.order_date);
                        dateStr = dateObj.toLocaleDateString('en-GB');
                        timeStr = dateObj.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
                    }
                    price = formatAmount(transaction.wallet_redeem_amount);
                    const html = `
                        <div id="t-c-cs" class="transaction-item">
                            <div class="transaction-icon">
                                <span class="${type === 'credit' ? 'up-image' : 'down-image'}">${icon}</span>
                            </div>
                            <div class="order-date-date-time" style="display: flex; flex-direction: column;">
                                <span class="order-date"><?= __('OrderID') ?> #${transaction.order_number}</span>
                                <span class="date-time">
                                    <span>${dateStr} </span>
                                    ${timeStr}
                                </span>
                            </div>
                            <div>
                                <span class="${priceClass}">${sign}${transaction.wallet_redeem_amount}</span>
                            </div>
                        </div>
                    `;
                    list.insertAdjacentHTML('beforeend', html);
                });
            }

            function fetchTransactions(page, filter, append = false) {
                if (loading || endReached) return;
                loading = true;
                document.getElementById('wallet-transactions-loader').style.display = 'block';
                fetch('<?= $this->Url->build(['controller' => 'Account', 'action' => 'walletTransactionsAjax']) ?>?page=' + page + '&filter=' + filter + '&limit=' + limit)
                    .then(res => res.json())
                    .then(data => {
                        document.getElementById('wallet-transactions-loader').style.display = 'none';
                        if (data.status === 'success') {
                            if (data.transactions.length < limit) {
                                endReached = true;
                                document.getElementById('wallet-transactions-end').style.display = 'block';
                            } else {
                                document.getElementById('wallet-transactions-end').style.display = 'none';
                            }
                            renderTransactions(data.transactions, append);
                        }
                        loading = false;
                    });
            }

            // Tab click handler
            document.querySelectorAll('.wallet-tab').forEach(function(tab) {
                tab.addEventListener('click', function() {
                    if (this.classList.contains('active')) return;
                    document.querySelectorAll('.wallet-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.filter;
                    currentPage = 1;
                    endReached = false;
                    document.getElementById('wallet-transactions-end').style.display = 'none';
                    fetchTransactions(currentPage, currentFilter, false);
                });
            });

            // Infinite scroll on page (not on div)
            window.addEventListener('scroll', function() {
                if (loading || endReached) return;
                // Trigger when user is near the bottom (footer)
                if ((window.innerHeight + window.scrollY) >= (document.body.offsetHeight - 100)) {
                    currentPage++;
                    fetchTransactions(currentPage, currentFilter, true);
                }
            });

            // Optionally, load more if the list is short and page is not scrollable
            document.addEventListener('DOMContentLoaded', function() {
                // If the page is not scrollable and not enough items, try to load more
                if (document.body.scrollHeight <= window.innerHeight && !endReached) {
                    currentPage++;
                    fetchTransactions(currentPage, currentFilter, true);
                }
            });

        })();

        function formatAmount(amount) {
            if (isNaN(amount)) return '0';
            return amount.toString().split('.')[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
        }
        </script>


    </div>

</div>
