<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;

/**
 * DeliveryCharges Controller
 *
 * @property \App\Model\Table\DeliveryChargesTable $DeliveryCharges
 */
class DeliveryChargesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Showrooms;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Showrooms = $this->fetchTable('Showrooms');
    }

    public function index()
    {
        $query = $this->DeliveryCharges->find()
            ->contain(['Cities']);
        $deliveryCharges = $this->paginate($query);

        $this->set(compact('deliveryCharges'));
    }

    /**
     * View method
     *
     * @param string|null $id Delivery Charge id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $deliveryCharge = $this->DeliveryCharges->get($id, contain: ['Cities']);
        $this->set(compact('deliveryCharge'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $deliveryCharge = $this->DeliveryCharges->newEmptyEntity();
        if ($this->request->is('post')) {
            $deliveryCharge = $this->DeliveryCharges->patchEntity($deliveryCharge, $this->request->getData());
            if ($this->DeliveryCharges->save($deliveryCharge)) {
                $this->Flash->success(__('The delivery charge has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The delivery charge could not be saved. Please, try again.'));
        }
        $cities = $this->DeliveryCharges->Cities->find('list', limit: 200)->all();
        $this->set(compact('deliveryCharge', 'cities'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Delivery Charge id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $deliveryCharge = $this->DeliveryCharges->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $deliveryCharge = $this->DeliveryCharges->patchEntity($deliveryCharge, $this->request->getData());
            if ($this->DeliveryCharges->save($deliveryCharge)) {
                $this->Flash->success(__('The delivery charge has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The delivery charge could not be saved. Please, try again.'));
        }
        $cities = $this->DeliveryCharges->Cities->find('list', limit: 200)->all();
        $this->set(compact('deliveryCharge', 'cities'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Delivery Charge id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $deliveryCharge = $this->DeliveryCharges->get($id);
        if ($this->DeliveryCharges->delete($deliveryCharge)) {
            $this->Flash->success(__('The delivery charge has been deleted.'));
        } else {
            $this->Flash->error(__('The delivery charge could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function getDeliveryChargefromShowroom()
    {
        $this->request->allowMethod(['post']);

        $showroomId = $this->request->getData('id');
        $deliveryMode = $this->request->getData('delivery_mode');
        $weightQuantityArray = $this->request->getData('weightQuantityArray');

        if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Invalid weight and quantity data.')
            ]));
        }

        $showroom = $this->Showrooms->find()
            ->select(['city_id'])
            ->where(['id' => $showroomId])
            ->first();

        if ($showroom) {
            $cityId = $showroom->city_id;

            $deliveryCharges = $this->DeliveryCharges->find()
                ->where([
                    'city_id' => $cityId,
                    'delivery_mode' => $deliveryMode,
                    'status' => 'A'
                ])
                ->order(['weight' => 'ASC'])
                ->toArray();

            if (empty($deliveryCharges)) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('No delivery charges found for the selected criteria.')
                ]));
            }

            $total_delivery_charge = 0.00;

            foreach ($weightQuantityArray as $item) {
                $weight = $item['weight'];
                $quantity = $item['quantity'];
                $applicableCharge = null;

                foreach ($deliveryCharges as $charge) {
                    if ($weight > $charge->weight) {
                        $applicableCharge = $charge;
                    } else {
                        break;
                    }
                }

                if ($applicableCharge) {
                    $quotient = $quantity / 2;
                    $remainder = $quantity % 2;
                    $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                    $total_delivery_charge += $calculated_charge;
                }
            }

            $result = [
                'status' => 'success',
                'total_delivery_charge' => $total_delivery_charge
            ];
        } else {
            $result = [
                'status' => 'error',
                'message' => __('Showroom not found.')
            ];
        }

        $this->response = $this->response->withType('application/json')->withStringBody(json_encode($result));
        return $this->response;
    }

    public function getDeliveryChargeFromCity()
    {
        $this->request->allowMethod(['post']);

        $cityId = $this->request->getData('id');
        $deliveryMode = $this->request->getData('delivery_mode');
        $weightQuantityArray = $this->request->getData('weightQuantityArray');
        $sizeQuantityArray = $this->request->getData('sizeQuantityArray');

        $result = $this->DeliveryCharges->calculateDeliveryCharge($cityId, $deliveryMode, $weightQuantityArray, $sizeQuantityArray);

        return $this->response->withType('application/json')->withStringBody(json_encode($result));
    }
}
