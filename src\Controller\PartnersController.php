<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;

/**
 * Partners Controller
 *
 * @property \App\Model\Table\PartnersTable $Partners
 */
class PartnersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */


    protected \App\Model\Table\CitiesTable $Cities;
    // protected \App\Model\Table\PartnerPaymentTermsTable $PartnerPaymentTerms;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Cities = $this->fetchTable('Cities');
        // $this->PartnerPaymentTerms = $this->fetchTable('PartnerPaymentTerms');
    }

    public function index()
    {
        $query = $this->Partners->find()
            ->contain(['Cities'])->where(['status !=' => 'D'])->applyOptions(['order' => ['business_name' => 'ASC']]);
        $partners = $query->all();
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');

        $this->set(compact('partners', 'status', 'statusMap'));
    }

    /**
     * View method
     *
     * @param string|null $id Partner id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $partner = $this->Partners->get($id, [
            'Cities',
            'ProductPaymentSettings',
            'Users',
            'contain' => [
                'PartnerPaymentTerms' => function ($q) {
                    return $q->where(['PartnerPaymentTerms.status' => 'A']);
                }
            ]
        ]);
        $statuses = Configure::read('Constants.STATUS');
        $approval_statuses = Configure::read('Constants.APPROVAL_STATUS');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $paymentTerms = Configure::read('Constants.PAYMENT_TERMS');
        $this->set(compact('partner', 'statuses', 'approval_statuses', 'dateFormat', 'timeFormat', 'paymentTerms'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $partner = $this->Partners->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $phoneNumber = $this->request->getData('phone_number');
            $countryCode = $this->request->getData('country_code');

            $phone = str_replace($countryCode, '', $phoneNumber);
            if (strpos($phone, '+') === 0) {
                $phone = ltrim($phone, '+');
            }
            $data['country_code'] = $countryCode;
            $data['phone'] = $phone;

            $partner = $this->Partners->patchEntity($partner, $data);
            if ($this->Partners->save($partner)) {

                $partnerId = $partner->id;

                $this->savePaymentTerms($partnerId, $this->request->getData());

                $this->Flash->success(__('The partner has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The partner could not be saved. Please, try again.'));
        }
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])->all()->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No City available'];
        }

        $paymentTerms = Configure::read('Constants.PAYMENT_TERMS');
        $this->set(compact('partner', 'cities', 'paymentTerms'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Partner id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $partner = $this->Partners->get($id, [
            'contain' => [
                'PartnerPaymentTerms' => function ($q) {
                    return $q->where(['PartnerPaymentTerms.status' => 'A']);
                }
            ]
        ]);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            $phoneNumber = $this->request->getData('phone_number');
            $countryCode = $this->request->getData('country_code');

            $phone = str_replace($countryCode, '', $phoneNumber);
            if (strpos($phone, '+') === 0) {
                $phone = ltrim($phone, '+');
            }
            $data['country_code'] = $countryCode;
            $data['phone'] = $phone;
            $partner = $this->Partners->patchEntity($partner, $data);
            if ($this->Partners->save($partner)) {
                $partnerId = $partner->id;

                $this->savePaymentTerms($partnerId, $this->request->getData());
                $this->Flash->success(__('The partner has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The partner could not be saved. Please, try again.'));
        }
        $statuses = Configure::read('Constants.STATUS');
        $approval_statuses = Configure::read('Constants.APPROVAL_STATUS');
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])->all()->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No City available'];
        }
        $paymentTerms = Configure::read('Constants.PAYMENT_TERMS');
        $this->set(compact('partner', 'cities', 'statuses', 'approval_statuses', 'paymentTerms'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Partner id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $partner = $this->Partners->get($id);
        $response = ['success' => false, 'message' => 'The Partner could not be deleted. Please, try again.'];
        if ($partner) {
            if ($this->Partners->delete($partner)) {
                $response = ['success' => true, 'message' => 'The Partner has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Partner could not be deleted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Partner does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function approvePartner()
    {
        $this->request->allowMethod(['post']);
        $id = $this->request->getData('partner_id');
        $comments = $this->request->getData('comments');

        $response = [];

        $partner = $this->Partners->get($id);

        if (!$partner) {
            $response = [
                'status' => 'error',
                'message' => __('Partner not found.')
            ];
        } else {
            $partner->approval_status = 'Approved';
            $partner->comments = $comments;
            $partner->approval_at = date('Y-m-d H:i:s');
            $user = $this->Authentication->getIdentity();
            $partner->approval_by = $user->id;

            if ($this->Partners->save($partner)) {
                $response = [
                    'status' => 'success',
                    'message' => __('Partner has been approved.')
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to approve the partner. Please try again.')
                ];
            }
        }
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function rejectPartner()
    {
        $this->request->allowMethod(['post']);
        $id = $this->request->getData('partner_id');
        $comments = $this->request->getData('comments');

        $partner = $this->Partners->get($id);

        $response = [];

        if (!$partner) {
            $response = [
                'status' => 'error',
                'message' => __('Partner not found.')
            ];
        } else {
            $partner->approval_status = 'Rejected';
            $partner->comments = $comments;
            $partner->approval_at = date('Y-m-d H:i:s');
            $user = $this->Authentication->getIdentity();
            $partner->approval_by = $user->id;

            if ($this->Partners->save($partner)) {
                $response = [
                    'status' => 'success',
                    'message' => __('Partner has been rejected.')
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to reject the partner. Please try again.')
                ];
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    protected function savePaymentTerms(int $partnerId, array $data): void
    {
        $this->Partners->PartnerPaymentTerms->updateAll(
            ['status' => 'D'],
            ['partner_id' => $partnerId]
        );

        if (!empty($data['payment_terms'])) {
            foreach ($data['payment_terms'] as $index => $paymentTerm) {
                $commissionPercent = $data['term_commission_percent'][$index];

                $partnerPaymentTerm = $this->Partners->PartnerPaymentTerms->newEntity([
                    'partner_id' => $partnerId,
                    'payment_terms' => $paymentTerm,
                    'term_commission_percent' => $commissionPercent,
                    'status' => 'A'
                ]);

                $this->Partners->PartnerPaymentTerms->save($partnerPaymentTerm);
            }
        }
    }

    public function fetchCreditTerms()
    {
        $this->request->allowMethod(['post']); 

        $partnerId = $this->request->getData('partner_id');

        if (empty($partnerId)) {
            $response = [
                'success' => false,
                'message' => 'Invalid Partner ID.',
            ];
        } else {
            $paymentTerms = $this->Partners->PartnerPaymentTerms->find('all')
                ->where(['PartnerPaymentTerms.partner_id' => $partnerId])
                ->where(['PartnerPaymentTerms.status' => 'A'])
                ->select([
                    'id' => 'PartnerPaymentTerms.id',
                    'payment_terms' => 'PartnerPaymentTerms.payment_terms'
                ])
                ->toArray();

            $response = [
                'success' => true,
                'paymentTerms' => $paymentTerms,
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }
}
