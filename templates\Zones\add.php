<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Showroom $showroom
 * @var \Cake\Collection\CollectionInterface|string[] $cities
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style type="text/css">
    .is-invalid {
        border: 1px solid red !important;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    select.is-invalid {
        border-color: #dc3545 !important;
    }

    .error {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 4px;
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Zones', 'action' => 'index']) ?>"><?= __('Zones') ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __('Add') ?></li>
    </ul>
    <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
        <small class="p-10 fw-bold"><?= __('BACK') ?></small>
        <span class="rotate me-2">⤣</span>
    </button>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20"><?= __('Add Zone') ?></h6>
            <?php echo $this->Form->create($zone, ['id' => 'add', 'novalidate' => true]); ?>
            <?php if (!empty($zone->getErrors())): ?>
                <div class="validation-errors">
                    <?php foreach ($zone->getErrors() as $field => $errors): ?>
                        <div class="field-errors">
                            <strong><?php echo h(ucwords($field)); ?>:</strong>
                            <ul>
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo h($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label fw-bold"><?= __('Zone Name') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name',
                        'placeholder' => 'Zone Name',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="municipality_id" class="col-sm-2 col-form-label fw-bold"><?= __('Municipality Name') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('municipality_id', 
                    ['id' => 'municipality_id_model',
                        'type' => 'select',
                        'label' => false,
                        'div' => false,
                        'title' => 'Select Municipality',
                        'options' => $municipalities,
                        'multiple' => 'multiple',
                        'data-live-search' => "true",
                        'class' => 'form-control select2'
                    ]);
                    ?>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label for="supervisor" class="col-sm-2 col-form-label fw-bold">< ?= __('Supervisor') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <select data-live-search="true" id="supervisor" name="supervisor_id" class="form-control select2">
                        <option value="">< ?= __('Select Supervisor') ?></option>
                        < ?php if(!empty($supervisors)) { foreach ($supervisors as $supervisor): ?>
                            <option value="< ?= h($supervisor['id']) ?>">< ?= h($supervisor['first_name'].' '.$supervisor['last_name']) ?></option> 
                        < ?php endforeach; } ?>
                    </select>
                </div>
            </div> -->

            <div class="form-group row">
                <label for="showroom_id" class="col-sm-2 col-form-label fw-bold"><?= __('Showroom Name') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('showroom_id', 
                    ['id' => 'showroom_id_model',
                        'type' => 'select',
                        'label' => false,
                        'div' => false,
                        'title' => 'Select Showroom',
                        'options' => $showrooms,
                        'multiple' => 'multiple',
                        'data-live-search' => "true",
                        'class' => 'form-control select2'
                    ]);
                    ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="area" class="col-sm-2 col-form-label fw-bold"><?= __('Area') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('area', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'area',
                        'placeholder' => 'Area',
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
            <div class="col-sm-10 offset-sm-2">
                <button type="submit" class="btn"><?= __('Save') ?></button>
                <button type="reset" id="resetButton" class="btn"><?= __('Reset') ?></button>
            </div>
        </div>
        </div>
        </form>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>
    $(document).ready(function () {
        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });

        $('#resetButton').on('click', function() {
            $('#showroom_id_model').val(null).trigger('change');
        });
    });

    $(document).ready(function () {

        $.validator.addMethod("pattern", function (value, element) {
            return this.optional(element) || /^[a-z-]+$/.test(value);
        }, "<?= __('Only lowercase letters and underscores are allowed') ?>");

        $("#add").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true
                },
                'municipality_id[]': {
                    required: true
                },
                'showroom_id[]': {
                    required: true
                }
            },
            messages: {
                'name': {
                    required: "<?= __('Please enter zone name') ?>"
                },
                'municipality_id[]': {
                    required: "<?= __('Please select municipality') ?>"
                },
                'showroom_id[]': {
                    required: "<?= __('Please select showroom') ?>"
                }
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');

                // If it's a select2 or select box, also add to the container
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').addClass('is-invalid');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');

                // Remove from select2 or select container too
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').removeClass('is-invalid');
                }
            },
            submitHandler: function (form) {
                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            }
        });
    });
</script>
<?php $this->end(); ?>
