<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Loyalty Controller
 *
 * @property \App\Model\Table\LoyaltyTable $Loyalty
 */
class LoyaltyController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->Loyalty->find()
            ->contain(['Customers']);
        $loyalty = $this->paginate($query);

        $this->set(compact('loyalty'));
    }

    /**
     * View method
     *
     * @param string|null $id Loyalty id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $loyalty = $this->Loyalty->get($id, contain: ['Customers']);
        $this->set(compact('loyalty'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $loyalty = $this->Loyalty->newEmptyEntity();
        if ($this->request->is('post')) {
            $loyalty = $this->Loyalty->patchEntity($loyalty, $this->request->getData());
            if ($this->Loyalty->save($loyalty)) {
                $this->Flash->success(__('The loyalty has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The loyalty could not be saved. Please, try again.'));
        }
        $customers = $this->Loyalty->Customers->find('list', limit: 200)->all();
        $this->set(compact('loyalty', 'customers'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Loyalty id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $loyalty = $this->Loyalty->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $loyalty = $this->Loyalty->patchEntity($loyalty, $this->request->getData());
            if ($this->Loyalty->save($loyalty)) {
                $this->Flash->success(__('The loyalty has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The loyalty could not be saved. Please, try again.'));
        }
        $customers = $this->Loyalty->Customers->find('list', limit: 200)->all();
        $this->set(compact('loyalty', 'customers'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Loyalty id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $loyalty = $this->Loyalty->get($id);
        if ($this->Loyalty->delete($loyalty)) {
            $this->Flash->success(__('The loyalty has been deleted.'));
        } else {
            $this->Flash->error(__('The loyalty could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
