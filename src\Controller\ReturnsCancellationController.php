<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;
use Cake\ORM\Expression\QueryExpression;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ReturnsCancellationController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Orders;
    protected $OrderItems;
    protected $Products;
    protected $Roles;
    protected $ProductAttributes;
    protected $OrderReturns;
    protected $OrderReturnCategories;
    protected $Warehouses;
    protected $Showrooms;
    protected $Shipments;
    protected $ShipmentOrders;
    protected $ShipmentOrderItems;
    protected $CustomerAddresses;
    protected $ZoneMunicipalities;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Products = $this->fetchTable('Products');
        $this->Roles = $this->fetchTable('Roles');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->ShipmentOrderItems = $this->fetchTable('ShipmentOrderItems');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ZoneMunicipalities = $this->fetchTable('ZoneMunicipalities');
    }

    public function index()
    {

    }

    public function add()
    {
        $returnRequest = $this->OrderReturns->newEmptyEntity();

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $orderId = $data['order_id'];
            $pickupRequired = $data['pickup_required'] ?? 'No';
            $pickupCharge = $data['pickup_charge'] ?? 0;
            $note = $data['note'] ?? null;
            $return_to = ($pickupRequired === 'Yes') ? $data['return_to'] : null;
            $return_to_id = ($pickupRequired === 'Yes') ? $data['return_to_id'] : null;
            $orderItemIds = $data['order_item_id'] ?? [];
            $prices = $data['price'] ?? [];
            $returnQuantities = $data['return_quantity'] ?? [];
            $returnReasons = $data['return_reason'] ?? [];
            $returnProductImages = $data['return_product_image'] ?? []; // Uploaded images

            // Get logged-in user ID
            $user = $this->Authentication->getIdentity(); // If using CakePHP Authentication plugin
            $requestedBy = $user->id;

            for ($i = 0; $i < count($orderItemIds); $i++) {
                $uploadedImages = [];

                // Handle image uploads (if any)
                if (!empty($returnProductImages[$orderItemIds[$i]])) {
                    foreach ($returnProductImages[$orderItemIds[$i]] as $image) {

                        if ($image instanceof \Laminas\Diactoros\UploadedFile && $image->getError() === UPLOAD_ERR_OK) {
                            $fileName = $image->getClientFilename();
                            $imageTmpName = $image->getStream()->getMetadata('uri');
                            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $filePath = Configure::read('Constants.ORDER_RETURN_DEFECT_IMAGE');
                            $folderPath = $uploadFolder . $filePath;
                            $targetDir = WWW_ROOT . $folderPath;

                            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                            $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                            $uploadResult = $this->Media->upload($imageTmpName, $targetDir, $imageFile, $folderPath);

                            if ($uploadResult !== 'Success') {
                                $this->Flash->error(__('Image upload failed for item ID: ') . $orderItemIds[$i]);
                                return $this->redirect(['action' => 'add']);
                            } else {
                                $uploadedImages[] = $folderPath . $imageFile;
                            }
                        }
                    }
                }

                $returnImage = !empty($uploadedImages) ? implode(',', $uploadedImages) : null;

                $returnAmount = (float)$returnQuantities[$i] * (float)$prices[$i];

                $orderReturnData = [
                    'order_id' => $orderId,
                    'order_item_id' => $orderItemIds[$i],
                    'order_return_category_id' => $returnReasons[$i],
                    'return_quantity' => $returnQuantities[$i],
                    'return_product_image' => $returnImage,
                    'status' => 'Pending',
                    'return_to' => $return_to,
                    'return_to_id' => $return_to_id,
                    'pickup_required' => $pickupRequired,
                    'pickup_charge' => $pickupCharge,
                    'note' => $note,
                    'requested_by' => $requestedBy,
                    'requested_at' => date('Y-m-d H:i:s'),
                    'return_amount' => $returnAmount,
                ];

                $orderReturn = $this->OrderReturns->newEntity($orderReturnData);

                if (!$this->OrderReturns->save($orderReturn)) {
                    $this->Flash->error(__('Failed to save return entry for order item ID: ') . $orderItemIds[$i]);
                    return $this->redirect(['action' => 'add']);
                } else {
                    
                    $orderItem = $this->OrderItems->get($orderItemIds[$i]);
                    $orderItem->status = 'Pending Return';
                    $this->OrderItems->save($orderItem);

                    $order = $this->Orders->get($orderId);
                    $order->status = 'Pending Return';
                    $this->Orders->save($order);
                }
            }

            $this->Flash->success(__('Order return(s) saved successfully.'));
            return $this->redirect(['controller' => 'ReturnsRefunds', 'action' => 'index']);
        }

        $order_ids = [];
        $order_return_categories_ids = [];

        try {
            $orders = $this->Orders->find()
                ->where([
                    'Orders.status' => 'Delivered'
                ])
                ->order(['Orders.created' => 'DESC'])
                ->all();

            if ($orders->isEmpty()) {
                // You can handle the case where no orders are found
                // For example: set a flag or message
                $noOrdersFound = true;
            } else {
                foreach ($orders as $order) {
                    if (!empty($order->id)) {
                        $order_ids[$order->id] = $order->id;
                    }
                }
            }


            $order_return_categories = $this->OrderReturnCategories->find()
                ->order(['OrderReturnCategories.id' => 'ASC'])
                ->all();

            if ($order_return_categories->isEmpty()) {
                // You can handle the case where no orders are found
                // For example: set a flag or message
                $noOrdersFound = true;
            } else {
                foreach ($order_return_categories as $order_return_category) {
                    if (!empty($order_return_category->id)) {
                        $order_return_categories_ids[$order_return_category->id] = $order_return_category->name;
                    }
                }
            }

        } catch (\Exception $e) {
            // Handle exception, for example by setting an error flag or message
            $errorMessage = __('Something went wrong while fetching delivered orders.');
        }

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();

        $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

        $this->set(compact('order_ids', 'order_return_categories_ids', 'showrooms', 'warehouses'));

    }

    public function getOrderItemsById($id = null)
    {

        // Check order status
        $order = $this->OrderItems->Orders->find()
            ->where(['Orders.id' => $id])
            ->select(['id', 'status'])
            ->first();

        if (!$order || in_array($order->status, ['Cancelled', 'Returned', 'Deleted'])) {
            $response = [
                'status' => 'error',
                'message' => __('This order is either Cancelled, Returned, or Deleted and cannot be processed.')
            ];
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        }

        $excludedStatuses = ['Cancelled', 'Returned', 'Deleted'];

        // Fetch OrderItems by order_id and quantity > 0
        $order_items = $this->OrderItems->find()
            ->where([
                'OrderItems.order_id' => $id,
                'OrderItems.quantity >' => 0,
                'Orders.status NOT IN' => $excludedStatuses
            ])
            ->contain([
                'Orders' => ['fields' => ['id', 'status']],
                'Products' => function ($q) {
                    return $q->select(['Products.id', 'Products.name', 'Products.sku'])
                        ->contain([
                            'ProductVariants' => function ($q) {
                                return $q->select([
                                    'ProductVariants.product_id',
                                    'ProductVariants.id',
                                    'ProductVariants.sku',
                                    'ProductVariants.variant_name'
                                ]);
                            }
                        ]);
                }
            ])
            ->toArray();

        foreach ($order_items as $item) {
            $variantDetails = null;

            if ($item->product_variant_id) {
                $filtered_variants = array_filter($item->product->product_variants, function ($variant) use ($item) {
                    return $variant->id == $item->product_variant_id;
                });

                if (!empty($filtered_variants)) {
                    $variant = reset($filtered_variants);
                    $variantDetails = [
                        'variant_name' => $variant->variant_name,
                        'sku' => $variant->sku
                    ];
                }
            }

            $item->product->sku = $variantDetails['sku'] ?? $item->product->sku;

            if ($variantDetails) {
                $item->product->variant_name = $variantDetails['variant_name'];
            }

            if ($item->product_attribute_id) {
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                $item->product->attributes = $attributes ?: [];
            } else {
                $item->product->attributes = [];
            }
        }

        $response = [
            'status' => 'success',
            'order_products' => $order_items
        ];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    public function view($id = null)
    {
        $orderReturn = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => [
                    'Products' => function ($q) {
                        return $q->select(['Products.id', 'Products.name', 'Products.sku']);
                    },
                    'ProductVariants' => function ($q) {
                        return $q->select(['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.sku']);
                    },
                    'ProductAttributes' => function ($q) {
                        return $q->select(['ProductAttributes.id', 'ProductAttributes.attribute_id', 'ProductAttributes.attribute_value_id']);
                    }
                ],
                'Orders' => [
                    'Customers' => ['Users']
                ],
                'Users' => function ($q) {
                    return $q->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                },
                'VerifiedByUser' => function ($q) {
                    return $q->select(['VerifiedByUser.id', 'VerifiedByUser.first_name', 'VerifiedByUser.last_name']);
                },
                'OrderReturnCategories' => function ($q) {
                    return $q->select(['OrderReturnCategories.id', 'OrderReturnCategories.name']);
                }
            ])
            ->where(['OrderReturns.id' => $id])
            ->first();

        // Add return_to_name manually
        $returnTo = $orderReturn->return_to ?? null;
        $returnToId = $orderReturn->return_to_id ?? null;

        $returnToName = __('N/A');

        if(strtolower($orderReturn->pickup_required) === 'yes') {
            if (strtolower($returnTo) === 'showroom' && $returnToId) {
                $showroom = $this->Showrooms->find()
                    ->select(['id', 'name'])
                    ->where(['id' => $returnToId])
                    ->first();

                if ($showroom) {
                    $returnToName = __('Showroom: ') . $showroom->name;
                }
            } elseif (strtolower($returnTo) === 'warehouse' && $returnToId) {
                $warehouse = $this->Warehouses->find()
                    ->select(['id', 'name'])
                    ->where(['id' => $returnToId])
                    ->first();

                if ($warehouse) {
                    $returnToName = __('Warehouse: ') . $warehouse->name;
                }
            }
        }

        $orderReturn->return_to_name = $returnToName;

        // Handle Product Attribute name/value if present
        if (!empty($orderReturn->order_item->product_attribute_id)) {

            $attribute = $this->ProductAttributes->find()
                ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                ->contain([
                    'Attributes' => ['fields' => ['Attributes.name']],
                    'AttributeValues' => ['fields' => ['AttributeValues.value']]
                ])
                ->first();

            if ($attribute) {
                $orderReturn->order_item->attributes = [
                    'attribute_name' => $attribute->attribute->name ?? '',
                    'attribute_value' => $attribute->attribute_value->value ?? ''
                ];
            }
        }

        // Handle Return Images
        if (!empty($orderReturn->return_product_image)) {
            $imageUrls = explode(',', $orderReturn->return_product_image);

            $imageUrls = array_map(function ($image) {
                return $this->Media->getCloudFrontURL(trim($image));
            }, $imageUrls);

            $orderReturn->return_product_image = implode(',', $imageUrls);
        }

        $this->set(compact('orderReturn'));
    }

    public function saveNote()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false; // No view rendered

        $response = ['success' => false, 'message' => ''];

        $data = $this->request->getData();

        if (empty($data['id'])) {
            $response['message'] = __('Invalid Order Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        $orderReturn = $this->OrderReturns->get($data['id']);

        if (!$orderReturn) {
            $response['message'] = __('Order Return not found.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        // Patch entity with new note
        $orderReturn = $this->OrderReturns->patchEntity($orderReturn, ['note' => $data['note']]);

        if ($this->OrderReturns->save($orderReturn)) {
            $response['success'] = true;
        } else {
            $response['message'] = __('Failed to save note. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function rejectReturn()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Rejected');
            $orderReturn->verified_by = $user_detail->id;
            $orderReturn->verified_time = date('Y-m-d H:i:s');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            if ($orderReturn->order_item_id) {
                $orderItem = $this->OrderItems->get($orderReturn->order_item_id);
                $orderItem->status = 'Return Rejected';

                if (!$this->OrderItems->save($orderItem)) {
                    $response['message'] = __('Failed to update order item status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }
            }

            if ($orderReturn->order_id) {
                $order = $this->Orders->get($orderReturn->order_id);
                $order->status = 'Return Rejected';

                if (!$this->Orders->save($order)) {
                    $response['message'] = __('Failed to update order status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }
            }

            if (!empty($orderReturn->user->email)) {
                // Reload orderReturn with additional associations for email
                $orderReturn = $this->OrderReturns->find()
                    ->contain([
                        'OrderItems' => [
                            'Products',
                            'ProductVariants',
                            'ProductAttributes'
                        ],
                        'Orders' => ['Customers' => ['Users']],
                        'Users',
                        'VerifiedByUser',
                        'OrderReturnCategories'
                    ])
                    ->where(['OrderReturns.id' => $id])
                    ->first();

                $returnToName = 'N/A';
                if (!empty($orderReturn->return_to) && !empty($orderReturn->return_to_id)) {
                    if (strtolower($orderReturn->return_to) === 'showroom') {
                        $showroom = $this->Showrooms->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $showroom ? __('Showroom: ') . $showroom->name : $returnToName;
                    } elseif (strtolower($orderReturn->return_to) === 'warehouse') {
                        $warehouse = $this->Warehouses->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $warehouse ? __('Warehouse: ') . $warehouse->name : $returnToName;
                    }
                }
                $orderReturn->return_to_name = $returnToName;

                $attributeDetails = __('N/A');
                if (!empty($orderReturn->order_item->product_attribute_id)) {
                    $attribute = $this->ProductAttributes->find()
                        ->contain(['Attributes', 'AttributeValues'])
                        ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                        ->first();
                    if ($attribute) {
                        $attributeDetails = ($attribute->attribute->name ?? '') . ': ' . ($attribute->attribute_value->value ?? '');
                        $orderReturn->order_item->attributes = [
                            'attribute_name' => $attribute->attribute->name ?? '',
                            'attribute_value' => $attribute->attribute_value->value ?? ''
                        ];
                    }
                }

                $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
                $greeting = __('Dear {0},', $userName);

                $emailData = [
                    'greeting' => $greeting,
                    'message' => __("Your return request for the following product has been rejected by the verification team."),
                    'request_id' => $orderReturn->id,
                    'requested_date' => $orderReturn->requested_at ? $orderReturn->requested_at->format('Y-m-d') : 'N/A',
                    'order_id' => $orderReturn->order_id,
                    'order_date' => $orderReturn->order->order_date ? $orderReturn->order->order_date->format('Y-m-d') : 'N/A',
                    'product_name' => $orderReturn->order_item->product->name ?? 'N/A',
                    'sku' => !empty($orderReturn->order_item->product_variant_id)
                        ? ($orderReturn->order_item->product_variant->sku ?? 'N/A')
                        : ($orderReturn->order_item->product->sku ?? 'N/A'),
                    'variant' => $orderReturn->order_item->product_variant->variant_name ?? 'N/A',
                    'attributes' => $attributeDetails,
                    'rejected_date' => date('d-m-Y'),
                    'rejected_by' => ($orderReturn->verified_by_user->first_name ?? '') . ' ' . ($orderReturn->verified_by_user->last_name ?? ''),
                    'status' => __('Rejected')
                ];

                $subject = __("Order Return Request Rejected - #{$orderReturn->id}");
                $toEmails = [$orderReturn->user->email];

                $this->Global->send_email(
                    $toEmails,
                    null,
                    $subject,
                    'return_request_rejected',
                    $emailData
                );
            }

            $response['success'] = true;
            $response['message'] = __('Return request rejected successfully.');
        } catch (\Exception $e) {
            $this->log("Error rejecting return: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function approveReturn()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Approved');
            $orderReturn->verified_by = $user_detail->id;
            $orderReturn->verified_time = date('Y-m-d H:i:s');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            if ($orderReturn->order_item_id) {
                $orderItem = $this->OrderItems->get($orderReturn->order_item_id);
                $orderItem->status = 'Return Approved';

                if (!$this->OrderItems->save($orderItem)) {
                    $response['message'] = __('Failed to update order item status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }
            }

            if ($orderReturn->order_id) {
                $order = $this->Orders->get($orderReturn->order_id);
                $order->status = 'Return Approved';

                if (!$this->Orders->save($order)) {
                    $response['message'] = __('Failed to update order status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }
            }

            if (!empty($orderReturn->user->email)) {
                // Reload orderReturn with additional associations for email
                $orderReturn = $this->OrderReturns->find()
                    ->contain([
                        'OrderItems' => [
                            'Products',
                            'ProductVariants',
                            'ProductAttributes'
                        ],
                        'Orders' => ['Customers' => ['Users']],
                        'Users',
                        'VerifiedByUser',
                        'OrderReturnCategories'
                    ])
                    ->where(['OrderReturns.id' => $id])
                    ->first();

                $returnToName = 'N/A';
                if (!empty($orderReturn->return_to) && !empty($orderReturn->return_to_id)) {
                    if (strtolower($orderReturn->return_to) === 'showroom') {
                        $showroom = $this->Showrooms->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $showroom ? __('Showroom: ') . $showroom->name : $returnToName;
                    } elseif (strtolower($orderReturn->return_to) === 'warehouse') {
                        $warehouse = $this->Warehouses->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $warehouse ? __('Warehouse: ') . $warehouse->name : $returnToName;
                    }
                }
                $orderReturn->return_to_name = $returnToName;

                $attributeDetails = __('N/A');
                if (!empty($orderReturn->order_item->product_attribute_id)) {
                    $attribute = $this->ProductAttributes->find()
                        ->contain(['Attributes', 'AttributeValues'])
                        ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                        ->first();
                    if ($attribute) {
                        $attributeDetails = ($attribute->attribute->name ?? '') . ': ' . ($attribute->attribute_value->value ?? '');
                        $orderReturn->order_item->attributes = [
                            'attribute_name' => $attribute->attribute->name ?? '',
                            'attribute_value' => $attribute->attribute_value->value ?? ''
                        ];
                    }
                }

                // Get customer address
                $customerAddress = $this->CustomerAddresses->find()
                    ->where(['id' => $orderReturn->order->customer_address_id ?? 0])
                    ->first();

                $municipalityId = $customerAddress->municipality_id ?? null;
                $cityId = $customerAddress->city_id ?? null;

                // Get zone
                $zoneMunicipality = $this->ZoneMunicipalities->find()
                    ->where(['municipality_id' => $municipalityId, 'status' => 'A'])
                    ->first();

                $zoneId = $zoneMunicipality->zone_id ?? null;

                // Create shipment
                $shipment = $this->Shipments->newEmptyEntity();
                $shipment->sender_type = $orderReturn->return_to;
                $shipment->senderID = $orderReturn->return_to_id;
                $shipment->shipment_status = 'Pending';
                $shipment->delivery_status = 'Return Pickup';

                if ($this->Shipments->save($shipment)) {
                    // Create shipment order
                    $shipmentOrder = $this->ShipmentOrders->newEmptyEntity();
                    $shipmentOrder->shipment_id = $shipment->id;
                    $shipmentOrder->order_id = $orderReturn->order_id;
                    $shipmentOrder->customer_id = $orderReturn->order->customer_id ?? null;
                    $shipmentOrder->customer_address_id = $orderReturn->order->customer_address_id ?? null;
                    $shipmentOrder->city_id = $cityId;
                    $shipmentOrder->municipality_id = $municipalityId;
                    $shipmentOrder->zone_id = $zoneId;
                    $shipmentOrder->order_delivery_status = 'Return Pickup';

                    if ($this->ShipmentOrders->save($shipmentOrder)) {
                        $shipmentOrderItem = $this->ShipmentOrderItems->newEmptyEntity();
                        $shipmentOrderItem->shipment_order_id = $shipmentOrder->id;
                        $shipmentOrderItem->order_item_id = $orderReturn->order_item_id;
                        $shipmentOrderItem->quantity = $orderReturn->return_quantity;
                        $shipmentOrderItem->item_delivery_status = 'Return Pickup';
                        $this->ShipmentOrderItems->save($shipmentOrderItem);
                    }
                }

                $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
                $greeting = __('Dear {0},', $userName);

                $emailData = [
                    'greeting' => $greeting,
                    'message' => __("Your return request for the following product has been approved by the verification team."),
                    'request_id' => $orderReturn->id,
                    'requested_date' => $orderReturn->requested_at ? $orderReturn->requested_at->format('Y-m-d') : 'N/A',
                    'order_id' => $orderReturn->order_id,
                    'order_date' => $orderReturn->order->order_date ? $orderReturn->order->order_date->format('Y-m-d') : 'N/A',
                    'product_name' => $orderReturn->order_item->product->name ?? 'N/A',
                    'sku' => !empty($orderReturn->order_item->product_variant_id)
                        ? ($orderReturn->order_item->product_variant->sku ?? 'N/A')
                        : ($orderReturn->order_item->product->sku ?? 'N/A'),
                    'variant' => $orderReturn->order_item->product_variant->variant_name ?? 'N/A',
                    'attributes' => $attributeDetails,
                    'rejected_date' => date('d-m-Y'),
                    'rejected_by' => ($orderReturn->verified_by_user->first_name ?? '') . ' ' . ($orderReturn->verified_by_user->last_name ?? ''),
                    'status' => __('Approved')
                ];

                $subject = __("Order Return Request Approved - #{$orderReturn->id}");

                // Send to Requested Person
                if (!empty($orderReturn->user->email)) {
                    $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
                    $requesterEmailData = $emailData;
                    $requesterEmailData['greeting'] = __('Dear {0},', $userName);

                    $this->Global->send_email(
                        [$orderReturn->user->email],
                        null,
                        $subject,
                        'return_request_approved',
                        $requesterEmailData
                    );
                }

                // Send to Customer
                if (!empty($orderReturn->order->customer->user->email)) {
                    $customerName = trim($orderReturn->order->customer->user->first_name . ' ' . $orderReturn->order->customer->user->last_name);
                    $customerEmailData = $emailData;
                    $customerEmailData['greeting'] = __('Dear {0},', $customerName);

                    $this->Global->send_email(
                        [$orderReturn->order->customer->user->email],
                        null,
                        $subject,
                        'return_request_approved',
                        $customerEmailData
                    );
                }


            }

            $response['success'] = true;
            $response['message'] = __('Return request approved successfully.');
        } catch (\Exception $e) {
            $this->log("Error rejecting return: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function processRefund()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Refund Pending');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $response['success'] = true;
            $response['message'] = __('Refund pending approved successfully.');
        } catch (\Exception $e) {
            $this->log("Error rejecting return: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

}
