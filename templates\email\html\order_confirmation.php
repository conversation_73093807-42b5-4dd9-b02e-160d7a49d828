<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Order Confirmation</title>
    </head>
    <body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3;">
        <table role="presentation" style="width: 100%; background-color: #f3f3f3; border-collapse: collapse; margin: 0;">
            <tr>
                <td>
                    <table role="presentation" style="width: 100%; max-width: 600px; margin: auto; background-color: #ffffff; border: 1px solid #dddddd; padding: 20px;">
                        <!-- Header -->
                        <tr>
                            <td style="text-align: center; padding-bottom: 20px;">
                                <a href="<?= $site_url ?>">
                                    <img src="<?= $logo_url ?>" alt="Babiken Logo" style="width: 120px; height: auto;" />
                                </a>
                            </td>
                        </tr>

                        <!-- Order Confirmation -->
                        <tr>
                            <td style="padding-bottom: 20px;">
                                <h1 style="color: #f77f00; margin: 0;">Order Confirmation</h1>
                                <p style="font-size: 16px; color: #555555; margin-top: 10px;">
                                    Thank you for your order, <?= $customer_name ?>. We're processing it now.
                                </p>
                            </td>
                        </tr>

                        <!-- Order Details -->
                        <tr>
                            <td style="padding-bottom: 20px;">
                                <h2 style="color: #f77f00; border-bottom: 1px solid #dddddd; padding-bottom: 10px;">Order Summary</h2>
                                <p><strong>Order Number:</strong> #<?= $order_number ?></p>
                                <p><strong>Order Date:</strong> <?= $order_date ?></p>
                                <p><strong>Payment Method:</strong> <?= $payment_method ?></p>
                                <p><strong>Order Type:</strong> <?= $order_type ?></p>
                            </td>
                        </tr>

                        <!-- Product Details -->
                        <tr>
                            <td>
                                <h2 style="color: #f77f00; border-bottom: 1px solid #dddddd; padding-bottom: 10px;">Products Ordered</h2>
                                <?php foreach ($order_items as $item): ?>
                                <div style="display: flex; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 20px;">
                                    <div style="width: 100px; height: 100px; background-color: #f5f5f5; margin-right: 20px;">
                                        <img src="<?= $item['thumb_image'] ?>" alt="<?= $item['product']['name'] ?>" style="width: 100%; height: 100%; object-fit: contain;">
                                    </div>
                                    <div>
                                        <h3 style="margin: 0 0 10px 0;"><?= $item['product']['name'] ?? 'Product' ?></h3>
                                        <p style="margin: 0 0 5px 0;">Quantity: <?= $item['quantity'] ?></p>
                                        <p style="margin: 0 0 5px 0;">Price: <?= $this->Price->setPriceFormat($item['price']) ?></p>
                                        <p style="margin: 0;">Total: <?= $this->Price->setPriceFormat($item['total_price']) ?></p>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </td>
                        </tr>

                        <!-- Order Total -->
                        <tr>
                            <td>
                                <h2 style="color: #f77f00; border-bottom: 1px solid #dddddd; padding-bottom: 10px;">Order Total</h2>
                                <table style="width: 100%;">
                                    <tr>
                                        <td>Subtotal:</td>
                                        <td style="text-align: right;"><?= $this->Price->setPriceFormat($subtotal) ?></td>
                                    </tr>
                                    <tr>
                                        <td>Delivery Charge:</td>
                                        <td style="text-align: right;"><?= $this->Price->setPriceFormat($delivery_charge) ?></td>
                                    </tr>
                                    <tr>
                                        <td>Discount:</td>
                                        <td style="text-align: right;"><?= $this->Price->setPriceFormat($discount) ?></td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight: bold; border-top: 1px solid #ddd; padding-top: 10px;">Total:</td>
                                        <td style="font-weight: bold; border-top: 1px solid #ddd; padding-top: 10px; text-align: right;"><?= $this->Price->setPriceFormat($total) ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>


                        <!-- Footer -->
                        <tr>
                            <td style="padding-top: 20px; text-align: center; font-size: 14px; color: #888;">
                                <p><?= __('If you have any questions, please visit our') ?> <a href="<?= $site_url ?>/cms/shipping%20policy" style="color: #f77f00;"><?= __('Help Center') ?></a> <?= __('or contact our customer service.') ?></p>
                                <p><?= __('Thank you for shopping with Babiken!') ?></p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
</html>
