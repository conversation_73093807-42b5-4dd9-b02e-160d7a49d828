<?php

declare(strict_types=1);

namespace App\Controller;

/**
 * WarehouseStocks Controller
 *
 * @property \App\Model\Table\WarehouseStocksTable $WarehouseStocks
 */
class WarehouseStocksController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->WarehouseStocks->find()
            ->contain(['Warehouses', 'Products']);
        $warehouseStocks = $this->paginate($query);

        $this->set(compact('warehouseStocks'));
    }

    /**
     * View method
     *
     * @param string|null $id Warehouse Stock id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $warehouseStock = $this->WarehouseStocks->get($id, contain: ['Warehouses', 'Products', 'WarehouseStockMovements']);
        $this->set(compact('warehouseStock'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $warehouseStock = $this->WarehouseStocks->newEmptyEntity();
        if ($this->request->is('post')) {
            $warehouseStock = $this->WarehouseStocks->patchEntity($warehouseStock, $this->request->getData());
            if ($this->WarehouseStocks->save($warehouseStock)) {
                $this->Flash->success(__('The warehouse stock has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The warehouse stock could not be saved. Please, try again.'));
        }
        $warehouses = $this->WarehouseStocks->Warehouses->find('list', limit: 200)->all();
        $products = $this->WarehouseStocks->Products->find('list', limit: 200)->all();
        $this->set(compact('warehouseStock', 'warehouses', 'products'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Warehouse Stock id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $warehouseStock = $this->WarehouseStocks->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $warehouseStock = $this->WarehouseStocks->patchEntity($warehouseStock, $this->request->getData());
            if ($this->WarehouseStocks->save($warehouseStock)) {
                $this->Flash->success(__('The warehouse stock has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The warehouse stock could not be saved. Please, try again.'));
        }
        $warehouses = $this->WarehouseStocks->Warehouses->find('list', limit: 200)->all();
        $products = $this->WarehouseStocks->Products->find('list', limit: 200)->all();
        $this->set(compact('warehouseStock', 'warehouses', 'products'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Warehouse Stock id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $warehouseStock = $this->WarehouseStocks->get($id);
        if ($this->WarehouseStocks->delete($warehouseStock)) {
            $this->Flash->success(__('The warehouse stock has been deleted.'));
        } else {
            $this->Flash->error(__('The warehouse stock could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function getWarehouseStocks()
    {
        $product_id = $this->request->getQuery('product_id');

        $warehousestocks = $this->WarehouseStocks->find()
            ->select([
                'id' => 'WarehouseStocks.id',
                'product_id' => 'Products.id',
                'product_name' => 'Products.name',
                'price' => 'Products.sales_price',
                'warehouse_stock' => 'WarehouseStocks.quantity',
                'reserved_stock' => 'WarehouseStocks.reserved_stock',
                'purchased_stock' => 'WarehouseStocks.purchased_stock',
                'total_stock' => '(IFNULL(WarehouseStocks.quantity, 0) - IFNULL(WarehouseStocks.reserved_stock, 0) + IFNULL(WarehouseStocks.purchased_stock, 0))',
                'parentCategory' => 'Categories.name',
                'warehouse' => 'Warehouses.name'
            ])
            ->where(['WarehouseStocks.product_id' => $product_id])
            ->where(['WarehouseStocks.quantity <>' => 0])
            ->join([
                'Warehouses' => [
                    'table' => 'warehouses',
                    'type' => 'LEFT',
                    'conditions' => 'Warehouses.id = WarehouseStocks.warehouse_id'
                ],
                'Products' => [
                    'table' => 'products',
                    'type' => 'INNER',
                    'conditions' => 'Products.id = WarehouseStocks.product_id'
                ],
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductCategories.product_id = Products.id',
                        'ProductCategories.level' => 1
                    ]
                ],
                'Categories' => [
                    'table' => 'categories',
                    'type' => 'LEFT',
                    'conditions' => 'Categories.id = ProductCategories.category_id'
                ]
            ])
            ->toArray();

        // $this->log('Fetched showroom stocks: ' . json_encode($showroomstocks), 'debug');

        $response = ['warehousestocks' => $warehousestocks];

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    public function addStock()
    {
        $warehouse_stock = $this->WarehouseStocks->newEmptyEntity();
        if ($this->request->is('post')) {
            $warehouse_stock = $this->WarehouseStocks->patchEntity($warehouse_stock, $this->request->getData());
            if ($this->WarehouseStocks->save($warehouse_stock)) {
                $response = ['status' => 'success', 'message' => __('The product stock has been saved.')];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            }
            $response = ['status' => 'error', 'message' => __('The product stock could not be saved. Please, try again.')];
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        }
    }

    public function editStock($id = null)
    {
        if ($id === null) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Invalid ID']));
        }

        $warehouse_stock = $this->WarehouseStocks->get($id);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $warehouse_stock = $this->WarehouseStocks->patchEntity($warehouse_stock, $this->request->getData());
            if ($this->WarehouseStocks->save($warehouse_stock)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'success', 'warehouse_stock' => $warehouse_stock]));
            }
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Could not save the product stock.']));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => 'success', 'warehouse_stock' => $warehouse_stock]));
    }

    public function deleteStock($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $warehouse_stock = $this->WarehouseStocks->get($id);
        $response = ['success' => false, 'message' => 'The product stock could not be deleted. Please, try again.'];
        if ($warehouse_stock) {
            if ($this->WarehouseStocks->delete($warehouse_stock)) {
                $response = ['success' => true, 'message' => 'The product stock has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The product stock could not be deleted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product stock does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function viewStock($id = null)
    {
        $this->request->allowMethod(['get']);

        if (!$id) {
            $response = ['status' => 'error', 'message' => 'Invalid Stock ID.'];
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        $warehouseStock = $this->WarehouseStocks->find()
            ->select([
                'id' => 'WarehouseStocks.id',
                'product_name' => 'Products.name',
                'price' => 'Products.sales_price',
                'warehouse_stock' => 'WarehouseStocks.quantity',
                'reserved_stock' => 'WarehouseStocks.reserved_stock',
                'purchased_stock' => 'WarehouseStocks.purchased_stock',
                'total_stock' => '(IFNULL(WarehouseStocks.quantity, 0) - IFNULL(WarehouseStocks.reserved_stock, 0) + IFNULL(WarehouseStocks.purchased_stock, 0))',
                'category_name' => 'Categories.name',
                'warehouse_name' => 'Warehouses.name'
            ])
            ->where(['WarehouseStocks.id' => $id])
            ->join([
                'Warehouses' => [
                    'table' => 'warehouses',
                    'type' => 'LEFT',
                    'conditions' => 'Warehouses.id = WarehouseStocks.warehouse_id'
                ],
                'Products' => [
                    'table' => 'products',
                    'type' => 'INNER',
                    'conditions' => 'Products.id = WarehouseStocks.product_id'
                ],
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductCategories.product_id = Products.id',
                        'ProductCategories.level' => 1
                    ]
                ],
                'Categories' => [
                    'table' => 'categories',
                    'type' => 'LEFT',
                    'conditions' => 'Categories.id = ProductCategories.category_id'
                ]
            ])
            ->first();

        if ($warehouseStock) {
            $response = [
                'status' => 'success',
                'data' => [
                    'product_name' => $warehouseStock->product_name,
                    'category_name' => $warehouseStock->category_name,
                    'warehouse_name' => $warehouseStock->warehouse_name,
                    'price' => $warehouseStock->price,
                    'stock_quantity' => $warehouseStock->warehouse_stock ?? 0,
                    'reserved_quantity' => $warehouseStock->reserved_stock ?? 0,
                    'purchased_quantity' => $warehouseStock->purchased_stock ?? 0,
                    'total_available_stock' => $warehouseStock->total_stock ?? 0
                ]
            ];
        } else {
            $response = ['status' => 'error', 'message' => 'Warehouse stock not found.'];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }
}
