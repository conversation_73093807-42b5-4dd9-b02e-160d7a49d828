<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Shipments Model
 *
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\BelongsTo $Orders
 *
 * @method \App\Model\Entity\Shipment newEmptyEntity()
 * @method \App\Model\Entity\Shipment newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Shipment> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Shipment get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Shipment findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Shipment patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Shipment> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Shipment|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Shipment saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Shipment>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Shipment>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Shipment>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Shipment> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Shipment>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Shipment>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Shipment>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Shipment> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ShipmentsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('shipments');
        $this->setDisplayField('tracking_number');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Orders', [
            'foreignKey' => 'order_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
        ]);
        $this->belongsTo('CustomerAddresses', [
            'foreignKey' => 'customer_address_id',
        ]);
        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id',
        ]);
        $this->belongsTo('Municipalities', [
            'foreignKey' => 'municipality_id',
        ]);
        $this->belongsTo('Drivers', [
            'foreignKey' => 'driver_id',
        ]);
        $this->belongsTo('DeliveryPartners', [
            'foreignKey' => 'delivery_partner_id',
        ]);
        $this->hasMany('ShipmentDeliveryTrackings', [
            'foreignKey' => 'shipment_id',
        ]);
        $this->hasMany('ShipmentItems', [
            'foreignKey' => 'shipment_id',
        ]);

        // Define association with ShipmentOrders
        $this->hasMany('ShipmentOrders', [
            'foreignKey' => 'shipment_id',
            'joinType' => 'INNER',
        ]);

    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('order_id')
            ->notEmptyString('order_id');

        $validator
            ->nonNegativeInteger('customer_id')
            ->allowEmptyString('customer_id');

        $validator
            ->nonNegativeInteger('customer_address_id')
            ->allowEmptyString('customer_address_id');

        $validator
            ->nonNegativeInteger('city_id')
            ->allowEmptyString('city_id');

        $validator
            ->integer('municipality_id')
            ->allowEmptyString('municipality_id');

        $validator
            ->scalar('stock_type')
            ->allowEmptyString('stock_type');

        $validator
            ->scalar('shipment_number')
            ->maxLength('shipment_number', 255)
            ->allowEmptyString('shipment_number');

        $validator
            ->scalar('delivery_type')
            ->allowEmptyString('delivery_type');

        $validator
            ->integer('driver_id')
            ->allowEmptyString('driver_id');

        $validator
            ->integer('delivery_partner_id')
            ->allowEmptyString('delivery_partner_id');

        $validator
            ->scalar('tracking_number')
            ->maxLength('tracking_number', 255)
            ->allowEmptyString('tracking_number');

        $validator
            ->scalar('delivery_note_number')
            ->maxLength('delivery_note_number', 255)
            ->allowEmptyString('delivery_note_number');

        $validator
            ->scalar('sender_type')
            ->allowEmptyString('sender_type');

        $validator
            ->integer('senderID')
            ->allowEmptyString('senderID');

        $validator
            ->scalar('assignment_status')
            ->notEmptyString('assignment_status');

        $validator
            ->dateTime('assigned_date')
            ->allowEmptyDateTime('assigned_date');

        $validator
            ->nonNegativeInteger('assigned_by')
            ->allowEmptyString('assigned_by');

        $validator
            ->date('schedule_delivery_date')
            ->allowEmptyDate('schedule_delivery_date');

        $validator
            ->time('schedule_delivery_time')
            ->allowEmptyTime('schedule_delivery_time');

        $validator
            ->scalar('delivery_status')
            ->notEmptyString('delivery_status');

        // $validator
        //     ->dateTime('delivery_status_date')
        //     ->allowEmptyDateTime('delivery_status_date');

        $validator
            ->scalar('comments')
            ->allowEmptyString('comments');

        $validator
            ->scalar('proof_of_delivery')
            ->maxLength('proof_of_delivery', 255)
            ->allowEmptyString('proof_of_delivery');

        $validator
            ->scalar('special_instructions')
            ->allowEmptyString('special_instructions');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['order_id'], 'Orders'), ['errorField' => 'order_id']);
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);
        $rules->add($rules->existsIn(['customer_address_id'], 'CustomerAddresses'), ['errorField' => 'customer_address_id']);
        $rules->add($rules->existsIn(['city_id'], 'Cities'), ['errorField' => 'city_id']);
        $rules->add($rules->existsIn(['municipality_id'], 'Municipalities'), ['errorField' => 'municipality_id']);
        $rules->add($rules->existsIn(['driver_id'], 'Drivers'), ['errorField' => 'driver_id']);
        $rules->add($rules->existsIn(['delivery_partner_id'], 'DeliveryPartners'), ['errorField' => 'delivery_partner_id']);

        return $rules;
    }

    //S
    public function changeDeliveryStatus ($shipment_id, $data)
    {       
        $delivery_entity = $this->get($shipment_id);        
        $statusupdate['delivery_status'] = $data['delivery_status'];
        if($data['cash_collected']){
            $statusupdate['cash_collected'] = $data['cash_collected'];
        }
        $statusupdate['delivery_status_date'] = $data['delivery_status_date'];
        $delivery_entity = $this->patchEntity($delivery_entity, $statusupdate);
        $res = $this->save($delivery_entity);
        if($res) {
            return true;
        } else {
            return false;
        }        
    }

    //S
    public function listAssignedOrderOLD($driverId, $date, $searchTerm = null) {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'Shipments.id',
                'Shipments.order_id',
                'Shipments.schedule_delivery_date',
                'Shipments.schedule_delivery_time',
                'Shipments.sender_type',
                'Shipments.senderID',
                'sender_name' => 'COALESCE(Warehouses.name, Showrooms.name)',
                'warehouse_id' => 'Warehouses.id',
                'warehouse_latitude' => 'Warehouses.latitude',
                'warehouse_longitude' => 'Warehouses.longitude',
                'showroom_id' => 'Showrooms.id',
                'showroom_latitude' => 'Showrooms.latitude',
                'showroom_longitude' => 'Showrooms.longitude'
            ])
            ->leftJoin(
                ['Warehouses' => 'warehouses'],
                ['Shipments.sender_type' => 'Warehouse', 'Shipments.senderID = Warehouses.id']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['Shipments.sender_type' => 'Showroom', 'Shipments.senderID = Showrooms.id']
            )
            ->contain([
                'Orders' => [
                    'fields' => ['Orders.id', 'Orders.order_number', 'Orders.status', 'Orders.payment_method',
                     'Orders.delivery_mode_type', 'Orders.created'],

                      'OrderItems' => [
                        'fields' => ['OrderItems.id', 'OrderItems.order_id'],
                    ]                 
                ],
                /*'ShipmentItems' => [
                    'fields' => [
                        'ShipmentItems.shipment_id',
                        'shipment_item_count' => $this->ShipmentItems->find()
                            ->func()
                            ->count('*'), // Count items per shipment
                    ],
                ],*/
                'Customers' => [
                    'fields' => ['id','profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'mobile_no']],
                ],
                'CustomerAddresses' => [ 
                    'fields' => ['customer_id', 'city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode', 'latitude', 'longitude', 'phone_no1', 'phone_no2'],                   
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ]
            ])
            ->where([
                    'Shipments.driver_id' => $driverId,
                    'Shipments.assignment_status' => 'assigned',
                    'Shipments.delivery_status' => 'pending',
                    'Shipments.schedule_delivery_date' =>$date
                ])
            ->order(['Shipments.created' => 'DESC'])
            ->enableHydration(false);

            // Apply search term filter
            if (!empty($searchTerm)) {
                $ordersQuery->andWhere([
                    'OR' => [
                        'Orders.order_number LIKE' => '%' . $searchTerm . '%',
                        'Users.first_name LIKE' => '%' . $searchTerm . '%',
                        'Users.last_name LIKE' => '%' . $searchTerm . '%',
                        'Users.mobile_no LIKE' => '%' . $searchTerm . '%'
                    ]
                ]);
            }    

        // Execute the query
        $orders = $ordersQuery->all();
        // Iterate through the orders to count OrderItems
        foreach ($orders as $order) {
            // Ensure we are accessing the correct array structure for order_items
            $order_item_count = 0;
            
            // Check if 'Orders' and 'OrderItems' are set in the structure
            if (isset($order['order']['order_items'])) {
                $order_item_count = count($order['order']['order_items']);
            }

            // Add the count to the order
           $order['order']['order_item_count'] = $order_item_count;
        }
        return $orders->toArray();
    }

    public function listAssignedOrder($driverId, $date = null, $searchTerm = null)
    {
        $ordersQuery = $this->find()
            ->select([
                'Shipments.id',
                'Shipments.shipment_number',
                'Shipments.shipment_date',
                'Shipments.delivery_status',
                'Shipments.special_instructions',
                'Shipments.sender_type',
                'Shipments.senderID',
                'sender_name' => 'COALESCE(Warehouses.name, Showrooms.name)',
                'warehouse_id' => 'Warehouses.id',
                'warehouse_latitude' => 'Warehouses.latitude',
                'warehouse_longitude' => 'Warehouses.longitude',
                'showroom_id' => 'Showrooms.id',
                'showroom_latitude' => 'Showrooms.latitude',
                'showroom_longitude' => 'Showrooms.longitude',
                // Order count per shipment (using virtual field via counter cache or subquery)
                'order_count' => '(SELECT COUNT(*) FROM shipment_orders so WHERE so.shipment_id = Shipments.id)',
            ])
            ->leftJoin(
                ['Warehouses' => 'warehouses'],
                ['Shipments.sender_type' => 'Warehouse', 'Shipments.senderID = Warehouses.id']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['Shipments.sender_type' => 'Showroom', 'Shipments.senderID = Showrooms.id']
            )
            ->contain([
                'ShipmentOrders' => [
                    'fields' => [
                        'ShipmentOrders.id',
                        'ShipmentOrders.shipment_id',
                        'ShipmentOrders.order_id',
                        'ShipmentOrders.expected_delivery_date',
                        'ShipmentOrders.order_delivery_status',
                        'ShipmentOrders.delivery_status_date',

                        // Count of order items assigned in this shipment order
                        'order_item_count' => '(SELECT COUNT(*) FROM shipment_order_items soi WHERE soi.shipment_order_id = ShipmentOrders.id)'
                    ],
                    'Orders' => [
                        'fields' => [
                            'Orders.id', 'Orders.order_number',
                            'Orders.payment_method', 'Orders.delivery_mode_type', 'Orders.order_date'
                        ]
                    ],
                    'Customers' => [
                    'fields' => ['id', 'profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'mobile_no']]
                    ],
                    'CustomerAddresses' => [
                        'fields' => [
                            'customer_id', 'city_id', 'name', 'type', 'address_line1', 'address_line2',
                            'house_no', 'landmark', 'zipcode', 'latitude', 'longitude', 'phone_no1', 'phone_no2'
                        ],
                        'Cities' => ['fields' => ['id', 'city_name']],
                        'Municipalities' => ['fields' => ['id', 'name']]
                    ],
                    'ShipmentOrderItems' => [
                        'OrderItems' => [
                            'fields' => [
                                'OrderItems.id', 'OrderItems.order_id'
                            ],
                            // optionally contain Product or Variant if needed
                        ]
                    ]
                ]               
            ])
            ->where([
                'Shipments.driver_id' => $driverId,
                'Shipments.delivery_type' => 'Driver',
                //'Shipments.delivery_status' => 'Pending',
                'Shipments.status' => 'A'
            ])
            ->order(['Shipments.shipment_date' => 'DESC'])
            ->enableHydration(false); // optional depending on use case

            // Apply search term filter
            if (!empty($searchTerm)) {
            /*  $ordersQuery->andWhere([
                    'OR' => [
                        'Orders.order_number LIKE' => '%' . $searchTerm . '%',
                        'Users.first_name LIKE' => '%' . $searchTerm . '%',
                        'Users.last_name LIKE' => '%' . $searchTerm . '%',
                        'Users.mobile_no LIKE' => '%' . $searchTerm . '%'
                    ]
                ]); */
                $ordersQuery->andWhere(['Orders.order_number LIKE' => '%' . $searchTerm . '%']);
            }    

            // Execute the query
            $orders = $ordersQuery->all();

        return $orders->toArray();
    }


    //S
    public function listDeliveredOrder($driverId, $filter_order_status, $filter_payment_status, $filter_sdate, $time_period, $startDate, $endDate, $search_str, $page, $limit) {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'Shipments.id',
                'Shipments.order_id',
                'Shipments.schedule_delivery_date',
                'Shipments.schedule_delivery_time'
            ])
            ->contain([
                'Orders' => [
                    'fields' => ['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.status', 'Orders.payment_method', 'Orders.delivery_mode_type', 'Orders.created'],

                    'OrderItems' => [
                        'fields' => ['OrderItems.id', 'OrderItems.order_id', 'OrderItems.quantity', 'OrderItems.status', 'OrderItems.total_price', 'OrderItems.price'],
                        'Products' => ['fields' => ['id', 'name', 'reference_name', 'sales_price','promotion_price']],
                        'ProductVariants'  => ['fields' => ['id','product_id','variant_name','reference_name','sku']],
                    ],

                    'Transactions'                 
                ],
                /*'ShipmentItems' => [
                    'fields' => [
                        'ShipmentItems.shipment_id',
                        'shipment_item_count' => $this->ShipmentItems->find()
                            ->func()
                            ->count('*'), // Count items per shipment
                    ],
                ],*/
                'Customers' => [
                    'fields' => ['id','profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']],
                ],
                'CustomerAddresses' => [ 
                    'fields' => ['customer_id', 'city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode','phone_no1', 'phone_no2'],                   
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ]
            ])
            /*->join(['Transactions' => [
                'table' => 'transactions',
                'type' => 'INNER',
                'conditions' => 'Transactions.order_id = Orders.id'
            ]
            ])*/
            ->where([
                    'Shipments.driver_id' => $driverId,
                    'Shipments.delivery_status' => 'delivered'
                ])
            ->order(['Shipments.created' => 'DESC'])
            ->enableHydration(false);

            // Apply search term filter
            if (!empty($searchTerm)) {
                $ordersQuery->andWhere([
                    'OR' => [
                        'Orders.order_number LIKE' => '%' . $searchTerm . '%',
                        'Users.first_name LIKE' => '%' . $searchTerm . '%',
                        'Users.last_name LIKE' => '%' . $searchTerm . '%',
                        'Users.mobile LIKE' => '%' . $searchTerm . '%'
                    ]
                ]);
            }

            // Apply filters if present
            if (!empty($filter_order_status)) {
                $ordersQuery->where(['Orders.status' => $filter_order_status]);
            }

            if (!empty($filter_payment_status)) {
                $ordersQuery->where(['Transactions.payment_status' => $filter_payment_status]);
            }

            //Filter by date
            if (!empty($filter_sdate)) {
                $ordersQuery->where(['DATE(Shipments.delivery_status_date)' => $filter_sdate]);
            }

            //Filter by time period
            if (!empty($time_period)) {
                $ordersQuery->where([
                    'Shipments.delivery_status_date >=' => $startDate,
                    'Shipments.delivery_status_date <=' => $endDate,
                ]);
            }

        // Execute the query
        $orders = $ordersQuery->all();
        // Iterate through the orders to count OrderItems
        foreach ($orders as $order) {
            // Ensure we are accessing the correct array structure for order_items
            $order_item_count = 0;
            
            // Check if 'Orders' and 'OrderItems' are set in the structure
            if (isset($order['order']['order_items'])) {
                $order_item_count = count($order['order']['order_items']);
            }

            // Add the count to the order
           $order['order']['order_item_count'] = $order_item_count;
        }
        return $orders->toArray();
    }

    //S
    public function cashDeliveredOrder($driverId, $page, $limit) {

        $current_date = date('Y-m-d');
        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'Shipments.id',
                'Shipments.order_id',
                'Shipments.schedule_delivery_date',
                'Shipments.schedule_delivery_time'
            ])
            ->contain([
                'Orders' => [
                    'fields' => ['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.status', 'Orders.payment_method', 'Orders.delivery_mode_type', 'Orders.created'],

                    'OrderItems' => [
                        'fields' => ['OrderItems.id', 'OrderItems.order_id', 'OrderItems.quantity', 'OrderItems.status', 'OrderItems.total_price', 'OrderItems.price'],
                        'Products' => ['fields' => ['id', 'name', 'reference_name', 'sales_price','promotion_price']],
                        'ProductVariants'  => ['fields' => ['id','product_id','variant_name','reference_name','sku']],
                    ],

                    'Transactions'                 
                ],
                /*'ShipmentItems' => [
                    'fields' => [
                        'ShipmentItems.shipment_id',
                        'shipment_item_count' => $this->ShipmentItems->find()
                            ->func()
                            ->count('*'), // Count items per shipment
                    ],
                ],*/
                'Customers' => [
                    'fields' => ['id','profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']],
                ],
                'CustomerAddresses' => [ 
                    'fields' => ['customer_id', 'city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode','phone_no1', 'phone_no2'],                   
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ]
            ])
            /*->join(['Transactions' => [
                'table' => 'transactions',
                'type' => 'INNER',
                'conditions' => 'Transactions.order_id = Orders.id'
            ]
            ])*/
            ->where([
                    'Shipments.driver_id' => $driverId,
                    'Shipments.delivery_status' => 'delivered',
                    'DATE(Shipments.delivery_status_date)' => $current_date,
                    'Shipments.cash_collected' => 1
                ])
            ->order(['Shipments.created' => 'DESC'])
            ->enableHydration(false);

        // Execute the query
        $orders = $ordersQuery->all();
        // Iterate through the orders to count OrderItems
        foreach ($orders as $order) {
            // Ensure we are accessing the correct array structure for order_items
            $order_item_count = 0;
            
            // Check if 'Orders' and 'OrderItems' are set in the structure
            if (isset($order['order']['order_items'])) {
                $order_item_count = count($order['order']['order_items']);
            }

            // Add the count to the order
           $order['order']['order_item_count'] = $order_item_count;
        }
        return $orders->toArray();
    }

    //S
    public function cashOnHand($driverId) {

        $current_date = date('Y-m-d');
        $totalAmount = $this->find()
        ->select([
            'total' => $this->find()->func()->sum('ROUND(Orders.total_amount, 2)') // Round to 2 decimals
        ])
        ->innerJoinWith('Orders') // Using CakePHP's association method
        ->where([
            'Shipments.driver_id' => $driverId,
            'Shipments.delivery_status' => 'delivered',
            'DATE(Shipments.delivery_status_date)' => $current_date,
            'Shipments.cash_collected' => 1
        ])
        ->first();
        return $totalAmount;
    }
}
