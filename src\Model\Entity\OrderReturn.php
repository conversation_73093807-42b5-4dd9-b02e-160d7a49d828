<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * OrderReturn Entity
 *
 * @property int $id
 * @property int $order_item_id
 * @property int $order_return_category_id
 * @property string $reason
 * @property string $status
 * @property \Cake\I18n\DateTime $requested_at
 * @property \Cake\I18n\DateTime|null $processed_at
 * @property string $return_amount
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $updated
 *
 * @property \App\Model\Entity\OrderItem $order_item
 * @property \App\Model\Entity\OrderReturnCategory $order_return_category
 */
class OrderReturn extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'order_item_id' => true,
        'order_id' => true,
        'customer_id' => true,
        'order_return_category_id' => true,
        'return_quantity' => true,
        'return_product_image' => true,
        'reason' => true,
        'status' => true,
        'pickup_charge' => true,
        'pickup_required' => true,
        'return_to' => true,
        'return_to_id' => true,
        'requested_by' => true,
        'requested_at' => true,
        'verified_by' => true,
        'verified_time' => true,
        'processed_at' => true,
        'return_amount' => true,
        'note' => true,
        'created' => true,
        'updated' => true,
        'order_item' => true,
        'order_return_category' => true,
    ];
}
