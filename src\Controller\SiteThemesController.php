<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * SiteThemes Controller
 *
 * @property \App\Model\Table\SiteThemesTable $SiteThemes
 */
class SiteThemesController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }
    
    public function index()
    {
        $query = $this->SiteThemes->find();
        $siteThemes = $this->paginate($query);

        $title = "Theme Settings";
        $this->set(compact('siteThemes', 'title'));
    }

    /**
     * View method
     *
     * @param string|null $id Site Theme id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $siteTheme = $this->SiteThemes->get($id, contain: []);
        $this->set(compact('siteTheme'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $siteTheme = $this->SiteThemes->newEmptyEntity();
        if ($this->request->is('post')) {
            $siteTheme = $this->SiteThemes->patchEntity($siteTheme, $this->request->getData());
            if ($this->SiteThemes->save($siteTheme)) {
                $this->Flash->success(__('The site theme has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The site theme could not be saved. Please, try again.'));
        }
        $this->set(compact('siteTheme'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Site Theme id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $siteTheme = $this->SiteThemes->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $siteTheme = $this->SiteThemes->patchEntity($siteTheme, $this->request->getData());
            if ($this->SiteThemes->save($siteTheme)) {
                $this->Flash->success(__('The site theme has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The site theme could not be saved. Please, try again.'));
        }

        $title = "Theme Settings | Edit";
        $this->set(compact('siteTheme', 'title'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Site Theme id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $siteTheme = $this->SiteThemes->get($id);
        if ($this->SiteThemes->delete($siteTheme)) {
            $this->Flash->success(__('The site theme has been deleted.'));
        } else {
            $this->Flash->error(__('The site theme could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
