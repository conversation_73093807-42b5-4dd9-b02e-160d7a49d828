<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;
use Cake\Routing\Router;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class SupplierReturnOrdersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    
    protected $Showrooms;
    protected $Warehouses;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $SupplierProducts;
    protected $SupplierReturnOrdersItems;
    protected $ProductAttributes;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->SupplierReturnOrdersItems = $this->fetchTable('SupplierReturnOrdersItems');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
    }
    
    public function index()
    {
        $return_request = $this->SupplierReturnOrders->find()
            ->where(['SupplierReturnOrders.status IN' => ['A', 'I']])
            ->order(['SupplierReturnOrders.created' => 'DESC'])->toArray();  

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
            
        $this->set(compact('return_request', 'dateFormat', 'timeFormat'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $return_request = $this->SupplierReturnOrders->get($id, contain: [
            'Showrooms', 'Warehouses'
        ]);

        $return_products = $this->SupplierReturnOrdersItems->find()
                    ->where(['SupplierReturnOrdersItems.supplier_return_order_id' => $id])
                    ->contain([
                        'Products' => [
                            'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
                            'SupplierProducts' => [
                                'fields' => ['SupplierProducts.product_id','SupplierProducts.supplier_price'],
                                'Suppliers' => [ 
                                    'fields' => ['Suppliers.id', 'Suppliers.name']
                                ]
                            ],
                            'ProductVariants' => [
                                'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
                                // Ensure that ProductVariants are fetched only if variant_id exists
                                'conditions' => ['ProductVariants.id IS NOT NULL']
                            ]
                        ]
                    ])->toArray();                    

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        foreach ($return_products as $product) {

            // Format supplier price with currency and separator
            if (!empty($product->product->supplier_products)) {
                foreach ($product->product->supplier_products as $supplierProduct) {
                    if (!empty($supplierProduct->supplier_price)) {
                        // Format supplier price to include currency and separators
                        $supplierProduct->supplier_price = number_format((float)$supplierProduct->supplier_price, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol;
                    }
                }
            }

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }

            if($product->return_product_image)
            {
                $product->return_product_image = $this->Media->getCloudFrontURL($product->return_product_image);
            }

            $variantDetails = null;

            // Check if a variant_id is present
            if ($product->product_variant_id) {
                // Fetch only variants that match the variant_id in SupplierPurchaseOrdersItems
                $filtered_variants = array_filter($product->product->product_variants, function($variant) use ($product) {
                    return $variant->id == $product->product_variant_id;
                });

                // If a matching variant is found, get its details
                if (!empty($filtered_variants)) {
                    $variant = reset($filtered_variants); // Get the first matching variant
                    $variantDetails = [
                        'variant_name' => $variant->variant_name,
                        'sku' => $variant->sku
                    ];
                }
            }

            // Assign variant details back to the product
            if ($variantDetails) {
                $product->product->variant_name = $variantDetails['variant_name'];
                $product->product->sku = $variantDetails['sku'];
            }
            
            if ($product->product_attribute_id) 
            {
                // Assuming you have a ProductAttributes model to fetch the attributes
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $product->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    $product->product->attributes = $attributes; // Add attributes to the product
                } else {
                    $product->product->attributes = []; // Set to empty if no attributes found
                }
            } else {
                $product->product->attributes = []; // No attributes if attribute_id is null
            }
        }

        $response = [
            'status' => 'success',
            'return_request' => $return_request,
            'return_products' => $return_products
        ];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $return_request = $this->SupplierReturnOrders->newEmptyEntity();

        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $supplierId = $data['supplier_id'];

            $return_request = $this->SupplierReturnOrders->patchEntity($return_request, $data);

            if ($this->SupplierReturnOrders->save($return_request)) {
                $this->saveSupplierReturnOrdersItems($return_request);
                
                $query_return_request = $this->SupplierReturnOrders->find()
                                ->where(['SupplierReturnOrders.supplier_id' => $supplierId])
                                ->where(['SupplierReturnOrders.status IN' => ['A', 'I']])
                                ->order(['SupplierReturnOrders.created' => 'DESC'])->toArray();                                        

                $dateFormat = Configure::read('Settings.DATE_FORMAT');
                $timeFormat = Configure::read('Settings.TIME_FORMAT');

                $return_orders = [];
                $i = 1;
                foreach ($query_return_request as $order) {

                    $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                    'D' => ['label' => __('Deleted'), 'class' => 'col-red']
                    ];

                    $status = $statusMap[$order->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

                    $returnStatusMap = [
                        'Completed' => ['label' => __('Completed'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $return_status = $returnStatusMap[$order->return_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                    $return_orders[] = [
                        'return_date' => $order->initiated_date->format($dateFormat . ' ' . $timeFormat),
                        'bill_no' => h($order->bill_no),
                        'supp_bill_no' => h($order->supplier_bill_no),
                        'return_status' => '<div class="badge-outline ' . h($return_status['class']) . '">' . h($return_status['label']) . '</div>',
                        'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                        'actions' => '
                            <a onClick="openViewReturnOrderModal(' . $order->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'.
                            '<a onClick="openEditReturnOrderModal('.$order->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                            '<a href="' . Router::url(['controller' => 'SupplierReturnOrders', 'action' => 'delete', $order->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'
                    ];

                }    

                $this->set([
                    'return_orders' => $return_orders,
                    '_serialize' => ['return_orders'],
                ]);

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['data' => $return_orders]));

            }
            $this->Flash->error(__('The return order request could not be saved. Please, try again.'));
        }

        $showrooms = $this->Showrooms->getShowrooms();

        $warehouses = $this->Warehouses->getWarehouses();

        $purchase_order_bills = $this->SupplierPurchaseOrders->getPurchaseOrderBills();

        $supplier_products = $this->SupplierProducts->find()
            ->where(['SupplierProducts.status IN' => 'A'])
            ->contain(['Products', 'Suppliers'])->toArray();           

        foreach ($supplier_products as $product) {

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }
        }                                      
        
        $this->set(compact('return_request', 'showrooms', 'warehouses', 'purchase_order_bills', 'supplier_products'));
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $returnProductId = $this->request->getData('image_id');

        $return_product = $this->SupplierReturnOrdersItems->get($returnProductId);

        // echo "<pre>";print_r($return_product);die;

        if (!$return_product) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => __('failure'), 'message' => __('Product not found')]));
        }

        //babiken_uploads/order_return_defect_images/image-300x300_242F4.jpg

        if ($return_product) {
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');

            $filePath = WWW_ROOT . $uploadFolder . $return_product->return_product_image;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $return_product->return_product_image = null;
            if ($this->SupplierReturnOrdersItems->save($return_product)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => __('success'), 'message' => __('Image deleted successfully')]));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => __('failure'), 'message' => __('Failed to update product')]));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => __('failure'), 'message' => __('Image not found')]));
        }
    }

    protected function saveSupplierReturnOrdersItems($return_request)
    {
        // echo "<pre>";print_r($return_request->product_id);die;

        if (!empty($return_request->product_id) && is_array($return_request->product_id)) {

            $supplier_return_order_id = $return_request->id;
            $product_id = $return_request->product_id;
            $variant_id = $return_request->product_variant_id;
            $attribute_id = $return_request->product_attribute_id;
            $product_sku = $return_request->sku;
            $product_quantity = $return_request->quantity;
            $product_return_quantity = $return_request->return_quantity;
            $return_product_image = $return_request->return_product_image;
            $return_reason = $return_request->return_reason;

            // print_r($product_id);die;

            $this->SupplierReturnOrdersItems->deleteAll(['supplier_return_order_id' => $supplier_return_order_id]);

            for($i=0; $i < sizeof($product_id); $i++)
            {
                // print_r($product_id[$i]);

                if (isset($return_product_image[$i]) && $return_product_image[$i]->getError() === UPLOAD_ERR_OK) 
                {
                    $defect_image = $return_product_image[$i];
                    $fileName = trim($defect_image->getClientFilename());

                    if (!empty($fileName)) {
                        $imageTmpName = $defect_image->getStream()->getMetadata('uri');

                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.ORDER_RETURN_DEFECT_IMAGE');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__('Image could not be uploaded. Please, try again.'));
                            return $this->redirect(['action' => 'add']);
                        } else {
                            $return_image = $folderPath . $imageFile;
                        }
                    } else {
                        $this->Flash->error(__('Image is required.'));
                        return $this->redirect(['action' => 'add']);
                    }
                } else {
                    $return_image = '';
                }

                $mapping = $this->SupplierReturnOrdersItems->find()
                    ->where(['supplier_return_order_id' => $supplier_return_order_id, 'product_id' => $product_id[$i], 'sku' => $product_sku[$i], 'quantity' => $product_quantity[$i], 'return_quantity' => $product_return_quantity[$i], 'return_product_image' => $return_image, 'return_reason' => $return_reason[$i]])
                    ->first();

                if ($mapping) {
                    $mapping->product_id = $product_id[$i];
                } else {
                    $mapping = $this->SupplierReturnOrdersItems->newEntity([
                        'supplier_return_order_id' => $supplier_return_order_id,
                        'product_id' => $product_id[$i],
                        'product_variant_id' => $variant_id[$i],
                        'product_attribute_id' => $attribute_id[$i],
                        'sku' => $product_sku[$i],
                        'quantity' => $product_quantity[$i],
                        'return_quantity' => $product_return_quantity[$i],
                        'return_product_image' => $return_image,
                        'return_reason' => $return_reason[$i],
                    ]);
                }

                if (!$this->SupplierReturnOrdersItems->save($mapping)) {
                    return false;
                }
            }
        }

        return true;
    }

    
    protected function editSupplierReturnOrdersItems($return_request)
    {
        // echo "<pre>";print_r($return_request);die;

        if (!empty($return_request->product_id) && is_array($return_request->product_id)) {

            $supplier_return_order_id = $return_request->id;
            $return_product_id = $return_request->return_product_id;
            $product_id = $return_request->product_id;
            $variant_id = $return_request->variant_id;
            $attribute_id = $return_request->attribute_id;
            $product_sku = $return_request->sku;
            $product_quantity = $return_request->quantity;
            $product_return_quantity = $return_request->return_quantity;
            $return_product_image = $return_request->return_product_image;
            $return_reason = $return_request->return_reason;

            for($i=0; $i < sizeof($product_id); $i++)
            {
                $record = $this->SupplierReturnOrdersItems->get($return_product_id[$i]);
                // echo "<pre>";print_r($record);die;

                if (isset($return_product_image[$i]) && $return_product_image[$i]->getError() === UPLOAD_ERR_OK) 
                {
                    $defect_image = $return_product_image[$i];
                    $fileName = trim($defect_image->getClientFilename());

                    if (!empty($fileName)) {
                        
                        $imageTmpName = $defect_image->getStream()->getMetadata('uri');

                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.ORDER_RETURN_DEFECT_IMAGE');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__('Image could not be uploaded. Please, try again.'));
                            return $this->redirect(['action' => 'add']);
                        } else {

                            $return_image = $folderPath . $imageFile;
                        }
                    } else {
                        
                        $this->Flash->error(__('Image is required.'));
                        return $this->redirect(['action' => 'add']);
                    }
                } else {

                    $return_image = $record->return_product_image;
                }

                // if($return_image == '' || $return_image == null)
                // {
                //     $return_image = null;
                // }

                // echo "<pre>";print_r($return_image);die;

                $this->SupplierReturnOrdersItems->delete($record);

                $mapping = $this->SupplierReturnOrdersItems->find()
                    ->where(['supplier_return_order_id' => $supplier_return_order_id, 'product_id' => $product_id[$i], 'sku' => $product_sku[$i], 'quantity' => $product_quantity[$i], 'return_quantity' => $product_return_quantity[$i], 'return_product_image IS' => $return_image, 'return_reason' => $return_reason[$i]])
                    ->first();

                if ($mapping) {
                    $mapping->product_id = $product_id[$i];
                } else {
                    $mapping = $this->SupplierReturnOrdersItems->newEntity([
                        'supplier_return_order_id' => $supplier_return_order_id,
                        'product_id' => $product_id[$i],
                        'variant_id' => $variant_id[$i],
                        'attribute_id' => $attribute_id[$i],
                        'sku' => $product_sku[$i],
                        'quantity' => $product_quantity[$i],
                        'return_quantity' => $product_return_quantity[$i],
                        'return_product_image' => $return_image,
                        'return_reason' => $return_reason[$i],
                    ]);
                }

                if (!$this->SupplierReturnOrdersItems->save($mapping)) {
                    return false;
                }
            }
        }

        return true;
    }

    public function edit($id = null)
    {
        $return_request = $this->SupplierReturnOrders->get($id, contain: []);

        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $supplierId = $data['supplier_id'];

            // echo "<pre>";print_r($data);die;
            $record = $this->SupplierReturnOrders->get($id);
            $record->return_status = $data['return_status'];
            $record->status = $data['status'];

            $return_request = $this->SupplierReturnOrders->patchEntity($return_request, $data);

            if ($this->SupplierReturnOrders->save($record)) {

                if($data['return_status'] == 'Pending')
                {
                    $this->editSupplierReturnOrdersItems($return_request);
                }
                
                $query_return_request = $this->SupplierReturnOrders->find()
                                ->where(['SupplierReturnOrders.supplier_id' => $supplierId])
                                ->where(['SupplierReturnOrders.status IN' => ['A', 'I']])
                                ->order(['SupplierReturnOrders.created' => 'DESC'])->toArray();                                        

                $dateFormat = Configure::read('Settings.DATE_FORMAT');
                $timeFormat = Configure::read('Settings.TIME_FORMAT');

                $return_orders = [];
                $i = 1;
                foreach ($query_return_request as $order) {

                    $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                    'D' => ['label' => __('Deleted'), 'class' => 'col-red']
                    ];

                    $status = $statusMap[$order->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

                    $returnStatusMap = [
                        'Completed' => ['label' => __('Completed'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $return_status = $returnStatusMap[$order->return_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                    $return_orders[] = [
                        'return_date' => $order->initiated_date->format($dateFormat . ' ' . $timeFormat),
                        'bill_no' => h($order->bill_no),
                        'supp_bill_no' => h($order->supplier_bill_no),
                        'return_status' => '<div class="badge-outline ' . h($return_status['class']) . '">' . h($return_status['label']) . '</div>',
                        'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                        'actions' => '
                            <a onClick="openViewReturnOrderModal(' . $order->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'.
                            '<a onClick="openEditReturnOrderModal('.$order->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                            '<a href="' . Router::url(['controller' => 'SupplierReturnOrders', 'action' => 'delete', $order->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'
                    ];

                }    

                $this->set([
                    'return_orders' => $return_orders,
                    '_serialize' => ['return_orders'],
                ]);

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['data' => $return_orders]));

            }
            $this->Flash->error(__('The return order request could not be saved. Please, try again.'));
        }
    }

    public function getPurchaseOrderById($id = null)
    {

        $order_request = $this->SupplierPurchaseOrders->get($id, contain: ['Showrooms', 'Warehouses']);

        // Get supplier_id from the SupplierPurchaseOrders
        $purchaseOrder = $this->SupplierPurchaseOrders->get($id, [
            'fields' => ['supplier_id']
        ]);

        $supplier_id = $purchaseOrder->supplier_id;

        // Fetch purchase order products and join related tables
        $order_products = $this->SupplierPurchaseOrdersItems->find()
                ->where(['SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id])
                ->contain([
                    'Products' => function ($q) use ($supplier_id) {
                        return $q->select(['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'])
                            ->contain([
                                'SupplierProducts' => function ($q) use ($supplier_id) {
                                    return $q->select(['SupplierProducts.product_id', 'SupplierProducts.supplier_price'])
                                        ->where(['SupplierProducts.supplier_id' => $supplier_id])
                                        ->contain([
                                            'Suppliers' => function ($q) {
                                                return $q->select(['Suppliers.id', 'Suppliers.name']);
                                            }
                                        ]);
                                },
                                'ProductVariants' => function ($q) {
                                    return $q->select(['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'])
                                        ->where(['ProductVariants.id IS NOT NULL']); // Fetch only if variant exists
                                }
                            ]);
                    }
                ])
                ->toArray();

        // $order_products = $this->SupplierPurchaseOrdersItems->find()
        //             ->where(['SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id])
        //             ->contain([
        //                 'Products' => [
        //                     'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
        //                     'SupplierProducts' => [
        //                         'fields' => ['SupplierProducts.product_id','SupplierProducts.supplier_price'],
        //                         'Suppliers' => [ 
        //                             'fields' => ['Suppliers.id', 'Suppliers.name']
        //                         ]
        //                     ]
        //                 ]
        //             ])->toArray();                    

        foreach ($order_products as $product) {

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }

            $variantDetails = null;

            // Check if a variant_id is present
            if ($product->product_variant_id) {
                // Fetch only variants that match the variant_id in SupplierPurchaseOrdersItems
                $filtered_variants = array_filter($product->product->product_variants, function($variant) use ($product) {
                    return $variant->id == $product->product_variant_id;
                });

                // If a matching variant is found, get its details
                if (!empty($filtered_variants)) {
                    $variant = reset($filtered_variants); // Get the first matching variant
                    $variantDetails = [
                        'variant_name' => $variant->variant_name,
                        'sku' => $variant->sku
                    ];
                }
            }

            // Assign variant details back to the product
            if ($variantDetails) {
                $product->product->variant_name = $variantDetails['variant_name'];
                $product->product->sku = $variantDetails['sku'];
            }
            
            if ($product->product_attribute_id) 
            {
                // Assuming you have a ProductAttributes model to fetch the attributes
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $product->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    $product->product->attributes = $attributes; // Add attributes to the product
                } else {
                    $product->product->attributes = []; // Set to empty if no attributes found
                }
            } else {
                $product->product->attributes = []; // No attributes if attribute_id is null
            }
        }

        $response = [
            'status' => 'success',
            'order_request' => $order_request,
            'order_products' => $order_products
        ];
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    public function getMultiplePurchaseOrders()
    {
        $this->request->allowMethod(['post']); // Ensure only POST requests are allowed
        $purchase_order_ids = $this->request->getData('purchase_order_ids');

        if (empty($purchase_order_ids)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'No purchase orders selected']));
        }

        $orders = [];
        foreach ($purchase_order_ids as $id) {
            $order_request = $this->SupplierPurchaseOrders->get($id, contain: ['Showrooms', 'Warehouses']);
            $purchaseOrder = $this->SupplierPurchaseOrders->get($id, ['fields' => ['supplier_id']]);
            $supplier_id = $purchaseOrder->supplier_id;

            $order_products = $this->SupplierPurchaseOrdersItems->find()
                ->where(['SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id])
                ->contain([
                    'Products' => function ($q) {
                        return $q->select(['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'])
                            ->contain([
                                'ProductVariants' => function ($q) {
                                    return $q->select(['ProductVariants.id', 'ProductVariants.product_id', 'ProductVariants.variant_name']);
                                }
                            ]);
                    }
                ])
                ->toArray();

            // Fetch supplier_price separately
            foreach ($order_products as &$order_product) {
                $product = $order_product->product;
                $supplier_price = null;

                // Check if order_product has a product_variant_id
                if (!empty($order_product->product_variant_id)) {
                    $supplierProduct = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where([
                            'supplier_id' => $supplier_id,
                            'product_variant_id' => $order_product->product_variant_id, // Directly filter by variant ID
                            'status' => 'A'
                        ])
                        ->first();

                    if ($supplierProduct) {
                        $supplier_price = $supplierProduct->supplier_price;
                    }
                }

                // If no variant price found, fetch supplier price for product_id
                if ($supplier_price === null) {
                    $supplierProduct = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where([
                            'supplier_id' => $supplier_id,
                            'product_id' => $product->id,
                            'status' => 'A'
                        ])
                        ->first();

                    if ($supplierProduct) {
                        $supplier_price = $supplierProduct->supplier_price;
                    }
                }

                // Attach supplier_price to response
                $order_product->supplier_price = $supplier_price;
            }

            $orders[] = [
                'order_request' => $order_request,
                'order_products' => $order_products
            ];
        }

        $response = [
            'status' => 'success',
            'orders' => $orders
        ];
        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }
    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The return order request could not be deleted. Please, try again.'];

        try {
            $record = $this->SupplierReturnOrders->get($id);
            $record->status = 'D';

            if ($this->SupplierReturnOrders->save($record)) {
                $response = ['success' => true, 'message' => 'The return order request has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

}
