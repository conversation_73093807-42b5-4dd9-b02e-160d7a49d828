<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;
use DateTime;
use Mpdf\Mpdf;
use Cake\Http\Response;

/**
 * Orders Controller
 *
 * @property \App\Model\Table\OrdersTable $Orders
 */
class OrdersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\CustomersTable $Customers;
    protected \App\Model\Table\ProductsTable $Products;
    protected \App\Model\Table\UsersTable $Users;
    protected \App\Model\Table\ShowroomsTable $Showrooms;
    protected \App\Model\Table\TransactionsTable $Transactions;
    protected \App\Model\Table\CitiesTable $Cities;
    protected \App\Model\Table\MunicipalitiesTable $Municipalities;
    protected \App\Model\Table\ShowroomUsersTable $ShowRoomUsers;
    protected \App\Model\Table\CustomerAddressesTable $CustomerAddresses;
    protected \App\Model\Table\ShowroomUsersTable $ShowroomUsers;
    protected \App\Model\Table\SiteSettingsTable $SiteSettings;
    protected \App\Model\Table\ShipmentsTable $Shipments;
    protected \App\Model\Table\ShipmentOrdersTable $ShipmentOrders;
    protected \App\Model\Table\OrderCancellationsTable $OrderCancellations;
    protected \App\Model\Table\OrderCancellationCategoriesTable $OrderCancellationCategories;
    protected \App\Model\Table\OrderItemsTable $OrderItems;
    protected \App\Model\Table\OrderReturnsTable $OrderReturns;
    protected \App\Model\Table\ProductStocksTable $ProductStocks;
    protected \App\Model\Table\LoyaltyTable $Loyalty;
    protected \App\Model\Table\LoyaltySettingsTable $LoyaltySettings;
    protected \App\Model\Table\CustomerGroupMappingsTable $CustomerGroupMappings;
    protected \App\Model\Table\PartnersTable $Partners;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('WebsiteFunction');
        $this->Customers = $this->fetchTable('Customers');
        $this->Products = $this->fetchTable('Products');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->Cities = $this->fetchTable('Cities');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->ShowRoomUsers = $this->fetchTable('ShowroomUsers');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->Users = $this->fetchTable('Users');
        $this->ShowroomUsers = $this->fetchTable('ShowroomUsers');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Loyalty = $this->fetchTable('Loyalty');
        $this->LoyaltySettings = $this->fetchTable('LoyaltySettings');
        $this->CustomerGroupMappings = $this->fetchTable('CustomerGroupMappings');
        $this->Partners = $this->fetchTable('Partners');
    }

    public function index()
    {

        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        $SalesPersonRoleId = Configure::read('Constants.SALES_PERSON_ROLE_ID');
        $AdminRoleId = Configure::read('Constants.ADMIN_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');

        $OrderDetails = $this->Orders->getOrderDetails();
        $approvalShowrooms = [];
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'Showroom_Manager');
            } elseif ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'Showroom_Supervisor');
            } elseif ($user->role_id == $SalesPersonRoleId) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'SalesPerson');
            }
        }
        $orders = $OrderDetails->all();
        $ordersShowrooms = $OrderDetails->toArray();
        $approvalShowrooms = [];

        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();
                $approvalShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            }
            // elseif ($user->role_id == $ShowroomSupervisorRoleId) {
            //     $ShowroomsIDs = $this->Showrooms->find()
            //         ->select(['id'])
            //         ->where(['Showrooms.showroom_supervisor' => $user->id, 'Showrooms.status' => 'A'])
            //         ->toArray();
            //     $approvalShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            // } elseif ($user->role_id == $AdminRoleId) {
            //     if (!empty($ordersShowrooms)) {
            //         $approvalShowrooms = array_column($ordersShowrooms, 'showroom_id'); // Corrected field name
            //     }
            // }
        }

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $creditPartners = $this->Partners->find('list', [
            'keyField' => 'id',
            'valueField' => 'business_name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($creditPartners)) {
            $creditPartners  = ['' => 'No partners available'];
        }

        $this->set(compact('orders', 'orderstatuses', 'paymentstatuses', 'dateFormat', 'timeFormat', 'currencySymbol', 'approvalShowrooms', 'decimalSeparator', 'thousandSeparator', 'creditPartners'));
    }

    /**
     * View method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        // $order = $this->Orders->get($id, contain: ['Customers', 'CustomerAddresses', 'Offers', 'Showrooms', 'OrderItems', 'Returns', 'Shipments', 'Transactions']);

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'CustomerAddresses' => ['Cities', 'Municipalities'],
                'Offers',
                'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ]
                ],
                'Transactions'
            ]
        ]);

        // Assign the CloudFront URL to product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
        $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

        $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
        $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

        $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
        $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

        $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
        $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

        $orderStatus = Configure::read('Constants.ORDER_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();

        $OrderShipment = $this->ShipmentOrders->find()
            ->select([
                // 'Shipments.shipment_status',
                'Shipments.delivery_status'
            ])->contain('Shipments')
            ->where(['ShipmentOrders.order_id' => $id])
            ->order(['ShipmentOrders.id' => 'DESC']) // Sort by latest record
            ->first(); // Get only the last record


        // $this->log('test'.$orderStatusProgress,'debug');

        $orderCancellationCategories = $this->OrderCancellationCategories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($orderCancellationCategories)) {
            $orderCancellationCategories = ['' => 'No Categories available'];
        }

        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('order', 'currencySymbol', 'orderItemCount', 'orderStatusMap', 'orderStatus', 'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar', 'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar', 'deliveryStatusProgress', 'deliveryStatusProgressBar', 'OrderShipment', 'orderCancellationCategories', 'decimalSeparator', 'thousandSeparator'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $order = $this->Orders->newEmptyEntity();
        $uniqueOrderId = $this->Orders->generateUniqueOrderNum('Admin');
        $customerList = $this->Customers->find()
            ->select([
                'customer_id' => 'Customers.id',
                'full_name' => $this->Customers->Users->find()
                    ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
                    ->where(['Users.id = Customers.user_id', 'Users.status' => 'A']),
                'phone' => $this->Customers->Users->find()
                    ->func()
                    ->concat(['+', 'Users.country_code' => 'identifier', 'Users.mobile_no' => 'identifier']),
                'email' => 'Users.email'
            ])
            // ->select(['phone' => 'Customers.phone_number'])
            ->contain(['Users'])
            ->where(['Users.user_type' => 'Customer', 'Users.status' => 'A'])
            ->all();

        $customers = [];
        foreach ($customerList as $customer) {
            $customers[$customer->customer_id] = $customer->full_name . ' (' . $customer->phone . ')' . '(' . $customer->email . ')';
        }
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->where(['Products.status' => 'A', 'approval_status' => 'Approved'])
            ->order(['Products.name' => 'ASC'])
            ->all()
            ->toArray();

        if (empty($products)) {
            $products = ['' => 'No products available'];
        }
        $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        $loggedInShowroomId = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

                $loggedInShowroomId = !empty($showrooms) ? $showroomIds[0] : '';

                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];
                $loggedInShowroomId = !empty($showrooms) ? $showroomIds[0] : '';
                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else {
                $salesperson = $this->Users->find('list', [
                    'keyField' => 'id',
                    'valueField' => function ($row) {
                        return $row->first_name . ' ' . $row->last_name;
                    }
                ])
                    ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                    ->order(['Users.first_name' => 'ASC'])
                    ->all()
                    ->toArray();
            }
        } else {
            $salesperson = $this->Users->find('list', [
                'keyField' => 'id',
                'valueField' => function ($row) {
                    return $row->first_name . ' ' . $row->last_name;
                }
            ])
                ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                ->order(['Users.first_name' => 'ASC'])
                ->all()
                ->toArray();
        }


        if (empty($salesperson)) {
            $salesperson = ['' => 'No sales person available'];
        }



        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
            } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
            } else {
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['status' => 'A'])->toArray();
            }
        } else {
            $showrooms = $this->Showrooms->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['status' => 'A'])->toArray();
        }

        if (empty($showrooms)) {
            $showrooms = ['' => 'No showrooms available'];
        }
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])
            //->where(['Cities.status' => 'A'])
            ->all()
            ->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No cities available'];
        }

        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($municipalities)) {
            $municipalities = ['' => 'No Municipalities available'];
        }
        $statuses = Configure::read('Constants.ORDER_STATUSES');
        $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
        $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');

        $showroomPreSel = '';
        $IsShowroomanager = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showroomPreSel = $this->ShowRoomUsers->find()
                    ->select(['showroom_id'])
                    ->where(['user_id' => $user->id, 'status' => 'A'])
                    ->first();

                $showroomPreSel = $showroomPreSel ? $showroomPreSel->showroom_id : '';
            }
        }
        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');

        $StockShowrooms = [];

        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();
                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            } elseif ($user->role_id == $ShowroomSupervisorRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_supervisor' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();
                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            } elseif ($user->role_id == $SALES_PERSON_ROLE_ID) {
                $ShowroomsIDs = $this->ShowroomUsers->find()
                    ->select(['showroom_id'])
                    ->where(['ShowroomUsers.user_id' => $user->id, 'ShowroomUsers.status' => 'A'])
                    ->toArray();
                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            }
        }

        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('order', 'customers', 'products', 'uniqueOrderId', 'statuses', 'showrooms', 'shipping_methods', 'payment_statuses', 'currencySymbol', 'cities', 'Loyalty_Redeem', 'showroomPreSel', 'IsShowroomanager', 'municipalities', 'ABIDJAN_CITY_ID', 'salesperson', 'loggedInShowroomId', 'StockShowrooms', 'thousandSeparator', 'decimalSeparator'));

        if ($this->request->is('post')) {
            $order = $this->Orders->patchEntity($order, $this->request->getData(), [
                'associated' => [
                    'OrderItems' => [
                        'associated' => ['OrderItemAttributes']
                    ],
                    'Transactions',
                    'Customers' => ['Users'],
                ]
            ]);

            $deviceToken = $order->customer->user->fcm_token ?? '';

            $data = $this->request->getData();
            $user = $this->Authentication->getIdentity();
            $CallCentreAgentRoleId = Configure::read('Constants.CALL_CENTRE_AGENT_ROLE_ID');
            $CallCentreSupervisorRoleId = Configure::read('Constants.CALL_CENTRE_SUPERVISOR_ROLE_ID');
            if (!empty($user)) {
                if ($user->role_id == $CallCentreAgentRoleId || $user->role_id == $CallCentreSupervisorRoleId) {
                    $order['order_type'] = 'Online';
                } else {
                    $order['order_type'] = 'Showroom';
                }
            }

            $order['created_by'] = $user->id;
            $order['created_by_role'] = $user->role_id;
            $order->transactions[0]['invoice_number'] = $this->Transactions->generateUniqueInvoiceNum();
            $order->transactions[0]['transaction_number'] = $this->Transactions->generateUniqueTransactionNum();
            $order->transactions[0]['transaction_date'] = date('Y-m-d H:i:s');
            $order->transactions[0]['amount'] = $order->total_amount;
            $order->transactions[0]['payment_method'] = $order->payment_method;
            if ($order['delivery_mode'] == 'delivery') {
                $order['delivery_date'] = $this->CalculateEstimatedDelivery($data['delivery_mode_type']);
            }
            $errors = $order->getErrors();


            // foreach ($order->order_items as &$item) {
            //     if (!empty($item['order_item_attributes'])) {
            //         $item['order_item_attributes'] = array_filter($item['order_item_attributes'], function ($attribute) {
            //             return !empty($attribute['product_attribute_id']); // Keep only attributes with a value
            //         });
            //     }
            // }

            if ($order['order_type'] == 'Showroom' && $order['delivery_mode'] == 'delivery') {

                $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
                $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
                if (!empty($user)) {
                    $UserId = $user->id;
                    if ($user->role_id == $ShowroomManagerRoleId) {
                        $order['showroom_id'] = $this->Showrooms
                            ->find()
                            ->select(['id'])
                            ->where([
                                'Showrooms.showroom_manager' => $UserId,
                                'Showrooms.status' => 'A'
                            ])
                            ->first()
                            ->id;
                    } elseif ($user->role_id == $ShowroomSupervisorRoleId) {
                        $order['showroom_id'] = $this->Showrooms
                            ->find()
                            ->select(['id'])
                            ->where([
                                'Showrooms.showroom_supervisor' => $UserId,
                                'Showrooms.status' => 'A'
                            ])
                            ->first()
                            ->id;
                    }
                }
            }

            $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
            if ($user->role_id == $SALES_PERSON_ROLE_ID) {
                $UserId = $user->id;
                $order['sales_person_id'] = $UserId;
                $total = (float)$order['total_amount'];

                $salesperson_commissionpercent = (float)$this->SiteSettings->find()
                    ->select(['salesperson_commissionpercent'])
                    ->first()->salesperson_commissionpercent;

                $bonus = round($total * ($salesperson_commissionpercent / 100), 2);

                $order['sales_person_bonus'] = $bonus;
            }
            $order['status'] = 'Pending';

            if ($order['delivery_mode'] === 'delivery') {
                $order['status'] = 'Approved';
            } elseif ($order['delivery_mode'] === 'pickup' && !empty($user) && $user->role_id === $ShowroomManagerRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();

                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];

                if (in_array($order['showroom_id'], $StockShowrooms, true)) {
                    $order['status'] = 'Approved';
                }
            }


            if ($this->Orders->save($order, [
                'associated' => [
                    'OrderItems',
                    'Transactions'
                ]
            ])) {

                $this->WebsiteFunction->OrderConfirmationEmail($order->id);


                if (!empty($deviceToken)) {
                    $title = 'Order Confirmed - #' . $order['order_number'];
                    $body  = 'Your order has been confirmed and is now being processed.';
                    $customData = []; // Optional

                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );
                }


                $showroomsaved = $order['showroom_id'];

                if ($order['loyalty_amount'] > 0 && $order['loyalty_points_redeemed'] > 0) {
                    $loyalty = $this->Loyalty->find()->where(['customer_id' => $order['customer_id'], 'status' => 'A'])->first();
                    if ($loyalty) {
                        $loyalty->spent_amount += $order['loyalty_amount'];
                        $loyalty->points -= $order['loyalty_points_redeemed'];
                        $this->Loyalty->save($loyalty); // ✅ Save the changes
                    }
                }
                $customerGroupsQuery = $this->CustomerGroupMappings
                    ->find()
                    ->select(['customer_group_id'])
                    ->where(['customer_id' => $order['customer_id'], 'status' => 'A'])
                    ->all();

                $customerGroupIds = [];
                foreach ($customerGroupsQuery as $row) {
                    $customerGroupIds[] = $row->customer_group_id;
                }

                if (!empty($customerGroupIds)) {
                    $loyalty_setting = $this->LoyaltySettings
                        ->find()
                        ->where([
                            'customer_group_id IN' => $customerGroupIds,
                            // 'earning_threshold_amount <=' => $order['subtotal_amount'],
                            'status' => 'A'
                        ])
                        ->order(['earning_threshold_amount' => 'DESC'])
                        ->first();

                    if ($loyalty_setting) {
                        $subtotal_amount = round((float)$order['total_amount'] - (float)$order['delivery_charge'], 2);
                        $earning_threshold_amount = $loyalty_setting->earning_threshold_amount;
                        $points = $loyalty_setting->earning_points;

                        $earned_points = 0;
                        if ($earning_threshold_amount > 0) {
                            $earned_points = floor($subtotal_amount / $earning_threshold_amount) * $points;
                        }

                        $loyalty = $this->Loyalty
                            ->find()
                            ->where(['customer_id' => $order['customer_id'], 'status' => 'A'])
                            ->first();

                        if ($loyalty) {
                            $loyalty->points += $earned_points;
                            $this->Loyalty->save($loyalty);
                        } else {
                            $loyalty = $this->Loyalty->newEmptyEntity();
                            $loyalty->customer_id = $order['customer_id'];
                            $loyalty->points = $earned_points;
                            $loyalty->loyalty_category = 'standard';
                            $loyalty->spent_amount = '0.00';
                            $loyalty->status = 'A';
                            $this->Loyalty->save($loyalty);
                        }
                    }
                }



                // foreach ($order->order_items as &$item) {
                //     if (!empty($item['product_variant_id'])) {
                //         $conditions = ['product_variant_id' => $item['product_variant_id']];
                //         if (!empty($showroomsaved)) {
                //             $conditions['showroom_id'] = $showroomsaved;
                //         }

                //         $productStock = $this->ProductStocks->find()->where($conditions)->first();

                //         if ($productStock) {
                //             $productStock->reserved_stock += $item['quantity'];
                //         } else {
                //             $data = [
                //                 'product_variant_id' => $item['product_variant_id'],
                //                 'reserved_stock' => $item['quantity']
                //             ];
                //             if (!empty($showroomsaved)) {
                //                 $data['showroom_id'] = $showroomsaved;
                //             }
                //             $productStock = $this->ProductStocks->newEntity($data);
                //         }

                //         $this->ProductStocks->save($productStock);
                //     } elseif (!empty($item['product_id'])) {
                //         $conditions = [
                //             'product_id' => $item['product_id'],
                //             'product_variant_id IS' => null
                //         ];
                //         if (!empty($showroomsaved)) {
                //             $conditions['showroom_id'] = $showroomsaved;
                //         }

                //         $productStock = $this->ProductStocks->find()->where($conditions)->first();

                //         if ($productStock) {
                //             $productStock->reserved_stock += $item['quantity'];
                //         } else {
                //             $data = [
                //                 'product_id' => $item['product_id'],
                //                 'reserved_stock' => $item['quantity']
                //             ];
                //             if (!empty($showroomsaved)) {
                //                 $data['showroom_id'] = $showroomsaved;
                //             }
                //             $productStock = $this->ProductStocks->newEntity($data);
                //         }

                //         $this->ProductStocks->save($productStock);
                //     }
                // }

                $response = ['success' => true, 'message' => 'The Order has been saved.'];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                $this->Flash->success(__('The order has been saved.'));
                return $this->response;
                // return $this->redirect(['action' => 'index']);
            } else {
                $this->log('data: ' . json_encode($order->getErrors()), 'debug');
                $this->Flash->error(__('The order could not be saved. Please, try again.'));
            }
        }
        // $customerAddresses = $this->Orders->CustomerAddresses->find('list', limit: 200)->all();
        // $offers = $this->Orders->Offers->find('list', limit: 200)->all();
        // $showrooms = $this->Orders->Showrooms->find('list', limit: 200)->all();
        //$this->set(compact('order', 'customers', 'customerAddresses', 'offers', 'showrooms'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => [
                    'Users'
                ],
                'CustomerAddresses' => [
                    'Cities',
                    'Municipalities'
                ],
                'Offers',
                'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductVariants',
                        'ProductAttributes'
                    ],
                    'OrderItemAttributes' // Added association
                ],
                'Transactions'
            ]
        ]);
        $orderItems = $order->order_items;

        $customerList = $this->Customers->find()
            ->select([
                'customer_id' => 'Customers.id',
                'full_name' => $this->Customers->Users->find()
                    ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
                    ->where(['Users.id = Customers.user_id', 'Users.status' => 'A']),
                'phone' => $this->Customers->Users->find()
                    ->func()
                    ->concat(['+', 'Users.country_code' => 'identifier', 'Users.mobile_no' => 'identifier']),
                'email' => 'Users.email'
            ])
            // ->select(['phone' => 'Customers.phone_number'])
            ->contain(['Users'])
            ->where(['Users.user_type' => 'Customer', 'Users.status' => 'A'])
            ->all();

        $customers = [];
        foreach ($customerList as $customer) {
            $customers[$customer->customer_id] = $customer->full_name . ' (' . $customer->phone . ')' . '(' . $customer->email . ')';
        }
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->where(['Products.status' => 'A', 'approval_status' => 'Approved'])
            ->order(['Products.name' => 'ASC'])
            ->all()
            ->toArray();

        if (empty($products)) {
            $products = ['' => 'No products available'];
        }

        $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else {
                $salesperson = $this->Users->find('list', [
                    'keyField' => 'id',
                    'valueField' => function ($row) {
                        return $row->first_name . ' ' . $row->last_name;
                    }
                ])
                    ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                    ->order(['Users.first_name' => 'ASC'])
                    ->all()
                    ->toArray();
            }
        } else {
            $salesperson = $this->Users->find('list', [
                'keyField' => 'id',
                'valueField' => function ($row) {
                    return $row->first_name . ' ' . $row->last_name;
                }
            ])
                ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                ->order(['Users.first_name' => 'ASC'])
                ->all()
                ->toArray();
        }


        if (empty($salesperson)) {
            $salesperson = ['' => 'No sales person available'];
        }



        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
            } else if ($user->role_id == $ShowroomSupervisorRoleId) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
            } else {
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['status' => 'A'])->toArray();
            }
        } else {
            $showrooms = $this->Showrooms->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['status' => 'A'])->toArray();
        }

        if (empty($showrooms)) {
            $showrooms = ['' => 'No showrooms available'];
        }
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])
            //->where(['Cities.status' => 'A'])
            ->all()
            ->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No cities available'];
        }

        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($municipalities)) {
            $municipalities = ['' => 'No Municipalities available'];
        }
        $statuses = Configure::read('Constants.ORDER_STATUSES');
        $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
        $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');

        $showroomPreSel = '';
        $IsShowroomanager = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showroomPreSel = $this->ShowRoomUsers->find()
                    ->select(['showroom_id'])
                    ->where(['user_id' => $user->id, 'status' => 'A'])
                    ->first();

                $showroomPreSel = $showroomPreSel ? $showroomPreSel->showroom_id : '';
            }
        }
        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');
        // $uniqueOrderId = $order['order_number'];
        // $customerList = $this->Customers->find()
        //     ->select([
        //         'customer_id' => 'Customers.id',
        //         'full_name' => $this->Customers->Users->find()
        //             ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
        //             ->where(['Users.id = Customers.user_id', 'Users.status' => 'A'])
        //     ])
        //     ->select(['phone' => 'Customers.phone_number'])
        //     ->contain(['Users'])
        //     ->all();

        // $customers = [];
        // foreach ($customerList as $customer) {
        //     if ($customer->phone) {
        //         $formattedPhone = preg_replace('/(\d{3})(\d{3})(\d{4})/', '$1-$2-$3', $customer->phone);
        //     } else {
        //         $formattedPhone = '';
        //     }

        //     $customers[$customer->customer_id] = $customer->full_name . ' (' . $formattedPhone . ')';
        // }
        // $products = $this->Products->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])
        //     ->where(['Products.status' => 'A'])
        //     ->all()
        //     ->toArray();

        // if (empty($products)) {
        //     $products = ['' => 'No products available'];
        // }
        // $showrooms = $this->Showrooms->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])
        //     ->where(['Showrooms.status' => 'A'])
        //     ->all()
        //     ->toArray();

        // if (empty($showrooms)) {
        //     $showrooms = ['' => 'No showrooms available'];
        // }
        // $cities = $this->Cities->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])
        //     //->where(['Cities.status' => 'A'])
        //     ->all()
        //     ->toArray();

        // if (empty($cities)) {
        //     $cities = ['' => 'No products available'];
        // }
        // $statuses = Configure::read('Constants.ORDER_STATUSES');
        // $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
        // $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
        // $currencyConfig = Configure::read('Settings.Currency.format');
        // $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $this->set(compact('order', 'customers', 'products', 'statuses', 'showrooms', 'shipping_methods', 'payment_statuses', 'currencySymbol', 'cities', 'Loyalty_Redeem', 'showroomPreSel', 'IsShowroomanager', 'municipalities', 'ABIDJAN_CITY_ID', 'salesperson', 'orderItems'));

        if ($this->request->is(['patch', 'post', 'put'])) {
            $order = $this->Orders->patchEntity($order, $this->request->getData());
            if ($this->Orders->save($order)) {
                $this->Flash->success(__('The order has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The order could not be saved. Please, try again.'));
        }
        // $customers = $this->Orders->Customers->find('list', limit: 200)->all();
        // $customerAddresses = $this->Orders->CustomerAddresses->find('list', limit: 200)->all();
        // $offers = $this->Orders->Offers->find('list', limit: 200)->all();
        // $showrooms = $this->Orders->Showrooms->find('list', limit: 200)->all();
        // $this->set(compact('order', 'customers', 'customerAddresses', 'offers', 'showrooms'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $order = $this->Orders->get($id);
        $response = ['success' => false, 'message' => 'The order could not be cancelled. Please, try again.'];
        if ($order) {
            if ($this->Orders->delete($order)) {
                $response = ['success' => true, 'message' => 'The order has been marked as Cancelled.'];
            } else {
                $response = ['success' => false, 'message' => 'The order could not be cancelled. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The order does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function exportToCsv()
    {
        $OrderDetails = $this->Orders->getOrderDetails();
        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $header = ['ID', 'Order Number', 'Customer Name', 'Total Amount', 'Quantity', 'Order Status', 'Payment Method', 'Payment Status', 'Date & Time'];

        $this->response = $this->response->withType('text/csv');
        $this->response = $this->response->withDownload('orders_export.csv');
        $output = fopen('php://output', 'w');

        fputcsv($output, $header);

        foreach ($OrderDetails as $order) {
            fputcsv($output, [
                $order->id,
                (string)$order->order_number,
                $order->full_name . ' Customer Mo. No. ' . $order->phone_number,
                $order->total_amount,
                $order->quantity,
                isset($orderstatuses[$order->status]) ? $orderstatuses[$order->status] : '',
                $order->payment_method,
                isset($paymentstatuses[$order->transaction_status]) ? $paymentstatuses[$order->transaction_status] : '',
                $order->order_date ? $order->order_date->format($dateFormat . ' ' . $timeFormat) : ''
            ]);
        }

        fclose($output);
        return $this->response;
    }

    public function exportToExcel()
    {

        $orders = $this->Orders->getOrderDetails();
        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->response = $this->response->withType('application/vnd.ms-excel');
        $this->response = $this->response->withDownload('orders_export.xls');

        $output = '<table border="1">';
        $output .= '<tr>';
        $output .= '<th>ID</th>';
        $output .= '<th>Order Number</th>';
        $output .= '<th>Customer Name</th>';
        $output .= '<th>Total Amount</th>';
        $output .= '<th>Quantity</th>';
        $output .= '<th>Order Status</th>';
        $output .= '<th>Payment Method</th>';
        $output .= '<th>Payment Status</th>';
        $output .= '<th>Date & Time</th>';
        $output .= '</tr>';

        foreach ($orders as $order) {
            $output .= '<tr>';
            $output .= '<td>' . h($order->id) . '</td>';
            $output .= '<td>' . h((string)$order->order_number) . '</td>';
            $output .= '<td>' . h($order->full_name . ' Customer Mo. No. ' . $order->phone_number) . '</td>';
            $output .= '<td>' . h($order->total_amount) . '</td>';
            $output .= '<td>' . h($order->quantity) . '</td>';
            $output .= '<td>' . h(isset($orderstatuses[$order->status]) ? $orderstatuses[$order->status] : '') . '</td>';
            $output .= '<td>' . h($order->payment_method) . '</td>';
            $output .= '<td>' . h(isset($paymentstatuses[$order->transaction_status]) ? $paymentstatuses[$order->transaction_status] : '') . '</td>';
            $output .= '<td>' . h($order->order_date ? $order->order_date->format($dateFormat . ' ' . $timeFormat) : '') . '</td>';
            $output .= '</tr>';
        }

        $output .= '</table>';

        echo $output;

        return $this->response;
    }

    public function getCustomerOrders()
    {
        $customer_id = $this->request->getQuery('customer_id');

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $orders = $this->Orders->find()
            ->where(['Orders.customer_id' => $customer_id])
            ->select([
                'id' => 'Orders.id',
                'order_number' => 'Orders.order_number',
                'total_amount' => 'Orders.total_amount',
                'status' => 'Orders.status',
                'payment_method' => 'Orders.payment_method',
                'order_date' => 'Orders.order_date',
                'quantity' => $this->Orders->OrderItems->find()
                    ->func()
                    ->sum('OrderItems.quantity'),
                'payment_status' => 'Transactions.payment_status',
            ])
            ->leftJoinWith('OrderItems')
            ->leftJoinWith('Transactions')
            ->group('Orders.id')
            ->enableAutoFields(false)
            ->toArray();

        foreach ($orders as &$order) {
            $orderDate = $order['order_date'];
            $formattedDate = $orderDate->format($dateFormat . ' ' . $timeFormat);
            $order['order_date'] = $formattedDate;
        }

        $response = ['orders' => $orders];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function donwloadInvoice($id = null)
    {
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'CustomerAddresses' => ['Cities', 'Municipalities'],
                'Offers',
                'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ]
                ],
                'Transactions'
            ]
        ]);

        $customer_care_no = $this->SiteSettings->find()
            ->select(['customer_support_no'])
            ->first()->customer_support_no;
        $customer_care_email = $this->SiteSettings->find()
            ->select(['support_email'])
            ->first()->support_email;
        $call_center_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $whatsapp_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $after_sales_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;

        // Assign the CloudFront URL to product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
        $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

        $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
        $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

        $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
        $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

        $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
        $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

        $orderStatus = Configure::read('Constants.ORDER_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();

        // $this->log('test'.$orderStatusProgress,'debug');
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('order', 'currencySymbol', 'orderItemCount', 'orderStatusMap', 'orderStatus', 'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar', 'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar', 'deliveryStatusProgress', 'deliveryStatusProgressBar', 'customer_care_no', 'customer_care_email', 'call_center_no', 'whatsapp_no', 'after_sales_no', 'thousandSeparator', 'decimalSeparator'));
        $this->render('invoice');
    }

    function CalculateEstimatedDelivery($deliveryMode)
    {
        $now = new DateTime();
        $currentHour = (int)$now->format('H');

        $siteSettings = $this->SiteSettings->find()
            ->select(['express_delivery_order_cutoff_time'])
            ->first();
        $express_delivery_order_cutoff_time = (int)$siteSettings->express_delivery_order_cutoff_time;

        if ($deliveryMode === 'express') {
            if ($currentHour >= $express_delivery_order_cutoff_time) {
                $now->modify('+1 day'); // Move to next day if after cutoff
            }
        } else {
            $now->modify('+2 days'); // Standard delivery in 48 hours
        }

        return $now->format('Y-m-d');
    }

    public function printInvoicePdf()
    {
        $this->request->allowMethod(['post']); // Only allow POST requests
        $htmlContent = $this->request->getData('html');

        if (empty($htmlContent)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'No HTML content received'
            ]));
        }

        try {
            // Initialize MPDF with portrait mode (default)
            $mpdf = new Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4', // A4 portrait
                'orientation' => 'P' // "P" for Portrait, "L" for Landscape
            ]);

            // Read all CSS files from webroot/css directory
            $cssDirectory = WWW_ROOT . 'css' . DS;
            $cssFiles = glob($cssDirectory . '*.css'); // Get all CSS files
            $allCss = '';

            foreach ($cssFiles as $file) {
                $allCss .= file_get_contents($file) . "\n"; // Append CSS content
            }

            // Apply styles
            $mpdf->WriteHTML($allCss, \Mpdf\HTMLParserMode::HEADER_CSS);
            $mpdf->WriteHTML($htmlContent, \Mpdf\HTMLParserMode::HTML_BODY);

            // Output PDF
            $pdfFileName = "Invoice_" . time() . ".pdf";
            $mpdf->Output($pdfFileName, "I"); // Open in browser

        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]));
        }

        return $this->response->withType('application/pdf');
    }

    public function approveOrder($id = null)
    {

        $this->request->allowMethod(['post']);
        $id = $this->request->getData('id');
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            $order->status = 'Approved';

            if ($this->Orders->save($order)) {
                $this->WebsiteFunction->OrderApprovedEmail($order->id);
                $deviceToken = $order->customer->user->fcm_token;

                if (!empty($deviceToken)) {
                    $title = 'Order Approved - #' . $order['order_number'];
                    $body  = 'Your order has been approved and is now being processed.';
                    $customData = []; // Optional

                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );
                }
                $response = [
                    'status' => 'success',
                    'message' => __('Order has been approved.')
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to approve the order. Please try again.')
                ];
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function approveCancellation($id = null)
    {
        $this->request->allowMethod(['post']);

        $id = $this->request->getData('id');
        $condition = $this->request->getData('condition');
        // $order_cancellation_category_id = $this->request->getData('order_cancellation_category_id');
        // $reason = $this->request->getData('reason');

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            if ($condition == 'Approved') {
                $order->status = 'Cancelled';
            } else if ($condition == 'Rejected') {
                $order->status = 'Cancellation Rejected';
            } else {
                $order->status = 'Pending Cancellation';
            }


            if ($this->Orders->save($order)) {
                $lastOrderCancellation = null;

                foreach ($order->order_items as $item) {
                    $orderItem = $this->OrderItems
                        ->find()
                        ->where(['id' => $item->id])
                        ->first();
                    if ($orderItem) {
                        if ($condition == 'Approved') {
                            $orderItem->status = 'Cancelled';
                        } else if ($condition == 'Rejected') {
                            $orderItem->status = 'Cancellation Rejected';
                        } else {
                            $orderItem->status = 'Pending Cancellation';
                        }
                        $this->OrderItems->save($orderItem);
                    }
                    $orderCancellation = $this->OrderCancellations
                        ->find()
                        ->where(['order_item_id' => $item->id])
                        ->first();

                    if ($orderCancellation) {
                        if ($condition == 'Approved') {
                            $orderCancellation->status = 'Approved';
                        } else if ($condition == 'Rejected') {
                            $orderCancellation->status = 'Rejected';
                        } else {
                            $orderCancellation->status = 'Pending';
                        }

                        $orderCancellation->canceled_at = new DateTime();
                        // $orderCancellation = $this->OrderCancellations->newEmptyEntity();
                        // $orderCancellation->order_item_id = $item->id;
                        // $orderCancellation->order_id = $order->id;
                        // $orderCancellation->order_cancellation_category_id = $order_cancellation_category_id;
                        // $orderCancellation->reason = $reason;
                        // $orderCancellation->customer_id = $order->customer_id;
                        if ($this->OrderCancellations->save($orderCancellation)) {
                            $lastOrderCancellation = $orderCancellation; // Store last saved record
                        }
                    }
                }
                if ($lastOrderCancellation) {
                    $this->sendCancelledEmails($order, $lastOrderCancellation, $condition);
                    if ($condition == 'Approved') {
                        $this->WebsiteFunction->OrderCancellationApprovedEmail($order->id);
                        $deviceToken = $order->customer->user->fcm_token;;

                        if (!empty($deviceToken)) {
                            $title = 'Order Cancellation Approved - #' . $order['order_number'];
                            $body  = 'Your order cancellation has been approved and is now being processed.';
                            $customData = []; // Optional

                            $response = $this->Global->sendNotification(
                                [$deviceToken],
                                $title,
                                $body,
                                $customData
                            );
                        }
                        $response = [
                            'status' => 'success',
                            'message' => __('Order cancellation has been approved.')
                        ];
                    } else if ($condition == 'Rejected') {
                        $this->WebsiteFunction->OrderCancellationRejectedEmail($order->id);
                        $deviceToken = $order->customer->user->fcm_token;;

                        if (!empty($deviceToken)) {
                            $title = 'Order Cancellation Rejected - #' . $order['order_number'];
                            $body  = 'Your order cancellation has been rejected.';
                            $customData = []; // Optional

                            $response = $this->Global->sendNotification(
                                [$deviceToken],
                                $title,
                                $body,
                                $customData
                            );
                        }
                        $response = [
                            'status' => 'success',
                            'message' => __('Order cancellation has been rejected.')
                        ];
                    }
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => __('Order was not cancelled before and email not sent to client.')
                    ];
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to cancel the order. Please try again.')
                ];
            }
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }


    private function sendCancelledEmails($order, $orderCancellation, $condition)
    {
        if ($order && $orderCancellation) {
            $toEmails = [$order->customer->user->email];

            if (empty($toEmails)) {
                \Cake\Log\Log::warning("No valid recipients found for order ID: {$order->id}");
                return;
            }

            $emailData = [
                'order_number' => $order->order_number,
                'customer_name' => $order->customer->user->first_name . ' ' . $order->customer->user->last_name,
                'reason' => $orderCancellation->reason,  // Fixed double '$' typo
                'comment' => $orderCancellation->reason, // Fixed double '$' typo
                'canceled_at' => $orderCancellation->canceled_at, // Fixed double '$' typo & incorrect field name
                'status' => $orderCancellation->status
            ];

            $subject = "Order #{$order->order_number} Cancellation has been " . $condition;

            $this->Global->send_email(
                $toEmails,
                null,
                $subject,
                'order_cancellation',
                $emailData
            );
        }
    }

    public function approveReturn($id = null)
    {
        $this->request->allowMethod(['post']);

        $id = $this->request->getData('id');
        $condition = $this->request->getData('condition');
        // $order_cancellation_category_id = $this->request->getData('order_cancellation_category_id');
        // $reason = $this->request->getData('reason');

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            if ($condition == 'Approved') {
                $order->status = 'Return Approved';
            } else if ($condition == 'Rejected') {
                $order->status = 'Return Rejected';
            } else {
                $order->status = 'Pending Return';
            }


            if ($this->Orders->save($order)) {
                $lastOrderRejected = null;

                foreach ($order->order_items as $item) {
                    $orderItem = $this->OrderItems
                        ->find()
                        ->where(['id' => $item->id])
                        ->first();
                    if ($orderItem) {
                        if ($condition == 'Approved') {
                            $orderItem->status = 'Return Approved';
                        } else if ($condition == 'Rejected') {
                            $orderItem->status = 'Return Rejected';
                        } else {
                            $orderItem->status = 'Pending Return';
                        }
                        $this->OrderItems->save($orderItem);
                    }
                    $orderReturn = $this->OrderReturns
                        ->find()
                        ->where(['order_item_id' => $item->id])
                        ->first();

                    if ($orderReturn) {
                        if ($condition == 'Approved') {
                            $orderReturn->status = 'Approved';
                        } else if ($condition == 'Rejected') {
                            $orderReturn->status = 'Rejected';
                        } else {
                            $orderReturn->status = 'Pending';
                        }

                        $orderReturn->processed_at = new DateTime();
                        // $orderCancellation = $this->OrderCancellations->newEmptyEntity();
                        // $orderCancellation->order_item_id = $item->id;
                        // $orderCancellation->order_id = $order->id;
                        // $orderCancellation->order_cancellation_category_id = $order_cancellation_category_id;
                        // $orderCancellation->reason = $reason;
                        // $orderCancellation->customer_id = $order->customer_id;
                        if ($this->OrderReturns->save($orderReturn)) {
                            $lastOrderRejected = $orderReturn; // Store last saved record
                        }
                    }
                }
                if ($lastOrderRejected) {
                    $this->sendReturnedEmails($order, $lastOrderRejected, $condition);
                    if ($condition == 'Approved') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order return has been approved.')
                        ];
                    } else if ($condition == 'Rejected') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order return has been rejected.')
                        ];
                    }
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => __('Order was not returned before and email not sent to client.')
                    ];
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to retunr the order. Please try again.')
                ];
            }
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }
    private function sendReturnedEmails($order, $orderReturn, $condition)
    {
        if ($order && $orderReturn) {
            $toEmails = [$order->customer->user->email];

            if (empty($toEmails)) {
                \Cake\Log\Log::warning("No valid recipients found for order ID: {$order->id}");
                return;
            }

            $emailData = [
                'order_number' => $order->order_number,
                'customer_name' => $order->customer->user->first_name . ' ' . $order->customer->user->last_name,
                'reason' => $orderReturn->reason,
                'requested_at' => $orderReturn->requested_at,
                'processed_at' => $orderReturn->processed_at,
                'status' => $orderReturn->status
            ];

            $subject = "Order #{$order->order_number} Return has been " . $condition;

            $this->Global->send_email(
                $toEmails,
                null,
                $subject,
                'order_cancellation',
                $emailData
            );
        }
    }
}
