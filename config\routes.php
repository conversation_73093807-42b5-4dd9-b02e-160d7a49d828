<?php
/**
 * Routes configuration.
 *
 * In this file, you set up routes to your controllers and their actions.
 * Routes are very important mechanism that allows you to freely connect
 * different URLs to chosen controllers and their actions (functions).
 *
 * It's loaded within the context of `Application::routes()` method which
 * receives a `RouteBuilder` instance `$routes` as method argument.
 *
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */

use Cake\Routing\Route\DashedRoute;
use Cake\Routing\RouteBuilder;
use Cake\Routing\Router;

/*
 * This file is loaded in the context of the `Application` class.
  * So you can use  `$this` to reference the application class instance
  * if required.
 */
return function (RouteBuilder $routes): void {
    /*
     * The default class to use for all routes
     *
     * The following route classes are supplied with CakePHP and are appropriate
     * to set as the default:
     *
     * - Route
     * - InflectedRoute
     * - DashedRoute
     *
     * If no call is made to `Router::defaultRouteClass()`, the class used is
     * `Route` (`Cake\Routing\Route\Route`)
     *
     * Note that `Route` does not do any inflections on URLs which will result in
     * inconsistently cased URLs when used with `{plugin}`, `{controller}` and
     * `{action}` markers.
     */
    $routes->setRouteClass(DashedRoute::class);

    $routes->scope('/', function (RouteBuilder $builder): void {
        /*
         * Here, we are connecting '/' (base path) to a controller called 'Pages',
         * its action called 'display', and we pass a param to select the view file
         * to use (in this case, templates/Pages/home.php)...
         */
        $builder->connect('/customer/test', ['controller' => 'Customer', 'action' => 'test']);

        $builder->scope('/support-tickets', function ($routes) {
            $routes->connect('/', ['controller' => 'SupportTickets', 'action' => 'supportTicketDashboard']);
            $routes->connect('/dashboard', ['controller' => 'SupportTickets', 'action' => 'supportTicketDashboard']);
            $routes->connect('/list', ['controller' => 'SupportTickets', 'action' => 'supportTicketList']);
            $routes->connect('/add', ['controller' => 'SupportTickets', 'action' => 'supportTicketAdd'],['_method' => ['GET', 'POST']]);
            $routes->connect('/updateReply', ['controller' => 'SupportTickets', 'action' => 'reply'],['_method' => ['GET', 'POST']]);
            $routes->connect('/view/*', ['controller' => 'SupportTickets', 'action' => 'view'])
                ->setPass(['id'])
                ->setMethods(['GET', 'POST']);

            // write here update status route
            $routes->connect('/updateStatus', ['controller' => 'SupportTickets', 'action' => 'updateStatus'],['_method' => ['POST']]);

            // for add support ticket time search customer details
            $routes->connect('/customers/search', ['controller' => 'SupportTickets', 'action' => 'search']);
        });

        // Website routes
        $builder->connect("credit-payment/{id}", ['controller' => 'Website', 'action' => 'creditPayment']);


        $builder->connect('/shops', ['controller' => 'Website', 'action' => 'shopList']);
        $builder->connect('/deals', ['controller' => 'Website', 'action' => 'deals']);
        $builder->connect('/checkout/buy-now', ['controller' => 'Account', 'action' => 'buyNow'], ['_method' => ['POST']]);

        // Add route for support-desk/list
        $builder->connect('/support-desk/list', ['controller' => 'WebsiteCustomerSupport', 'action' => 'supportDesk']);
        $builder->connect('/support-desk/view/{ticketId}', ['controller' => 'WebsiteCustomerSupport', 'action' => 'supportDeskManage'])->setPass(['ticketId']);
        $builder->connect('/support-desk/add', ['controller' => 'WebsiteCustomerSupport', 'action' => 'reply'], ['_method' => ['POST']]);

        // Social login route
        $builder->connect('/users/social/login/:provider', ['controller' => 'Customer', 'action' => 'socialLogin'])
            ->setPatterns(['provider' => 'facebook|google']);

        // Social callback route
        $builder->connect('/customer/social-callback/:provider', ['controller' => 'Customer', 'action' => 'socialCallback'])
            ->setPatterns(['provider' => 'facebook|google']);

        // CMS Pages route
        $builder->connect('/cms/*', ['controller' => 'Website', 'action' => 'cmsPage']);
        $builder->connect('/become-seller', ['controller' => 'Website', 'action' => 'cmsPage', 'become-seller']);
        
      //  $builder->connect('/', ['controller' => 'Pages', 'action' => 'display', 'home']);
        $builder->connect('/', ['controller' => 'Website', 'action' => 'home']);

        /*Sitemaps*/
        $builder->connect('/sitemap.xml', ['controller' => 'Sitemaps', 'action' => 'index']);
        $builder->connect('/sitemap/products.xml', ['controller' => 'Sitemaps', 'action' => 'products']);
        $builder->connect('/sitemap/categories.xml', ['controller' => 'Sitemaps', 'action' => 'categories']);
        $builder->connect('/sitemap/brands.xml', ['controller' => 'Sitemaps', 'action' => 'brands']);
        $builder->connect('/sitemap/pages.xml', ['controller' => 'Sitemaps', 'action' => 'pages']);

        /*
         * ...and connect the rest of 'Pages' controller's URLs.
         */
        $builder->connect('/pages/*', 'Pages::display');

        $builder->connect('/account/cart', ['controller' => 'Account', 'action' => 'cart']);

        /** Website Routes Start ***/

        $builder->connect('/product-list/{categoryId}', ['controller' => 'Website', 'action' => 'productList'])
            ->setPass(['categoryId'])
            ->setPatterns(['categoryId' => '[a-zA-Z0-9\-]+']);

           //  http://babiken.com/website/product/44
           $builder->connect('/product/{product}', ['controller' => 'Website', 'action' => 'product'])
            ->setPass(['product'])
            ->setPatterns(['product' => '[a-zA-Z0-9\-]+']);
        
        $builder->connect('/product-brand-list/{categoryId}/{brandId}', ['controller' => 'Website', 'action' => 'productList'])
            ->setPass(['categoryId', 'brandId'])
            ->setPatterns([
            'categoryId' => '[a-zA-Z0-9\-]+',
            'brandId' => '[a-zA-Z0-9\-]+'
            ]);

        /** Website Routes End ***/
        

        $builder->setExtensions(['json']);

        /*
         * Connect catchall routes for all controllers.
         *
         * The `fallbacks` method is a shortcut for
         *
         * ```
         * $builder->connect('/{controller}', ['action' => 'index']);
         * $builder->connect('/{controller}/{action}/*', []);
         * ```
         *
         * You can remove these routes once you've connected the
         * routes you want in your application.
         */
        $builder->fallbacks();
    });


    /*
     * If you need a different set of middleware or none at all,
     * open new scope and define routes there.
     *
     * ```
     * $routes->scope('/api', function (RouteBuilder $builder): void {
     *     // No $builder->applyMiddleware() here.
     *
     *     // Parse specified extensions from URLs
     *     // $builder->setExtensions(['json', 'xml']);
     *
     *     // Connect API actions here.
     * });
     * ```
     */

    $routes->prefix('api', function(RouteBuilder $routes) {
        $routes->prefix('v10', ['path' => '/v1.0'], function($routes) {
            $routes->setExtensions(['json']);

            // Route for ApisController
            $routes->connect('/{action}/*', ['controller' => 'Apis']);

            // Route for SupervisorApisController
            $routes->connect('/supervisor/{action}/*', ['controller' => 'SupervisorApis']);

            // Route for DriverApisController
            $routes->connect('/driver/{action}/*', ['controller' => 'DriverApis']);

            $routes->fallbacks(DashedRoute::class);
        });
    });

};
