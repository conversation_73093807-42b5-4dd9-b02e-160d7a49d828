<?php

declare(strict_types=1);

namespace App\Controller;

/**
 * categoryAttributes Controller
 *
 * @property \App\Model\Table\categoryAttributesTable $categoryAttributes
 */
class CategoryAttributesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->categoryAttributes->find()
            ->contain(['Categories', 'Attributes']);
        $categoryAttributes = $this->paginate($query);

        $this->set(compact('categoryAttributes'));
    }

    /**
     * View method
     *
     * @param string|null $id Category Attribute id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function viewcategoryAttribute($id = null)
    {
        $this->request->allowMethod(['get']);
        $categoryAttribute = $this->categoryAttributes->get($id, [
            'contain' => ['Attributes']
        ]);
        if ($categoryAttribute) {
            $response = [
                'status' => 'success',
                'data' => [
                    'attribute_name' => $categoryAttribute->attribute->name,
                    'attribute_value' => $categoryAttribute->attribute_value->value,
                    'attribute_description' => $categoryAttribute->attribute_description
                ]
            ];
        } else {
            $response = ['status' => 'error', 'message' => 'Category Attribute not found'];
        }
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function addcategoryAttribute()
    {
        $categoryAttribute = $this->categoryAttributes->newEmptyEntity();
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $Category_id = $data['Category_id'];
            $attribute_id = $data['attribute_id'];

            $existingAttribute = $this->categoryAttributes->find()
                ->where([
                    'Category_id' => $Category_id,
                    'attribute_id' => $attribute_id,
                    'status' => 'A'
                ])
                ->first();

            if ($existingAttribute) {
                $response = ['status' => 'error', 'message' => __('The Category attribute could not be saved, Existing attribute exists. Please, try again.')];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            }
            $categoryAttribute = $this->categoryAttributes->patchEntity($categoryAttribute, $this->request->getData());
            if ($this->categoryAttributes->save($categoryAttribute)) {
                $response = ['status' => 'success', 'message' => __('The Category attribute has been saved.')];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            }
            $response = ['status' => 'error', 'message' => __('The Category attribute could not be saved. Please, try again.')];
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Category Attribute id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function editcategoryAttribute($id = null)
    {
        if ($id === null) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Invalid ID']));
        }

        $categoryAttribute = $this->categoryAttributes->get($id);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            $Category_id = $data['Category_id'];
            $attribute_id = $data['attribute_id'];

            $existingAttribute = $this->categoryAttributes->find()
                ->where([
                    'Category_id' => $Category_id,
                    'attribute_id' => $attribute_id,
                    'status' => 'A'
                ])
                ->first();

            if ($existingAttribute) {
                $response = ['status' => 'error', 'message' => __('The Category attribute could not be saved, Existing attribute exists. Please, try again.')];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            }
            $categoryAttribute = $this->categoryAttributes->patchEntity($categoryAttribute, $this->request->getData());
            if ($this->categoryAttributes->save($categoryAttribute)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'success', 'categoryAttribute' => $categoryAttribute, 'message' => 'Category Attribute saved.']));
            }
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Could not save the Category attribute.']));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => 'success', 'categoryAttribute' => $categoryAttribute]));
    }


    /**
     * Delete method
     *
     * @param string|null $id Category Attribute id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $categoryAttribute = $this->categoryAttributes->get($id);
        $response = ['success' => false, 'message' => 'The Category attribute could not be deleted. Please, try again.'];
        if ($categoryAttribute) {
            if ($this->categoryAttributes->delete($categoryAttribute)) {
                $response = ['success' => true, 'message' => 'The Category attribute has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Category attribute could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Category attribute does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function getAttributesData()
    {
        $category_id = $this->request->getQuery('category_id');
        $attributes = $this->CategoryAttributes->find('all')
            ->where(['CategoryAttributes.category_id' => $category_id])
            ->where(['CategoryAttributes.status !=' => 'D'])
            ->contain(['Attributes'])
            ->select([
                'id' => 'CategoryAttributes.id',
                'name' => 'Attributes.name',
                'status' => 'CategoryAttributes.status'
            ])
            ->toArray();

        $response = ['attributes' => $attributes];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }
}
