<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/orderScrollPagination.css') ?>">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<style>
    .cancel-popup {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
    .return-popup{
        z-index: 1001;
    }
    .cancel-popup-content {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        width: 400px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        position: relative;
        margin: 0 auto;
        top: 20%;
    }

    .cancel-popup-close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        color: #333;
    }

    .cancel-popup h3 {
        margin: 0 0 15px;
        font-size: 20px;
        text-align: center;
        color: #333;
    }

    .cancel-popup .form-group {
        margin-bottom: 15px;
    }

    .cancel-popup label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
        color: #555;
    }

    .cancel-popup select,
    .cancel-popup textarea {
        width: 100%;
        padding: 8px;
        font-size: 14px;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-sizing: border-box;
    }

    .cancel-popup textarea {
        resize: none;
    }

    .cancel-popup button[type="submit"] {
        width: 100%;
        padding: 10px;
        font-size: 16px;
        background-color: #007bff;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .cancel-popup button[type="submit"]:hover {
        background-color: #0056b3;
    }
</style>
<?php $this->end(); ?>

<div class="productCategoryListingC">
    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span
            class="productCategoryListing-home-span"><?= $this->Html->link('Home', ['controller' => 'Website', 'action' => 'home', 'prefix' => false]) ?></span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span Electronic-devices"><?= __('My Orders') ?></span>
    </div>
</div>

<div class="my-a-my-o-c">
<?php echo $this->element('web_sidebar'); ?>

    <div class="my-a-my-o-main">
    <div class="my-a-my-o-main-continer">
        <div id="order-list-container">
             <?php if(empty($orderList)): ?>
                        <div class="no-wishlist-msg"><?= __("Your order list is empty. Explore our products and place your first order!") ?></div>
            <?php endif; ?>
            <?php foreach ($orderList as $val): ?>
                <div class="my-a-my-o-main">
                    <div class="my-a-my-o-main-my-orders-one">

                        <div class="my-a-my-o-main-my-orders-header">

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label"><?= __('Order Number') ?></div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= $val['order_number']; ?></div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label"><?= __('Order Placed') ?></div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= $val['order_date']; ?></div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label"><?= __('Order Value') ?></div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= $this->Price->setPriceFormat($val['total_amount']) ?></div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label"><?= __('No of Items') ?></div>
                                <div class="my-a-my-o-main-my-orders-one-input"><?= count($val['order_items']); ?> Nos</div>
                            </div>

                            <div class="my-a-my-o-main-my-orders-one-label-input">
                                <div class="my-a-my-o-main-my-orders-one-label"><?= __('Delivary Address') ?></div>
                                <div class="my-a-my-o-main-my-orders-one-input">
                                    <?= h(
                                        $val['customer_address']['address_line1']
                                        ?? $val['showroom']['address']
                                        ?? 'N/A'
                                    ); ?>
                                </div>
                            </div>


                            <button class="my-a-my-o-main-my-orders-invoice-btn"><i class="fa fa-file"></i> <?= __('Invoice') ?>
                            </button>

                        </div>

                        <?php if (isset($val['order_items']) && !empty($val['order_items'])) : ?>
                            <?php foreach ($val['order_items'] as $product): ?>
                                <div class="my-order-item-details-container">
                                    <div>

                                        <div class="my-order-item-details">
                                            <a href="/product/<?= $product['product']['url_key'] ?>">
                                                <img src="<?= $product['thumb_image'] ?>" class="my-order-item-icon">
                                            </a>
                                            <div class="my-order-items-details-tpc">
                                                <div class="my-order-item-details-title-container">
                                                    <div class="my-order-item-details-title">
                                                    <a href="/product/<?= $product['product']['url_key'] ?>">
                                                        <?php if (!empty($product['product_variant_id']) && !empty($product['product_variant']['variant_name'])): ?>
                                                            <?= $product['product_variant']['variant_name'] ?>
                                                        <?php else: ?>
                                                            <?= $product['product']['name'] ?>
                                                        <?php endif; ?>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="my-order-item-details-price">
                                                   <?= __('Qty') ?> <?= $product['quantity']; ?>
                                                </div>
                                                <div class="my-order-item-details-price">
                                                    <?php if (!empty($product['product_variant_id'])): ?>
                                                        <?= $this->Price->setPriceFormat($product['product_variant']['promotion_price']) ?>
                                                    <?php else: ?>
                                                        <?= $this->Price->setPriceFormat($this->Price->getPriceById($product['product']['id'])) ?>
                                                    <?php endif; ?>

                                                </div>

                                                <div class="my-order-item-details-buttons">
                                                <?php if(!in_array($product['status'],['Pending','Pending Cancellation', 'Pending Return']) && empty($product['order_returns'])): ?>

                                                    <button class="my-order-item-details-return-btn"
                                                            data-order-id="<?= $product['order_id']; ?>"
                                                            data-order-item-id="<?= $product['id']; ?>"
                                                            data-return-amount="<?= empty($product['product_variant_id']) ? $product['product']['promotion_price'] : $product['product_variant']['promotion_price']; ?>">
                                                        <i class="fas fa-repeat my-order-item-details-return-btn-icn"></i>
                                                        <?= __('Return') ?>
                                                    </button>

                                                <?php endif; ?>



                                                <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'cart']) ?>" data-product-id="<?php echo $product['product']['id']; ?>" class="my-order-item-details-again-btn add-to-cart">
                                                <i class="fas fa-repeat my-order-item-details-again-btn-icn"></i>
                                                    <?= __('Buy it again') ?>
                                                </a>
                                                    <?php if(!in_array($val['status'],  ['Cancel Pending'])): ?>
                                                    <button class="my-order-item-details-cancel-btn cancel-api"
                                                            data-order-id="<?= $product['order_id']; ?>"
                                                            data-product-id="<?= $product['product']['id']; ?>"
                                                            data-order-item-id="<?= $product['id']; ?>">
                                                        <i class="fas fa-times my-order-item-details-cancel-btn-icn"></i> <?= __('Cancel') ?>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="body-background">
                                        <div class="my-acnt-tracking-items">
                                            <div class="progress-container">
                                                <div class="progress-bar">
                                                    <div class="progress-item">
                                                        <div class="progress-text">Order confirmed</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                    <div class="progress-item">
                                                        <div class="progress-text">Shipped</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                    <div class="progress-item">
                                                        <div class="progress-text">Out for Delivery</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                    <div class="progress-item">
                                                        <div class="progress-text">Delivered</div>
                                                        <div class="progress-dot" id="order-confirmed"></div>
                                                        <div class="progress-text">Wed, 8th Aug</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if(!empty($orderList) || count($orderList) != 0): ?>
            <div id="order-loading-indicator" class="order-loading-indicator">
                <div class="spinner"></div>
                <p><?= __('Loading more orders...') ?></p>
            </div>
            <div id="no-more-orders" class="no-more-orders">
                <p><?= __('No more orders to display') ?></p>
            </div>
        <?php endif; ?>

        <div id="cancel-popup" class="cancel-popup" style="display: none;">
            <div class="cancel-popup-content">
                <span class="cancel-popup-close-btn">×</span>
                <h3><?= __('Cancel Order') ?></h3>
                <form id="cancel-form">
                    <input type="hidden" id="cancel-order-id" name="order_id">
                    <input type="hidden" id="cancel-product-id" name="product_id">
                    <input type="hidden" id="order-item-id" name="order_item_id">
                    <div class="form-group">
                        <label for="cancel-category"><?= __('Select Category') ?></label>
                        <select id="cancel-category" name="category" required>
                            <?php foreach($categoriesArray as $value): ?>
                                <option value="<?= $value->id ?>"><?= $value->name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="cancel-reason"><?= __('Reason for Cancellation') ?></label>
                        <textarea id="cancel-reason" name="reason" rows="4" required></textarea>
                    </div>
                    <button type="submit"><?= __('Submit') ?></button>
                </form>
            </div>
        </div>
        <div id="return-popup" class="return-popup">
            <div class="return-popup-popup-content">
                <span class="return-popup-close-btn">×</span>
                <div class="pop-up-internal-splitter">
                    <div class="pop-up-internal-splitter-div">

                        <p class="return-confirmation"><?= __('Product Return') ?></p>
                        <input type="hidden" name="order_id" id="return-order-id">
                        <input type="hidden" name="order_item_id"  id="return-order-item-id">
                        <div class="return-confirmation-label-input">

                            <label for="cancel-category"><?= __('Select Category') ?></label>

                            <select id="cancel-category" name="reason_id" required>
                                <?php foreach($categoriesArray as $value): ?>
                                    <option value="<?= $value->id ?>"><?= $value->name ?></option>
                                <?php endforeach; ?>
                            </select>


                        </div>
                        <div class="return-confirmation-label-input">
                            <label id="return-reas0n-pop-up"><?= __('Return Reason') ?></label>
                            <textarea rows="4" cols="40" name='reason' placeholder="Description"
                                        class="return-popup-textarea"></textarea>
                        </div>

                        <div class="refund-price-c">
                            <div><?= __('Refund Value') ?></div>
                            <div id="return-amount" style="font-size: 18px;"></div>
                        </div>
                        <p class="font-small-blue"><i class="fa fa-warning"></i><span><?= __('The refund amount will be credited to the Wallet of payment.') ?></span>
                        </p>
                        <button id="return-popup-confirm-return"><?= __('RETURN THE') ?>
                            <?= __('PRODUCT') ?>
                        </button>
                        <button id="return-popup-cancel-return"><?= __('CANCEL') ?></button>
                    </div>
                </div>
            </div>
        </div>
<?php $this->start('add_js'); ?>
        <script>
            $(document).ready(function() {
                var loading = false;
                var currentPage = 2; // Start from page 2 since page 1 is loaded initially
                var allOrdersLoaded = false;
                var loadingIndicator = $('#order-loading-indicator');
                var noMoreOrdersMessage = $('#no-more-orders');

                // Function to check if we've scrolled to the bottom of the page
                function isNearBottom() {
                    return $(window).scrollTop() + $(window).height() > $(document).height() - 200;
                }

                // Function to load more orders
                function loadMoreOrders() {
                    if (loading || allOrdersLoaded) return;

                    loading = true;
                    loadingIndicator.show();

                    $.ajax({
                        url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'order']) ?>',
                        type: 'GET',
                        data: { page: currentPage },
                        success: function(response) {
                            if (response.trim()) {
                                $('#order-list-container').append(response);
                                currentPage++;
                            } else {
                                allOrdersLoaded = true;
                                noMoreOrdersMessage.show();
                            }
                        },
                        error: function() {
                            console.error('Error loading orders');
                        },
                        complete: function() {
                            loading = false;
                            loadingIndicator.hide();
                        }
                    });
                }

                // Check on scroll if we need to load more orders
                $(window).on('scroll', function() {
                    if (isNearBottom()) {
                        loadMoreOrders();
                    }
                });

                // Initial check in case the page isn't tall enough to scroll
                if (isNearBottom()) {
                    loadMoreOrders();
                }
            });
        </script>
 <?php $this->end(); ?>



<script>
document.addEventListener('DOMContentLoaded', function () {
    initializeReturnPopup();
});

function initializeReturnPopup() {
    var popup = document.querySelector(".return-popup");
    var closeBtn = document.querySelector(".return-popup .return-popup-close-btn");
    var confirmBtn = document.getElementById("return-popup-confirm-return");
    var cancelBtn = document.getElementById("return-popup-cancel-return");

    setupPopupCloseEvents(popup, closeBtn, cancelBtn);
    setupConfirmReturnAction(popup, confirmBtn);

    // Use event delegation to handle dynamically added buttons
    $(document).on('click', '.my-order-item-details-return-btn', function(event) {
        openReturnPopup(this, popup);
    });
}

function openReturnPopup(button, popup) {
    var orderId = button.getAttribute('data-order-id');
    var orderItemId = button.getAttribute('data-order-item-id');
    var returnAmount = button.getAttribute('data-return-amount');

    document.getElementById('return-order-id').value = orderId;
    document.getElementById('return-order-item-id').value = orderItemId;
    document.getElementById('return-amount').textContent = returnAmount + ' FCFA';

    popup.style.display = "block";
}

function setupPopupCloseEvents(popup, closeBtn, cancelBtn) {
    closeBtn.addEventListener('click', function () {
        closeReturnPopup(popup);
    });

    cancelBtn.addEventListener('click', function () {
        closeReturnPopup(popup);
    });

    window.addEventListener('click', function (event) {
        if (event.target == popup) {
            closeReturnPopup(popup);
        }
    });
}

function closeReturnPopup(popup) {
    popup.style.display = "none";
}

function setupConfirmReturnAction(popup, confirmBtn) {
    confirmBtn.addEventListener('click', function () {
        confirmReturn(popup);
    });
}

function confirmReturn(popup) {
    var orderId = document.getElementById('return-order-id').value;
    var orderItemId = document.getElementById('return-order-item-id').value;
    var categoryId = document.getElementById('cancel-category').value;
    var reason = document.querySelector('.return-popup-textarea').value;

    $.ajax({
        headers: {
            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
        },
        url: '<?= $this->Url->build(["controller" => "Account", "action" => "orderReturnWeb"]) ?>',
        type: 'POST',
        data: {
            order_id: orderId,
            order_item_id: orderItemId,
            order_return_category_id: categoryId,
            reason: reason
        },
        dataType: "json",
        success: function (response) {
            popup.style.display = 'none';
            showSuccessMessage(response.message);
        },
        error: function (xhr) {
            var response = JSON.parse(xhr.responseText);
            showError(response.message || 'An error occurred');
            popup.style.display = 'none';
        }
    });
}

function showSuccessMessage(message) {
    toastr.success(message, '', {
        timeOut: 3000,
        progressBar: true,
        onHidden: function () {
            location.reload();
        }
    });
}

function showError(message) {
    toastr.warning(message, '', {
        timeOut: 3000,
        progressBar: true,
    });
}





        function showCancelPopup(orderId, productId, orderItemId) {
            document.getElementById('cancel-order-id').value = orderId;
            document.getElementById('cancel-product-id').value = productId;
            document.getElementById('order-item-id').value = orderItemId;
            document.getElementById('cancel-popup').style.display = 'block';
        }

        $(document).ready(function() {
            const cancelPopup = document.getElementById('cancel-popup');
            const closeBtn = document.querySelector('.cancel-popup-close-btn');
            const cancelForm = document.getElementById('cancel-form');

            // Close the popup
            $(closeBtn).on('click', function() {
                cancelPopup.style.display = 'none';
            });

            // Close the popup if clicked outside of it
            $(window).on('click', function(event) {
                if (event.target == cancelPopup) {
                    cancelPopup.style.display = 'none';
                }
            });

            // Use event delegation for cancel buttons
            $(document).on('click', '.my-order-item-details-cancel-btn', function() {
                const orderId = $(this).attr('data-order-id');
                const productId = $(this).attr('data-product-id');
                const orderItemId = $(this).attr('data-order-item-id');
                showCancelPopup(orderId, productId, orderItemId);
            });

            // Handle form submission
            $(cancelForm).on('submit', function(event) {
                event.preventDefault();
                const formData = new FormData(cancelForm);

                $.ajax({
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'orderCancelWeb']) ?>',
                    type: 'POST',
                    data: {
                        order_id: formData.get('order_id'),
                        product_id: formData.get('product_id'),
                        order_item_id: formData.get('order_item_id'),
                        reason_id: formData.get('category'),
                        reason: formData.get('reason')
                    },
                    dataType: "json",
                    success: function (response) {
                        cancelPopup.style.display = 'none';
                        toastr.success(response.message, '', {
                        timeOut: 3000,  // 3 seconds before hiding the toastr message
                        progressBar: true,
                        onHidden: function () {
                            // Reload the page after the toastr message disappears
                            location.reload();
                        }
                        });

                    },
                    error: function (xhr, status, error) {
                        var response = JSON.parse(xhr.responseText);
                        toastr.warning(response.message || 'An error occurred', '', {
                                timeOut: 3000,
                                progressBar: true,
                        });
                        cancelPopup.style.display = 'none';
                    }
                });
            };
        });
</script>

<script>



</script>


    </div>
</div>
</div>
