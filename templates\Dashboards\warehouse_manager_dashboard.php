<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
    }

    .pending_actions{
        min-height: max-content;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
        </ul>
    </div>

    <?php if (!empty($noActiveWarehouseMessage)): ?>
        <div class="alert alert-warning">
            <?= h($noActiveWarehouseMessage); ?>
        </div>
    <?php endif; ?>

    <div class="row mt-5 pe-3 date_picker">
        <div class="col-sm-5">
        	<h2 class="p-15"><?php echo !empty($warehouse_detail) ? h($warehouse_detail->name) : ''; ?></h2>
        </div>
        <div class="col-sm-7 text-end">
            <div class="row align-items-center mb-2">
                <div class="col-md-7">

                </div>
                <div class="col-sm-5">
                    <div class="float-end">
                        <div class="col-sm-5">
                        </div>
                        <div class="col-sm-13">
                            <select id="date-period" class="form-select" onchange="handleChange(this)">
                                <option value="current_month"><?= __('Current Month') ?></option>
                                <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                                <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                                <option value="current_year"><?= __('Current Year') ?></option>
                                <option value="4"><?= __('Custom') ?></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row align-items-center mb-2 d-none" id="datesappear">
                <form id="dateRangeForm" class="d-flex">
                    <div class="col-sm-2">
                        <label for="from-date" class="col-form-label fw-400 m-r-10"><?= __('From Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="from-date" name="from-date" />
                    </div>
                    <div class="col-sm-2">
                        <label for="to-date" class="col-form-label fw-400 m-r-10"><?= __('To Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="to-date" name="to-date" />
                    </div>
                    <div class="col-sm-2">
                        <button class="btn m-l-20" type="submit"><?= __('Submit') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="section-body mt-3">
        <div class="container-fluid">
            <div class="row ">
                <div class="col-xl-3 col-lg-6">
				    <div class="card l-bg-style1 dashboard_box h-100">
				        <div class="card-statistic-3 bg1">
				            <div class="card-icon card-icon-large"><i class="fa fa-box"></i></div>
				            <div class="card-content">
				                <h4 class="card-title"><?= __('Warehouse Stock') ?></h4>
				                <!-- Total Stock Display -->
				                <span id="totalStocks" class="font-weight-bold">
				                    <?= !empty($totalStocks) ? h($totalStocks) . ' Items' : '0 Items'; ?>
				                </span>
				                <!-- Stock Progress Indicator -->
				                <div class="progress mt-2 mb-2" data-height="8">
				                    <div class="progress-bar bg-success" 
				                         id="stockProgressBar" 
				                         role="progressbar" 
				                         style="width: <?= h($percentageStocks) ?>%;" 
				                         aria-valuenow="<?= h($percentageStocks) ?>" 
				                         aria-valuemin="0" 
				                         aria-valuemax="100">
				                    </div>
				                </div>
				                <!-- Low Stock Indicator -->
				                <p class="mb-0 text-sm">
				                    <span class="text-danger">
				                        <i class="fa fa-exclamation-circle"></i> 
				                        <?= __('Low Stock Items: ') ?>
				                        <span id="lowStockItems">
				                            <?= !empty($lowInventoryProducts) ? h(count($lowInventoryProducts)) : '0'; ?>
				                        </span>
				                    </span>
				                </p>
				            </div>
				        </div>
				    </div>
				</div>

                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style2 dashboard_box h-100">
                        <div class="card-statistic-3 bg2">
                            <div class="card-icon card-icon-large"><i class="fa fa-truck"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Incoming Shipments') ?></h4>
                                <span id="totalShipments">
                                    <?php 
                                        echo !empty($totalRequests) 
                                            ? h($totalRequests . ' Shipments') 
                                            : h('0 Shipments'); 
                                    ?>
                                </span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="shipmentProgressBar" role="progressbar" 
                                        data-width="<?= h($percentageCompleted) ?>%" 
                                        aria-valuenow="<?= h($percentageCompleted) ?>" 
                                        aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> 
                                        <span id="completedShipments">
                                            <?php 
                                                echo !empty($completedRequests) 
                                                    ? h($completedRequests . ' Received') 
                                                    : h('0 Received'); 
                                            ?>
                                        </span>
                                    </span>
                                    <span class="text-nowrap" id="pendingShipments">
                                        <?= __('Pending: ') . (!empty($pendingRequests) ? h($pendingRequests) : '0') ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style3 dashboard_box h-100">
                        <div class="card-statistic-3 bg3">
                            <div class="card-icon card-icon-large"><i class="fa fa-truck"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Outgoing Shipments') ?></h4>
                                <span id="totalDispatchedItems"><?php echo !empty($totalDispatchedItems) ? h($totalDispatchedItems) . ' Items' : h('0 Items'); ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="dispatchProgressBar" role="progressbar" data-width="<?= $dispatchPercentage ?>%"
                                        aria-valuenow="<?= $dispatchPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalDispatchedMovements"><?php echo !empty($totalDispatchedMovements) ? h($totalDispatchedMovements) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="dispatchText"><?= __('Total outgoing shipments') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style4 dashboard_box h-100">
                        <div class="card-statistic-3 bg4">
                            <div class="card-icon card-icon-large"><i class="fa fa-money-bill-alt"></i>
                            </div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Total Stock Value') ?></h4>
                                <span class="start font-30">
                                    <?= isset($summary['In Stock']['value']) 
                                        ? number_format((float)$summary['In Stock']['value'], 0, '', $thousandSeparator) . ' ' . $currencySymbol 
                                        : '0 ' . $currencySymbol ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Incoming Stocks') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart4"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Outgoing Stocks') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart5"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
  <!-- Deliveries Scheduled Today -->
  <div class="col-12 col-lg-4 mb-4">
    <div class="card supplier_payments">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="mb-0"><?= __('Deliveries Scheduled today') ?></h4>
        <a class="text-decoration-none" data-bs-toggle="collapse" href="#supplierPendingBills" role="button" aria-expanded="false" aria-controls="supplierPendingBills">
          <i class="bi bi-chevron-down"></i>
        </a>
      </div>
      <div id="supplierPendingBills" class="collapse">
        <div class="content" style="max-height: 360px;overflow-y: scroll;padding: 0px 40px 0px 9px;">
          <ul>
            <?php foreach ($todays_shipments as $shipment): ?>
              <li><b><?= h($shipment['shipment_id']) ?> - </b><?= __($shipment['name']) ?></li>
            <?php endforeach; ?>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Low Inventory Products -->
  <div class="col-12 col-lg-4 mb-4">
    <div class="card supplier_payments">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="mb-0"><?= __('Low Inventory Products') ?></h4>
        <a class="text-decoration-none" data-bs-toggle="collapse" href="#lowInventorySection" role="button" aria-expanded="false" aria-controls="lowInventorySection">
          <i class="bi bi-chevron-down"></i>
        </a>
      </div>
      <div id="lowInventorySection" class="collapse">
        <div class="content" style="max-height: 360px;overflow-y: scroll;padding: 0px 40px 0px 9px;">
          <ul id="initial-low-inventory">
            <?php foreach ($initialLowInventory as $item): ?>
              <li>
                <strong><?= h($item->product->name) ?></strong>
                (<?= __('Qty') ?>: <?= h($item->quantity) ?> | <?= __('Min') ?>: <?= h($item->min_product_quantity) ?>)
              </li>
            <?php endforeach; ?>
          </ul>
          <ul>
            <?php foreach ($remainingLowInventory as $item): ?>
              <li>
                <strong><?= h($item->product->name) ?></strong>
                (<?= __('Qty') ?>: <?= h($item->quantity) ?> | <?= __('Min') ?>: <?= h($item->min_product_quantity) ?>)
              </li>
            <?php endforeach; ?>
          </ul>
          <!-- <?php if (!empty($remainingLowInventory)): ?>
            <div style="margin: 10px;text-align: center;">
            <button id="toggle-low-inventory" class="btn m-10" onclick="toggleLowInventoryList()"><?= __('View More') ?></button>
            </div>
          <?php endif; ?> -->
        </div>
      </div>
    </div>
  </div>

  <!-- Pending Approvals -->
  <div class="col-12 col-lg-4 mb-4">
    <div class="card supplier_payments">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="mb-0"><?= __('Pending Approvals') ?></h4>
        <a class="text-decoration-none" data-bs-toggle="collapse" href="#pendingApprovalsList" role="button" aria-expanded="false" aria-controls="pendingApprovalsList">
          <i class="bi bi-chevron-down"></i>
        </a>
      </div>
      <div id="pendingApprovalsList" class="collapse">
        <div class="content" style="max-height: 360px;overflow-y: scroll;padding: 0px 40px 0px 9px;">
          <?php if (!empty($pendingApprovals)): ?>
            <ul class="list-group list-group-flush">
              <?php foreach ($pendingApprovals as $approval): ?>
                <li class="list-group-item">
                  <strong><?= h($approval->movement_type) ?> #<?= h($approval->id) ?></strong> — 
                  <span><small><?= h($approval->created->format('Y-m-d')) ?></small></span><br/>
                  <?php if ($approval->movement_type === 'Incoming'): ?>
                    <?= __('Warehouse') ?>: <?= h($approval->warehouse->name ?? '-') ?><br/>
                    <?= __('Supplier') ?>: <?= h($approval['Suppliers']['name'] ?? '-') ?><br/>
                    <?= __('Purchase Order') ?>: <?= h($approval['SupplierPurchaseOrders']['bill_no'] ?? '-') ?><br/>
                  <?php else: ?>
                    <?= __('Warehouse') ?>: <?= h($approval->warehouse->name ?? '-') ?><br/>
                    <?= __('Requested By') ?>: <?= h($approval['FromUser']['first_name'] ?? '-') ?> <?= h($approval['FromUser']['last_name'] ?? '') ?><br/>
                  <?php endif; ?>
                </li>
              <?php endforeach; ?>
            </ul>
          <?php else: ?>
            <p><?= __('No pending approvals found.') ?></p>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <script>
  let lowInventoryExpanded = false;

  function toggleLowInventoryList() {
    const remaining = document.getElementById("remaining-low-inventory");
    const button = document.getElementById("toggle-low-inventory");

    if (!lowInventoryExpanded) {
      remaining.style.display = "block";
      button.textContent = "<?= __('View Less') ?>";
    } else {
      remaining.style.display = "none";
      button.textContent = "<?= __('View More') ?>";
    }

    lowInventoryExpanded = !lowInventoryExpanded;
  }
</script> -->

            <div class="row">
                <div class="col-12 col-lg-6 col-xl-6">
                    <div class="card supplier_payments">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 mt-0"><?= __('Inventory Categorization') ?></h4>
                        </div>

                        <div id="supplierPaymentsTable">
                            <div class="card-body">
                                <div class="media-list position-relative">
                                    <div class="table-responsive" tabindex="1" style="height: 300px; overflow-y: scroll; outline: none; touch-action: none;">
                                        <table class="table table-hover table-xl mb-0">
                                            <thead>
                                                <tr>
                                                    <th><?= __('Stock Type') ?></th>
                                                    <th><?= __('Total Count') ?></th>
                                                    <th><?= __('Value') ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($summary as $type => $data): ?>
                                                    <tr>
                                                        <td><?= h($type) ?></td>
                                                        <td><?= number_format($data['count']) ?></td>
                                                        <td><?= number_format((float)$data['value'], 0, '', $thousandSeparator) . ' ' . $currencySymbol ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-lg-6 col-xl-6">
                    <div class="card supplier_payments">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 mt-0"><?= __('Stocks by Seller') ?></h4>
                        </div>

                        <div id="supplierPaymentsTable">
                            <div class="card-body">
                                <div class="media-list position-relative">
                                    <div class="table-responsive" tabindex="1" style="height: 300px; overflow-y: scroll; outline: none; touch-action: none;">
                                        <table class="table table-hover table-xl mb-0">
                                            <thead>
                                                <tr>
                                                    <th><?= __('Seller') ?></th>
                                                    <th><?= __('Total Count') ?></th>
                                                    <th><?= __('Product') ?></th>
                                                    <th><?= __('Value') ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><?= __('Coming Soon') ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/page/chart-apexcharts.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>
    function handleChange(answer) {
        
        if (answer.value == 4) {
            document.getElementById('datesappear').classList.remove('d-none');
        } else 
        {

            document.getElementById('datesappear').classList.add('d-none');

            /** GRAPH FILTER **/
            fetchFilterGraphData();
        }
    }

    function generateMonthLabelsByDateRange(startDate, endDate) {
        
        const monthLabels = [];
        let currentDate = new Date(startDate);
        const end = new Date(endDate);

        // Ensure currentDate is at the start of its month
        currentDate.setDate(1);

        // Loop until the end month is reached
        while (currentDate <= end) {
            // Format month as "MMM" (e.g., "Aug", "Sep")
            const month = currentDate.toLocaleString('default', { month: 'short' });
            
            // Add the current month to the list
            monthLabels.push(month);

            // Move to the next month
            currentDate.setMonth(currentDate.getMonth() + 1);
        }

        return monthLabels;
    }

    function getMonthsBetween(startDate, endDate) {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        let start = new Date(startDate);
        let end = new Date(endDate);
        let monthLabels = [];

        // Loop over months between start and end dates
        while (start <= end) {
            let month = start.getMonth();  // Get the month index (0-11)
            let year = start.getFullYear();  // Get the year
            monthLabels.push(months[month]);  // Append "Month Year"
            
            // Increment the date by 1 month
            start.setMonth(start.getMonth() + 1);
        }

        return monthLabels;
    }

    function fetchFilterGraphData()
    {
        /** GRAPH FILTER **/
        const selectedValue = document.getElementById('date-period').value;

        // console.log(selectedValue);
        if(selectedValue == 4)
        {   
            var fromDate = $('#from-date').val();
            var toDate = $('#to-date').val();
            
            var startDateString = fromDate;
            var endDateString = toDate;
            var monthLabels = generateMonthLabelsByDateRange(fromDate, toDate);
            // console.log(monthLabels);
        }
        else
        {
            let newStartDate = '';
            let newEndDate = new Date();
            
            switch (selectedValue) {
                case 'current_month':
                    newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth(), 2);
                    break;
                case 'last_3_months':
                    newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 2, 2);
                    break;
                case 'last_6_months':
                    newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 5, 2);
                    break;
                case 'current_year':
                    newStartDate = new Date(newEndDate.getFullYear(), 0, 2);
                    break;
            }
            // Convert dates to string format (Y-m-d)
            var startDateString = newStartDate.toISOString().split('T')[0];
            var endDateString = newEndDate.toISOString().split('T')[0];
            var monthLabels = getMonthsBetween(newStartDate, newEndDate);
        }

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterWarehouseDashboardGraphData']); ?>',
            type: 'POST',
            data: {
                startDate: startDateString,
                endDate: endDateString,
                months: monthLabels,
                warehouse_id: <?= $warehouse_detail->id ?>,
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            // success: function(response) {

            //     updateGraph(response.graph_data, monthLabels);
            // },
            success: function(response) {

                // Assuming response.graph_data contains both incoming and outgoing stock data
                const incomingStockData = response.incoming_stock_graph_data;
                const outgoingStockData = response.outgoing_stock_graph_data;
                const monthLabels = response.month_labels;

                // Update both graphs by calling the updated updateGraph function
                updateGraph(incomingStockData, outgoingStockData, monthLabels);
            },
            error: function() {
                swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
            }
        });

        // Fetch data based on the selected range
        // fetchFilterGraphData(startDateString, endDateString, monthLabels);
    }

    $('#dateRangeForm').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission

        // Get values from the input fields
        const fromDate = $('#from-date').val();
        const toDate = $('#to-date').val();

        // Validate date inputs
        if (!fromDate || !toDate) {
            swal('<?= __('Failed') ?>', '<?= __('Both dates are required.') ?>', 'error');
            return false;
        }

        // Check if the from date is earlier than or equal to the to date
        if (new Date(fromDate) > new Date(toDate)) {
            swal('<?= __('Failed') ?>', '<?= __('The "From Date" must be earlier than or equal to the "To Date".') ?>', 'error');
            return false;
        }

        fetchFilterGraphData();
    });

    let chart1Instance;
    let chart2Instance;

    $(function () {
        chart4();
        chart5();
    });

    function chart4() {
        var options = {
            chart: {
                height: 350,
                type: "bar",
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    endingShape: "rounded",
                    columnWidth: "20%",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            series: [
                {
                    name: "Stocks",  // The name for the series
                    type: "column",
                    data: <?= json_encode($incomingStockGraphData['data']); ?>  // Data for stock quantities
                },
            ],
            colors: ["#0d839b"],  // Color of the bars
            xaxis: {
                categories: <?= json_encode($incomingStockGraphData['categories']); ?>,  // Categories are months
                title: {
                    text: "Months",  // X-axis title
                },
                labels: {
                    style: {
                        colors: "#8e8da4",  // Color for the labels on the X-axis
                    },
                },
            },
            yaxis: {
                title: {
                    text: 'No. of Stocks',  // Y-axis title
                },
                labels: {
                    style: {
                        color: "#8e8da4",  // Color for the labels on the Y-axis
                    },
                },
            },
            fill: {
                opacity: 1,
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " Stocks";  // Tooltip text format for the bar chart
                    },
                },
            },
            legend: {
                position: "top",
                horizontalAlign: "right",
                floating: true,
            },
        };

        // Create the chart and render it to the specified DOM element
        chart1Instance = new ApexCharts(document.querySelector("#chart4"), options);
        chart1Instance.render();
    }


    function chart5() {
        var options = {
            chart: {
                height: 350,
                type: "bar",
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    endingShape: "rounded",
                    columnWidth: "20%",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            series: [
                {
                    name: "Stocks",
                    type: "column",
                    data: <?= json_encode($outgoingStockGraphData['data']); ?>
                },
            ],
            colors: ["#0d839b"],
            xaxis: {
                categories: <?= json_encode($outgoingStockGraphData['categories']); ?>,
                title: {
                    text: "Months",
                },
                labels: {
                    style: {
                        colors: "#8e8da4",
                    },
                },
            },
            yaxis: {
                title: {
                    text: 'No. of Stocks',  // Updated here
                },
                labels: {
                    style: {
                        color: "#8e8da4",
                    },
                },
            },
            fill: {
                opacity: 1,
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " Stocks";
                    },
                },
            },
            legend: {
                position: "top",
                horizontalAlign: "right",
                floating: true,
            },
        };

        chart2Instance = new ApexCharts(document.querySelector("#chart5"), options);

        chart2Instance.render();
    }

    // function updateGraph(incomingStockData, monthLabels) {

    //     if (!Array.isArray(incomingStockData) || incomingStockData.length === 0) {
    //         console.error("incomingStockData is not valid:", incomingStockData);
    //         return;
    //     }

    //     if (!Array.isArray(monthLabels) || monthLabels.length === 0) {
    //         console.error("monthLabels is not valid:", monthLabels);
    //         return;
    //     }

    //     let seriesData = [];

    //     for (let i = 0; i < incomingStockData.length; i++) {
    //         const name = incomingStockData[i].name; 
    //         const data = incomingStockData[i].data; 

    //         if (typeof name === "undefined" || data === undefined) {
    //             console.warn("Missing data for incoming stock:", incomingStockData[i]);
    //             continue; 
    //         }

    //         if (Array.isArray(data)) {
    //             seriesData.push({
    //                 name: name,
    //                 data: data
    //             });
    //         } else {
    //             console.warn(`Data for "${name}" is not valid:`, data);
    //         }
    //     }

    //     // Check if chart1Instance is already defined and destroy it if necessary
    //     if (chart1Instance) {
    //         chart1Instance.destroy();
    //         console.log("Chart destroyed.");
    //     }

    //     // Set up the new options for the stock chart
    //     const newOptions = {
    //         chart: {
    //             height: 350,
    //             type: "bar",
    //         },
    //         plotOptions: {
    //             bar: {
    //                 horizontal: false,
    //                 endingShape: "rounded",
    //                 columnWidth: "20%",  // Adjust this for better column width
    //             },
    //         },
    //         dataLabels: {
    //             enabled: false,
    //         },
    //         stroke: {
    //             show: true,
    //             width: 2,
    //             colors: ["transparent"],
    //         },
    //         series: seriesData,  // Incoming stock data for the graph
    //         colors: ["#0d839b"],  // The color for the bars
    //         xaxis: {
    //             categories: monthLabels,  // Categories are months from the server-side data
    //             title: {
    //                 text: "Months",  // X-axis title
    //             },
    //             labels: {
    //                 style: {
    //                     colors: "#8e8da4",  // Color for the labels on the X-axis
    //                 },
    //             },
    //         },
    //         yaxis: {
    //             title: {
    //                 text: 'No. of Stocks',  // Y-axis title
    //             },
    //             labels: {
    //                 style: {
    //                     color: "#8e8da4",  // Color for the labels on the Y-axis
    //                 },
    //             },
    //         },
    //         fill: {
    //             opacity: 1,
    //         },
    //         tooltip: {
    //             y: {
    //                 formatter: function (val) {
    //                     return val + " Stocks";  // Tooltip text format for the bar chart
    //                 },
    //             },
    //         },
    //         legend: {
    //             position: "top",
    //             horizontalAlign: "right",
    //             floating: true,
    //         },
    //     };

    //     // Create a new chart instance with the updated options
    //     chart1Instance = new ApexCharts(document.querySelector("#chart4"), newOptions);
    //     chart1Instance.render().then(() => {
    //         console.log("Chart rendered successfully.");
    //     }).catch(err => {
    //         console.error("Error rendering chart:", err);
    //     });
    // }

    function updateGraph(incomingStockData, outgoingStockData, monthLabels) {


    // Validate incomingStockData
    if (!Array.isArray(incomingStockData) || incomingStockData.length === 0) {
        console.error("incomingStockData is not valid:", incomingStockData);
        return;
    }

    // Validate outgoingStockData
    if (!Array.isArray(outgoingStockData) || outgoingStockData.length === 0) {
        console.error("outgoingStockData is not valid:", outgoingStockData);
        return;
    }

    // Validate monthLabels
    if (!Array.isArray(monthLabels) || monthLabels.length === 0) {
        console.error("monthLabels is not valid:", monthLabels);
        return;
    }

    let incomingSeriesData = [];

    // Add Incoming Stock Data to the series
    for (let i = 0; i < incomingStockData.length; i++) {
        const name = incomingStockData[i].name; 
        const data = incomingStockData[i].data; 

        if (typeof name === "undefined" || data === undefined) {
            console.warn("Missing data for incoming stock:", incomingStockData[i]);
            continue; 
        }

        if (Array.isArray(data)) {
            incomingSeriesData.push({
                name: name,
                data: data
            });
        } else {
            console.warn(`Data for "${name}" is not valid:`, data);
        }
    }

    let outgoingSeriesData = [];
    // Add Outgoing Stock Data to the series
    for (let i = 0; i < outgoingStockData.length; i++) {
        const name = outgoingStockData[i].name; 
        const data = outgoingStockData[i].data; 

        if (typeof name === "undefined" || data === undefined) {
            console.warn("Missing data for outgoing stock:", outgoingStockData[i]);
            continue; 
        }

        if (Array.isArray(data)) {
            outgoingSeriesData.push({
                name: name,
                data: data
            });
        } else {
            console.warn(`Data for "${name}" is not valid:`, data);
        }
    }

    // Check if chart1Instance is already defined and destroy it if necessary
    if (chart1Instance) {
        chart1Instance.destroy();
        console.log("Chart destroyed.");
    }

    // Set up the new options for the stock chart
    const newOptions = {
        chart: {
            height: 350,
            type: "bar",
        },
        plotOptions: {
            bar: {
                horizontal: false,
                endingShape: "rounded",
                columnWidth: "20%",  // Adjust this for better column width
            },
        },
        dataLabels: {
            enabled: false,
        },
        stroke: {
            show: true,
            width: 2,
            colors: ["transparent"],
        },
        series: incomingSeriesData,  // Both incoming and outgoing stock data for the graph
        colors: ["#0d839b", "#f44336"],  // Different colors for incoming and outgoing stocks
        xaxis: {
            categories: monthLabels,  // Categories are months from the server-side data
            title: {
                text: "Months",  // X-axis title
            },
            labels: {
                style: {
                    colors: "#8e8da4",  // Color for the labels on the X-axis
                },
            },
        },
        yaxis: {
            title: {
                text: 'No. of Stocks',  // Y-axis title
            },
            labels: {
                style: {
                    color: "#8e8da4",  // Color for the labels on the Y-axis
                },
            },
        },
        fill: {
            opacity: 1,
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " Stocks";  // Tooltip text format for the bar chart
                },
            },
        },
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
        },
    };

    // Create a new chart instance with the updated options
    chart1Instance = new ApexCharts(document.querySelector("#chart4"), newOptions);
    chart1Instance.render().then(() => {
        console.log("Chart rendered successfully.");
    }).catch(err => {
        console.error("Error rendering chart:", err);
    });

    // Check if chart2Instance is already defined and destroy it if necessary
    if (chart2Instance) {
        chart2Instance.destroy();
        console.log("Chart destroyed.");
    }

    // Set up the new options for the stock chart
    const newOptions1 = {
        chart: {
            height: 350,
            type: "bar",
        },
        plotOptions: {
            bar: {
                horizontal: false,
                endingShape: "rounded",
                columnWidth: "20%",  // Adjust this for better column width
            },
        },
        dataLabels: {
            enabled: false,
        },
        stroke: {
            show: true,
            width: 2,
            colors: ["transparent"],
        },
        series: outgoingSeriesData,  // Both incoming and outgoing stock data for the graph
        colors: ["#0d839b", "#f44336"],  // Different colors for incoming and outgoing stocks
        xaxis: {
            categories: monthLabels,  // Categories are months from the server-side data
            title: {
                text: "Months",  // X-axis title
            },
            labels: {
                style: {
                    colors: "#8e8da4",  // Color for the labels on the X-axis
                },
            },
        },
        yaxis: {
            title: {
                text: 'No. of Stocks',  // Y-axis title
            },
            labels: {
                style: {
                    color: "#8e8da4",  // Color for the labels on the Y-axis
                },
            },
        },
        fill: {
            opacity: 1,
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " Stocks";  // Tooltip text format for the bar chart
                },
            },
        },
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
        },
    };

    // Create a new chart instance with the updated options
    chart2Instance = new ApexCharts(document.querySelector("#chart5"), newOptions1);
    chart2Instance.render().then(() => {
        console.log("Chart rendered successfully.");
    }).catch(err => {
        console.error("Error rendering chart:", err);
    });
}



</script>
<?php $this->end(); ?>