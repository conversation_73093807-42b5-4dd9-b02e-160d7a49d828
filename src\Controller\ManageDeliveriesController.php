<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ManageDeliveriesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Zones;
    protected $Showrooms;
    protected $ZoneShowrooms;
    protected $Orders;
    protected $OrderItems;
    protected $Customers;
    protected $CustomerAddresses;
    protected $Users;
    protected $Drivers;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Zones = $this->fetchTable('Zones');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->ZoneShowrooms = $this->fetchTable('ZoneShowrooms');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Customers = $this->fetchTable('Customers');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->Users = $this->fetchTable('Users');
        $this->Drivers = $this->fetchTable('Drivers');
    }
    
    public function index()
    {
        $zones = $this->Zones->find('all')
            ->where(['Zones.status IN' => 'A'])
            ->order(['Zones.name' => 'ASC'])->toArray();

        $zonesData = [];
        foreach ($zones as $zone) {
            
            $showrooms = $this->ZoneShowrooms->find()
                ->contain(['Showrooms'])
                ->where(['ZoneShowrooms.zone_id' => $zone->id])
                ->all();

            
            $showroomOrders = [];
            $totalOrderCount = 0;
            $assigned = 0;
            $unassigned = 0;
            foreach ($showrooms as $zoneShowroom) {
                
                $orders = $this->Orders->find()
                    ->select(['Orders.id', 'Orders.order_number', 'Orders.driver_id', 'Orders.delivery_date'])
                    ->contain([
                        'Customers' => [
                            'fields' => ['Customers.id', 'Customers.user_id'],
                            'Users' => ['fields' => ['Users.first_name', 'Users.last_name']],
                            'CustomerAddresses' => ['fields' => ['CustomerAddresses.id', 'CustomerAddresses.customer_id', 'CustomerAddresses.address_line1', 'CustomerAddresses.address_line2', 'CustomerAddresses.house_no', 'CustomerAddresses.municipality_id', 'CustomerAddresses.landmark', 'CustomerAddresses.zipcode']]
                        ]
                    ])
                    ->where(['Orders.showroom_id' => $zoneShowroom->showroom_id])
                    ->all();

                $orderCount = $orders->count();
                $totalOrderCount += $orderCount;

                if (!$orders->isEmpty()) {
                    
                    $ordersWithItemCount = [];
                    foreach ($orders as $order) {

                        if($order->driver_id != null || $order->driver_id != '')
                        {
                            $assigned = $assigned + 1;
                        }
                        else
                        {
                            $unassigned = $unassigned + 1;
                        }


                        $itemCount = $this->OrderItems->find()
                            ->where(['OrderItems.order_id' => $order->id])
                            ->count();    

                        $ordersWithItemCount[] = [
                            'order' => $order, 
                            'item_count' => $itemCount, 
                        ];
                    }

                    $showroomOrders[] = [
                        
                        'orders' => $ordersWithItemCount
                    ];
                }
            }

            if (!empty($showroomOrders)) {
                $zonesData[] = [
                    'zone' => $zone,
                    'order_count' => $totalOrderCount,
                    'assigned' => $assigned,
                    'unassigned' => $unassigned,
                    'showroomOrders' => $showroomOrders 
                ];
            }
        }

        // Query the Drivers table and join with Users to get driver names
        $query = $this->Drivers->find()
            ->select(['Drivers.id', 'Users.first_name', 'Users.last_name'])
            ->where(['Users.status' => 'A', 'Users.user_type' => 'Driver'])
            ->contain(['Users']);

        $driversList = $query->toArray();

        $drivers = [];
        foreach ($driversList as $driver) {
            $drivers[$driver->id] = $driver->user->first_name . ' ' . $driver->user->last_name;
        }

        $this->set(compact('zonesData', 'drivers'));
    }

    public function assignDriver()
    {
        $this->request->allowMethod(['post']);

        $response = ['success' => false, 'message' => __('The driver could not be assigned. Please, try again.')];

        try {

            
            $driver_id = $this->request->getData('driver_id');
            $zone_id = $this->request->getData('zone_id');
            $order_id = $this->request->getData('order_id');
            
            if($driver_id == __('unassigned'))
            {
                $driver_id = null;
            }

            $record = $this->Orders->get($order_id);
            $record->driver_id = $driver_id;

            if ($this->Orders->save($record)) {
                    
                $showrooms = $this->ZoneShowrooms->find()
                    ->contain(['Showrooms'])
                    ->where(['ZoneShowrooms.zone_id' => $zone_id])
                    ->all();

                $assigned = 0;
                $unassigned = 0;
                foreach ($showrooms as $zoneShowroom) {
                    
                    $orders = $this->Orders->find()
                        ->select(['Orders.id', 'Orders.order_number', 'Orders.driver_id'])
                        ->contain([
                            'Customers' => [
                                'fields' => ['Customers.id', 'Customers.user_id'],
                                'Users' => ['fields' => ['Users.first_name', 'Users.last_name']],
                                'CustomerAddresses' => ['fields' => ['CustomerAddresses.id', 'CustomerAddresses.customer_id', 'CustomerAddresses.address_line1', 'CustomerAddresses.address_line2', 'CustomerAddresses.house_no', 'CustomerAddresses.municipality_id', 'CustomerAddresses.landmark', 'CustomerAddresses.zipcode']]
                            ]
                        ])
                        ->where(['Orders.showroom_id' => $zoneShowroom->showroom_id])
                        ->all();


                    if (!$orders->isEmpty()) {
                        
                        $ordersWithItemCount = [];
                        foreach ($orders as $order) {

                            if($order->driver_id != null || $order->driver_id != '')
                            {
                                $assigned = $assigned + 1;
                            }
                            else
                            {
                                $unassigned = $unassigned + 1;
                            }
                        }
                    }
                }

                $response = [
                    'status' => 'success',
                    'assigned' => $assigned,
                    'unassigned' => $unassigned
                ];
            }

        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function assignDriverToAllOrders()
    {
        if ($this->request->is('ajax')) {

            $driverId = $this->request->getData('driver_id');
            $zoneId = $this->request->getData('zone_id');

            // Ensure driver is not "unassigned"
            if ($driverId === 'unassigned') {
                $driverId = null;
            }

            try {
                // Step 1: Get showrooms associated with the given zone
                $showrooms = $this->ZoneShowrooms->find()
                    ->select(['ZoneShowrooms.showroom_id'])
                    ->where(['ZoneShowrooms.zone_id' => $zoneId])
                    ->all()
                    ->extract('showroom_id')
                    ->toArray();    

                if (empty($showrooms)) {
                    throw new \Exception(__('No showrooms found for this zone.'));
                }

                // Step 2: Fetch unassigned orders for these showrooms
                $orders = $this->Orders->find()
                    ->where([
                        // 'Orders.driver_id IS' => null,
                        'Orders.showroom_id IN' => $showrooms
                    ])
                    ->all();

                if ($orders->isEmpty()) {
                    throw new \Exception(__('No unassigned orders found for this zone.'));
                }

                // Step 3: Assign driver to all unassigned orders
                foreach ($orders as $order) {
                    $order->driver_id = $driverId;
                    if (!$this->Orders->save($order)) {
                        throw new \Exception(__('Failed to assign driver to order ID: ' . $order->id));
                    }
                }

                // Step 4: Get updated assignment counts
                $assignedCount = $this->Orders->find()
                    ->where([
                        'Orders.driver_id IS NOT' => null,
                        'Orders.showroom_id IN' => $showrooms
                    ])
                    ->count();

                $unassignedCount = $this->Orders->find()
                    ->where([
                        'Orders.driver_id IS' => null,
                        'Orders.showroom_id IN' => $showrooms
                    ])
                    ->count();

                // Step 5: Return success response with updated counts
                // return $this->json([
                //     'status' => 'success',
                //     'assigned' => $assignedCount,
                //     'unassigned' => $unassignedCount
                // ]);

                $response = [
                    'status' => 'success',
                    'assigned' => $assignedCount,
                    'unassigned' => $unassignedCount
                ];

            } catch (\Exception $e) {
                // Handle errors gracefully
                $response['message'] = $e->getMessage();
            }

            if ($this->request->is('ajax')) {
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            } else {
                if ($response['success']) {
                    $this->Flash->success($response['message']);
                } else {
                    $this->Flash->error($response['message']);
                }
                return $this->redirect(['action' => 'index']);
            }
        }
    }


}
