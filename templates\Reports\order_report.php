<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/export-tables/buttons.dataTables.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
    }

    #select2-showroom-option-container {
        display:flex;
        align-items:center;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Reports') ?></h4>
            </li>
        </ul>
    </div>

    <div class="row mt-5 date_picker">
        <div class="m-l-15">
            <h5 style="color: #f77f00;"><?= __('Order Reports') ?></h5>
        </div>
        <div class="col-sm-9 m-t-20 m-l-15">
         <div class="row align-items-center mb-2">

        <!-- Order Status -->
        <div class="col-md-6 mb-2">
            <div class="row">
                <div class="col-sm-4">
                    <label for="orderStatus" class="col-form-label fw-400"><?= __('Order Status') ?></label>
                </div>
                <div class="col-sm-6">
                    <?= $this->Form->control('orderStatus', [
                        'type' => 'select',
                        'options' => $orderstatuses,
                        'id' => 'orderStatus',
                        'class' => 'form-control form-select p-10',
                        'label' => false,
                        'empty' => __('Order Status'),
                        'data' => ['bs-toggle' => 'dropdown'],
                        'aria-expanded' => 'false'
                    ]) ?>
                </div>
            </div>
        </div>

        <!-- Payment Status -->
        <div class="col-md-6 mb-2">
            <div class="row">
                <div class="col-sm-4">
                    <label for="paymentStatus" class="col-form-label fw-400"><?= __('Payment Status') ?></label>
                </div>
                <div class="col-sm-6">
                    <?= $this->Form->control('paymentStatus', [
                        'type' => 'select',
                        'options' => $paymentstatuses,
                        'id' => 'paymentStatus',
                        'class' => 'form-control form-select p-10',
                        'label' => false,
                        'empty' => __('Payment Status'),
                        'data' => ['bs-toggle' => 'dropdown'],
                        'aria-expanded' => 'false'
                    ]) ?>
                </div>
            </div>
        </div>

        <!-- Showroom -->
        <div class="col-md-6 mb-2">
            <div class="row">
                <div class="col-sm-4">
                    <label for="showroom-option" class="col-form-label fw-400"><?= __('Showroom') ?></label>
                </div>
                <div class="col-sm-6">
                    <select id="showroom-option" class="form-select select2" <?= $readonly ? 'readonly' : '' ?>>
                        <option value=""><?= __('Select Showroom') ?></option>
                        <?php foreach ($showrooms as $showroom): ?>
                            <option value="<?= $showroom->id ?>"
                                <?= $readonly ? 'disabled' : '' ?>
                                <?= $showroom->id == $selectedShowroomId ? 'selected' : '' ?>>
                                <?= h($showroom->name) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
        </div>

        <!-- Order Type -->
        <div class="col-md-6 mb-2">
            <div class="row">
                <div class="col-sm-4">
                    <label for="order-type-option" class="col-form-label fw-400"><?= __('Order Type') ?></label>
                </div>
                <div class="col-sm-6">
                    <select id="order-type-option" class="form-select">
                        <option value=""><?= __('Select Order Type') ?></option>
                        <option value="Online"><?= __('Online') ?></option>
                        <option value="Showroom"><?= __('Showroom') ?></option>
                    </select>
                </div>
            </div>
        </div>


        <!--Delivery Mode -->
        <div class="col-md-6 mb-2">
            <div class="row">
                <div class="col-sm-4">
                    <label for="delivery-mode-option" class="col-form-label fw-400"><?= __('Delivery Mode') ?></label>
                </div>
                <div class="col-sm-6">
                    <select id="delivery-mode-option" class="form-select">
                        <option value=""><?= __('Select Delivery Mode') ?></option>
                        <option value="pickup"><?= __('Pickup') ?></option>
                        <option value="delivery"><?= __('Delivery') ?></option>
                    </select>
                </div>
            </div>
        </div>
        <!-- Date Period -->
        <div class="col-md-6 mb-2">
            <div class="row">
                <div class="col-sm-4">
                    <label for="date-period" class="col-form-label fw-400"><?= __('Date Period') ?></label>
                </div>
                <div class="col-sm-6">
                    <select id="date-period" class="form-select" onchange="handleChange(this)">
                        <option value="current_month"><?= __('Current Month') ?></option>
                        <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                        <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                        <option value="current_year"><?= __('Current Year') ?></option>
                        <option value="4"><?= __('Custom') ?></option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Date Range (Initially Hidden) -->
    <div class="row align-items-center mb-2 d-none" id="datesappear">
        <form id="dateRangeForm" class="d-flex flex-wrap align-items-end">
            <div class="col-sm-3 mb-2">
                <label for="from-date" class="col-form-label fw-400"><?= __('From Date') ?></label>
                <input type="date" id="from-date" name="from-date" class="form-control" />
            </div>
            <div class="col-sm-3 mb-2">
                <label for="to-date" class="col-form-label fw-400"><?= __('To Date') ?></label>
                <input type="date" id="to-date" name="to-date" class="form-control" />
            </div>
            <div class="col-sm-3 mb-2 d-flex align-items-end">
                <button class="btn btn-primary btn-sm w-100" type="submit"><?= __('Submit') ?></button>
            </div>
        </form>
    </div>
</div>

    </div>

    <div class="section-body mt-3">
        <div class="container-fluid">
            <div class="row mt-4">
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Order Trend') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart6"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row align-items-center mt-5 mb-4 d-none">
                <div class="col-sm-3">
                    <span style="display: contents;">Search:</span>
                    <input type="text" id="customSearchBox" placeholder=""
                        name="sales_person" />
                </div>
                <div class="col-sm-4">
                    <button type="button" id="export_button" class="btn btn_export me-0 font-16"
                        data-toggle="tooltip" data-placement="right" title="Export">
                        <img src="<?= $this->Url->webroot('img/export_btn.svg') ?>" alt="export" />
                    </button>
                </div>
            </div>
            <div class="row mt-5 mb-5">
                <div class="col-12">
                    <div class="table-responsive" id="table_report" tabindex="1">
                        <table class="table table-hover table-xl mb-0" id="OrdersTable">
                            <thead>
                                <tr>
                                    <th><?= __('Order Id') ?></th>
                                    <th><?= __('Showroom Name') ?></th>
                                    <th><?= __('Customer Name') ?></th>
                                    <th><?= __('Order Date') ?></th>
                                    <th><?= __('Order Amount') ?></th>
                                    <th><?= __('Order Fulfilment Time') ?></th>
                                    <th><?= __('Order Status') ?></th>
                                    <th><?= __('Payment Status') ?></th>
                                    <th><?= __('Order Type') ?></th>
                                    <th><?= __('Delivery Mode') ?></th>
                                    <th><?= __('Rating') ?></th>
                                </tr>
                            </thead>
                            <tbody id="table_datalist">
                                <?php foreach ($OrderDetails as $list): ?>
                                <tr>
                                    <td class="text-truncate"><?= h($list->id ?? 'N/A') ?></td>
                                    <td class="text-truncate"><?= h($list->showroom_name ? $list->showroom_name : 'N/A') ?></td>
                                    <td class="text-truncate"><?= h($list->full_name ? $list->full_name : 'N/A') ?></td>
                                    <td class="text-truncate"><?= h($list->order_date ? $list->order_date->format($dateFormat) : 'N/A') ?></td>
                                    <td class="text-truncate"><?= h($list->total_amount) ? h(number_format($list->total_amount, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) : 'N/A' ?></td>
                                    <td class="text-truncate"><?= h($list->order_fulfillment_time ?? 'N/A') ?></td>
                                    <td class="text-truncate">
                                        <?= h(isset($orderstatuses[$list->status]) ? $orderstatuses[$list->status] : 'N/A') ?>
                                    </td>
                                    <td class="text-truncate">
                                        <?= h(isset($paymentstatuses[$list->transaction_status]) ? $paymentstatuses[$list->transaction_status] : 'N/A') ?>
                                    </td>
                                    <td class="text-truncate"><?= h($list->order_type ?? 'N/A') ?></td>
                                    <td class="text-truncate"><?= h($list->delivery_mode ?? 'N/A') ?></td>
                                    <td class="text-truncate">N/A</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>">
</script>
<!-- DataTables Buttons JS -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/dataTables.buttons.min.js') ?>"></script>

<!-- JSZip (For Excel export support) -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/jszip.min.js') ?>"></script>

<!-- Buttons HTML5 export JS -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/buttons.html5.min.js') ?>"></script>

<!-- Buttons Print export JS -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/buttons.print.min.js') ?>"></script>

<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#OrdersTable").DataTable({
        dom: 'Bfrtip',  // B - Buttons, f - filter input, r - processing display, t - table, i - table information, p - pagination
        buttons: [
            {
                extend: 'csvHtml5',
                text: '<i class="fas fa-file-download"></i>',  // CSV icon
                titleAttr: 'Export CSV',
                title: 'Order Reports',
                exportOptions: {
                    columns: ':visible'
                }
            }
        ],
        columnDefs: [{
                orderable: false,
                targets: [-1]
            }, // Make the last column non-sortable
        ],
        order: [],
        // dom: "rtip", // Remove the default search box
        pageLength: paginationCount
    });

    $("#customSearchBox").on("keyup", function() {
        table.search(this.value).draw();
    });


    let chart;

    $(function () {
        chart6();
    });

    function chart6() {
        var options = {
            chart: {
                height: 350,
                type: "line",
            },
            series: [
                <?php foreach ($orderTrendsgraphData as $data): ?>
                {
                    name: "<?= $data['name'] ?>",
                    data: <?= json_encode($data['data']) ?>
                },
                <?php endforeach; ?>
            ],
            // colors: ["#77B6EA", "#0d839b", "#f77f00"],
            plotOptions: {
                bar: {
                    horizontal: false,
                    endingShape: "rounded",
                    columnWidth: "50%",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            title: {
                text: ".",
            },
            xaxis: {
                categories: <?= json_encode($monthLabels) ?>,
                title: {
                    text: "Date Period",
                },
                labels: {
                    style: {
                        colors: "#8e8da4",
                    },
                },
            },
            yaxis: [{
                title: {
                    text: "Order Trend",
                },
                labels: {
                    style: {
                        color: "#000000",
                    },
                },
            }, ],
            legend: {
                position: "top",
                horizontalAlign: "right",
                floating: true,
            },
        };

        chart = new ApexCharts(document.querySelector("#chart6"), options);
        chart.render() // Render the chart for the first time
        .then(() => {
            console.log("Chart initialized successfully:", chart);
        })
        .catch((error) => {
            console.error("Failed to render chart:", error);
        });
    }

    $("#orderStatus").on("change", function() {
        // event.preventDefault();
        var orderStatus = $(this).val();
        var paymentStatus = $("#paymentStatus option:selected").val();
        var showroom = $("#showroom-option option:selected").text().trim();
        var order_type = $("#order-type-option option:selected").text();
        var delivery_mode = $("#delivery-mode-option option:selected").val();
        // alert(paymentStatus);
        // var paymentStatus = $("#paymentStatus").val();

        var orderStatusMap = <?= json_encode($orderstatuses) ?>;
        var paymentStatusMap = <?= json_encode($paymentstatuses) ?>;
        var orderTypeStatusMap = <?= json_encode($ordertypestatuses) ?>;

        var orderStatusVal = orderStatusMap[orderStatus] !== undefined ? orderStatusMap[orderStatus] :
            '';
        var paymentStatusVal = paymentStatusMap[paymentStatus] !== undefined ? paymentStatusMap[
            paymentStatus] : '';

        var OrderTypeValue = orderTypeStatusMap[order_type] !== undefined ? orderTypeStatusMap[
            order_type] : '';

        var showroomVal = showroom !== '<?= __('Select Showroom') ?>' ? showroom : '';

        table.column(6).search(orderStatusVal).draw();
        table.column(7).search(paymentStatusVal, true, false, false).draw();
        table.column(8).search(OrderTypeValue, true, false, false).draw();
        table.column(9).search(delivery_mode, true, false, false).draw();
        table.column(1).search(showroomVal).draw();

        fetchFilterGraphData();
    });

    $("#paymentStatus").on("change", function() {
        // event.preventDefault();
        var paymentStatus = $(this).val();
        var orderStatus = $("#orderStatus option:selected").val();
        var showroom = $("#showroom-option option:selected").text().trim();
        var order_type = $("#order-type-option option:selected").text();
        var delivery_mode = $("#delivery-mode-option option:selected").val();

        var orderStatusMap = <?= json_encode($orderstatuses) ?>;
        var paymentStatusMap = <?= json_encode($paymentstatuses) ?>;
        var orderTypeStatusMap = <?= json_encode($ordertypestatuses) ?>;

        var orderStatusVal = orderStatusMap[orderStatus] !== undefined ? orderStatusMap[orderStatus] :
            '';
        var paymentStatusVal = paymentStatusMap[paymentStatus] !== undefined ? paymentStatusMap[
            paymentStatus] : '';

        var OrderTypeValue = orderTypeStatusMap[order_type] !== undefined ? orderTypeStatusMap[
            order_type] : '';

        var showroomVal = showroom !== '<?= __('Select Showroom') ?>' ? showroom : '';

        table.column(6).search(orderStatusVal).draw();
        table.column(7).search(paymentStatusVal, true, false, false).draw();
        table.column(8).search(OrderTypeValue, true, false, false).draw();
        table.column(8).search(delivery_mode, true, false, false).draw();
        table.column(1).search(showroomVal).draw();

        fetchFilterGraphData();
    });

    $("#showroom-option").on("change", function() {
        // event.preventDefault();
        var showroom = $("#showroom-option option:selected").text().trim();
        var orderStatus = $("#orderStatus option:selected").val();
        var paymentStatus = $("#paymentStatus option:selected").val();
        var order_type = $("#order-type-option option:selected").text();
        var delivery_mode = $("#delivery-mode-option option:selected").val();

        var orderStatusMap = <?= json_encode($orderstatuses) ?>;
        var paymentStatusMap = <?= json_encode($paymentstatuses) ?>;
        var orderTypeStatusMap = <?= json_encode($ordertypestatuses) ?>;

        var orderStatusVal = orderStatusMap[orderStatus] !== undefined ? orderStatusMap[orderStatus] :
            '';
        var paymentStatusVal = paymentStatusMap[paymentStatus] !== undefined ? paymentStatusMap[
            paymentStatus] : '';

        var OrderTypeValue = orderTypeStatusMap[order_type] !== undefined ? orderTypeStatusMap[
            order_type] : '';

        var showroomVal = showroom !== '<?= __('Select Showroom') ?>' ? showroom : '';


        table.column(6).search(orderStatusVal).draw();
        table.column(7).search(paymentStatusVal, true, false, false).draw();
        table.column(8).search(OrderTypeValue, true, false, false).draw();
        table.column(9).search(delivery_mode, true, false, false).draw();
        table.column(1).search(showroomVal).draw();

        fetchFilterGraphData();
    });

    $("#order-type-option").on("change", function() {

        var orderStatus = $("#orderStatus option:selected").val();
        var paymentStatus = $("#paymentStatus option:selected").val();
        var showroom = $("#showroom-option option:selected").text().trim();
        var order_type = $("#order-type-option option:selected").text();
        var delivery_mode = $("#delivery-mode-option option:selected").val();
        // alert(paymentStatus);
        // var paymentStatus = $("#paymentStatus").val();

        var orderStatusMap = <?= json_encode($orderstatuses) ?>;
        var paymentStatusMap = <?= json_encode($paymentstatuses) ?>;
        var orderTypeStatusMap = <?= json_encode($ordertypestatuses) ?>;

        var orderStatusVal = orderStatusMap[orderStatus] !== undefined ? orderStatusMap[orderStatus] :
            '';
        var paymentStatusVal = paymentStatusMap[paymentStatus] !== undefined ? paymentStatusMap[
            paymentStatus] : '';

        var OrderTypeValue = orderTypeStatusMap[order_type] !== undefined ? orderTypeStatusMap[
            order_type] : '';

        var showroomVal = showroom !== '<?= __('Select Showroom') ?>' ? showroom : '';

        table.column(6).search(orderStatusVal).draw();
        table.column(7).search(paymentStatusVal, true, false, false).draw();
        table.column(8).search(OrderTypeValue, true, false, false).draw();
        table.column(9).search(delivery_mode, true, false, false).draw();
        table.column(1).search(showroomVal).draw();

        fetchFilterGraphData();
    });

    $("#delivery-mode-option").on("change", function() {

        var orderStatus = $("#orderStatus option:selected").val();
        var paymentStatus = $("#paymentStatus option:selected").val();
        var showroom = $("#showroom-option option:selected").text().trim();
        var order_type = $("#order-type-option option:selected").text();
        var delivery_type = $("#delivery-mode-option option:selected").val();
        // alert(paymentStatus);
        // var paymentStatus = $("#paymentStatus").val();

        var orderStatusMap = <?= json_encode($orderstatuses) ?>;
        var paymentStatusMap = <?= json_encode($paymentstatuses) ?>;
        var orderTypeStatusMap = <?= json_encode($ordertypestatuses) ?>;

        var orderStatusVal = orderStatusMap[orderStatus] !== undefined ? orderStatusMap[orderStatus] :
            '';
        var paymentStatusVal = paymentStatusMap[paymentStatus] !== undefined ? paymentStatusMap[
            paymentStatus] : '';

        var OrderTypeValue = orderTypeStatusMap[order_type] !== undefined ? orderTypeStatusMap[
            order_type] : '';

        var showroomVal = showroom !== '<?= __('Select Showroom') ?>' ? showroom : '';

        table.column(6).search(orderStatusVal).draw();
        table.column(7).search(paymentStatusVal, true, false, false).draw();
        table.column(8).search(OrderTypeValue, true, false, false).draw();
        table.column(9).search(delivery_type, true, false, false).draw();
        table.column(1).search(showroomVal).draw();

        fetchFilterGraphData();
    });

    // Custom filter function for the date range
    $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
        var min = $('#from-date').val();
        var max = $('#to-date').val();
        var orderDate = data[3]; // Assuming the order_date is in the 6th column (index 5)

        if (!min && !max) {
            return true; // No filter applied
        }

        if (min && !max) {
            return orderDate >= min;
        }

        if (!min && max) {
            return orderDate <= max;
        }

        return orderDate >= min && orderDate <= max; // Filter between min and max
    });

    $('#dateRangeForm').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission

        var fromDate = $('#from-date').val();
        var toDate = $('#to-date').val();

        // Validate date inputs
        if (!fromDate || !toDate) {
            swal('<?= __('Failed') ?>', '<?= __('Both dates are required.') ?>', 'error');
            return false;
        }

        // Check if the from date is earlier than or equal to the to date
        if (new Date(fromDate) > new Date(toDate)) {
            swal('<?= __('Failed') ?>', '<?= __('The "From Date" must be earlier than or equal to the "To Date".') ?>', 'error');
            return false;
        }

        fetchFilterGraphData();
        table.draw();

    });

    function generateMonthLabelsByDateRange(startDate, endDate) {
        
        const monthLabels = [];
        let currentDate = new Date(startDate);
        const end = new Date(endDate);

        // Ensure currentDate is at the start of its month
        currentDate.setDate(1);

        // Loop until the end month is reached
        while (currentDate <= end) {
            // Format month as "MMM" (e.g., "Aug", "Sep")
            const month = currentDate.toLocaleString('default', { month: 'short' });
            
            // Add the current month to the list
            monthLabels.push(month);

            // Move to the next month
            currentDate.setMonth(currentDate.getMonth() + 1);
        }

        return monthLabels;
    }

    function handleChange(answer) {
        
        if (answer.value == 4) {
            document.getElementById('datesappear').classList.remove('d-none');
        } else 
        {

            document.getElementById('datesappear').classList.add('d-none');

            const filterValue = document.getElementById('date-period').value;
            let startDate, endDate;
            const currentDate = moment();

            $.fn.dataTable.ext.search = [];

            switch (filterValue) {
                case "current_month":
                    startDate = moment().startOf('month');
                    endDate = moment().endOf('month');
                    break;
                case "last_3_months":
                    startDate = moment().subtract(3, 'months').startOf('month');
                    endDate = moment();
                    break;
                case "last_6_months":
                    startDate = moment().subtract(6, 'months').startOf('month');
                    endDate = moment();
                    break;
                case "current_year":
                    startDate = moment().startOf('year');
                    endDate = moment();
                    break;
                case "custom":
                    // You can integrate a custom date picker here for manual selection
                    startDate = null;
                    endDate = null;
                    break;
            }

            if (startDate && endDate) {
                // Custom filter for date range
                $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                    const orderDate = moment(data[3], 'YYYY-MM-DD'); // Assuming order_date is in 6th column (index 5)
                    return orderDate.isBetween(startDate, endDate, 'day', '[]'); // Inclusive of start and end dates
                });
            } else {
                // Remove any previously applied filters if "custom" or no date range selected
                $.fn.dataTable.ext.search.pop();
            }

            // Redraw the DataTable with the new filter
            $('#OrdersTable').DataTable().draw();

            /** GRAPH FILTER **/
            fetchFilterGraphData();


            // const selectedValue = document.getElementById('date-period').value;
            // let newStartDate = '';
            // let newEndDate = new Date();

            // // alert(newStartDate+"   "+ newEndDate);

            // switch (selectedValue) {
            //     case 'current_month':
            //         newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth(), 2);
            //         break;
            //     case 'last_3_months':
            //         newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 2, 2);
            //         break;
            //     case 'last_6_months':
            //         newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 5, 2);
            //         break;
            //     case 'current_year':
            //         newStartDate = new Date(newEndDate.getFullYear(), 0, 2);
            //         break;
            // }
            // // Convert dates to string format (Y-m-d)
            // const startDateString = newStartDate.toISOString().split('T')[0];
            // const endDateString = newEndDate.toISOString().split('T')[0];
            // const monthLabels = getMonthsBetween(newStartDate, newEndDate);
            // // Fetch data based on the selected range
            // fetchFilterGraphData(startDateString, endDateString, monthLabels);
        }
    }

    function getMonthsBetween(startDate, endDate) {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        let start = new Date(startDate);
        let end = new Date(endDate);
        let monthLabels = [];

        // Loop over months between start and end dates
        while (start <= end) {
            let month = start.getMonth();  // Get the month index (0-11)
            let year = start.getFullYear();  // Get the year
            monthLabels.push(months[month]);  // Append "Month Year"
            
            // Increment the date by 1 month
            start.setMonth(start.getMonth() + 1);
        }

        return monthLabels;
    }

    function fetchFilterGraphData()
    {
        /** GRAPH FILTER **/
        const selectedValue = document.getElementById('date-period').value;

        var showroom_id = $("#showroom-option option:selected").val();
        var order_status = $("#orderStatus option:selected").val();
        var payment_status = $("#paymentStatus option:selected").val();
        var order_type = $("#order-type-option option:selected").val();
        var delivery_mode = $("#delivery-mode-option option:selected").val();

        // console.log(selectedValue);
        if(selectedValue == 4)
        {   
            var fromDate = $('#from-date').val();
            var toDate = $('#to-date').val();
            
            var startDateString = fromDate;
            var endDateString = toDate;
            var monthLabels = generateMonthLabelsByDateRange(fromDate, toDate);
            // console.log(monthLabels);
        }
        else
        {
            // let newStartDate = '';
            // let newEndDate = new Date();
            
            // switch (selectedValue) {
            //     case 'current_month':
            //         newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth(), 2);
            //         break;
            //     case 'last_3_months':
            //         newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 2, 2);
            //         break;
            //     case 'last_6_months':
            //         newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 5, 2);
            //         break;
            //     case 'current_year':
            //         newStartDate = new Date(newEndDate.getFullYear(), 0, 2);
            //         break;
            // }
            // // Convert dates to string format (Y-m-d)
            // var startDateString = newStartDate.toISOString().split('T')[0];
            // var endDateString = newEndDate.toISOString().split('T')[0];
            // var monthLabels = getMonthsBetween(newStartDate, newEndDate);

            let newEndDate = new Date();

            function getStartOfMonth(year, month) {
                return new Date(year, month, 1);
            }

            let newStartDate;

            switch (selectedValue) {
                case 'current_month':
                    newStartDate = getStartOfMonth(newEndDate.getFullYear(), newEndDate.getMonth());
                    break;
                case 'last_3_months':
                    newStartDate = getStartOfMonth(newEndDate.getFullYear(), newEndDate.getMonth() - 2);
                    break;
                case 'last_6_months':
                    newStartDate = getStartOfMonth(newEndDate.getFullYear(), newEndDate.getMonth() - 5);
                    break;
                case 'current_year':
                    newStartDate = getStartOfMonth(newEndDate.getFullYear(), 0);
                    break;
                default:
                    newStartDate = getStartOfMonth(newEndDate.getFullYear(), newEndDate.getMonth());
            }

            // Convert dates to string format (Y-m-d)
            var startDateString = newStartDate.toISOString().split('T')[0];
            var endDateString = newEndDate.toISOString().split('T')[0];
            var monthLabels = getMonthsBetween(newStartDate, newEndDate);

        }

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Reports', 'action' => 'filterGraphData']); ?>',
            type: 'POST',
            data: {
                startDate: startDateString,
                endDate: endDateString,
                months: monthLabels,
                showroom_id: showroom_id,
                order_status: order_status,
                payment_status: payment_status,
                order_type: order_type,
                delivery_mode: delivery_mode,
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {

                // console.log(response)
                // setTimeout(() => {
                    updateGraph(response.graph_data, monthLabels);
                // }, 1000);
            },
            error: function() {
                swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
            }
        });

        // Fetch data based on the selected range
        // fetchFilterGraphData(startDateString, endDateString, monthLabels);
    }

    // function fetchFilterGraphData(startDate, endDate, monthLabels)
    // {
    //     $.ajax({
    //             url: '<?= $this->Url->build(['controller' => 'Reports', 'action' => 'filterGraphData']); ?>',
    //             type: 'POST',
    //             data: {
    //                 startDate: startDate,
    //                 endDate: endDate,
    //                 months: monthLabels
    //             },
    //             headers: {
    //                 'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
    //             },
    //             success: function(response) {

    //                 // console.log(response)
    //                 setTimeout(() => {
    //                     updateGraph(response.graph_data, monthLabels);
    //                 }, 1000);
    //             },
    //             error: function() {
    //                 swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
    //             }
    //         });
    // }

    function updateGraph(orderData, monthLabels) { 

        if (!Array.isArray(orderData) || orderData.length === 0) {
            console.error("orderData is not valid:", orderData);
            return;
        }

        if (!Array.isArray(monthLabels) || monthLabels.length === 0) {
            console.error("monthLabels is not valid:", monthLabels);
            return;
        }

        let seriesData = [];

        for (let i = 0; i < orderData.length; i++) {
            const showroom = orderData[i].name; 
            const data = orderData[i].data; 

            if (typeof showroom === "undefined" || data === undefined) {
                console.warn("Missing data for showroom:", orderData[i]);
                continue; 
            }

            if (Array.isArray(data)) {
                seriesData.push({
                    name: showroom,
                    data: data
                });
            } else {
                console.warn(`Data for showroom "${showroom}" is not valid:`, data);
            }
        }

        if (chart) {
            chart.destroy();
            console.log("Chart destroyed.");
        }

        const newOptions = {
            chart: {
                height: 350,
                type: "line",
            },
            series: seriesData,
            plotOptions: {
                bar: {
                    horizontal: false,
                    endingShape: "rounded",
                    columnWidth: "50%",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            title: {
                text: ".", // Set an appropriate title
            },
            xaxis: {
                categories: monthLabels, // Update month labels here
                title: {
                    text: "Date Period",
                },
                labels: {
                    style: {
                        colors: "#8e8da4",
                    },
                },
            },
            yaxis: [{
                title: {
                    text: "Order Trend",
                },
                labels: {
                    style: {
                        color: "#000000",
                    },
                },
            }],
            legend: {
                position: "top",
                horizontalAlign: "right",
                floating: true,
            },
        };

        // Create a new chart instance
        chart = new ApexCharts(document.querySelector("#chart6"), newOptions);
        chart.render().then(() => {
            console.log("Chart rendered successfully.");
        }).catch(err => {
            console.error("Error rendering chart:", err);
        });

    }

</script>
<?php $this->end(); ?>