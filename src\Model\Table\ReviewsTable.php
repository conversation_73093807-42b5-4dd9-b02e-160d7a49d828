<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Reviews Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\BelongsTo $Products
 *
 * @method \App\Model\Entity\Review newEmptyEntity()
 * @method \App\Model\Entity\Review newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Review> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Review get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Review findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Review patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Review> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Review|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Review saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Review>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Review>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Review>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Review> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Review>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Review>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Review>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Review> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ReviewsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('reviews');
        $this->setDisplayField('status');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->nonNegativeInteger('product_id')
            ->notEmptyString('product_id');

        $validator
            ->integer('rating')
            ->requirePresence('rating', 'create')
            ->notEmptyString('rating');

        $validator
            ->scalar('comment')
            ->allowEmptyString('comment');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);
        $rules->add($rules->existsIn(['product_id'], 'Products'), ['errorField' => 'product_id']);

        return $rules;
    }

    public function productReviews($productId, $limit=null) {

        $conditions = [];

        // Base conditions that always apply
        $conditions[] = [
            'Reviews.status' => 'A',
            'Reviews.product_id' => $productId
        ];
        $order = ['Reviews.created' => 'DESC'];

        $reviews = $this->find('all')
            ->select(['Reviews.id', 'Reviews.rating','Reviews.comment','Reviews.created','Users.first_name', 'Users.last_name','Customers.profile_photo', 'ReviewImages.image_url'])
            ->join([
                'Customers' => [
                    'table' => 'customers',
                    'type' => 'INNER',
                    'conditions' => 'Reviews.customer_id = Customers.id'
                ],
                'Users' => [
                    'table' => 'users',
                    'type' => 'INNER',
                    'conditions' => 'Customers.user_id = Users.id'
                ],
                'ReviewImages' => [
                    'table' => 'review_images',
                    'type' => 'LEFT',
                    'conditions' => 'ReviewImages.review_id = Reviews.id'
                ]
            ])
            ->where($conditions)
            ->order($order)
            ->limit($limit)
            ->toArray();

        return $reviews;

    }

    //M
    public function add_record($attributes) {

        $new = $this->newEmptyEntity();
         foreach($attributes as $key => $value) {
             $new->$key = $value;
         }

         if($this->save($new)) {
             return $new->id;
         } else {
             return false;
         }
    }

    // Ax
    public function isReviewExistsOrAdd($attributes) {
        $review = $this->find()
            ->where($attributes)
            ->first();
        if($review) {
            return ["status"=> false, "message"=> __("Already rated!")];
        } else {
            // write code for insert
            $new = $this->newEmptyEntity();
            foreach ($attributes as $key => $value) {
                $new->$key = $value;
            }
            $new->status = 'A';
            if($this->save($new)) { 
                return ['status'=> true, 'message'=> __("Thank you for rating this product!")];
            } else {
                return ['status'=> false, 'message'=> __("Failed to save your rating.")];
            }
        }
    }

    //M
    public function update_record($id, $attributes) {
        $old_attr = $this->get($id);
        $old_attr = $this->patchEntity($old_attr, $attributes);
        if ($this->save($old_attr)) {
            return $old_attr;
        } else {
            return false;
        }
    }

    public function getAverageRating($productId)
    {
        $average = $this->find()
            ->select(['avg_rating' => 'AVG(rating)'])
            ->where(['product_id' => $productId, 'status' => 'A'])
            ->first();

        return $average ? (float)$average->avg_rating : 0.0;
    }
    public function getMaxOneRating($productId)
    {
        $result = $this->find()
            ->select([
                'max_rating' => 'MAX(Reviews.rating)',
                'Users.first_name',
                'Users.last_name'
            ])
            ->join([
                'Users' => [
                    'table' => 'users',
                    'type' => 'INNER',
                    'conditions' => 'Reviews.customer_id = Users.id'
                ]
            ])
            ->where(['Reviews.product_id' => $productId, 'Reviews.status' => 'A'])
            ->first();
        if ($result && $result->max_rating != null) {
            return [
                'max_rating' => (int)$result->max_rating,
                'full_name' => $result->Users['first_name'].' '.$result->Users['last_name']
            ];
        } else {
            return [
                'max_rating' => 0,
                'full_name' => null
            ];
        }
    }

    public function getTotalReviews($productId)
    {
        $totalReviews = $this->find()
            ->select(['total_reviews' => 'COUNT(id)'])
            ->where(['product_id' => $productId, 'status' => 'A'])
            ->first();

        return $totalReviews ? (int)$totalReviews->total_reviews : 0;
    }












    public function webProductReviews($productId, $sortBy = 'newest')
    {
        $conditions = [
            'Reviews.status' => 'A',
            'Reviews.product_id' => $productId
        ];

        // Set the default order
        $order = ['Reviews.created' => 'DESC'];

        // Adjust the order based on the sortBy parameter
        switch ($sortBy) {
            case 'oldest':
                $order = ['Reviews.created' => 'ASC'];
                break;
            case 'highest-rated':
                $order = ['Reviews.rating' => 'DESC'];
                break;
            case 'lowest-rated':
                $order = ['Reviews.rating' => 'ASC'];
                break;
            case 'newest':
            default:
                $order = ['Reviews.created' => 'DESC'];
                break;
        }

        // Build the query
        $query = $this->find('all')
            ->select([
                'Reviews.id', 'Reviews.rating', 'Reviews.comment', 'Reviews.created',
                'Users.first_name', 'Users.last_name',
                'Customers.profile_photo', 'ReviewImages.image_url'
            ])
            ->join([
                'Customers' => [
                    'table' => 'customers',
                    'type' => 'INNER',
                    'conditions' => 'Reviews.customer_id = Customers.id'
                ],
                'Users' => [
                    'table' => 'users',
                    'type' => 'INNER',
                    'conditions' => 'Customers.user_id = Users.id'
                ],
                'ReviewImages' => [
                    'table' => 'review_images',
                    'type' => 'LEFT',
                    'conditions' => 'ReviewImages.review_id = Reviews.id'
                ]
            ])
            ->where($conditions)
            ->order($order);

        return $query;
    }



}
