<?php

declare(strict_types=1);

namespace App\Controller;

/**
 * ShowroomStocks Controller
 *
 */
class ShowroomStocksController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->ShowroomStocks->find();
        $showroomStocks = $this->paginate($query);

        $this->set(compact('showroomStocks'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom Stock id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $showroomStock = $this->ShowroomStocks->get($id, contain: []);
        $this->set(compact('showroomStock'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $showroomStock = $this->ShowroomStocks->newEmptyEntity();
        if ($this->request->is('post')) {
            $showroomStock = $this->ShowroomStocks->patchEntity($showroomStock, $this->request->getData());
            if ($this->ShowroomStocks->save($showroomStock)) {
                $this->Flash->success(__('The showroom stock has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The showroom stock could not be saved. Please, try again.'));
        }
        $this->set(compact('showroomStock'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Showroom Stock id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $showroomStock = $this->ShowroomStocks->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $showroomStock = $this->ShowroomStocks->patchEntity($showroomStock, $this->request->getData());
            if ($this->ShowroomStocks->save($showroomStock)) {
                $this->Flash->success(__('The showroom stock has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The showroom stock could not be saved. Please, try again.'));
        }
        $this->set(compact('showroomStock'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom Stock id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $showroomStock = $this->ShowroomStocks->get($id);
        if ($this->ShowroomStocks->delete($showroomStock)) {
            $this->Flash->success(__('The showroom stock has been deleted.'));
        } else {
            $this->Flash->error(__('The showroom stock could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function getShowRoomStocks()
    {
        $product_id = $this->request->getQuery('product_id');

        $showroomstocks = $this->ShowroomStocks->find()
            ->select([
                'id' => 'ShowroomStocks.id',
                'product_id' => 'Products.id',
                'product_name' => 'Products.name',
                'price' => 'Products.sales_price',
                'showroom_stock' => 'ShowroomStocks.quantity',
                'reserved_stock' => 'ShowroomStocks.reserved_stock',
                'purchased_stock' => 'ShowroomStocks.purchased_stock',
                'total_stock' => '(IFNULL(ShowroomStocks.quantity, 0) - IFNULL(ShowroomStocks.reserved_stock, 0) + IFNULL(ShowroomStocks.purchased_stock, 0))',
                'parentCategory' => 'Categories.name',
                'showroom' => 'Showrooms.name'
            ])
            ->where(['ShowroomStocks.product_id' => $product_id])
            ->where(['ShowroomStocks.quantity <>' => 0])
            ->join([
                'Showrooms' => [
                    'table' => 'showrooms',
                    'type' => 'LEFT',
                    'conditions' => 'Showrooms.id = ShowroomStocks.showroom_id'
                ],
                'Products' => [
                    'table' => 'products',
                    'type' => 'INNER',
                    'conditions' => 'Products.id = ShowroomStocks.product_id'
                ],
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductCategories.product_id = Products.id',
                        'ProductCategories.level' => 1
                    ]
                ],
                'Categories' => [
                    'table' => 'categories',
                    'type' => 'LEFT',
                    'conditions' => 'Categories.id = ProductCategories.category_id'
                ]
            ])
            ->toArray();

        $this->log('Fetched showroom stocks: ' . json_encode($showroomstocks), 'debug');

        $response = ['showroomstocks' => $showroomstocks];

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }


    public function addStock()
    {
        $showroom_stock = $this->ShowroomStocks->newEmptyEntity();
        if ($this->request->is('post')) {
            $showroom_stock = $this->ShowroomStocks->patchEntity($showroom_stock, $this->request->getData());
            if ($this->ShowroomStocks->save($showroom_stock)) {
                $response = ['status' => 'success', 'message' => __('The product stock has been saved.')];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            }
            $response = ['status' => 'error', 'message' => __('The product stock could not be saved. Please, try again.')];
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        }
    }

    public function editStock($id = null)
    {
        if ($id === null) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Invalid ID']));
        }

        $showroom_stock = $this->ShowroomStocks->get($id);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $showroom_stock = $this->ShowroomStocks->patchEntity($showroom_stock, $this->request->getData());
            if ($this->ShowroomStocks->save($showroom_stock)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'success', 'showroom_stock' => $showroom_stock]));
            }
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Could not save the product stock.']));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => 'success', 'showroom_stock' => $showroom_stock]));
    }

    public function deleteStock($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $showroom_stock = $this->ShowroomStocks->get($id);
        $response = ['success' => false, 'message' => 'The product stock could not be deleted. Please, try again.'];
        if ($showroom_stock) {
            if ($this->ShowroomStocks->delete($showroom_stock)) {
                $response = ['success' => true, 'message' => 'The product stock has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The product stock could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product stock does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function viewStock($id = null)
    {
        $this->request->allowMethod(['get']);

        if (!$id) {
            $response = ['status' => 'error', 'message' => 'Invalid Stock ID.'];
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        $showroomStock = $this->ShowroomStocks->find()
            ->select([
                'id' => 'ShowroomStocks.id',
                'product_name' => 'Products.name',
                'price' => 'Products.sales_price',
                'showroom_stock' => 'ShowroomStocks.quantity',
                'reserved_stock' => 'ShowroomStocks.reserved_stock',
                'purchased_stock' => 'ShowroomStocks.purchased_stock',
                'total_stock' => '(IFNULL(ShowroomStocks.quantity, 0) - IFNULL(ShowroomStocks.reserved_stock, 0) + IFNULL(ShowroomStocks.purchased_stock, 0))',
                'category_name' => 'Categories.name',
                'showroom_name' => 'Showrooms.name'
            ])
            ->where(['ShowroomStocks.id' => $id])
            ->join([
                'Showrooms' => [
                    'table' => 'showrooms',
                    'type' => 'LEFT',
                    'conditions' => 'Showrooms.id = ShowroomStocks.showroom_id'
                ],
                'Products' => [
                    'table' => 'products',
                    'type' => 'INNER',
                    'conditions' => 'Products.id = ShowroomStocks.product_id'
                ],
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductCategories.product_id = Products.id',
                        'ProductCategories.level' => 1
                    ]
                ],
                'Categories' => [
                    'table' => 'categories',
                    'type' => 'LEFT',
                    'conditions' => 'Categories.id = ProductCategories.category_id'
                ]
            ])
            ->first();

        if ($showroomStock) {
            $response = [
                'status' => 'success',
                'data' => [
                    'product_name' => $showroomStock->product_name,
                    'category_name' => $showroomStock->category_name,
                    'showroom_name' => $showroomStock->showroom_name,
                    'price' => $showroomStock->price,
                    'stock_quantity' => $showroomStock->showroom_stock ?? 0,
                    'reserved_quantity' => $showroomStock->reserved_stock ?? 0,
                    'purchased_quantity' => $showroomStock->purchased_stock ?? 0,
                    'total_available_stock' => $showroomStock->total_stock ?? 0
                ]
            ];
        } else {
            $response = ['status' => 'error', 'message' => 'Showroom stock not found.'];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }
}
