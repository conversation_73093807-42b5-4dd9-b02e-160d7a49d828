<?php

declare(strict_types=1);

namespace App\Controller;

/**
 * ProductPaymentSettings Controller
 *
 * @property \App\Model\Table\ProductPaymentSettingsTable $ProductPaymentSettings
 */
class ProductPaymentSettingsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->ProductPaymentSettings->find()
            ->contain(['Products', 'Partners']);
        $productPaymentSettings = $this->paginate($query);

        $this->set(compact('productPaymentSettings'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Payment Setting id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $productPaymentSetting = $this->ProductPaymentSettings->get($id, contain: ['Products', 'Partners']);
        $this->set(compact('productPaymentSetting'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $productPaymentSetting = $this->ProductPaymentSettings->newEmptyEntity();
        if ($this->request->is('post')) {
            $productPaymentSetting = $this->ProductPaymentSettings->patchEntity($productPaymentSetting, $this->request->getData());
            if ($this->ProductPaymentSettings->save($productPaymentSetting)) {
                $this->Flash->success(__('The product payment setting has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product payment setting could not be saved. Please, try again.'));
        }
        $products = $this->ProductPaymentSettings->Products->find('list', limit: 200)->all();
        $partners = $this->ProductPaymentSettings->Partners->find('list', limit: 200)->all();
        $this->set(compact('productPaymentSetting', 'products', 'partners'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Product Payment Setting id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $productPaymentSetting = $this->ProductPaymentSettings->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $productPaymentSetting = $this->ProductPaymentSettings->patchEntity($productPaymentSetting, $this->request->getData());
            if ($this->ProductPaymentSettings->save($productPaymentSetting)) {
                $this->Flash->success(__('The product payment setting has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product payment setting could not be saved. Please, try again.'));
        }
        $products = $this->ProductPaymentSettings->Products->find('list', limit: 200)->all();
        $partners = $this->ProductPaymentSettings->Partners->find('list', limit: 200)->all();
        $this->set(compact('productPaymentSetting', 'products', 'partners'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Product Payment Setting id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $productPaymentSetting = $this->ProductPaymentSettings->get($id);
        if ($this->ProductPaymentSettings->delete($productPaymentSetting)) {
            $this->Flash->success(__('The product payment setting has been deleted.'));
        } else {
            $this->Flash->error(__('The product payment setting could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function getPaymentSettings()
    {
        $product_id = $this->request->getQuery('product_id');

        $paymentSettings = $this->ProductPaymentSettings->find('all')
            ->where(['ProductPaymentSettings.product_id' => $product_id])
            ->where(['ProductPaymentSettings.status !='=>'D'])
            ->contain(['Partners', 'Products'])
            ->select([
                'id' => 'ProductPaymentSettings.id',
                'product_name' => 'Products.name',
                'sales_price' => 'Products.sales_price',
                // 'quantity' => 'ProductPaymentSettings.quantity',
                // 'payment_terms' => 'ProductPaymentSettings.payment_terms',
                'avl_on_credit' => 'ProductPaymentSettings.avl_on_credit',
                'partner_name' => 'Partners.business_name',
                'credit_terms' => 'ProductPaymentSettings.credit_terms',
                'status' => 'ProductPaymentSettings.status'
            ])
            ->order(['Partners.business_name' => 'ASC'])
            ->toArray();

        $response = ['paymentSettings' => $paymentSettings];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function addPaymentSettings()
    {
        $this->request->allowMethod(['post']);

        $data = $this->request->getData();

        $existingRecord = $this->ProductPaymentSettings->find()
            ->where([
                'partner_id' => $data['partner_id'],
                'product_id' => $data['product_id'],
                'status' => 'A'
            ])
            ->first();

        if ($existingRecord) {
            $response = [
                'status' => 'error',
                'message' => __('Payment settings already exist for this partner and product.')
            ];
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
            return $this->response;
        } else {
            $paymentSetting = $this->ProductPaymentSettings->newEmptyEntity();
            $paymentSetting = $this->ProductPaymentSettings->patchEntity($paymentSetting, $data);

            if ($this->ProductPaymentSettings->save($paymentSetting)) {
                if (!empty($data['payment_terms'])) {
                    $this->savePaymentTerms($paymentSetting->id, $data['payment_terms']);
                }

                $response = [
                    'status' => 'success',
                    'message' => __('Payment settings added successfully.'),
                    'payment_setting' => $paymentSetting
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to add payment settings. Please try again.'),
                    'errors' => $paymentSetting->getErrors()
                ];
            }
        }

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }



    protected function savePaymentTerms($paymentSettingId, $paymentTerms)
    {
        $productPaymentTermsTable = $this->ProductPaymentSettings->ProductPaymentTerms;

        $productPaymentTermsTable->updateAll(
            ['status' => 'D'],
            ['payment_id' => $paymentSettingId]
        );

        $data = [];
        foreach ($paymentTerms as $term) {
            $data[] = [
                'payment_id' => $paymentSettingId,
                'payment_term_id' => $term,
                'status' => 'A'
            ];
        }

        $entities = $productPaymentTermsTable->newEntities($data);
        if (!$productPaymentTermsTable->saveMany($entities)) {
            $this->Flash->error(__('The payment terms could not be saved. Please, try again.'));
        }
    }

    public function editPaymentSettings($id)
    {
        $this->request->allowMethod(['get', 'post']);

        if ($this->request->is('post')) {
            $paymentSetting = $this->ProductPaymentSettings->get($id);

            if ($paymentSetting) {
                $data = $this->request->getData();
                $paymentSetting = $this->ProductPaymentSettings->patchEntity($paymentSetting, $data);
                $existingRecord = $this->ProductPaymentSettings->find()
                    ->where([
                        'partner_id' => $data['partner_id'],
                        'product_id' => $data['product_id'],
                        'id <>' => $id,
                        'status' => 'A'
                    ])
                    ->first();

                if ($existingRecord) {
                    $response = [
                        'status' => 'error',
                        'message' => __('Payment settings already exist for this partner and product.')
                    ];
                    $this->response = $this->response->withType('application/json')
                        ->withStringBody(json_encode($response));
                    return $this->response;
                } else {

                    if ($this->ProductPaymentSettings->save($paymentSetting)) {
                        if (!empty($data['payment_terms'])) {
                            $this->savePaymentTerms($paymentSetting->id, $data['payment_terms']);
                        }

                        $response = [
                            'status' => 'success',
                            'message' => __('Payment settings updated successfully.'),
                            'payment_setting' => $paymentSetting
                        ];
                    } else {
                        $response = [
                            'status' => 'error',
                            'message' => __('Unable to update payment settings. Please try again.'),
                            'errors' => $paymentSetting->getErrors()
                        ];
                    }
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to find payment settings.')
                ];
            }
        } else {
            $paymentSetting = $this->ProductPaymentSettings->get($id, [
                'contain' => [
                    'ProductPaymentTerms' => function ($q) {
                        return $q->where(['ProductPaymentTerms.status' => 'A'])
                            ->contain(['PartnerPaymentTerms']);
                    }
                ]
            ]);

            if ($paymentSetting) {
                $response = [
                    'status' => 'success',
                    'payment_setting' => [
                        'id' => $paymentSetting->id,
                        'partner_id' => $paymentSetting->partner_id,
                        'payment_terms' => array_filter(array_map(function ($term) {
                            return $term->partner_payment_term->id ?? null;
                        }, $paymentSetting->product_payment_terms)),

                        // 'quantity' => $paymentSetting->quantity,
                        'credit_terms' => $paymentSetting->credit_terms,
                        'status' => $paymentSetting->status
                    ]
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to find payment settings.')
                ];
            }
        }

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    public function viewPaymentSettings($id)
    {
        $this->request->allowMethod(['get']);
        $paymentSetting = $this->ProductPaymentSettings->get($id, [
            'contain' => [
                'ProductPaymentTerms' => function ($q) {
                    return $q->where(['ProductPaymentTerms.status' => 'A'])
                        ->contain(['PartnerPaymentTerms']);
                },
                'Partners'
            ]
        ]);

        if ($paymentSetting) {
            $response = [
                'status' => 'success',
                'payment_setting' => [
                    'id' => $paymentSetting->id,
                    'partner_name' => $paymentSetting->partner ? $paymentSetting->partner->business_name : null,
                    'payment_terms' => array_filter(array_map(function ($term) {
                        return $term->partner_payment_term->payment_terms ?? null;
                    }, $paymentSetting->product_payment_terms)),
                    // 'quantity' => $paymentSetting->quantity,
                    'credit_terms' => $paymentSetting->credit_terms,
                ]
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => __('Unable to find payment settings.')
            ];
        }

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    public function deletePaymentSettings($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $productpayment = $this->ProductPaymentSettings->get($id);
        $response = ['success' => false, 'message' => 'The product payment could not be deleted. Please, try again.'];
        if ($productpayment) {
            if ($this->ProductPaymentSettings->delete($productpayment)) {
                $response = ['success' => true, 'message' => 'The product payment has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The product payment could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product payment does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
