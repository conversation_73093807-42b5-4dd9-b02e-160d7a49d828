# LANGUAGE translation of CakePHP Application
# <AUTHOR> <EMAIL>
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CakePHP 5.0.10\n"
"POT-Creation-Date: 2025-05-02 06:43+0000\n"
"PO-Revision-Date: YYYY-mm-DD HH:MM+ZZZZ\n"
"Last-Translator: NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <EMAIL@ADDRESS>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: ./templates/Error/error400.php:40
#: ./templates/Error/error500.php:46
msgid "Error"
msgstr ""

#: ./templates/Error/error400.php:41
msgid "The requested address {0} was not found on this server."
msgstr ""

#: ./templates/Error/error500.php:44
msgid "An Internal Error Has Occurred."
msgstr ""

