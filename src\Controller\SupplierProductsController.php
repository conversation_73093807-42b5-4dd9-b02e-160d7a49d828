<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Routing\Router;
use Cake\Core\Configure;

/**
 * SupplierProducts Controller
 *
 * @property \App\Model\Table\SupplierProductsTable $SupplierProducts
 */
class SupplierProductsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $SupplierProducts;
    protected $Products;
    protected $ProductVariants;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
    }
    
    public function index()
    {
        $supplier_products = $this->SupplierProducts->find()
            ->where(['SupplierProducts.status IN' => ['A', 'I']])
            ->contain(['Suppliers', 'Products'])
            ->order(['Suppliers.name' => 'ASC'])->toArray(); 

        $this->set(compact('supplier_products'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    // public function view($id = null)
    // {
    //     $zone = $this->Zones->get($id, contain: [
    //         'Showrooms'
    //     ]);
    //     $this->set(compact('zone'));
    // }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $supplier_product = $this->SupplierProducts->newEmptyEntity();
        if ($this->request->is('post')) {

            $currencyConfig = Configure::read('Settings.Currency.format');
            $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
            $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
            $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

            $data = $this->request->getData();

            $supplierId = $data['supplier_id'];
            $productId = $data['product_id'];
            $variantId = !empty($data['product_variant_id']) ? $data['product_variant_id'] : null;

            // Check if the combination already exists
            $conditions = [
                'SupplierProducts.status IN' => ['A', 'I'],
                'supplier_id' => $supplierId,
                'product_id' => $productId
            ];
            
            if ($variantId) {
                $conditions['product_variant_id'] = $variantId;
            }

            $existingSupplierProduct = $this->SupplierProducts->find()->where($conditions)->first();

            // Check if the combination already exists
            // $existingSupplierProduct = $this->SupplierProducts->find()
            //     ->where(['SupplierProducts.status IN' => ['A', 'I']])
            //     ->where(['supplier_id' => $supplierId, 'product_id' => $productId])
            //     ->first();  
            
            if ($existingSupplierProduct) {

                $response = [
                    'status' => __('exist_error'),
                    'message' => __('This product has already assigned to supplier.')
                ];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;

            }

            $supplier_product = $this->SupplierProducts->patchEntity($supplier_product, $data);

            if ($this->SupplierProducts->save($supplier_product)) {

                $query_supplier_products = $this->SupplierProducts->find()
                    ->where(['SupplierProducts.supplier_id' => $supplierId])
                    ->where(['SupplierProducts.status IN' => ['A', 'I']])
                    ->contain([
                        'Suppliers',
                        'Products' => function ($q) {
                            return $q->contain([
                                'ProductImages' => function ($q2) {
                                    return $q2->where([
                                        'ProductImages.status' => 'A',
                                        'ProductImages.image_default' => 1
                                    ])->select(['id', 'product_id', 'image']);
                                }
                            ]);
                        },
                        'ProductVariants' => function ($q) {
                            return $q->select(['id', 'variant_name']);
                        }
                    ])
                    ->order(['Suppliers.name' => 'ASC'])
                    ->toArray();

                foreach ($query_supplier_products as $product) {

                    if (!empty($product->product->product_images) && !empty($product->product->product_images[0]->image)) {
                        $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_images[0]->image);
                    } else {
                        $product->product->product_image = null;
                    }
                }

                $supplier_products = [];
                $i = 1;
                foreach ($query_supplier_products as $supplier_product) {

                    $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                    'D' => ['label' => __('Deleted'), 'class' => 'col-red']
                    ];

                    $status = $statusMap[$supplier_product->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

                    // Get the variant name, if available
                    $variantName = 'N/A'; // Default value
                    if (!empty($supplier_product->product_variant_id)) {
                        $variantName = $supplier_product->product_variant->variant_name;
                    }

                    $supplier_products[] = [
                        'id' => $supplier_product->id,
                        'supplier_name' => h($supplier_product->supplier->name),
                        'sku_id' => h($supplier_product->product->sku),
                        'product_name' => h($supplier_product->product->name),
                        'product_variant_name' => h($variantName),
                        'supplier_price' => h(number_format((float)$supplier_product->supplier_price, 0, '', $thousandSeparator)).' '.$currencySymbol,
                        'product_image' => '<img src="'.$supplier_product->product->product_image.'" style="height:40px; width:40px">',
                        'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                        'actions' => '
                            <a target="_blank" href="' . Router::url(['controller' => 'Products', 'action' => 'view', $supplier_product->product->id], true) . '" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>' .
                            '<a style="cursor: pointer;" data-id="' . $supplier_product->id . '" onClick="openEditSupplierProductModal('.$supplier_product->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                            '<a href="' . Router::url(['controller' => 'SupplierProducts', 'action' => 'delete', $supplier_product->id], true) . '" 
                                class="delete-btn" 
                                data-toggle="tooltip" 
                                title="Delete"
                                data-delete-confirmation="' . __(Configure::read('Constants.DELETE_CONFIRMATION_MESSAGE')) . '"
                                data-delete-warning="' . __(Configure::read('Constants.DELETE_WARNING_MESSAGE')) . '"
                                data-delete-fail="' . __(Configure::read('Constants.DELETE_FAIL_MESSAGE')) . '">
                                <i class="far fa-trash-alt"></i>
                            </a>'
                    ];

                }    

                $this->set([
                    'supplier_products' => $supplier_products,
                    '_serialize' => ['supplier_products'],
                ]);

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['data' => $supplier_products]));
            }

        }

        $suppliers = $this->fetchTable('Suppliers')
                        ->find('list')
                        ->where(['status' => 'A'])
                        ->all();

        $products = $this->fetchTable('Products')
                        ->find('list')
                        ->where(['status' => 'A'])
                        ->all();
        
        $this->set(compact('supplier_product', 'suppliers', 'products'));
    }

    public function getSupplierProductById($id = null)
    {
        // $supplier_product = $this->SupplierProducts->get($id, contain: []);

        // Fetch the supplier product along with the related product and product variants
        $supplier_product = $this->SupplierProducts->get($id, [
            'contain' => [
                'Products', // Include product details
                'ProductVariants' => function ($q) {
                    return $q->select(['id', 'variant_name']); // Select the variant id and name
                }
            ]
        ]);

        $response = [
            'status' => 'success',
            'supplier_product' => $supplier_product
        ];
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    public function edit($id = null)
    {

        $supplier_product = $this->SupplierProducts->get($id, contain: ['Suppliers', 'Products']);
        if ($this->request->is(['patch', 'post', 'put'])) {

            $currencyConfig = Configure::read('Settings.Currency.format');
            $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
            $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
            $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

            $data = $this->request->getData();
            $supplierId = $data['supplier_id'];
            $productId = $data['product_id'];
            $variantId = !empty($data['product_variant_id']) ? $data['product_variant_id'] : null;

            $supplier_product = $this->SupplierProducts->patchEntity($supplier_product, $this->request->getData());
            if ($this->SupplierProducts->save($supplier_product)) {

                $query_supplier_products = $this->SupplierProducts->find()
                    ->where(['SupplierProducts.supplier_id' => $supplierId])
                    ->where(['SupplierProducts.status IN' => ['A', 'I']])
                    ->contain([
                        'Suppliers',
                        'Products' => function ($q) {
                            return $q->contain([
                                'ProductImages' => function ($q2) {
                                    return $q2->where([
                                        'ProductImages.status' => 'A',
                                        'ProductImages.image_default' => 1
                                    ])->select(['id', 'product_id', 'image']);
                                }
                            ]);
                        },
                        'ProductVariants' => function ($q) {
                            return $q->select(['id', 'variant_name']);
                        }
                    ])
                    ->order(['Suppliers.name' => 'ASC'])
                    ->toArray();

                foreach ($query_supplier_products as $product) {

                    if (!empty($product->product->product_images) && !empty($product->product->product_images[0]->image)) {
                        $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_images[0]->image);
                    } else {
                        $product->product->product_image = null;
                    }
                }

                $supplier_products = [];
                $i = 1;
                foreach ($query_supplier_products as $supplier_product) {

                    $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                    'D' => ['label' => __('Deleted'), 'class' => 'col-red']
                    ];

                    $status = $statusMap[$supplier_product->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

                    // Get the variant name, if available
                    $variantName = 'N/A'; // Default value
                    if (!empty($supplier_product->product_variant_id)) {
                        $variantName = $supplier_product->product_variant->variant_name;
                    }

                    $supplier_products[] = [
                        'id' => $supplier_product->id,
                        'supplier_name' => h($supplier_product->supplier->name),
                        'sku_id' => h($supplier_product->product->sku),
                        'product_name' => h($supplier_product->product->name),
                        'product_variant_name' => h($variantName),
                        'supplier_price' => h(number_format((float)$supplier_product->supplier_price, 0, '', $thousandSeparator)).' '.$currencySymbol,
                        'product_image' => '<img src="'.$supplier_product->product->product_image.'" style="height:40px; width:40px">',
                        'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                        'actions' => '
                            <a target="_blank" href="' . Router::url(['controller' => 'Products', 'action' => 'view', $supplier_product->product->id], true) . '" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>' .
                            '<a style="cursor: pointer;" data-id="' . $supplier_product->id . '" onClick="openEditSupplierProductModal('.$supplier_product->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                            '<a href="' . Router::url(['controller' => 'SupplierProducts', 'action' => 'delete', $supplier_product->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete" data-delete-confirmation="' . __(Configure::read('Constants.DELETE_CONFIRMATION_MESSAGE')) . '"
                                data-delete-warning="' . __(Configure::read('Constants.DELETE_WARNING_MESSAGE')) . '"
                                data-delete-fail="' . __(Configure::read('Constants.DELETE_FAIL_MESSAGE')) . '"><i class="far fa-trash-alt"></i></a>'
                    ];

                }    

                $this->set([
                    'supplier_products' => $supplier_products,
                    '_serialize' => ['supplier_products'],
                ]);

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['data' => $supplier_products]));

            }
            $this->Flash->error(__('The supplier product could not be saved. Please, try again.'));
        }

        $suppliers = $this->fetchTable('Suppliers')
                        ->find('list')
                        ->where(['status' => 'A'])
                        ->all();

        $products = $this->fetchTable('Products')
                        ->find('list')
                        ->where(['status' => 'A'])
                        ->all();
        
        $this->set(compact('supplier_product', 'suppliers', 'products'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The supplier product could not be deleted. Please, try again.'];

        try {
            $record = $this->SupplierProducts->get($id);
            $record->status = 'D';

            if ($this->SupplierProducts->save($record)) {
                $response = ['success' => true, 'message' => 'The supplier product has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function exportToCsv()
    {
        $this->response = $this->response->withType('text/csv');
        $this->response = $this->response->withDownload('sample_supplier_product_format.csv');    
        $header = ['Product Name', 'Product SKU', 'Supplier Price'];
        $data = [
            ['Product Name','product_sku',151.00],
            ['Product Name','product_sku',151.00],
            ['Product Name','product_sku',151.00]
        ];

        $output = fopen('php://output', 'w');

        fputcsv($output, $header);

        foreach ($data as $row) {
            fputcsv($output, $row);
        }

        fclose($output);
        return $this->response;
    }

    // public function uploadSupplierProducts()
    // {
    //     $this->request->allowMethod(['post']); // Only allow POST requests

    //     // Get the file from the request
    //     $file = $this->request->getData('csv_file');
    //     $supplierId = $this->request->getData('supplier_id');

    //     // Read the CSV content directly from the uploaded file
    //     $csvContent = $file->getStream()->getContents();
    //     $rows = array_map(fn($line) => str_getcsv($line, ",", '"', "\\"), explode("\n", $csvContent));

    //     if (empty($rows) || count($rows) < 2) {
    //         return $this->response->withType('application/json')
    //             ->withStringBody(json_encode(['errors' => ['Invalid or empty CSV file'], 'data' => []]));
    //     }

    //     $errors = [];
    //     $rowCount = 1;

    //     for ($i = 1; $i < count($rows); $i++) {
    //         $rowCount++;
    //         $row = $rows[$i];

    //         // Skip empty rows
    //         if (empty(array_filter($row))) {
    //             continue;
    //         }

    //         if (count($row) === 3) {
    //             $productName = trim($row[0]);
    //             $productSKU = trim($row[1]);
    //             $supplierPrice = trim($row[2]);

    //             if (!is_numeric($supplierPrice)) {
    //                 $errors[] = __("Row {$rowCount}: Invalid Supplier Price for product '{$productName}'");
    //                 continue;
    //             }

    //             // Fetch the product ID by SKU
    //             $product = $this->Products->find()
    //                 ->select(['id'])
    //                 ->where(['sku' => $productSKU])
    //                 ->first();

    //             if (!$product) {
    //                 $errors[] = __("Row {$rowCount}: Product with SKU '{$productSKU}' does not exist.");
    //                 continue;
    //             }

    //             // Check if this product is already assigned to the supplier
    //             $existingProduct = $this->SupplierProducts->find()
    //                 ->where(['supplier_id' => $supplierId, 'product_id' => $product->id])
    //                 ->first();

    //             if (!$existingProduct) {
    //                 $supplierProduct = $this->SupplierProducts->newEntity([
    //                     'supplier_id' => $supplierId,
    //                     'product_id' => $product->id,
    //                     'supplier_price' => $supplierPrice,
    //                 ]);

    //                 if (!$this->SupplierProducts->save($supplierProduct)) {
    //                     $errors[] = __("Row {$rowCount}: Error saving product '{$productName}'");
    //                 }
    //             }
    //         }
    //     }

    //     // Fetch the latest supplier products
    //     $query_supplier_products = $this->SupplierProducts->find()
    //         ->where(['SupplierProducts.supplier_id' => $supplierId])
    //         ->where(['SupplierProducts.status IN' => ['A', 'I']])
    //         ->contain([
    //             'Suppliers',
    //             'Products',
    //             'ProductVariants' => function ($q) {
    //                 return $q->select(['id', 'variant_name']);
    //             }
    //         ])
    //         ->order(['Suppliers.name' => 'ASC'])
    //         ->toArray();

    //     foreach ($query_supplier_products as $product) {
    //         if ($product->product->product_image) {
    //             $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
    //         }
    //     }

    //     $supplier_products = [];
    //     foreach ($query_supplier_products as $supplier_product) {
    //         $statusMap = [
    //             'A' => ['label' => __('Active'), 'class' => 'col-green'],
    //             'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
    //             'D' => ['label' => __('Deleted'), 'class' => 'col-red']
    //         ];

    //         $status = $statusMap[$supplier_product->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

    //         $variantName = !empty($supplier_product->product_variant_id) ? $supplier_product->product_variant->variant_name : 'N/A';

    //         $supplier_products[] = [
    //             'id' => $supplier_product->id,
    //             'supplier_name' => h($supplier_product->supplier->name),
    //             'sku_id' => h($supplier_product->product->sku),
    //             'product_name' => h($supplier_product->product->name),
    //             'product_variant_name' => h($variantName),
    //             'supplier_price' => h($supplier_product->supplier_price),
    //             'product_image' => '<img src="' . $supplier_product->product->product_image . '" style="height:40px; width:40px">',
    //             'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
    //             'actions' => '
    //                 <a target="_blank" href="' . Router::url(['controller' => 'Products', 'action' => 'view', $supplier_product->product->id], true) . '" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>' .
    //                 '<a style="cursor: pointer;" data-id="' . $supplier_product->id . '" onClick="openEditSupplierProductModal(' . $supplier_product->id . ')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
    //                 '<a href="' . Router::url(['controller' => 'SupplierProducts', 'action' => 'delete', $supplier_product->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'
    //         ];
    //     }

    //     return $this->response->withType('application/json')
    //         ->withStringBody(json_encode(['errors' => $errors, 'data' => $supplier_products]));
    // }

    public function uploadSupplierProducts()
    {
        $this->request->allowMethod(['post']); // Only allow POST requests

        // Get the file from the request
        $file = $this->request->getData('csv_file');
        $supplierId = $this->request->getData('supplier_id');

        // Read the CSV content directly from the uploaded file
        $csvContent = $file->getStream()->getContents();
        $rows = array_map(fn($line) => str_getcsv($line, ",", '"', "\\"), explode("\n", $csvContent));

        if (empty($rows) || count($rows) < 2) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['errors' => ['Invalid or empty CSV file'], 'data' => []]));
        }

        $errors = [];
        $rowCount = 1;

        for ($i = 1; $i < count($rows); $i++) {
            $rowCount++;
            $row = $rows[$i];

            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }

            if (count($row) === 3) {
                $productName = trim($row[0]);
                $productSKU = trim($row[1]);
                $supplierPrice = trim($row[2]);

                if (!is_numeric($supplierPrice)) {
                    // $errors[] = __("Row {$rowCount}: Invalid Supplier Price for product '{$productName}'");

                    $errors[] = __(
                        "Row {0}: Invalid Supplier Price for product '{1}'",
                        $rowCount,
                        $productName
                    );

                    continue;
                }

                // First, check if SKU exists in product_variants
                $productVariant = $this->ProductVariants->find()
                    ->select(['id', 'product_id'])
                    ->where(['sku' => $productSKU])
                    ->first();

                if ($productVariant) {
                    $productId = $productVariant->product_id;
                    $productVariantId = $productVariant->id;
                } else {
                    // If not found, check in products table
                    $product = $this->Products->find()
                        ->select(['id'])
                        ->where(['sku' => $productSKU])
                        ->first();

                    if (!$product) {
                        // $errors[] = __("Row {$rowCount}: Product with SKU '{$productSKU}' does not exist.");

                        $errors[] = __(
                            "Row {0}: Product with SKU '{1}' does not exist.",
                            $rowCount,
                            $productSKU
                        );
                        continue;
                    }

                    $productId = $product->id;
                    $productVariantId = null;
                }

                // Check if this product is already assigned to the supplier
                $existingProduct = $this->SupplierProducts->find()
                    ->where([
                        'supplier_id' => $supplierId,
                        'product_id' => $productId,
                        'product_variant_id IS' => $productVariantId,
                        'status IN' => ['A', 'I']
                    ])
                    ->first();

                // if (!$existingProduct) {
                //     $supplierProduct = $this->SupplierProducts->newEntity([
                //         'supplier_id' => $supplierId,
                //         'product_id' => $productId,
                //         'product_variant_id' => $productVariantId,
                //         'supplier_price' => $supplierPrice,
                //     ]);

                //     if (!$this->SupplierProducts->save($supplierProduct)) {
                //         $errors[] = __("Row {$rowCount}: Error saving product '{$productName}'");
                //     }
                // }

                if ($existingProduct) {
                    // Update existing product's supplier price
                    $existingProduct->supplier_price = $supplierPrice;

                    if (!$this->SupplierProducts->save($existingProduct)) {
                        // $errors[] = __("Row {$rowCount}: Error updating product '{$productName}'");
                        $errors[] = __(
                            "Row {0}: Error updating product '{1}'",
                            $rowCount,
                            $productName
                        );
                    }
                } else {
                    // Insert a new product record
                    $supplierProduct = $this->SupplierProducts->newEntity([
                        'supplier_id' => $supplierId,
                        'product_id' => $productId,
                        'product_variant_id' => $productVariantId,
                        'supplier_price' => $supplierPrice,
                    ]);

                    if (!$this->SupplierProducts->save($supplierProduct)) {
                        // $errors[] = __("Row {$rowCount}: Error saving product '{$productName}'");
                        $errors[] = __(
                            "Row {0}: Error saving product '{1}'",
                            $rowCount,
                            $productName
                        );
                    }
                }
            }
        }

        // Fetch the latest supplier products
        $query_supplier_products = $this->SupplierProducts->find()
            ->where(['SupplierProducts.supplier_id' => $supplierId])
            ->where(['SupplierProducts.status IN' => ['A', 'I']])
            ->contain([
                'Suppliers',
                'Products',
                'ProductVariants' => function ($q) {
                    return $q->select(['id', 'variant_name', 'sku']);
                }
            ])
            ->order(['Suppliers.name' => 'ASC'])
            ->toArray();

        foreach ($query_supplier_products as $product) {
            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }

            if (!empty($product->product_variant_id) && !empty($product->product_variant->sku)) {
                $product->product->sku = $product->product_variant->sku;
            }
        }

        $supplier_products = [];
        foreach ($query_supplier_products as $supplier_product) {
            $statusMap = [
                'A' => ['label' => __('Active'), 'class' => 'col-green'],
                'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                'D' => ['label' => __('Deleted'), 'class' => 'col-red']
            ];

            $status = $statusMap[$supplier_product->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

            $variantName = !empty($supplier_product->product_variant_id) ? $supplier_product->product_variant->variant_name : 'N/A';

            $supplier_products[] = [
                'id' => $supplier_product->id,
                'supplier_name' => h($supplier_product->supplier->name),
                'sku_id' => h($supplier_product->product->sku),
                'product_name' => h($supplier_product->product->name),
                'product_variant_name' => h($variantName),
                'supplier_price' => h($supplier_product->supplier_price),
                'product_image' => '<img src="' . $supplier_product->product->product_image . '" style="height:40px; width:40px">',
                'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                'actions' => '
                    <a target="_blank" href="' . Router::url(['controller' => 'Products', 'action' => 'view', $supplier_product->product->id], true) . '" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>' .
                    '<a style="cursor: pointer;" data-id="' . $supplier_product->id . '" onClick="openEditSupplierProductModal(' . $supplier_product->id . ')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                    '<a href="' . Router::url(['controller' => 'SupplierProducts', 'action' => 'delete', $supplier_product->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'
            ];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['errors' => $errors, 'data' => $supplier_products]));
    }



}
