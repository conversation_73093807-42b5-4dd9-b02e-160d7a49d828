<?php

declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\Datasource\FactoryLocator;
use Cake\ORM\Query;

class AssignCustomerGroupsCommand extends Command
{
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        return parent::buildOptionParser($parser);
    }

    public function execute(Arguments $args, ConsoleIo $io)
    {
        $io->out("Starting customer group assignment...");

        $customersTable = FactoryLocator::get('Table')->get('Customers');
        $loyaltySettingsTable = FactoryLocator::get('Table')->get('LoyaltySettings');
        $customerGroupMappingsTable = FactoryLocator::get('Table')->get('CustomerGroupMappings');

        // Step 1: Get all active loyalty settings ordered by threshold DESC
        $loyaltySettings = $loyaltySettingsTable->find()
            ->where(['status' => 'A'])
            ->order(['threshold' => 'DESC'])
            ->all();

        // Step 2: Get customers with their active user and total orders amount
        $customers = $customersTable->find()
            ->contain([
                'Users',
                'Orders' => function ($q) {
                    return $q->select([
                        'Orders.customer_id',
                        'total_amount_sum' => $q->func()->sum('Orders.total_amount')
                    ])
                        ->group('Orders.customer_id');
                },
                'CustomerGroupMappings' => function ($q) {
                    return $q->where(['CustomerGroupMappings.status' => 'A']);
                }
            ])
            ->where(['Users.status !=' => 'D'])
            ->all();

        foreach ($customers as $customer) {
            $order = $customer->orders[0] ?? null;
            $amount = $order->total_amount_sum ?? 0;

            $matchedGroupId = null;
            foreach ($loyaltySettings as $setting) {
                if ($amount >= $setting->threshold) {
                    $matchedGroupId = $setting->customer_group_id;
                    break;
                }
            }

            if ($matchedGroupId) {
                $alreadyInCorrectGroup = false;

                // Step 1: Mark incorrect active mappings as 'D'
                foreach ($customer->customer_group_mappings as $existingMapping) {
                    if ($existingMapping->status === 'A') {
                        if ($existingMapping->customer_group_id == $matchedGroupId) {
                            $alreadyInCorrectGroup = true;
                        } else {
                            $existingMapping->status = 'D';
                            $customerGroupMappingsTable->save($existingMapping);
                        }
                    }
                }

                // Step 2: If not already in correct group, create new mapping
                if (!$alreadyInCorrectGroup) {
                    $newMapping = $customerGroupMappingsTable->newEmptyEntity();
                    $newMapping->customer_id = $customer->id;
                    $newMapping->customer_group_id = $matchedGroupId;
                    $newMapping->status = 'A';

                    if ($customerGroupMappingsTable->save($newMapping)) {
                        $io->out("Assigned Customer ID {$customer->id} to Group {$matchedGroupId}");
                    } else {
                        $io->err("Failed to assign Customer ID {$customer->id}");
                    }
                } else {
                    $io->out("Customer ID {$customer->id} already in correct group");
                }
            } else {
                $io->out("No matching group for Customer ID {$customer->id} with amount {$amount}");
            }
        }

        $io->out("Customer group assignment completed.");
    }
}
