<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $contentpages
 */
class FaqsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $FaqCategories;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->FaqCategories = $this->fetchTable('FaqCategories');
    }
    
    public function index()
    {
        $menus = $this->FaqCategories->find('list', [
            'keyField' => 'name',
            'valueField' => 'name',
        ])->toArray();

        $content_pages = $this->Faqs->find()
            ->where(['Faqs.status IN' => ['A', 'I']])
            ->contain([
                'FaqCategories' => [
                    'fields' => ['FaqCategories.id', 'FaqCategories.name']
                ]
            ])
            ->order(['Faqs.title' => 'ASC'])->toArray();

        $title = 'FAQs | List';
        $this->set(compact('content_pages', 'menus', 'title'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $content = $this->Faqs->get($id, contain: [
            'FaqCategories'
        ]);

        if (!empty($content->content_images)) {
            foreach ($content->content_images as &$image) {
                $image->image = $this->Media->getCloudFrontURL($image->image);
            }
        }

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $title = 'FAQ | View';
        $this->set(compact('content', 'dateFormat', 'timeFormat', 'title'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $content = $this->Faqs->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $content = $this->Faqs->patchEntity($content, $data);

            if ($this->Faqs->save($content)) {

                $this->Flash->success(__('The FAQ has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The FAQ could not be saved. Please, try again.'));
        }

        $menus = $this->FaqCategories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
        ])->toArray();

        $title = 'FAQ | Add';
        $this->set(compact('content', 'menus', 'title'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function edit($id = null)
    {

        $content = $this->Faqs->get($id, contain: ['FaqCategories']);
        // echo "<pre>"; print_r($content);die;
        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $content = $this->Faqs->patchEntity($content, $data);

            if ($this->Faqs->save($content)) {

                $this->Flash->success(__('The FAQ has been saved.'));

                return $this->redirect(['action' => 'index']);
            }

            $this->Flash->error(__('The FAQ could not be saved. Please, try again.'));
        }

        if (!empty($content->content_images)) {
            foreach ($content->content_images as &$image) {
                $image->image = $this->Media->getCloudFrontURL($image->image);
            }
        }

        $menus = $this->FaqCategories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
        ])->toArray();

        $title = 'FAQ | Edit';
        $this->set(compact('content', 'menus', 'title'));

    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The FAQ could not be deleted. Please, try again.'];

        try {
            $record = $this->Faqs->get($id);
            $record->status = 'D';

            if ($this->Faqs->save($record)) {
                $response = ['success' => true, 'message' => 'The FAQ has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
