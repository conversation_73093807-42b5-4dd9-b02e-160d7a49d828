<?php

declare(strict_types=1);

namespace App\Controller;

use App\Enum\ExpenseStatus;
use App\Model\Entity\Expense;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;

/**
 * Expenses Controller
 *
 * @property \App\Model\Table\ExpensesTable $Expenses
 */
class ExpensesController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }

    public function index()
    {
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';

        // $query = $this->Expenses->find('notDeleted')
        //     ->contain(['Showrooms', 'Users', 'ShowroomExpenseCategories']);

        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $IsShowroomanager = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $IsShowroomanager = $user->id;
                $query = $this->Expenses->find('notDeleted')
                    ->contain(['Showrooms', 'Users', 'ShowroomExpenseCategories'])
                    ->where(['Showrooms.showroom_manager' => $IsShowroomanager]);
                $expenses = $query->all();
                if (!$expenses->isEmpty()) {
                    // Retrieve the first expense and get the related showroom name using PHP 8's nullsafe operator
                    $showroomName = $expenses->first()->showroom?->name;
                }
            } else if ($user->role_id == $ShowroomSupervisorRoleId) {
                $IsShowroomsupervisor = $user->id;
                $query = $this->Expenses->find('notDeleted')
                    ->contain(['Showrooms', 'Users', 'ShowroomExpenseCategories'])
                    ->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor]);
                $expenses = $query->all();
                if (!$expenses->isEmpty()) {
                    // Retrieve the first expense and get the related showroom name using PHP 8's nullsafe operator
                    $showroomName = $expenses->first()->showroom?->name;
                }
            } else {
                $query = $this->Expenses->find('notDeleted')
                    ->contain(['Showrooms', 'Users', 'ShowroomExpenseCategories']);
                $expenses = $query->all();
                $showroomName = '';
            }
        } else {
            $query = $this->Expenses->find('notDeleted')
                ->contain(['Showrooms', 'Users', 'ShowroomExpenseCategories']);
            $expenses = $query->all();
            $showroomName = '';
        }
        $totalExpenses = clone $query;
        $totalExpenses = $totalExpenses->where(['MONTH(Expenses.created)' => date('m')])->count();
        $totalPendingPay = clone $query;
        $totalPendingPay = $totalPendingPay->where(['Expenses.payment_status' => 'Unpaid'])->count();

        $totalCompletedPay = clone $query;
        $totalCompletedPay = $totalCompletedPay->where(['Expenses.payment_status' => 'Paid'])->count();


        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $expenseCategories = $this->Expenses->ShowroomExpenseCategories->find('list')->all();

        $Expstatus = Configure::read('Constants.EXP_PAYMENT_STATUSES');
        $ExpstatusMap = Configure::read('Constants.EXP_PAYMENT_STATUSES_MAP');
        $this->set(compact('expenses', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'status', 'statusMap', 'showroomName', 'Expstatus', 'ExpstatusMap', 'expenseCategories', 'totalExpenses', 'totalPendingPay', 'totalCompletedPay'));
    }

    /**
     * View method
     *
     * @param string|null $id Expense id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view(?string $id = null)
    {
        $title = 'Showroom Expense | View';
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';
        $expense = $this->Expenses->find('notDeleted')
            ->contain(['Showrooms', 'Users', 'ShowroomExpenseCategories'])
            ->where(['Expenses.id' => $id])
            ->first();
        $showroomExpenseImagesTable = TableRegistry::getTableLocator()->get('ShowroomExpenseImages');
        $expense->receipt = $showroomExpenseImagesTable->find()
            ->where(['showroom_expense_id' => $expense->id, 'status' => 'A'])
            ->all();
        $this->set(compact('expense', 'title', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $title = 'Showroom Expense | Add';
        $expense = $this->Expenses->newEmptyEntity();

        if ($this->request->is('post')) {
            $files = $this->request->getData('receipt'); // Multiple files
            $data = $this->request->getData();

            // Initialize the array to hold image data
            $imageData = [];

            // Check if files are uploaded
            if ($files && is_array($files)) {
                foreach ($files as $file) {
                    if ($file->getError() === UPLOAD_ERR_OK) {
                        // Upload the file and get the file path
                        $filePath = $this->uploadFile($file);

                        // Add the image data for saving in the ShowroomExpenseImages table
                        $imageData[] = [
                            'image' => $filePath,
                            'status' => 'A',
                            'created' => date('Y-m-d H:i:s'),
                            'updated' => date('Y-m-d H:i:s'),
                        ];
                    }
                }
            }

            // Add the incurred_by field to the expense
            $data['incurred_by'] = $this->Authentication->getIdentity()->id;
            $data['incurred_date'] = $data['payment_date'] = date('Y-m-d');

            // Patch the expense data to the entity
            $expense = $this->Expenses->patchEntity($expense, $data);

            // Save the expense
            if ($this->Expenses->save($expense)) {
                // Save the associated images if any
                if (!empty($imageData)) {
                    // Add showroom_expense_id to image data
                    foreach ($imageData as &$image) {
                        $image['showroom_expense_id'] = $expense->id;
                    }

                    $showroomExpenseImagesTable = TableRegistry::getTableLocator()->get('ShowroomExpenseImages');
                    $showroomExpenseImageEntities = $showroomExpenseImagesTable->newEntities($imageData);
                    $showroomExpenseImagesTable->saveMany($showroomExpenseImageEntities);
                }

                $this->Flash->success(__('The expense has been saved.'));

                return $this->redirect(['action' => 'index']);
            } else {
                $this->Flash->error(__('Unable to save the expense.'));
            }
        }

        // Fetch other data like Showrooms and Categories
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $IsShowroomanager = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Expenses->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
            } else if ($user->role_id == $ShowroomSupervisorRoleId) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Expenses->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
            } else {
                $showrooms = $this->Expenses->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['status' => 'A'])->toArray();
            }
        } else {
            $showrooms = $this->Expenses->Showrooms->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['status' => 'A'])->toArray();
        }

        $expenseCategories = $this->Expenses->ShowroomExpenseCategories->find('list', limit: 200)->all();

        $this->set(compact('expense', 'showrooms', 'title', 'expenseCategories'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Expense id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit(?string $id = null)
    {
        $title = 'Showroom Expense | Edit';
        // Fetch the expense entity by ID
        $expense = $this->Expenses->find('notDeleted')
            ->contain(['Showrooms', 'Users', 'ShowroomExpenseCategories'])
            ->where(['Expenses.id' => $id])
            ->first();
        if ($expense) {
            $showroomExpenseImagesTable = TableRegistry::getTableLocator()->get('ShowroomExpenseImages');
            $expense->receipt = $showroomExpenseImagesTable->find()
                ->where(['showroom_expense_id' => $expense->id, 'status' => 'A'])
                ->all();
        }

        // Check if the request is 'patch', 'post', or 'put'
        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            if (empty($data['payment_date'])) {
                $data['payment_date'] = date('Y-m-d');
            }
            if (empty($data['incurred_date'])) {
                $data['incurred_date'] = date('Y-m-d');
            }
            $expense = $this->Expenses->patchEntity($expense, $data);

            // Initialize the array to hold image data
            $imageData = [];
            $files = $this->request->getData('receipt');

            // Check if files are uploaded
            if ($files && is_array($files)) {
                foreach ($files as $file) {
                    if ($file->getError() === UPLOAD_ERR_OK) {
                        // Upload the file and get the file path
                        $filePath = $this->uploadFile($file);

                        // Add the image data for saving in the ShowroomExpenseImages table
                        $imageData[] = [
                            'image' => $filePath,
                            'status' => 'A',
                            'created' => date('Y-m-d H:i:s'),
                            'updated' => date('Y-m-d H:i:s'),
                        ];
                    }
                }
            }

            if ($this->Expenses->save($expense)) {
                if (!empty($imageData)) {
                    // Add showroom_expense_id to image data
                    foreach ($imageData as &$image) {
                        $image['showroom_expense_id'] = $expense->id;
                    }

                    $showroomExpenseImagesTable = TableRegistry::getTableLocator()->get('ShowroomExpenseImages');
                    $showroomExpenseImageEntities = $showroomExpenseImagesTable->newEntities($imageData);
                    $showroomExpenseImagesTable->saveMany($showroomExpenseImageEntities);
                }

                $this->Flash->success(__('The expense has been updated.'));

                return $this->redirect(['action' => 'index']);
            } else {
                $this->Flash->error(__('The expense could not be updated. Please, try again.'));
            }
        }

        // Fetch data for showrooms and categories
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $IsShowroomanager = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Expenses->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
            } else if ($user->role_id == $ShowroomSupervisorRoleId) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Expenses->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
            } else {
                $showrooms = $this->Expenses->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['status' => 'A'])->toArray();
            }
        } else {
            $showrooms = $this->Expenses->Showrooms->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['status' => 'A'])->toArray();
        }
        $expenseCategories = $this->Expenses->ShowroomExpenseCategories->find('list', limit: 200)->all();

        // Pass the data to the view
        $this->set(compact('expense', 'showrooms', 'title', 'expenseCategories'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Expense id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete(?string $id = null)
    {
        //$this->request->allowMethod(['post', 'delete']);
        $expense = $this->Expenses->get($id);
        $expense->status = ExpenseStatus::Deleted->value;
        if ($this->Expenses->save($expense)) {
            $response = ['success' => true, 'message' => 'The expense has been deleted.'];
        } else {
            $response = ['success' => false, 'message' => 'The expense could not be delted. Please, try again.'];
        }
        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));

            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }

            return $this->redirect(['action' => 'index']);
        }
    }

    private function uploadFile($file, ?string $existingFile = null): string
    {
        // Check if a file has been uploaded
        if ($file && $file->getError() === UPLOAD_ERR_OK) {
            // Ensure the upload directory exists
            if (!is_dir(Expense::RECEIPT_UPLOAD_PATH)) {
                mkdir(Expense::RECEIPT_UPLOAD_PATH, 0777, true);
            }

            // Generate a new file name to prevent overwriting
            $filename = time() . '-' . $file->getClientFilename();

            // Move the uploaded file to the correct directory
            $file->moveTo(Expense::RECEIPT_UPLOAD_PATH . $filename);

            // If there's an existing file, delete it (to prevent old files from lingering)
            if ($existingFile && file_exists(Expense::RECEIPT_UPLOAD_PATH . $existingFile)) {
                unlink(Expense::RECEIPT_UPLOAD_PATH . $existingFile);
            }

            // Return the new filename (to be saved in the database)
            return $filename;
        }

        // Return the existing file if no new file was uploaded
        return $existingFile;
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $recepitId = $this->request->getData('image_id');

        if (!$recepitId) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $showroomExpenseImagesTable = TableRegistry::getTableLocator()->get('ShowroomExpenseImages');

        try {
            $showroomExpenseImage = $showroomExpenseImagesTable->get($recepitId);
        } catch (RecordNotFoundException $e) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'failure', 'message' => 'Expense Receipt not found']));
        }

        $existingImagePath = $showroomExpenseImage->image;

        if ($existingImagePath) {
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
            $filePath = WWW_ROOT . $uploadFolder . $existingImagePath;

            if (file_exists($filePath)) {
                if (!unlink($filePath)) {
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to delete image file']));
                }
            }

            $showroomExpenseImage->status = 'D';
            if ($showroomExpenseImagesTable->save($showroomExpenseImage)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'success', 'message' => 'Receipt deleted successfully']));
            } else {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update Receipt']));
            }
        } else {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }
}
