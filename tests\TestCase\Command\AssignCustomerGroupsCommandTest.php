<?php
declare(strict_types=1);

namespace App\Test\TestCase\Command;

use App\Command\AssignCustomerGroupsCommand;
use Cake\Console\TestSuite\ConsoleIntegrationTestTrait;
use Cake\TestSuite\TestCase;

/**
 * App\Command\AssignCustomerGroupsCommand Test Case
 *
 * @uses \App\Command\AssignCustomerGroupsCommand
 */
class AssignCustomerGroupsCommandTest extends TestCase
{
    use ConsoleIntegrationTestTrait;

    /**
     * Test buildOptionParser method
     *
     * @return void
     * @uses \App\Command\AssignCustomerGroupsCommand::buildOptionParser()
     */
    public function testBuildOptionParser(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test execute method
     *
     * @return void
     * @uses \App\Command\AssignCustomerGroupsCommand::execute()
     */
    public function testExecute(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
