<?php

use AWS\CRT\Options;

 $this->start('add_css'); ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/contact-us.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">

  <style>
/* For Single Select */
.select2-container .select2-selection--single {
    height: 36px !important;
    line-height: 36px !important;
    padding: 4px 8px;
    
}
.searchable-select-container {
    width: 330px;
    margin: 10px 0 0 0;
    border: 1px solid #cccccc;
    border-radius: 4px;
}
/* Adjust the arrow inside the box */
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px !important;
}

/* For Multi Select */
.select2-container .select2-selection--multiple {
    min-height: 36px !important;
    padding: 4px 8px;
}

.ax-field-wrapper {
      width: 100%;
      max-width: 425px;
      margin-top: 1%;
    }

    .ax-input-box {
      display: flex;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
    }

    .ax-country-select {
      border: none;
      padding: 10px 0px 10px 10px;
      font-size: 12px;
      outline: none;
      appearance: none;
      flex-shrink: 0;
      width: auto;
    }

    .ax-phone-input {
      flex: 1;
      padding: 10px 10px 10px 0px;
      font-size: 13px;
      border: none;
      outline: none;
    }

    .ax-width-measure {
      visibility: hidden;
      white-space: nowrap;
      position: absolute;
      top: -9999px;
      left: -9999px;
      font-size: 14px;
      font-family: Arial, sans-serif;
      padding: 10px 8px;
    }

    .ax-button {
      margin-top: 15px;
      padding: 10px 15px;
      font-size: 16px;
      border-radius: 4px;
      border: 1px solid #999;
      cursor: pointer;
    }
  </style>
    <style type="text/css">
        /* .error {
            color: red !important;
        } */
         [id$="error"]{
            color: red !important;
         }
        .contact-body form input + div.error{
            position: relative;
            left: 5px;
        }
        #ax-mobile-input-error {
            position: relative;
            left: 5px;
            margin-top: 5px;
            display: block;
        }
    </style>
 <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
  <style>
    #map { height: 500px; }
  </style>

  <style>
    .ax-field-wrapper {
      width: 100%;
      max-width: 425px;
      margin-top: 3%;
    }

    .ax-input-box {
      display: flex;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
      background: white;
    }

    .ax-country-select {
      border: none;
      padding: 10px 0px 10px 10px;
      font-size: 12px;
      outline: none;
      appearance: none;
      flex-shrink: 0;
      width: auto;
    }

    .ax-phone-input {
      flex: 1;
      padding: 10px 10px 10px 0px;
      font-size: 13px;
      border: none;
      outline: none;
    }

    .ax-width-measure {
      visibility: hidden;
      white-space: nowrap;
      position: absolute;
      top: -9999px;
      left: -9999px;
      font-size: 14px;
      font-family: Arial, sans-serif;
      padding: 10px 8px;
    }

    .ax-button {
      margin-top: 15px;
      padding: 10px 15px;
      font-size: 16px;
      border-radius: 4px;
      border: 1px solid #999;
      cursor: pointer;
    }
    /* Style done by karthi for map position */
    #map {
        margin-left: 337px;
    }
    /* style done for textarea by karthi */
    .splitter-form textarea{
        resize: none;
        overflow: hidden;
        min-height: 50px;
        max-height: 100px;
        font-family: "arial";
    }
    .search-container input.search-input {
        height: 30px;
        margin-top: 4px;
        width: 75%;
    }
  </style>
<?php $this->end(); ?>

    <div class="productCategoryListingC">
        <div class="productCategoryListing">
            <img src="<?= $this->Url->webroot('assets/icons8-home-100.png') ?>"
                 class="productCategoryListing-home-icn"/>
            <span class="productCategoryListing-home-span"><a
                    href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'home']) ?>">Home</a> </span>
            <span class="productCategoryListing-home-span">></span>
            <span class="productCategoryListing-home-span">Contact us</span>
        </div>
    </div>

    <div class="contact-body">
        <div class="splitter">
            <div class="checkout-payment-title">Contact Us, its easy</div>
            <div class="question">If you have a question, or need help</div>

            <div class="phone-stuff">
                <div class="cll-icon"><a href="tel:+22521007007" style="display:block; color: inherit; text-decoration: none; cursor: pointer; padding:5px 10px"><i class="fa fa-phone"></i></a></div>
                <div>
                    <div class="checkout-payment-title">
                        <a href="tel:+22521007007" style="color: inherit; text-decoration: none; cursor: pointer; padding-left:13px;">
                            Call today
                        </a>
                    </div>
                    <div class="question">
                        <a href="tel:+22521007007" style="color: inherit; text-decoration: none; padding-left:13px;">
                            +225 21 007 007
                        </a>
                    </div>
                </div>
            </div>

            <div class="clock-stuff">
                <div class="clocl-icon"><i class="fa fa-clock-o"></i></div>
                <div>
                    <div class="checkout-payment-title">Monday - Friday</div>
                    <div class="question">8AM - 6AM</div>
                </div>
            </div>

        </div>

        <div>
            <?= $this->Form->create(null, ['id' => 'contact-form', 'class' => 'splitter-form']) ?>
            <?= $this->Flash->render() ?>
            <label for="fullName"><?= __('Full Name:') ?></label>
            <input type="text" id="fullName" class="name-input sign-up-links-input-mail" style="width: 100%"
            value="<?= !empty($users->first_name) ? $users->first_name .' '.$users->last_name : '' ?>"
            placeholder="<?= __('Enter Full Name...') ?>" name="issue_name"/>
            <div class="error-message" id="fullNameError"></div>




            <div class="ph-no-mail">
                <div class="phone-tel">

                    <label for="phone_no1"><?= __('Phone Number:') ?></label>
                    <div style="display: flex; gap: 10px;">



                    <div id="mobile-container" class="toggle-input">
                        <div class="ax-field-wrapper">
                            <div class="ax-input-box mobile-input-wrapper">
                                <select id="ax-country-select" name="country_code1" class="ax-country-select searchable-select">
                                    <option value="1">+1 <small>(USA, Canada)</small></option>
                                    <option value="7">+7 <small>(Russia, Kazakhstan)</small></option>
                                    <option value="20">+20 <small>(Egypt)</small></option>
                                    <option value="27">+27 <small>(South Africa)</small></option>
                                    <option value="30">+30 <small>(Greece)</small></option>
                                    <option value="31">+31 <small>(Netherlands)</small></option>
                                    <option value="32">+32 <small>(Belgium)</small></option>
                                    <option value="33">+33 <small>(France)</small></option>
                                    <option value="34">+34 <small>(Spain)</small></option>
                                    <option value="36">+36 <small>(Hungary)</small></option>
                                    <option value="39">+39 <small>(Italy)</small></option>
                                    <option value="40">+40 <small>(Romania)</small></option>
                                    <option value="41">+41 <small>(Switzerland)</small></option>
                                    <option value="43">+43 <small>(Austria)</small></option>
                                    <option value="44">+44 <small>(United Kingdom)</small></option>
                                    <option value="45">+45 <small>(Denmark)</small></option>
                                    <option value="46">+46 <small>(Sweden)</small></option>
                                    <option value="47">+47 <small>(Norway)</small></option>
                                    <option value="48">+48 <small>(Poland)</small></option>
                                    <option value="49">+49 <small>(Germany)</small></option>
                                    <option value="51">+51 <small>(Peru)</small></option>
                                    <option value="52">+52 <small>(Mexico)</small></option>
                                    <option value="53">+53 <small>(Cuba)</small></option>
                                    <option value="54">+54 <small>(Argentina)</small></option>
                                    <option value="55">+55 <small>(Brazil)</small></option>
                                    <option value="56">+56 <small>(Chile)</small></option>
                                    <option value="57">+57 <small>(Colombia)</small></option>
                                    <option value="58">+58 <small>(Venezuela)</small></option>
                                    <option value="60">+60 <small>(Malaysia)</small></option>
                                    <option value="61">+61 <small>(Australia)</small></option>
                                    <option value="62">+62 <small>(Indonesia)</small></option>
                                    <option value="63">+63 <small>(Philippines)</small></option>
                                    <option value="64">+64 <small>(New Zealand)</small></option>
                                    <option value="65">+65 <small>(Singapore)</small></option>
                                    <option value="66">+66 <small>(Thailand)</small></option>
                                    <option value="81">+81 <small>(Japan)</small></option>
                                    <option value="82">+82 <small>(South Korea)</small></option>
                                    <option value="84">+84 <small>(Vietnam)</small></option>
                                    <option value="86">+86 <small>(China)</small></option>
                                    <option value="90">+90 <small>(Turkey)</small></option>
                                    <option value="91">+91 <small>(India)</small></option>
                                    <option value="92">+92 <small>(Pakistan)</small></option>
                                    <option value="93">+93 <small>(Afghanistan)</small></option>
                                    <option value="94">+94 <small>(Sri Lanka)</small></option>
                                    <option value="95">+95 <small>(Myanmar)</small></option>
                                    <option value="98">+98 <small>(Iran)</small></option>
                                    <option value="211">+211 <small>(South Sudan)</small></option>
                                    <option value="212">+212 <small>(Morocco)</small></option>
                                    <option value="213">+213 <small>(Algeria)</small></option>
                                    <option value="216">+216 <small>(Tunisia)</small></option>
                                    <option value="218">+218 <small>(Libya)</small></option>
                                    <option value="220">+220 <small>(Gambia)</small></option>
                                    <option value="221">+221 <small>(Senegal)</small></option>
                                    <option value="222">+222 <small>(Mauritania)</small></option>
                                    <option value="223">+223 <small>(Mali)</small></option>
                                    <option value="224">+224 <small>(Guinea)</small></option>
                                    <option value="225" selected="selected">+225 <small>(Ivory Coast)</small></option>
                                    <option value="226">+226 <small>(Burkina Faso)</small></option>
                                    <option value="227">+227 <small>(Niger)</small></option>
                                    <option value="228">+228 <small>(Togo)</small></option>
                                    <option value="229">+229 <small>(Benin)</small></option>
                                    <option value="230">+230 <small>(Mauritius)</small></option>
                                    <option value="231">+231 <small>(Liberia)</small></option>
                                    <option value="232">+232 <small>(Sierra Leone)</small></option>
                                    <option value="233">+233 <small>(Ghana)</small></option>
                                    <option value="234">+234 <small>(Nigeria)</small></option>
                                    <option value="235">+235 <small>(Chad)</small></option>
                                    <option value="236">+236 <small>(Central African Republic)</small></option>
                                    <option value="237">+237 <small>(Cameroon)</small></option>
                                    <option value="238">+238 <small>(Cape Verde)</small></option>
                                    <option value="239">+239 <small>(Sao Tome and Principe)</small></option>
                                    <option value="240">+240 <small>(Equatorial Guinea)</small></option>
                                    <option value="241">+241 <small>(Gabon)</small></option>
                                    <option value="242">+242 <small>(Congo - Brazzaville)</small></option>
                                    <option value="243">+243 <small>(Congo - Kinshasa)</small></option>
                                    <option value="244">+244 <small>(Angola)</small></option>
                                    <option value="245">+245 <small>(Guinea-Bissau)</small></option>
                                    <option value="246">+246 <small>(British Indian Ocean Territory)</small></option>
                                    <option value="248">+248 <small>(Seychelles)</small></option>
                                    <option value="249">+249 <small>(Sudan)</small></option>
                                    <option value="250">+250 <small>(Rwanda)</small></option>
                                    <option value="251">+251 <small>(Ethiopia)</small></option>
                                    <option value="252">+252 <small>(Somalia)</small></option>
                                    <option value="253">+253 <small>(Djibouti)</small></option>
                                    <option value="254">+254 <small>(Kenya)</small></option>
                                    <option value="256">+256 <small>(Uganda)</small></option>
                                    <option value="257">+257 <small>(Burundi)</small></option>
                                    <option value="258">+258 <small>(Mozambique)</small></option>
                                    <option value="260">+260 <small>(Zambia)</small></option>
                                    <option value="261">+261 <small>(Madagascar)</small></option>
                                    <option value="262">+262 <small>(Réunion, Mayotte)</small></option>
                                    <option value="263">+263 <small>(Zimbabwe)</small></option>
                                    <option value="264">+264 <small>(Namibia)</small></option>
                                    <option value="265">+265 <small>(Malawi)</small></option>
                                    <option value="266">+266 <small>(Lesotho)</small></option>
                                    <option value="267">+267 <small>(Botswana)</small></option>
                                    <option value="268">+268 <small>(Eswatini)</small></option>
                                    <option value="269">+269 <small>(Comoros)</small></option>
                                    <option value="290">+290 <small>(Saint Helena)</small></option>
                                    <option value="291">+291 <small>(Eritrea)</small></option>
                                    <option value="297">+297 <small>(Aruba)</small></option>
                                    <option value="298">+298 <small>(Faroe Islands)</small></option>
                                    <option value="299">+299 <small>(Greenland)</small></option>

                                </select>
                                <input type="tel" name="phone_number" id="ax-mobile-input" class="ax-phone-input" placeholder="Enter mobile number" required>
                                <span id="ax-width-measure" class="ax-width-measure"></span>
                            </div>
                        </div>
                        <span id="ax-mobile-input-error" class="error" for="ax-mobile-input" style="display: none;"></label>
                    </div>

                </div>
                    <!-- <div class="error-message" id="phoneError"></div> -->
                </div>

                <div class="phone-tel">
                    <label for="email"><?= __('Email ID:') ?></label>
                    <input type="email" class="sign-up-links-input-mail" required name="customer_email" id="email" value="
                    <?= !empty($users->email) ? $users->email : '' ?>"
                    placeholder="Enter your email">
                    <div class="error-message" id="emailError"></div>
                </div>

            </div>
            <div class="ph-no-mail" style="gap: 5px;">
                <div class="phone-tel">
                    <label for="queryType"><?= __('Query Type:') ?></label>
                    <select name="support_category_id" id="support_category_id" class="sign-up-links-input-mail" >
                        <?php foreach ($supportCategories as $category): ?>
                            <!-- Make parent category selectable -->
                            <option value="<?= h($category['id']) ?>"><?= h($category['name']) ?></option>

                            <!-- Subcategories as indented options -->
                            <?php foreach ($category['support_subcategories'] as $subCategory): ?>
                                <option value="<?= h($subCategory['id']) ?>"><?= h($subCategory['name']) ?></option>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php if(!empty($orders)) : ?>
                <div class="phone-tel">            
                    <label for="order_id"><?= __('Order Number:') ?></label>
                    <select name="order_id" id="order_id" class="sign-up-links-input-mail searchable-select-search ">
                        <?php foreach ($orders as $val): ?>
                            <option value="<?= h($val['id']) ?>"><?= h($val['order_number']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>  
                <?php else: ?>
                    <div class="phone-tel">
                        <label for="order_id"><?= __('Order Number:') ?></label>
                        <input type="number" name="order_id" id="order_id" class="sign-up-links-input-mail" placeholder="Enter Order Number">
                    </div>
                <?php endif; ?>
            </div>  
            <div class="error-message" id="queryTypeError"></div>

            <label for="message"><?= __('Message:') ?></label>
            <textarea id="description" name="description" class="message-input input sign-up-links-input-mail"
                      style="width: 100%" placeholder="Enter Your Message..." oninput="auto_grow(this)"></textarea>

            <div class="request-div-containers" id="request-div-containers" style="left: 0">
                <button type="submit" class="request-otp"><?= __('Submit') ?></button>
            </div>

            <?= $this->Form->end() ?>

        </div>

    </div>
    <div style="display: flex;justify-content: center;">
        <div class="map-ctn" id="map"></div>
    </div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
<script>
        // Validation
        $("#contact-form").validate({
            rules: {
                issue_name: {
                    required: true,
                    minlength: 4,
                    maxlength: 100
                },
                email: {
                    required: true,
                    email: true
                },
                phone_number: {
                    required: true,
                    digits: true,
                    minlength: 10,
                    maxlength: 15
                },
            },
            messages: {
                issue_name: {
                    required: "Please enter your name.",
                },
                email: {
                    required: "Please enter your email.",
                    email: "Please enter a valid email address."
                },
                phone_number: {
                    required: "Please enter your phone number.",
                    digits: "Please enter only numbers.",
                    minlength: "Mobile number must be at least 10 digits.",
                    maxlength: "Mobile number must not exceed 15 digits."
                }
            },
            errorElement: "div",
            errorPlacement: function (error, element) {
                error.addClass("error");
                if (element.attr("id") === "ax-mobile-input") {
                    // For mobile input, place error in the dedicated label
                    $("#ax-mobile-input-error").text(error.text()).show();
                } else {
                    error.insertAfter(element);
                }
            }
        });
    </script>

<?php $this->start('add_js'); ?>
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
  <script>
    const map = L.map('map').setView([1.6508, 17.6791], 4); // Central Africa

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
  </script>


  <script>
    const axCountrySelect = document.getElementById('ax-country-select');
    const axWidthMeasure = document.getElementById('ax-width-measure');

    function axAdjustSelectWidth() {
      const selectedText = axCountrySelect.options[axCountrySelect.selectedIndex].text;
      axWidthMeasure.textContent = selectedText;
      const newWidth = axWidthMeasure.offsetWidth;
      axCountrySelect.style.width = `${newWidth + 0}px`; // 20px for padding/buffer
    }

    // Initial width adjustment
    axAdjustSelectWidth();

    // Update on change
    axCountrySelect.addEventListener('change', axAdjustSelectWidth);

    // Clear error message when user starts typing in mobile input
    document.getElementById('ax-mobile-input').addEventListener('input', function() {
      document.getElementById('ax-mobile-input-error').style.display = 'none';
    });

    //script for auto height increament upto 100px
    function auto_grow(element) {
        element.style.height = "5px";
        element.style.height = (element.scrollHeight) + "px";
    }
  </script>
<?php $this->end(); ?>