<?php $this->start('add_css'); ?>
<style>
    .navbar, .search-container, .search-container-agn {
        z-index: 1 !important;
    }
</style>

<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">

<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkOutPayment.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkoutNewCards.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkOrderSummary.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/cartDeliverToAddress.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkoutDeliverAddress.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkout-responsive-fix.css') ?>">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">


<style>
    #delivery-div {
        max-height: 500px;
        overflow-x: auto;
    }
    .showroom-list-container {
        max-height: 500px;
        overflow-x: auto;
    }

    .select2-container--default .select2-selection--single {
        height: 0 !important;
    }
    .navbar, .search-container, .search-container-agn {
        z-index: 1 !important;
    }
    .pay-button {
       padding: 10px 20% !important;
    }
    .checkout-order-summary {
        max-height: max-content;
    }
    .checkout-deliver-address-row {
        background: unset !important;
        margin: auto !important;
        padding: initial !important;
        border-radius: 0 !important;
        box-shadow: unset !important;
        display: block !important;
    }
    .Order-s-nameandaddress {
        width: auto !important;
    }
    *{
        box-sizing: border-box;
    }
    #sch-icn {
           top: 35%;
    }
    .I-CTA-input {
            margin: 1px 0px !important;
    }
    .ax-popup {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 300px;
        padding: 20px;
        background: white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        z-index: 9999999; 
        border-radius: 8px;
    }

    #ax-popup-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999998; 
    }

    .ax-popup button {
        margin-top: 10px;
        padding: 8px 16px;
        background: #7059a8;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .ax-popup button:hover {
        background: #5a4291;
    }

    #momo-mobile-input,
    div#momo-mobile-input,
    .form-group#momo-mobile-input,
    body .form-group#momo-mobile-input,
    body div#momo-mobile-input {
        display: none !important; 
    }

    #momo-mobile-input.show-input,
    div#momo-mobile-input.show-input,
    .form-group#momo-mobile-input.show-input,
    body .form-group#momo-mobile-input.show-input,
    body div#momo-mobile-input.show-input {
        display: flex !important; /* Show when class is added */
        flex-direction: column !important;
    }

    /* Override any other CSS rules */
    @media all {
        #momo-mobile-input:not(.show-input) {
            display: none !important;
        }
    }

    /* Enhanced Select2 styling */
    .select2-container {
        z-index: 9999;
    }

    /* Improved dropdown styling */
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 42px;
        padding-left: 15px;
        color: #333;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }

    .select2-dropdown {
        border: 1px solid #ddd;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        background-color: #fff;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #FA9313;
    }

    .select2-results__option {
        padding: 8px 15px;
        font-size: 14px;
    }

    .select2-search--dropdown .select2-search__field {
        padding: 8px;
        border: 1px solid #ddd;
    }

    /* Country code dropdown specific styling */
    .country-code-dropdown {
        width: 100% !important;
        background-color: #fff !important;
    }

    .select2-container--open .select2-dropdown {
        margin-top: 2px;
    }

    /* Additional Select2 styling for improved appearance */
    .select2-dropdown-improved {
        border-color: #ddd !important;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.15) !important;
        border-radius: 5px !important;
        overflow: visible !important;
        margin-top: 3px !important;
        background-color: #fff !important;
    }

    /* Improved country code dropdown styling */
    .country-code-dropdown {
        min-width: 250px !important;
        width: auto !important;
        background-color: #fff !important;
        z-index: 99999 !important;
    }

    .country-code-dropdown .select2-results__option {
        padding: 8px 12px;
    }

    .select2-country-option {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .select2-country-code {
        font-weight: 500;
        font-size: 14px;
        min-width: 45px;
        color: #333;
    }

    .select2-country-name {
        font-size: 13px;
        color: #555;
        margin-left: 10px;
    }

    .country-code-dropdown .select2-results__option--highlighted[aria-selected] {
        background-color: #FA9313 !important;
    }

    .country-code-dropdown .select2-results__option--highlighted[aria-selected] .select2-country-code,
    .country-code-dropdown .select2-results__option--highlighted[aria-selected] .select2-country-name {
        color: white !important;
    }

    /* Ensure the dropdown is wide enough to show country names */
    .select2-container--open .select2-dropdown.country-code-dropdown {
        min-width: 250px !important;
        width: auto !important;
    }

    .select2-container--open .select2-dropdown--below {
        border-top: 1px solid #ddd !important;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #FA9313 !important;
        color: white !important;
    }

    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #fff9f0 !important;
        color: #FA9313 !important;
    }

    .select2-results__option {
        transition: background-color 0.2s ease;
    }

    .country-code-dropdown .select2-results__option {
        display: flex;
        align-items: center;
        padding: 8px 15px;
    }

    /* Mobile responsiveness for Select2 */
    @media (max-width: 576px) {
        .select2-container--default .select2-selection--single {
            height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            font-size: 13px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-results__option {
            padding: 6px 12px;
            font-size: 13px;
        }

        .select2-dropdown {
            width: auto !important;
            min-width: 200px !important;
        }
    }

    .text-muted {
        font-size: 10px;
        margin: 2px 20px;
    }

    #momo_mobile {
        padding: 5px 12px;
        margin: 4px 19px;
        width: 300px;
        padding-left: 83px;
    }
    .checkout-deliver-address-row {
        width: 100%;
        background: #fff;
        margin: 30px 0 0 0;
        padding: 30px 20px 20px 20px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        display: block;
    }
    /* Optionally, adjust for mobile responsiveness */
    @media (max-width: 768px) {
        .checkout-deliver-address-row {
            padding: 15px 5px;
        }
    }

    /* Showroom Header Styles */
    .showroom-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .showroom-section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    /* Showroom List Grid Styles */
    #showroom-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-top: 15px;
        width: 100%;
        min-height: 100px; /* Ensure there's space for the loading indicator */
    }

    /* Loading indicator styles */
    #load-more-indicator {
        width: 100%;
        text-align: center;
        padding: 15px;
        color: #7059a8;
        font-size: 14px;
    }

    #load-more-indicator .fa-spinner {
        margin-right: 8px;
        animation: spin 1s infinite linear;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Showroom Item Styles */
    .showroom-item {
        border: 2px solid #eee;
        border-radius: 10px;
        background: white;
        transition: all 0.2s ease;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0,0,0,0.03);
        cursor: pointer;
    }

    .showroom-item:hover {
        border-color: #FA9313;
        box-shadow: 0 4px 12px rgba(250,147,19,0.1);
        transform: translateY(-2px);
    }

    .showroom-item.selected {
        border-color: #FA9313;
        box-shadow: 0 4px 12px rgba(250,147,19,0.1);
        transform: translateY(-2px);
    }

    .showroom-item.selected .showroom-item-header {
        background-color: #fffbf4;
        border-bottom-color: #ffecd9;
    }

    .showroom-item.selected .showroom-details {
        background-color: #fffdf9;
    }

    .showroom-item-content {
        padding: 0;
    }

    .showroom-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 12px 12px 12px;
        border-bottom: 1px solid #f5f5f5;
        background-color: #fafafa;
    }

    .showroom-radio-container {
        display: flex;
        align-items: center;
    }

    .showroom-radio-container input[type="radio"] {
        margin-right: 10px;
        accent-color: #FA9313;
        cursor: pointer;
        width: 18px;
        height: 18px;
        flex-shrink: 0;
        position: relative;
        top: 1px;
        z-index: 1;
    }

    .showroom-radio-container .pop-up-name {
        font-weight: 600;
        font-size: 14px;
        color: #333;
        margin-right: 8px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    .pop-up-showroom-type {
        padding: 3px 8px;
        border-radius: 12px;
        background: #fff0e1;
        color: #fa9313;
        font-size: 11px;
        font-weight: 600;
        margin-left: 8px;
        display: inline-block;
    }

    .showroom-actions {
        display: flex;
        gap: 8px;
    }

    .showroom-actions a {
        background: transparent;
        border: 1px solid #eee;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 4px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        font-size: 12px;
        text-decoration: none;
        color: #007bff;
        position: relative;
        z-index: 2;
    }

    .showroom-actions a i {
        margin-right: 5px;
    }

    .view-map-btn:hover {
        background-color: rgba(0, 123, 255, 0.1);
        border-color: #007bff;
    }

    .showroom-details {
        padding: 15px 15px 15px 15px;
        color: #555;
        font-size: 13px;
        line-height: 1.5;
    }

    .showroom-line {
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
    }

    .showroom-icon {
        color: #FA9313;
        width: 16px;
        margin-right: 10px;
        text-align: center;
        font-size: 14px;
        position: relative;
        top: 2px;
    }

    .showroom-phone {
        display: flex;
        align-items: center;
        margin-top: 8px;
    }

    .showroom-phone .showroom-icon {
        font-size: 14px;
    }

    .showroom-phone-link {
        color: #555;
        text-decoration: none;
    }

    .showroom-phone-link:hover {
        color: #FA9313;
        text-decoration: underline;
    }
    /* Mobile styles moved to checkout-responsive-fix.css */

    /* Mobile styles moved to checkout-responsive-fix.css */

    /* Main container styles moved to checkout-responsive-fix.css */

    /* Add smooth scrollbar for better UX */
    .checkout-order-summary::-webkit-scrollbar {
        width: 6px;
    }

    .checkout-order-summary::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .checkout-order-summary::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }

    .checkout-order-summary::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Ensure sticky works on mobile too */
    /* Mobile styles moved to checkout-responsive-fix.css */



    /* Search Container Styles */
    .showroom-search-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 20px;
        position: relative;
    }

    #showroom-search {
        padding: 10px 15px 10px 40px;
        border-radius: 25px;
        border: 2px solid #fa9313;
        width: 100%;
        max-width: 355px;
        font-size: 13px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    #showroom-search:focus {
        outline: none;
        box-shadow: 0 2px 10px rgba(250,147,19,0.2);
    }

    #sch-icn {
        position: absolute;
        left: 10px;
        transform: translateY(-50%) scaleX(1);
        color: #fa9313;
        font-size: 16px;
        pointer-events: none;
    }

    .showroom-count {
        color: #666;
        font-size: 0.9em;
        background: #f8f8f8;
        padding: 6px 12px;
        border-radius: 20px;
        white-space: nowrap;
    }

    @media (max-width: 600px) {
        .showroom-search-container {
            flex-direction: column;
            align-items: flex-start;
        }

        #showroom-search {
            max-width: 100%;
        }
    }

    /* Redesigned Delivery Options */

    .pick-up-locatn-checkbox {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin: 25px 20px auto;
        max-width: 600px;
        padding: 0;
        position: relative;
        z-index: 1;
    }

    .delivery-option {
        flex: 1;
        position: relative;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: visible;
        background: white;
        max-width: 280px;
        z-index: 1;
    }

    .delivery-option input[type="radio"] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }



    .delivery-option input[type="radio"]:checked + .delivery-option-content {
        border-color: #FA9313;
        background-color: #fffbf4;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(250, 147, 19, 0.2);
        z-index: 2;
    }

    .delivery-option-icon {
        font-size: 28px;
        color: #008080;
        transition: all 0.3s ease;
        min-width: 40px;
        text-align: center;
    }
.I-CTA-button{
        margin: 0px -55px !important;
}


.pay-button .spinner {
    display: none;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

.pay-button.loading .spinner {
    display: inline-block;
}

.pay-button:disabled {
    background-color: #ccc !important;
    cursor: not-allowed;
}


#add-address-btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
    animation: pulse-address-btn 2s infinite;
}

#add-address-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(250,147,19,0.4);
}

#add-address-btn:active {
    transform: scale(0.95);
}

#add-address-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #FA9313, #ffb347);
    z-index: -1;
    border-radius: 50%;
    transition: all 0.3s ease;
}

#add-address-btn:hover::before {
    transform: rotate(180deg);
}

@keyframes pulse-address-btn {
    0% {
        box-shadow: 0 0 0 0 rgba(250,147,19,0.5);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(250,147,19,0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(250,147,19,0);
    }
}



#add-address-btn::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    pointer-events: none;
}

#add-address-btn:hover::after {
    opacity: 1;
    visibility: visible;
}


.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.address-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

#add-address-btn {
    background-color: #FA9313;
    border-color: #FA9313;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(250,147,19,0.3);
    transition: all 0.3s ease;
}

#add-address-btn i {
    font-size: 16px;
    color: white;
}

#address-list-container {
    display: grid;
    gap: 20px;
    margin-top: 15px;
    width: 100%;
}

.address-item {
    border: 2px solid #eee;
    border-radius: 10px;
    background: white;
    transition: all 0.2s ease;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.03);
    cursor: pointer;
    margin-bottom: 15px;
}

.address-item:hover {
    border-color: #FA9313;
    box-shadow: 0 4px 12px rgba(250,147,19,0.1);
    transform: translateY(-2px);
}

.address-item.selected {
    border-color: #FA9313;
    box-shadow: 0 4px 12px rgba(250,147,19,0.1);
    transform: translateY(-2px);
}

.address-item.selected .address-item-header {
    background-color: #fffbf4;
    border-bottom-color: #ffecd9;
}

.address-item.selected .address-details {
    background-color: #fffdf9;
}

.address-item-content {
    padding: 0;
    width: inherit;
}

.address-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 12px 12px 12px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #fafafa;
}

.address-radio-container {
    display: flex;
    align-items: center;
}

.address-radio-container input[type="radio"] {
    margin-right: 10px;
    accent-color: #FA9313;
    cursor: pointer;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    position: relative;
    top: 1px;
    z-index: 1;
}

.address-radio-container .pop-up-name {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-right: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.address-name {
    display: inline-block;
    margin-right: 5px;
}

.pop-up-work-home {
    padding: 3px 8px;
    border-radius: 12px;
    background: #eef4ff;
    color: #3538cd;
    font-size: 11px;
    font-weight: 600;
    margin-left: 0;
    display: inline-block;
}

.address-actions {
    display: flex;
    gap: 8px;
}
.address-item {
    padding: 0 !important;
}

.address-actions button {
    background: transparent;
    border: 1px solid #eee;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 12px;
    position: relative;
    z-index: 2;
}



.action-text {
    display: inline-block;
}

.edit-address-btn {
    color: #007bff;
}

.edit-address-btn:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-color: #007bff;
}

.delete-address-btn {
    color: #dc3545;
}

.delete-address-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
}

.address-details {
    padding: 15px 15px 15px 15px;
    color: #555;
    font-size: 13px;
    line-height: 1.5;
}

.address-line {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
}

.address-icon {
    color: #FA9313;
    width: 16px;
    margin-right: 10px;
    text-align: center;
    font-size: 14px;
    position: relative;
    top: 2px;
}

.address-phone {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.address-phone .address-icon {
    font-size: 14px;
}

.no-address-message {
    text-align: center;
    padding: 30px 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #ddd;
    color: #666;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.no-address-message p {
    margin: 5px 0;
    font-size: 14px;
}

/* Responsive styles for address list */
@media (max-width: 768px) {
    .address-header {
        flex-direction: row;
        align-items: center;
    }

    .address-section-title {
        font-size: 16px;
    }

    #add-address-btn {
        width: 36px;
        height: 36px;
    }

    #add-address-btn i {
        font-size: 14px;
    }

    #address-list-container {
        grid-template-columns: 1fr;
    }

    .address-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .address-actions {
        margin-top: 10px;
        align-self: flex-end;
        width: 100%;
        justify-content: flex-end;
    }

    .address-details {
        padding-left: 15px;
    }

    .address-icon {
        width: 20px;
        margin-right: 8px;
    }
}

@media (max-width: 480px) {
    .address-header {
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .address-section-title {
        font-size: 15px;
    }

    .address-item {
        border-width: 1px;
    }

    .address-radio-container .pop-up-name {
        font-size: 13px;
    }

    .pop-up-work-home {
        font-size: 11px;
        padding: 2px 6px;
    }

    .address-actions {
        flex-direction: row;
        gap: 5px;
    }

    .address-actions button {
        padding: 4px 8px;
        font-size: 11px;
    }

    .action-text {
        display: none;
    }

    .address-actions button i {
        margin-right: 0;
        font-size: 14px;
    }
}
    .delivery-option input[type="radio"]:checked + .delivery-option-content .delivery-option-icon {
        color: #FA9313;
        transform: scale(1.1);
    }

    .delivery-option-title {
        font-weight: 700;
        font-size: 14px;
        color: #333;
        transition: all 0.3s ease;
    }

    /* Redesigned delivery option styles */
    .delivery-option-content {
        position: relative;
        display: flex;
        align-items: center;
        padding: 20px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        overflow: visible;
        text-align: left;
        margin-top: 0;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    }

    .delivery-option-info {
        flex: 1;
        text-align: center;
    }

    .delivery-option-description {
        font-size: 14px;
        color: #666;
        line-height: 1.4;
        margin-top: 5px;
    }

    /* Animation for delivery options */
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    .pulse-animation {
        animation: pulse 0.5s ease-in-out;
    }

    /* Responsive styles for delivery options */
    @media (max-width: 768px) {
        .pick-up-locatn-checkbox {
            flex-direction: column;
            align-items: center;
            gap: 15px;
            margin: 15px auto;
        }

        .delivery-option {
            width: 100%;
            max-width: 300px;
        }

        .delivery-option-content {
            padding: 15px;
        }

        .delivery-option-icon {
            font-size: 24px;
            min-width: 30px;
        }

        .delivery-option-info {
            margin-left: 10px;
        }
    }

    @media (max-width: 480px) {
        .delivery-option-title {
            font-size: 16px;
        }

        .delivery-option-description {
            font-size: 13px;
        }
    }

    /* Wallet Popup Styles */
    #wallet-popup {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border: 1px solid orange;
        z-index: 1001;
        border-radius: 15px;
        max-width: 90%;
        width: auto !important;
    }

    #wallet-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    .wallet-input {
        width: 100%;
        max-width: 200px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .wallet-apply-btn {
        background-color: #FA9313;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 600;
        transition: background-color 0.3s ease;
    }

    .wallet-apply-btn:hover {
        background-color: #e8840f;
    }

    .wallet-apply-btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }

    .walletPointsCheck {
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .walletPointsCheck:hover {
        color: #FA9313 !important;
    }

    /* Mobile responsive styles for wallet popup */
    @media (max-width: 768px) {
        #wallet-popup {
            max-width: 95%;
            padding: 15px;
            margin: 10px;
        }

        .wallet-input {
            max-width: 100%;
            font-size: 16px; /* Prevent zoom on iOS */
        }

        .wallet-apply-btn {
            width: 100%;
            padding: 12px;
            font-size: 16px;
        }
    }

    @media (max-width: 480px) {
        #wallet-popup {
            max-width: 98%;
            padding: 10px;
        }

        .loyalty-contents {
            padding: 10px 20px;
        }

        .radeem-btn-c {
            padding: 10px;
        }
    }
</style>

<?php $this->end(); ?>

<div class="productCategoryListingC">



    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span class="productCategoryListing-home-span"><?= $this->Html->link('Home', ['controller' => 'Website', 'action' => 'home', 'prefix' => false]) ?></span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span">Checkout</span>
    </div>
</div>
<div style="text-align:center;">
            <?= $this->Flash->render() ?>
</div>
<div class="productCategoryListingC back-transparent">
    <div class="productCategoryListing">
        <div class="checkout-payment-title">CART <span>(<?= $total_items ?>)</span></div>
    </div>
</div>


<div class="check-out-container">

    <div class="p-v-p-item-description-container">

        <div class="show-order-summary-container">
            <div class="show-order-summary">
                Show Order Summary
            </div>
            <?php foreach ($cartItems as $item): ?>
                <div class="my-order-item-details">
                    <img src="<?= $item['product_image'] ?>" class="my-order-item-icon">
                    <div class="my-order-items-details-tpc">
                        <div class="my-order-item-details-title-container">
                            <div class="my-order-item-details-title"><?= $item['product_name'] ?></div>
                        </div>
                        <div class="my-order-item-details-price"><?= $this->Price->setPriceFormat($item['price']) ?>
                        <input type="hidden" value="<?= $item['price'] ?>" class='cart_items_list_price'>
                        <input type="hidden" value="<?= $item['product_id'] ?>" class='cart_items_list_id'>
                        <input type="hidden" value="<?= $item['category_name'] ?>" class='cart_items_list_category_name'>
                        <input type="hidden" value="<?= $item['brand_name'] ?>" class='cart_items_list_brand_name'>
                        </div>
                        <div class="my-order-item-details-price"><span class="strikethrough-text" style="margin-left:0;"><?= $this->Price->setPriceFormat($item['sale']) ?></span></div>
                        <div>
                            <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $item['discount'] ?></span>% 0ff</span>
                        </div>
                        <div class="my-order-item-details-price"><?= __("Reference: "). $item['reference_name'] ?> </div>
                        <div class="units-left"><?= $item['get_available_status'] ?></div>

                    </div>
                    <div class="add-to-cart-ctnr">
                        <div class="counter">
                            <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                    data-item-type="decrease">-
                            </button>
                            <div class="counter-value cartItemQty" id="value"
                                 value="<?= $item['quantity'] ?>"><?= $item['quantity'] ?>
                            </div>
                            <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                    data-item-type="increase">+
                            </button>
                        </div>
                        <div class="p-v-p-item-description-add-to-wishlist-share">

                             <?php if ($item['whishlist']): ?>
                                <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                    data-product-id="<?= $item['product_id'] ?>"><span
                                        class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                            src="/assets/heart-background.png" class="wishlist"> </span>
                                </div>
                            <?php else: ?>
                                    <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                        data-product-id="<?= $item['product_id'] ?>"><span
                                            class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                                src="/assets/heart-nobackgrounddark.png" class="wishlist"> </span>
                                    </div>
                            <?php endif; ?>
                        </div>
                        <button class="close-btn-btn closeCartItem" data-item-id="<?= $item['cart_item_id'] ?>">✖
                        </button>
                    </div>

                </div>
            <?php endforeach; ?>

            <div class="show-order-summary">
                    <?= __('Payment Method') ?>
            </div>
            <div class="my-order-item-details">
                <div class="card-details">
                   
                    <div class="pay-with-card-i-l">
                        <?php foreach ($methods as $k => $val): ?>
                            <?php if($val['name'] == 'Credit' && $payByCreditActive == true):  ?>
                                <div>
                                    <input type="radio" id="method_<?= $val['name'] ?>"
                                        name="method" <?php if ($k == 0): echo 'checked'; elseif($payByCreditActive == true): echo "checked"; endif; ?>
                                        value="<?= $val['name'] ?>" class="Pay-With-Credit-Card-input">
                                    <label for="method_<?= $val['name'] ?>"><?= $val['name'] ?></label>
                                </div>
                                <?php elseif($val['name'] != 'Credit'): ?>
                                <div>
                                    <input type="radio" id="method_<?= $val['name'] ?>"
                                        name="method" <?php if ($k == 0): echo 'checked'; elseif($payByCreditActive == true): echo "checked"; endif; ?>
                                        value="<?= $val['name'] ?>" class="Pay-With-Credit-Card-input">
                                    <label for="method_<?= $val['name'] ?>">
                                        <?= $val['name'] ?>
                                        <?php if ($val['name'] === 'MTN MoMo'): ?>

                                        <?php endif; ?>
                                    </label>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>

                    <!-- Mobile Number Input for MTN MoMo - Only visible when MTN MoMo is selected -->
                    <div id="momo-mobile-input" class="form-group mt-3" style="display: none !important;">
                        <label for="momo_mobile" class="Billing-Address">Mobile Number (required for MTN MoMo)</label>
                        <small class="text-muted">Format: Country code + Mobile number (e.g., +237XXXXXXXXX)</small>
                        <div id="mobile-container" class="toggle-input">
                            <div class="ax-field-wrapper">
                                <div class="ax-input-box mobile-input-wrapper">
                                    <select id="ax-country-select" name="country_code" class="ax-country-select2 searchable-select">
                                        <option value="1">+1 <small>(USA, Canada)</small></option>
                                                                   <option value="7">+7 <small>(Russia, Kazakhstan)</small></option>
                                                                   <option value="20">+20 <small>(Egypt)</small></option>
                                                                   <option value="27">+27 <small>(South Africa)</small></option>
                                                                   <option value="30">+30 <small>(Greece)</small></option>
                                                                   <option value="31">+31 <small>(Netherlands)</small></option>
                                                                   <option value="32">+32 <small>(Belgium)</small></option>
                                                                   <option value="33">+33 <small>(France)</small></option>
                                                                   <option value="34">+34 <small>(Spain)</small></option>
                                                                   <option value="36">+36 <small>(Hungary)</small></option>
                                                                   <option value="39">+39 <small>(Italy)</small></option>
                                                                   <option value="40">+40 <small>(Romania)</small></option>
                                                                   <option value="41">+41 <small>(Switzerland)</small></option>
                                                                   <option value="43">+43 <small>(Austria)</small></option>
                                                                   <option value="44">+44 <small>(United Kingdom)</small></option>
                                                                   <option value="45">+45 <small>(Denmark)</small></option>
                                                                   <option value="46">+46 <small>(Sweden)</small></option>
                                                                   <option value="47">+47 <small>(Norway)</small></option>
                                                                   <option value="48">+48 <small>(Poland)</small></option>
                                                                   <option value="49">+49 <small>(Germany)</small></option>
                                                                   <option value="51">+51 <small>(Peru)</small></option>
                                                                   <option value="52">+52 <small>(Mexico)</small></option>
                                                                   <option value="53">+53 <small>(Cuba)</small></option>
                                                                   <option value="54">+54 <small>(Argentina)</small></option>
                                                                   <option value="55">+55 <small>(Brazil)</small></option>
                                                                   <option value="56">+56 <small>(Chile)</small></option>
                                                                   <option value="57">+57 <small>(Colombia)</small></option>
                                                                   <option value="58">+58 <small>(Venezuela)</small></option>
                                                                   <option value="60">+60 <small>(Malaysia)</small></option>
                                                                   <option value="61">+61 <small>(Australia)</small></option>
                                                                   <option value="62">+62 <small>(Indonesia)</small></option>
                                                                   <option value="63">+63 <small>(Philippines)</small></option>
                                                                   <option value="64">+64 <small>(New Zealand)</small></option>
                                                                   <option value="65">+65 <small>(Singapore)</small></option>
                                                                   <option value="66">+66 <small>(Thailand)</small></option>
                                                                   <option value="81">+81 <small>(Japan)</small></option>
                                                                   <option value="82">+82 <small>(South Korea)</small></option>
                                                                   <option value="84">+84 <small>(Vietnam)</small></option>
                                                                   <option value="86">+86 <small>(China)</small></option>
                                                                   <option value="90">+90 <small>(Turkey)</small></option>
                                                                   <option value="91">+91 <small>(India)</small></option>
                                                                   <option value="92">+92 <small>(Pakistan)</small></option>
                                                                   <option value="93">+93 <small>(Afghanistan)</small></option>
                                                                   <option value="94">+94 <small>(Sri Lanka)</small></option>
                                                                   <option value="95">+95 <small>(Myanmar)</small></option>
                                                                   <option value="98">+98 <small>(Iran)</small></option>
                                                                   <option value="211">+211 <small>(South Sudan)</small></option>
                                                                   <option value="212">+212 <small>(Morocco)</small></option>
                                                                   <option value="213">+213 <small>(Algeria)</small></option>
                                                                   <option value="216">+216 <small>(Tunisia)</small></option>
                                                                   <option value="218">+218 <small>(Libya)</small></option>
                                                                   <option value="220">+220 <small>(Gambia)</small></option>
                                                                   <option value="221">+221 <small>(Senegal)</small></option>
                                                                   <option value="222">+222 <small>(Mauritania)</small></option>
                                                                   <option value="223">+223 <small>(Mali)</small></option>
                                                                   <option value="224">+224 <small>(Guinea)</small></option>
                                                                   <option value="225">+225 <small>(Ivory Coast)</small></option>
                                                                   <option value="226">+226 <small>(Burkina Faso)</small></option>
                                                                   <option value="227">+227 <small>(Niger)</small></option>
                                                                   <option value="228">+228 <small>(Togo)</small></option>
                                                                   <option value="229">+229 <small>(Benin)</small></option>
                                                                   <option value="230">+230 <small>(Mauritius)</small></option>
                                                                   <option value="231">+231 <small>(Liberia)</small></option>
                                                                   <option value="232">+232 <small>(Sierra Leone)</small></option>
                                                                   <option value="233">+233 <small>(Ghana)</small></option>
                                                                   <option value="234">+234 <small>(Nigeria)</small></option>
                                                                   <option value="235">+235 <small>(Chad)</small></option>
                                                                   <option value="236">+236 <small>(Central African Republic)</small></option>
                                                                   <option value="237">+237 <small>(Cameroon)</small></option>
                                                                   <option value="238">+238 <small>(Cape Verde)</small></option>
                                                                   <option value="239">+239 <small>(Sao Tome and Principe)</small></option>
                                                                   <option value="240">+240 <small>(Equatorial Guinea)</small></option>
                                                                   <option value="241">+241 <small>(Gabon)</small></option>
                                                                   <option value="242">+242 <small>(Congo - Brazzaville)</small></option>
                                                                   <option value="243">+243 <small>(Congo - Kinshasa)</small></option>
                                                                   <option value="244">+244 <small>(Angola)</small></option>
                                                                   <option value="245">+245 <small>(Guinea-Bissau)</small></option>
                                                                   <option value="246">+246 <small>(British Indian Ocean Territory)</small></option>
                                                                   <option value="248">+248 <small>(Seychelles)</small></option>
                                                                   <option value="249">+249 <small>(Sudan)</small></option>
                                                                   <option value="250">+250 <small>(Rwanda)</small></option>
                                                                   <option value="251">+251 <small>(Ethiopia)</small></option>
                                                                   <option value="252">+252 <small>(Somalia)</small></option>
                                                                   <option value="253">+253 <small>(Djibouti)</small></option>
                                                                   <option value="254">+254 <small>(Kenya)</small></option>
                                                                   <option value="256">+256 <small>(Uganda)</small></option>
                                                                   <option value="257">+257 <small>(Burundi)</small></option>
                                                                   <option value="258">+258 <small>(Mozambique)</small></option>
                                                                   <option value="260">+260 <small>(Zambia)</small></option>
                                                                   <option value="261">+261 <small>(Madagascar)</small></option>
                                                                   <option value="262">+262 <small>(Réunion, Mayotte)</small></option>
                                                                   <option value="263">+263 <small>(Zimbabwe)</small></option>
                                                                   <option value="264">+264 <small>(Namibia)</small></option>
                                                                   <option value="265">+265 <small>(Malawi)</small></option>
                                                                   <option value="266">+266 <small>(Lesotho)</small></option>
                                                                   <option value="267">+267 <small>(Botswana)</small></option>
                                                                   <option value="268">+268 <small>(Eswatini)</small></option>
                                                                   <option value="269">+269 <small>(Comoros)</small></option>
                                                                   <option value="290">+290 <small>(Saint Helena)</small></option>
                                                                   <option value="291">+291 <small>(Eritrea)</small></option>
                                                                   <option value="297">+297 <small>(Aruba)</small></option>
                                                                   <option value="298">+298 <small>(Faroe Islands)</small></option>
                                                                   <option value="299">+299 <small>(Greenland)</small></option>
                                    </select>
                                    <input type="tel" name="momo_mobile" id="ax-mobile-input" class="ax-phone-input" placeholder="Enter mobile number" value="<?= isset($mobile) ? $mobile : '' ?>" required>
                                    <span id="ax-width-measure" class="ax-width-measure"></span>
                                </div>
                            </div>
                            <label id="ax-mobile-input-error" class="error" for="ax-mobile-input" style="display: none;"></label>
                        </div>
                    </div>

                </div>
            </div>





        </div>

    </div>

    <div class="checkout-order-summary">
  <div class="checkout-deliver-address-row">
      <div class="Order-s-nameandaddress">
        <div class="checkout-order-summary-title">
        PRICE DETAILS
        </div>

        <div class="checkout-order-summary-price">
            <div>Price <span>(<?= $total_items ?> Items)</span> : </div>
            <div><?= $this->Price->setPriceFormat($totalPrice) ?></div>
        </div>
        <hr class="checkout-order-summary-hr">

        <div class="for-padding">

            <div class="checkout-order-discount-price-label">
                <div>Discount</div>
                <div style="text-decoration: line-through;"><?= $this->Price->setPriceFormat($totalDiscountedPrice) ?></div>
            </div>

            <div class="checkout-order-discount-price-label">
                <div>
                    <?= __('Coupon') ?> <span class="font-size"><?= __('Code') ?></span>
                    <span class="coupon-status">
                        <span class="coupon-applied" style="display: none;">
                            <span class="font-size" style="font-size: 10px;">(<span class="appliedCoupon"></span>)</span>
                            <span class="font-size" style="font-size: 10px; color: red; cursor: pointer;">
                                (<span class="removeCoupon"><?= __('Remove') ?></span>)
                            </span>
                        </span>
                        <span class="no-coupon" style="font-size: 10px;"><?= __('No coupon applied') ?></span>
                    </span>
                </div>
                <div class="offerApplied">0</div>
            </div>

            <div class="checkout-order-discount-price-label">
                <div id="loyaltyPoints" class="loyaltyPointsCheck"
                     style="color: teal; text-decoration: underline; cursor: pointer;font-weight:600"><?= __('Redeem Loyalty Points') ?>
                </div>
                <div id="loyalty-points"><span class="lyt-reedem-point">0</span> FCFA</div>
                <input type="hidden" name="lytPoints" class="lytPoints">
            </div>
            <!-- <div class="checkout-order-discount-price-label">
                <div>
                    <label>
                        <input name="wallet" type="checkbox" id="wallet-checkbox" <?= ($wallet > 0) ? '' : 'disabled' ?>>
                        <?= __('Wallet') ?>
                    </label>
                </div>
                <div id="wallet-remaining"><?= $this->Price->setPriceFormat($wallet) ?></div>
            </div> -->
            <div class="checkout-order-discount-price-label">
                <div>
                    <span id="walletPoints" class="walletPointsCheck"
                         style="color: teal; text-decoration: underline; cursor: pointer;font-weight:600"><?= __('Wallet') ?>
                    </span>
                    <span class="wallet-status">
                        <span class="wallet-applied" style="display: none;">
                            <span class="font-size" style="font-size: 10px; color: red; cursor: pointer;">
                                (<span class="removeWallet"><?= __('Remove') ?></span>)
                            </span>
                        </span>
                        <span class="no-wallet" style="font-size: 10px;"><?= __('No wallet amount applied') ?></span>
                    </span>
                </div>
                <div id="wallet-points"><span class="wallet-used-amount">0</span> FCFA</div>
                <input type="hidden" name="walletAmount" class="walletAmount">
            </div>
            <!-- Pop-up Modal -->
            <div id="popup" style="display: none;">
                <span onclick="closePopup()" class="close-popup"><span class="icon">×</span></span>
                <div class="loyalty-points-popup">


                    <div class="loyalty-contents">
                        <p class="emoji">🏅</p>
                        <p class="loyalty-points">Your Loyalty Points</p>
                        <p class="date">Valid untill <span class="setDateAjax"></span></p>
                        <div class="background-orange">
                            <p>Total Loyalty Points</p>
                            <p class="loyalty-price">0</p>

                        </div>
                        <div class="background-orange">
                            <p>Points Worth</p>
                            <p class="loyalty-price">0</p>
                        </div>
                    </div>


                    <div class="radeem-btn-c">
                        <input type="text" placeholder="Enter points to redeem" class="radeem-input">
                        <p class="color-blue">Points worth : <span> 0 </span></p>
                        <button class="radeem-btn">Redeem Points</button>
                    </div>

                </div>
            </div>
            <div id="overlay" style="display: none;"></div>

            <!-- Wallet Pop-up Modal -->
            <div id="wallet-popup" style="display: none;">
                <span onclick="closeWalletPopup()" class="close-popup"><span class="icon">×</span></span>
                <div class="loyalty-points-popup">

                    <div class="loyalty-contents">
                        <p class="emoji">💰</p>
                        <p class="loyalty-points">Your Wallet</p>
                        <p class="date">Available Balance</p>
                        <div class="background-orange">
                            <p>Total Wallet Amount</p>
                            <p class="loyalty-price"><?= $this->Price->setPriceFormat($wallet) ?></p>
                        </div>
                        <div class="background-orange">
                            <p>Amount to Use</p>
                            <p class="loyalty-price"><span class="wallet-amount-to-use">0</span> FCFA</p>
                        </div>
                    </div>

                    <div class="radeem-btn-c">
                        <input type="number" placeholder="Enter amount to use" class="wallet-input" min="0" max="<?= $wallet ?>" step="0.01">
                        <p class="color-blue">Available Balance: <span><?= $this->Price->setPriceFormat($wallet) ?></span></p>
                        <p class="color-blue wallet-max-info" style="font-size: 12px; color: #666;">Maximum usable: <span class="max-usable-amount"><?= $this->Price->setPriceFormat($wallet) ?></span></p>
                        <button class="wallet-apply-btn">Apply Wallet Amount</button>
                    </div>

                </div>
            </div>
            <div id="wallet-overlay" style="display: none;"></div>





            <div class="checkout-order-discount-price-label">
                <div>Shipping Charges :</div>
                <div  style="" class="delivery-state">-</div>
            </div>


            <div class="checkout-order-discount-price-label">
                <div>Delivery Mode</div>
                <div>
                    <!-- <select id="delivery_mode_type" name="delivery_mode_type">
                        <option value="standard">Standard</option>
                        <option value="express">Express</option>
                    </select> -->

                    <input type='radio' id="standard" class="delivery_mode_type delivery_mode_type2" name="delivery_mode_type" value="standard" checked> <label for="standard">Standard</label>
                    <input type='radio' id="express" class="delivery_mode_type delivery_mode_type2" name="delivery_mode_type" value="express"> <label for="express">Express</label>

                </div>
            </div>

        </div>

        <hr class="checkout-order-summary-hr">

        <div class="checkout-order-summary-price">
            <div>Total</div>
             <div><span class="totalAmount"></span> FCFA</div>
        </div>

        <div class="I-CTA-Btn">
            <input placeholder="Enter Code" maxlength="12" class="I-CTA-input couponName">
            <button class="I-CTA-button checkCouponCode">Apply Coupon</button>
        </div>


        <hr class="checkout-order-summary-hr">
        <button id="payment-confirm" class="pay-button">
            <span class="spinner"></span>Pay <?= $this->Price->setPriceFormat($totalPrice) ?>
        </button>

     <hr class="checkout-order-summary-hr">

                <div class="checkout-order-summary-title"><i class="fa fa-truck"></i> &nbsp; <?= __("Deliver To Address") ?></div>

</div></div>
        <!-- NEW FULL WIDTH ROW FOR DELIVER TO ADDRESS -->
        <div class="checkout-deliver-address-row">


            <div class="delivery-options">
                <div class="pick-up-locatn-checkbox">
                    <div class="delivery-option">
                        <input type="radio" name="delivery_option" checked value="pickup" tooltip="Pick up your order from one of our showrooms" id="pickup-from-showroom">
                        <label class="delivery-option-content" for="pickup-from-showroom">
                            <i class="fas fa-store delivery-option-icon"></i>
                            <div class="delivery-option-info">
                                <div class="delivery-option-title"><?= __('Showroom') ?></div>
                            </div>
                        </label>
                    </div>
                    <div class="delivery-option">
                        <input type="radio" name="delivery_option" value="delivery" tooltip="Get your order delivered to your address" id="deliver-to-address">
                        <label class="delivery-option-content" for="deliver-to-address">
                            <i class="fas fa-home delivery-option-icon"></i>
                            <div class="delivery-option-info">
                                <div class="delivery-option-title"><?= __('Home') ?></div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>

            <div class="Order-s-nameandaddress" >
                <div class="showroom-header">
                    <h3 class="showroom-section-title">Available Showrooms</h3>
                </div>

                <div class="showroom-search-container">
                    <i class="fa fa-search" id="sch-icn"></i>
                    <input type="text" id="showroom-search" maxlength="25" placeholder="Search by location or showroom name">
                </div>

                <div id="loading-message" style="display:none;">
                    <i class="fa fa-spinner fa-spin"></i> Loading showrooms...
                </div>
                <div class="showroom-list-container" id="pickup-div">
                    <div id="showroom-list">
                        <!-- Dynamic Showroom List will appear here -->
                    </div>
                </div>
            </div>

            <div class="Order-s-nameandaddress" id="delivery-div" style="display: none;">
                <?php if ($customerId): ?>
                    <div class="address-header">
                        <h3 class="address-section-title">My Addresses</h3>
                        <span href="" id="add-address-btn" class="btn btn-sm btn-primary" title="Add New Address">
                            <i class="fa fa-plus"></i>
                        </span>
                    </div>

                    <div id="address-list-container">
                        <?php if ($customerAddress): ?>
                            <?php foreach ($customerAddress as $val): ?>
                                <div class="address-item" data-address-id="<?= $val['id'] ?>">
                                    <div class="address-item-content">
                                        <div class="address-item-header">
                                            <div class="address-radio-container">
                                                <input type="radio" name="address" value="<?= $val['id'] ?>"
                                                    data-city-id="<?= $val['city_id'] ?>" id="address-<?= $val['id'] ?>">
                                                <label class="pop-up-name" for="address-<?= $val['id'] ?>">
                                                    <span class="address-name"><?= $val['name'] ?></span>
                                                    <span class="pop-up-work-home"><?= $val['type'] ?></span>
                                                </label>
                                            </div>
                                            <div class="address-actions">
                                                <button type="button" class="edit-address-btn" data-address-id="<?= $val['id'] ?>" title="Edit Address">
                                                    <i class="fa fa-edit"></i>
                                                </button>
                                                <button type="button" class="delete-address-btn" data-address-id="<?= $val['id'] ?>" title="Delete Address">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="address-details">
                                            <div class="address-line">
                                                <i class="fa fa-home address-icon"></i>
                                                <?= $val['type'] ?> <?= $val['house_no'] ?> <?= $val['address_line1'] ?>
                                            </div>

                                            <div class="address-line">
                                                <i class="fa fa-city address-icon"></i>
                                                <?= $val['city']['city_name'] ?>
                                            </div>
                                            <div class="address-phone">
                                                <i class="fa fa-phone address-icon"></i>
                                                <span><?= $val['phone_no1'] ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-address-message">
                                <i class="fa fa-map-marker-alt" style="font-size: 24px; color: #FA9313; margin-bottom: 10px;"></i>
                                <p>You don't have any saved addresses.</p>
                                <p>Please add a new address using the + button above.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="Order-s-nameandaddress-internal-ctn">
                        <?= __('Login required!') ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Address Modal Overlay -->
        <div id="address-modal-overlay" style="display: none; position: fixed !important; top: 0 !important; left: 0 !important; width: 100vw !important; height: 100vh !important; background: rgba(0, 0, 0, 0.5); z-index: 9999998 !important;"></div>

        <!-- Address Modal -->
        <div id="address-modal" class="ax-popup" style="display: none; max-width: 90%; width: 550px; z-index: ********** !important; position: fixed !important; top: 50% !important; left: 50% !important; transform: translate(-50%, -50%) !important; border-radius: 8px; box-shadow: 0 5px 30px rgba(0,0,0,0.3) !important;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                <h3 id="address-modal-title" style="font-size: 18px; font-weight: 600; color: #333; margin: 0;"><i class="fa fa-map-marker" style="margin-right: 8px; color: #FA9313;"></i>Add New Address</h3>
                <button id="close-address-modal" style="background: none; border: none; font-size: 22px; cursor: pointer; padding: 0; margin: 0; line-height: 1; color: #999; transition: color 0.2s ease;">&times;</button>
            </div>
            <form id="address-form" method="post" style="max-height: 70vh; overflow-y: auto; padding-right: 5px;">
                <input type="hidden" id="address_id" name="id" value="">
                <input type="hidden" name="customer_id" value="<?= $customerId ?>">

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="address_name" style="display: block; margin-bottom: 5px; font-weight: 500;">Full Name</label>
                        <input type="text" id="address_name" name="name" placeholder="Enter your full name" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>

                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="address_type" style="display: block; margin-bottom: 5px; font-weight: 500;">Address Type</label>
                        <select id="address_type" name="type" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="Home">Home</option>
                            <option value="Office">Office</option>
                        </select>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="house_no" style="display: block; margin-bottom: 5px; font-weight: 500;">House/Building Number</label>
                        <input type="text" id="house_no" name="house_no" placeholder="Enter house/building number" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>

                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="zipcode" style="display: block; margin-bottom: 5px; font-weight: 500;">Zipcode</label>
                        <input type="text" id="zipcode" name="zipcode" placeholder="Enter zipcode" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label for="address_line1" style="display: block; margin-bottom: 5px; font-weight: 500;">Address Line 1</label>
                    <input type="text" id="address_line1" name="address_line1" placeholder="Street address, apartment, etc." required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label for="address_line2" style="display: block; margin-bottom: 5px; font-weight: 500;">Address Line 2 (Optional)</label>
                    <input type="text" id="address_line2" name="address_line2" placeholder="Apartment, suite, unit, building, floor, etc." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="city_id" style="display: block; margin-bottom: 5px; font-weight: 500;">City</label>
                        <select id="city_id" name="city_id" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; ">
                            <option value="">Select City</option>
                            <?php foreach ($cities as $city): ?>
                                <option value="<?= $city->id ?>"><?= $city->city_name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="phone_no1" style="display: block; margin-bottom: 5px; font-weight: 500;">Phone Number</label>
                        <div class="ax-field-wrapper">
                            <div class="ax-input-box mobile-input-wrapper">
                                <select id="address-country-select" name="country_code1" class="ax-country-select searchable-select">
                                    <option value="1">+1 <small>(USA, Canada)</small></option>
                                    <option value="7">+7 <small>(Russia, Kazakhstan)</small></option>
                                    <option value="20">+20 <small>(Egypt)</small></option>
                                    <option value="27">+27 <small>(South Africa)</small></option>
                                    <option value="30">+30 <small>(Greece)</small></option>
                                    <option value="31">+31 <small>(Netherlands)</small></option>
                                    <option value="32">+32 <small>(Belgium)</small></option>
                                    <option value="33">+33 <small>(France)</small></option>
                                    <option value="34">+34 <small>(Spain)</small></option>
                                    <option value="36">+36 <small>(Hungary)</small></option>
                                    <option value="39">+39 <small>(Italy)</small></option>
                                    <option value="40">+40 <small>(Romania)</small></option>
                                    <option value="41">+41 <small>(Switzerland)</small></option>
                                    <option value="43">+43 <small>(Austria)</small></option>
                                    <option value="44">+44 <small>(UK)</small></option>
                                    <option value="45">+45 <small>(Denmark)</small></option>
                                    <option value="46">+46 <small>(Sweden)</small></option>
                                    <option value="47">+47 <small>(Norway)</small></option>
                                    <option value="48">+48 <small>(Poland)</small></option>
                                    <option value="49">+49 <small>(Germany)</small></option>
                                    <option value="51">+51 <small>(Peru)</small></option>
                                    <option value="52">+52 <small>(Mexico)</small></option>
                                    <option value="53">+53 <small>(Cuba)</small></option>
                                    <option value="54">+54 <small>(Argentina)</small></option>
                                    <option value="55">+55 <small>(Brazil)</small></option>
                                    <option value="56">+56 <small>(Chile)</small></option>
                                    <option value="57">+57 <small>(Colombia)</small></option>
                                    <option value="58">+58 <small>(Venezuela)</small></option>
                                    <option value="60">+60 <small>(Malaysia)</small></option>
                                    <option value="61">+61 <small>(Australia)</small></option>
                                    <option value="62">+62 <small>(Indonesia)</small></option>
                                    <option value="63">+63 <small>(Philippines)</small></option>
                                    <option value="64">+64 <small>(New Zealand)</small></option>
                                    <option value="65">+65 <small>(Singapore)</small></option>
                                    <option value="66">+66 <small>(Thailand)</small></option>
                                    <option value="81">+81 <small>(Japan)</small></option>
                                    <option value="82">+82 <small>(South Korea)</small></option>
                                    <option value="84">+84 <small>(Vietnam)</small></option>
                                    <option value="86">+86 <small>(China)</small></option>
                                    <option value="90">+90 <small>(Turkey)</small></option>
                                    <option value="91">+91 <small>(India)</small></option>
                                    <option value="92">+92 <small>(Pakistan)</small></option>
                                    <option value="93">+93 <small>(Afghanistan)</small></option>
                                    <option value="94">+94 <small>(Sri Lanka)</small></option>
                                    <option value="95">+95 <small>(Myanmar)</small></option>
                                    <option value="98">+98 <small>(Iran)</small></option>
                                    <option value="212">+212 <small>(Morocco)</small></option>
                                    <option value="213">+213 <small>(Algeria)</small></option>
                                    <option value="216">+216 <small>(Tunisia)</small></option>
                                    <option value="218">+218 <small>(Libya)</small></option>
                                    <option value="220">+220 <small>(Gambia)</small></option>
                                    <option value="221">+221 <small>(Senegal)</small></option>
                                    <option value="222">+222 <small>(Mauritania)</small></option>
                                    <option value="223">+223 <small>(Mali)</small></option>
                                    <option value="224">+224 <small>(Guinea)</small></option>
                                    <option value="225">+225 <small>(Côte d'Ivoire)</small></option>
                                    <option value="226">+226 <small>(Burkina Faso)</small></option>
                                    <option value="227">+227 <small>(Niger)</small></option>
                                    <option value="228">+228 <small>(Togo)</small></option>
                                    <option value="229">+229 <small>(Benin)</small></option>
                                    <option value="230">+230 <small>(Mauritius)</small></option>
                                    <option value="231">+231 <small>(Liberia)</small></option>
                                    <option value="232">+232 <small>(Sierra Leone)</small></option>
                                    <option value="233">+233 <small>(Ghana)</small></option>
                                    <option value="234">+234 <small>(Nigeria)</small></option>
                                    <option value="235">+235 <small>(Chad)</small></option>
                                    <option value="236">+236 <small>(Central African Republic)</small></option>
                                    <option value="237" selected>+237 <small>(Cameroon)</small></option>
                                    <option value="238">+238 <small>(Cape Verde)</small></option>
                                    <option value="239">+239 <small>(São Tomé and Príncipe)</small></option>
                                    <option value="240">+240 <small>(Equatorial Guinea)</small></option>
                                    <option value="241">+241 <small>(Gabon)</small></option>
                                    <option value="242">+242 <small>(Congo)</small></option>
                                    <option value="243">+243 <small>(DR Congo)</small></option>
                                    <option value="244">+244 <small>(Angola)</small></option>
                                    <option value="245">+245 <small>(Guinea-Bissau)</small></option>
                                    <option value="248">+248 <small>(Seychelles)</small></option>
                                    <option value="249">+249 <small>(Sudan)</small></option>
                                    <option value="250">+250 <small>(Rwanda)</small></option>
                                    <option value="251">+251 <small>(Ethiopia)</small></option>
                                    <option value="252">+252 <small>(Somalia)</small></option>
                                    <option value="253">+253 <small>(Djibouti)</small></option>
                                    <option value="254">+254 <small>(Kenya)</small></option>
                                    <option value="255">+255 <small>(Tanzania)</small></option>
                                    <option value="256">+256 <small>(Uganda)</small></option>
                                    <option value="257">+257 <small>(Burundi)</small></option>
                                    <option value="258">+258 <small>(Mozambique)</small></option>
                                    <option value="260">+260 <small>(Zambia)</small></option>
                                    <option value="261">+261 <small>(Madagascar)</small></option>
                                    <option value="263">+263 <small>(Zimbabwe)</small></option>
                                    <option value="264">+264 <small>(Namibia)</small></option>
                                    <option value="265">+265 <small>(Malawi)</small></option>
                                    <option value="266">+266 <small>(Lesotho)</small></option>
                                    <option value="267">+267 <small>(Botswana)</small></option>
                                    <option value="268">+268 <small>(Eswatini)</small></option>
                                    <option value="269">+269 <small>(Comoros)</small></option>
                                    <option value="290">+290 <small>(Saint Helena)</small></option>
                                    <option value="291">+291 <small>(Eritrea)</small></option>
                                    <option value="297">+297 <small>(Aruba)</small></option>
                                    <option value="298">+298 <small>(Faroe Islands)</small></option>
                                    <option value="299">+299 <small>(Greenland)</small></option>
                                </select>
                                <input type="tel" name="phone_no1" id="address-phone-input" class="ax-phone-input" placeholder="Enter phone number" required>
                                <span id="address-width-measure" class="ax-width-measure"></span>
                            </div>
                        </div>
                        <label id="address-phone-input-error" class="error" for="address-phone-input" style="display: none;"></label>
                    </div>
                </div>


                <div style="display: flex; justify-content: space-between; margin-top: 25px;">
                    <button type="button" id="save-address-btn" style="background-color: #FA9313; color: white; border: none; padding: 12px 20px; border-radius: 4px; cursor: pointer; flex: 1; margin-right: 10px; font-weight: 600; transition: all 0.3s ease;">
                        <i class="fa fa-save" style="margin-right: 5px;"></i> Save Address
                    </button>
                    <button type="button" id="cancel-address-btn" style="background-color: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 4px; cursor: pointer; flex: 1; font-weight: 600; transition: all 0.3s ease;">
                        <i class="fa fa-times" style="margin-right: 5px;"></i> Cancel
                    </button>
                </div>
            </form>
        </div>

        <!-- Confirmation Modal for Delete -->
        <div id="delete-confirm-modal" class="ax-popup" style="display: none; max-width: 90%; width: 350px; z-index: ********** !important; position: fixed !important; top: 50% !important; left: 50% !important; transform: translate(-50%, -50%) !important; border-radius: 8px; box-shadow: 0 5px 30px rgba(0,0,0,0.3) !important;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                <h3 style="font-size: 18px; font-weight: 600; color: #333; margin: 0;"><i class="fa fa-trash" style="margin-right: 8px; color: #dc3545;"></i>Confirm Delete</h3>
                <button id="close-delete-modal" style="background: none; border: none; font-size: 22px; cursor: pointer; padding: 0; margin: 0; line-height: 1; color: #999;">&times;</button>
            </div>
            <p style="margin-bottom: 25px; font-size: 15px; color: #555;">Are you sure you want to delete this address? This action cannot be undone.</p>
            <input type="hidden" id="delete_address_id" value="">
            <div style="display: flex; justify-content: flex-end;">
                <button type="button" id="confirm-delete-btn" style="background-color: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fa fa-trash" style="margin-right: 5px;"></i> Delete
                </button>
                <button type="button" id="cancel-delete-btn" style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fa fa-times" style="margin-right: 5px;"></i> Cancel
                </button>
            </div>
        </div>

        <!-- END NEW FULL WIDTH ROW -->


    </div>





</div>
</div>

<?php $this->start('add_js'); ?>
<script>
    // Global functions for address modal management
    // Show the popup overlay when a modal is opened
    function showOverlay() {
        $('#address-modal-overlay').show();
        // Add class to body to help with CSS targeting
        $('body').addClass('modal-open');
        // Force navbar elements behind the overlay
        $('header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky').css({
            'z-index': '1 !important',
            'position': 'relative !important'
        });

        // Add a style tag to force navbar elements behind the overlay
        if ($('#force-navbar-behind').length === 0) {
            $('head').append('<style id="force-navbar-behind">header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky { z-index: 1 !important; } #address-modal, #delete-confirm-modal { z-index: ********** !important; position: fixed !important; }</style>');
        }
    }

    // Hide the popup overlay when all modals are closed
    function hideOverlay() {
        $('#address-modal-overlay').hide();
        // Remove class from body
        $('body').removeClass('modal-open');
        // Remove the style tag
        $('#force-navbar-behind').remove();
        // Restore z-index of navbar elements
        $('header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky').css({
            'z-index': '1000',
            'position': 'relative'
        });
    }

    // Address Management Functions
    $(document).ready(function() {
        // Handle window resize to ensure modals stay centered
        $(window).on('resize', function() {
            if ($('#address-modal').is(':visible')) {
                $('#address-modal').css({
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)'
                });
            }
            if ($('#delete-confirm-modal').is(':visible')) {
                $('#delete-confirm-modal').css({
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)'
                });
            }
        });

        // Open the address modal for adding a new address
        $(document).on('click', '#add-address-btn', function(e) {
            e.preventDefault();
            console.log('Add address button clicked');
            // Reset the form
            $('#address-form')[0].reset();
            $('#address_id').val('');
            $('#address-modal-title').text('Add New Address');

            // Show the modal and overlay
            $('#address-modal').show();
            showOverlay();

            // Ensure the modal is on top
            $('#address-modal').css({
                'z-index': '**********',
                'display': 'block',
                'position': 'fixed',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)',
                'background-color': 'white'
            });

            // Force all other elements behind
            $('body > *:not(#address-modal, #address-modal-overlay, script, style)').css('z-index', '1');

            // Reinitialize Select2 for country code dropdown
            try {
                // Destroy existing Select2 instance if it exists
                if ($('#address-country-select').data('select2')) {
                    $('#address-country-select').select2('destroy');
                }

                // Reinitialize Select2
                $('#address-country-select').select2({
                    minimumResultsForSearch: 0,
                    allowClear: false,
                    placeholder: 'Select',
                    dropdownParent: $('#address-modal'),
                    templateResult: formatCountryCode,
                    templateSelection: formatCountryCode
                });

                // Format country code options
                function formatCountryCode(state) {
                    if (!state.id) return state.text;
                    return $('<span style="font-weight: 500;">+' + state.id + '</span>');
                }

                // Set default country code (237 for Cameroon)
                // First, make sure all options don't have selected attribute
                $('#address-country-select option').prop('selected', false);

                // Then set the selected option
                $('#address-country-select option[value="237"]').prop('selected', true);

                // Finally trigger change for Select2
                $('#address-country-select').val('237').trigger('change.select2');
                console.log('Set default country code to 237 for new address');

                console.log('Reinitialized Select2 for address country code dropdown');
            } catch (e) {
                console.error('Error reinitializing Select2:', e);
            }

            return false;
        });

        // Open the address modal for editing an existing address
        $(document).on('click', '.edit-address-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const addressId = $(this).data('address-id');

            // Fetch the address details
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutEditAddress']) ?>/" + addressId,
                type: 'GET',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Show the modal first to ensure Select2 initializes properly
                        $('#address-modal').show();
                        showOverlay();

                        // Ensure the modal is on top
                        $('#address-modal').css({
                            'z-index': '**********',
                            'display': 'block',
                            'position': 'fixed',
                            'top': '50%',
                            'left': '50%',
                            'transform': 'translate(-50%, -50%)',
                            'background-color': 'white'
                        });

                        // Force all other elements behind
                        $('body > *:not(#address-modal, #address-modal-overlay, script, style)').css('z-index', '1');

                        // Reinitialize Select2 for country code dropdown
                        try {
                            // Destroy existing Select2 instance if it exists
                            if ($('#address-country-select').data('select2')) {
                                $('#address-country-select').select2('destroy');
                            }

                            // Reinitialize Select2
                            $('#address-country-select').select2({
                                minimumResultsForSearch: 0,
                                allowClear: false,
                                placeholder: 'Select',
                                dropdownParent: $('#address-modal'),
                                templateResult: formatCountryCode,
                                templateSelection: formatCountryCode
                            });

                            // Format country code options
                            function formatCountryCode(state) {
                                if (!state.id) return state.text;
                                return $('<span style="font-weight: 500;">+' + state.id + '</span>');
                            }

                            console.log('Reinitialized Select2 for address country code dropdown');
                        } catch (e) {
                            console.error('Error reinitializing Select2:', e);
                        }

                        // Populate the form with the address details
                        const address = response.address;
                        $('#address_id').val(address.id);
                        $('#address_name').val(address.name);
                        $('#address_type').val(address.type);
                        $('#house_no').val(address.house_no);
                        $('#address_line1').val(address.address_line1);
                        $('#address_line2').val(address.address_line2);
                        $('#city_id').val(address.city_id);
                        $('#zipcode').val(address.zipcode);

                        // Set country code and trigger change event for Select2
                        if (address.country_code1) {
                            // First, make sure all options don't have selected attribute
                            $('#address-country-select option').prop('selected', false);

                            // Then set the selected option
                            $('#address-country-select option[value="' + address.country_code1 + '"]').prop('selected', true);

                            // Finally trigger change for Select2
                            $('#address-country-select').val(address.country_code1).trigger('change.select2');
                            console.log('Setting country code to: ' + address.country_code1);
                        }

                        $('#address-phone-input').val(address.phone_no1);

                        // Update the modal title
                        $('#address-modal-title').text('Edit Address');
                    } else {
                        toastr.error(response.message || 'Failed to load address details');
                    }
                },
                error: function() {
                    toastr.error('An error occurred while fetching address details');
                }
            });
        });

        // Close the address modal
        $(document).on('click', '#cancel-address-btn, #close-address-modal', function(e) {
            e.preventDefault();
            console.log('Close address modal clicked');
            $('#address-modal').hide();
            hideOverlay();
            return false;
        });

        // Close modal when clicking on overlay
        $(document).on('click', '#address-modal-overlay', function() {
            console.log('Overlay clicked');
            $('#address-modal').hide();
            hideOverlay();
        });

        // Handle input on address phone number field to clear error message
        $(document).on('input', '#address-phone-input', function() {
            $('#address-phone-input-error').hide();
        });

        // Handle address country code change
        $(document).on('change', '#address-country-select', function() {
            $('#address-phone-input-error').hide();
        });

        // Save the address (add or update)
        $(document).on('click', '#save-address-btn', function() {
            console.log('Save address button clicked');
            // Validate the phone number
            const phoneNumber = $('#address-phone-input').val().trim();
            if (phoneNumber && !/^\d{10,15}$/.test(phoneNumber)) {
                $('#address-phone-input-error').text('Please enter a valid phone number (10-15 digits)').show();
                return;
            }

            // Validate the form
            if (!$('#address-form')[0].checkValidity()) {
                $('#address-form')[0].reportValidity();
                return;
            }

            // Disable the button to prevent multiple submissions
            $(this).prop('disabled', true);

            // Determine if this is an add or edit operation
            const addressId = $('#address_id').val();
            const isEdit = addressId !== '';

            // Format the phone number with country code
            const countryCode = $('#address-country-select').val().trim();
            const formattedPhoneNumber = phoneNumber ? '+' + countryCode + phoneNumber : '';

            // Create a hidden input for the formatted phone number
            if ($('#formatted_phone_no').length) {
                $('#formatted_phone_no').val(formattedPhoneNumber);
            } else {
                $('#address-form').append('<input type="hidden" id="formatted_phone_no" name="formatted_phone_no" value="' + formattedPhoneNumber + '">');
            }

            // Prepare the data
            const formData = $('#address-form').serialize();

            // Determine the URL based on the operation
            let url;
            if (isEdit) {
                url = "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutEditAddress']) ?>/" + addressId;
            } else {
                url = "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutAddAddress']) ?>";
            }

            // Send the AJAX request
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    // Re-enable the button
                    $('#save-address-btn').prop('disabled', false);

                    if (response.status === 'success') {
                        // Close the modal
                        $('#address-modal').hide();
                        hideOverlay();

                        // Show success message
                        toastr.success(response.message || (isEdit ? 'Address updated successfully' : 'Address added successfully'));

                        // Reload the page to refresh the address list
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message || 'Failed to save address');
                    }
                },
                error: function() {
                    // Re-enable the button
                    $('#save-address-btn').prop('disabled', false);
                    toastr.error('An error occurred while saving the address');
                }
            });
        });

        // Open the delete confirmation modal
        $(document).on('click', '.delete-address-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const addressId = $(this).data('address-id');
            $('#delete_address_id').val(addressId);

            // Show the modal
            $('#delete-confirm-modal').show();
            $('#address-modal-overlay').show();

            // Ensure the modal is on top
            $('#delete-confirm-modal').css({
                'z-index': '**********',
                'display': 'block',
                'position': 'fixed',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)',
                'background-color': 'white'
            });

            // Force all other elements behind
            $('body > *:not(#delete-confirm-modal, #address-modal-overlay, script, style)').css('z-index', '1');
            return false;
        });

        // Close the delete confirmation modal
        $(document).on('click', '#cancel-delete-btn, #close-delete-modal', function(e) {
            e.preventDefault();
            console.log('Close delete modal clicked');
            $('#delete-confirm-modal').hide();
            $('#address-modal-overlay').hide();
            return false;
        });

        // Confirm and delete the address
        $(document).on('click', '#confirm-delete-btn', function() {
            console.log('Confirm delete clicked');
            // Disable the button to prevent multiple submissions
            $(this).prop('disabled', true);

            // Get the address ID
            const addressId = $('#delete_address_id').val();

            // Send the AJAX request
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutDeleteAddress']) ?>/" + addressId,
                type: 'POST',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    // Re-enable the button
                    $('#confirm-delete-btn').prop('disabled', false);

                    // Close the modal
                    $('#delete-confirm-modal').hide();
                    $('#address-modal-overlay').hide();

                    if (response.status === 'success') {
                        // Show success message
                        toastr.success(response.message || 'Address deleted successfully');

                        // Remove the address from the DOM
                        $('[data-address-id="' + addressId + '"]').remove();

                        // If no addresses left, show the no-address message
                        if ($('.address-item').length === 0) {
                            $('#address-list-container').html('<div class="no-address-message"><i class="fa fa-map-marker-alt" style="font-size: 24px; color: #FA9313; margin-bottom: 10px;"></i><p>You don\'t have any saved addresses.</p><p>Please add a new address using the + button above.</p></div>');
                        }
                    } else {
                        toastr.error(response.message || 'Failed to delete address');
                    }
                },
                error: function() {
                    // Re-enable the button
                    $('#confirm-delete-btn').prop('disabled', false);
                    toastr.error('An error occurred while deleting the address');
                }
            });
        });

        // Make address item clickable and highlight selection
        $(document).on('click keypress', '.address-item', function(e) {
            // Don't trigger if clicking on edit or delete buttons
            if ($(e.target).closest('.edit-address-btn, .delete-address-btn').length) {
                return;
            }

            if (e.type === 'click' || (e.type === 'keypress' && (e.which === 13 || e.which === 32))) {
                // Remove selection from all
                $('.address-item').removeClass('selected');

                // Add selection to clicked
                $(this).addClass('selected');

                // Set radio checked
                $(this).find('input[name="address"]').prop('checked', true).trigger('change');

                // Store in sessionStorage
                const addressId = $(this).data('address-id');
                const cityId = $(this).find('input[name="address"]').data('city-id');

                sessionStorage.setItem('customer_address_id', addressId);
                sessionStorage.setItem('city_id', cityId);
                sessionStorage.setItem('showroom_id', '');
                sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'delivered');

                // Show a confirmation toast
                if (typeof toastr !== 'undefined') {
                    toastr.success('Address selected successfully', '', {
                        timeOut: 2000,
                        progressBar: true
                    });
                }
            }
        });

        // Handle address selection via radio button change
        $(document).on('change', 'input[name="address"]', function() {
            // Remove selected class from all address items
            $('.address-item').removeClass('selected');

            // Add selected class to the parent of the checked radio button
            $(this).closest('.address-item').addClass('selected');
        });

        // Select the first address by default if available
        if ($('input[name="address"]').length > 0) {
            $('input[name="address"]:first').prop('checked', true).trigger('change');
        }
    });

    // Cart Item Functions
  
    function updateToCart(cart_item_id, currentQty) {

        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'updateCartItem']) ?>",
                type: 'POST',
                data: {cart_item_id: cart_item_id, quantity: currentQty},
                success: function (response) {
                    console.log(JSON.stringify(response));
                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });

    }


</script>
<script>
    let lytPoint = 0;
    $(document).on('click', '.loyaltyPointsCheck', function () {
        var customerId = <?= json_encode($customerId) ?>;
        if (customerId == null) {

            toastr.warning("<?= __('Login required to add loyalty points') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            checkLoyaltyPoint()
                .then((res) => {
                    if (res.status == 'success' || res.status == 200) {
                        toastr.success(res.message, '', {
                            timeOut: 3000,  // 3 seconds before hiding the toastr message
                            progressBar: true,
                            onHidden: function () {
                                document.getElementById('popup').style.display = 'block';
                                document.getElementById('overlay').style.display = 'block';
                            }
                        });
                    } else {
                        toastr.warning(res.message, '', {
                            timeOut: 3000,  // 3 seconds before hiding the toastr message
                            progressBar: true,
                            onHidden: function () {
                                // Reload the page after the toastr message disappears
                                //    location.reload();
                            }
                        });
                    }
                })
                .catch((error) => {
                    toastr.error(error, 'Error')
                });
        }
    });


    function checkLoyaltyPoint() {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkLoyaltyPoint']) ?>",
                type: 'POST',
                success: function (response) {
                    lytPoint = response.data.points_converted;
                    // sessionStorage.setItem('redeemPoint', parseFloat(response.data.points_converted.replace(/,/g, '')));
                    updateTotalAmount();

                    $(".setDateAjax").html(new Date(response.data.validity_end_date.date).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                    }));
                    $(".loyalty-price").html(formatAmount(response.data.points_converted));

                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }
    function formatAmount(amount){
        return amount.toString().split('.')[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    }
    $(".radeem-btn").on("click", handleRedeemClick);


    function handleRedeemClick(reload) {

        let inputPoint = parseFloat($(".radeem-input").val());
        final_amount_data = parseFloat(sessionStorage.getItem('final_amount'));
        let newam = final_amount_data - inputPoint;

        $.ajax({
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'loyaltyPointVerify']) ?>",
            type: 'POST',
            data: {
                'redeem_points': inputPoint,
                'final_amount': newam
            },
            success: function (response) {
                if (response.status == 'success') {
                $(".lyt-reedem-point").html(inputPoint);
                $(".lytPoints").val(inputPoint);
                closePopup();
                // Store the input value in sessionStorage
                sessionStorage.setItem('redeemPoint', inputPoint);
                // Update wallet popup if it's open
                updateWalletPopupIfOpen();
                    if(reload == 2){
                        toastr.success(response.message, '', {
                            timeOut: 1000,
                            progressBar: true,
                            onHidden: function () {
                                window.location.reload();
                            }
                        });
                   }


                } else {

                    toastr.warning(response.message, '', {
                        timeOut: 1000,
                        progressBar: true,
                        onHidden: function () {
                            sessionStorage.setItem('redeemPoint', 0);
                            if(reload == 2){
                                window.location.reload();
                            }
                        }
                    });

                }
            },
            error: function (xhr, status, error) {
                reject('An error occurred: ' + error);
            }
        });
    }

    // On page load, retrieve the value from sessionStorage
    $(document).ready(function () {
        let redeemPoint = sessionStorage.getItem('redeemPoint');
        if (redeemPoint) {
            $(".lyt-reedem-point").html(redeemPoint);
            $(".lytPoints").val(redeemPoint);
            $(".radeem-input").val(redeemPoint);
        }
        updateTotalAmount();
    });

    var customerId = <?= json_encode($customerId) ?>;
    if (customerId) {
        document.getElementById('loyaltyPoints').onclick = function () {
            document.getElementById('popup').style.display = 'block';
            document.getElementById('overlay').style.display = 'block';
        };
    }

    function closePopup() {
        document.getElementById('popup').style.display = 'none';
        document.getElementById('overlay').style.display = 'none';
    }

    // Wallet functionality
    let walletAmount = 0;
    let availableWallet = <?= $wallet ?>;

    $(document).on('click', '.walletPointsCheck', function () {
        var customerId = <?= json_encode($customerId) ?>;
        if (customerId == null) {
            toastr.warning("<?= __('Login required to use wallet') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            if (availableWallet <= 0) {
                toastr.warning("<?= __('No wallet balance available') ?>", '', {
                    timeOut: 3000,
                    progressBar: true,
                });
            } else {
                // Calculate maximum usable amount before showing popup
                updateMaxUsableAmount();
                document.getElementById('wallet-popup').style.display = 'block';
                document.getElementById('wallet-overlay').style.display = 'block';
            }
        }
    });

    function closeWalletPopup() {
        document.getElementById('wallet-popup').style.display = 'none';
        document.getElementById('wallet-overlay').style.display = 'none';
    }

    function updateMaxUsableAmount() {
        // Calculate the maximum amount that can be used from wallet
        let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
        let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
        let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
        let subAmount = <?= $totalPrice ?>;
        let totalBeforeWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;

        let maxUsable = Math.min(availableWallet, Math.max(0, totalBeforeWallet));
        $('.max-usable-amount').text(formatAmount(maxUsable));

        // Update the input max attribute
        $('.wallet-input').attr('max', maxUsable);
    }

    // Handle wallet input validation
    $(document).on('input', '.wallet-input', function() {
        // Remove any non-numeric characters except decimal point
        let value = $(this).val().replace(/[^0-9.]/g, '');

        // Ensure only one decimal point
        let parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }

        // Limit to 2 decimal places
        if (parts[1] && parts[1].length > 2) {
            value = parts[0] + '.' + parts[1].substring(0, 2);
        }

        $(this).val(value);

        let inputAmount = parseFloat(value) || 0;
        let maxAmount = availableWallet;

        // Get current total amount to limit wallet usage
        let currentTotal = parseFloat(sessionStorage.getItem('final_amount')) || <?= $totalPrice ?>;
        let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
        let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
        let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
        let subAmount = <?= $totalPrice ?>;
        let totalBeforeWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;

        // Limit wallet amount to available balance and total amount
        maxAmount = Math.min(availableWallet, Math.max(0, totalBeforeWallet));

        // Update the max usable amount display in real-time
        $('.max-usable-amount').text(formatAmount(maxAmount));
        $('.wallet-input').attr('max', maxAmount);

        if (inputAmount > maxAmount) {
            $(this).val(maxAmount);
            inputAmount = maxAmount;
        }

        if (inputAmount < 0) {
            $(this).val(0);
            inputAmount = 0;
        }

        // Update the display
        $('.wallet-amount-to-use').text(formatAmount(inputAmount));

        // Enable/disable apply button
        if (inputAmount > 0 && inputAmount <= maxAmount) {
            $('.wallet-apply-btn').prop('disabled', false);
        } else {
            $('.wallet-apply-btn').prop('disabled', true);
        }
    });

    // Prevent non-numeric input on keypress
    $(document).on('keypress', '.wallet-input', function(e) {
        // Allow: backspace, delete, tab, escape, enter
        if ($.inArray(e.keyCode, [46, 8, 9, 27, 13]) !== -1 ||
            // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
            (e.keyCode === 65 && e.ctrlKey === true) ||
            (e.keyCode === 67 && e.ctrlKey === true) ||
            (e.keyCode === 86 && e.ctrlKey === true) ||
            (e.keyCode === 88 && e.ctrlKey === true)) {
            return;
        }
        // Ensure that it is a number and stop the keypress
        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105) && e.keyCode !== 190) {
            e.preventDefault();
        }
    });

    // Handle wallet apply button
    $(document).on('click', '.wallet-apply-btn', function() {
        let inputAmount = parseFloat($('.wallet-input').val()) || 0;

        if (inputAmount > 0 && inputAmount <= availableWallet) {
            walletAmount = inputAmount;
            $('.wallet-used-amount').text(formatAmount(inputAmount));
            $('.walletAmount').val(inputAmount);

            // Store in sessionStorage
            sessionStorage.setItem('walletAmount', inputAmount);

            // Update wallet status display
            $('.wallet-applied').show();
            $('.no-wallet').hide();

            closeWalletPopup();
            updateTotalAmount();

            toastr.success("<?= __('Wallet amount applied successfully') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            toastr.error("<?= __('Invalid wallet amount') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        }
    });

    // Handle remove wallet
    $(document).on('click', '.removeWallet', function() {
        walletAmount = 0;
        $('.wallet-used-amount').text('0');
        $('.walletAmount').val('0');
        $('.wallet-input').val('');

        // Remove from sessionStorage
        sessionStorage.removeItem('walletAmount');

        // Update wallet status display
        $('.wallet-applied').hide();
        $('.no-wallet').show();

        updateTotalAmount();

        toastr.info("<?= __('Wallet amount removed') ?>", '', {
            timeOut: 3000,
            progressBar: true,
        });
    });

    // Load wallet amount from sessionStorage on page load
    $(document).ready(function() {
        let storedWalletAmount = sessionStorage.getItem('walletAmount');
        if (storedWalletAmount && parseFloat(storedWalletAmount) > 0) {
            walletAmount = parseFloat(storedWalletAmount);
            $('.wallet-used-amount').text(formatAmount(walletAmount));
            $('.walletAmount').val(walletAmount);
            $('.wallet-input').val(walletAmount);

            // Update wallet status display
            $('.wallet-applied').show();
            $('.no-wallet').hide();
        } else {
            // Show no wallet applied status
            $('.wallet-applied').hide();
            $('.no-wallet').show();
        }
    });

    // Close wallet popup when clicking on overlay
    $(document).on('click', '#wallet-overlay', function() {
        closeWalletPopup();
    });

    // Prevent closing when clicking inside the popup
    $(document).on('click', '#wallet-popup', function(e) {
        e.stopPropagation();
    });

    // Update wallet popup when loyalty points or coupons change
    function updateWalletPopupIfOpen() {
        if ($('#wallet-popup').is(':visible')) {
            updateMaxUsableAmount();
            // Trigger input validation to update the display
            $('.wallet-input').trigger('input');
        }

        // Also check if current wallet amount is still valid
        let currentWalletAmount = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
        if (currentWalletAmount > 0) {
            let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
            let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
            let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
            let subAmount = <?= $totalPrice ?>;
            let totalBeforeWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;
            let maxUsable = Math.min(availableWallet, Math.max(0, totalBeforeWallet));

            // If current wallet amount exceeds the new maximum, adjust it
            if (currentWalletAmount > maxUsable) {
                if (maxUsable > 0) {
                    // Adjust to maximum usable amount
                    sessionStorage.setItem('walletAmount', maxUsable);
                    $('.wallet-used-amount').text(formatAmount(maxUsable));
                    $('.walletAmount').val(maxUsable);
                    $('.wallet-input').val(maxUsable);
                    walletAmount = maxUsable;

                    toastr.info("<?= __('Wallet amount adjusted due to other discounts') ?>", '', {
                        timeOut: 3000,
                        progressBar: true,
                    });
                } else {
                    // Remove wallet amount completely
                    sessionStorage.removeItem('walletAmount');
                    $('.wallet-used-amount').text('0');
                    $('.walletAmount').val('0');
                    $('.wallet-input').val('');
                    $('.wallet-applied').hide();
                    $('.no-wallet').show();
                    walletAmount = 0;

                    toastr.info("<?= __('Wallet amount removed due to other discounts covering full amount') ?>", '', {
                        timeOut: 3000,
                        progressBar: true,
                    });
                }
                updateTotalAmount();
            }
        }
    }

    // Smart function to adjust wallet amount for optimal discount application
    function adjustWalletForOptimalDiscounts() {
        let currentWalletAmount = parseFloat(sessionStorage.getItem('walletAmount')) || 0;

        if (currentWalletAmount > 0) {
            // Get coupon amount from both storage methods
            let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) ||
                              parseFloat(storage.get('coupon_amount')) || 0;
            let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
            let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
            let subAmount = <?= $totalPrice ?>;

            // Calculate what the total would be without wallet
            let totalWithoutWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;

            // If there are other discounts (coupon or loyalty points), prioritize them
            if (couponAmount > 0 || redeemPoint > 0) {
                // Calculate optimal wallet amount: use wallet only for the remaining amount
                let optimalWalletAmount = Math.min(availableWallet, Math.max(0, totalWithoutWallet));

                // Only adjust if the current wallet amount is different from optimal
                if (currentWalletAmount !== optimalWalletAmount) {
                    if (optimalWalletAmount > 0) {
                        // Adjust wallet to optimal amount
                        sessionStorage.setItem('walletAmount', optimalWalletAmount);
                        $('.wallet-used-amount').text(formatAmount(optimalWalletAmount));
                        $('.walletAmount').val(optimalWalletAmount);
                        $('.wallet-input').val(optimalWalletAmount);
                        walletAmount = optimalWalletAmount;

                        // Show appropriate message based on what happened
                        if (optimalWalletAmount < currentWalletAmount) {
                            let savedAmount = currentWalletAmount - optimalWalletAmount;
                            toastr.info("<?= __('Wallet adjusted to ') ?>" + formatAmount(optimalWalletAmount) + " <?= __(' FCFA to allow coupon discount. Saved ') ?>" + formatAmount(savedAmount) + " <?= __(' FCFA in your wallet!') ?>", '', {
                                timeOut: 4000,
                                progressBar: true,
                            });
                        } else {
                            toastr.info("<?= __('Wallet amount increased to ') ?>" + formatAmount(optimalWalletAmount) + " <?= __(' FCFA') ?>", '', {
                                timeOut: 3000,
                                progressBar: true,
                            });
                        }
                    } else {
                        // Remove wallet amount completely if other discounts cover everything
                        sessionStorage.removeItem('walletAmount');
                        $('.wallet-used-amount').text('0');
                        $('.walletAmount').val('0');
                        $('.wallet-input').val('');
                        $('.wallet-applied').hide();
                        $('.no-wallet').show();
                        walletAmount = 0;

                        toastr.info("<?= __('Wallet amount removed as coupon covers the full amount. Your wallet balance is preserved!') ?>", '', {
                            timeOut: 4000,
                            progressBar: true,
                        });
                    }
                    updateTotalAmount();
                }
            }
        }
    }

    // Hook into existing loyalty points and coupon functions
    // Override the existing updateTotalAmount to also update wallet popup
    const originalUpdateTotalAmount = window.updateTotalAmount;
    window.updateTotalAmount = function() {
        if (originalUpdateTotalAmount) {
            originalUpdateTotalAmount();
        }
        updateWalletPopupIfOpen();
    };
</script>
<script>

    $(document).on('.checkCouponCode', function () {
        let couponName = $(".couponName").val();

        applyOffers(couponName, <?= $totalPrice ?>)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    $(".appliedCoupon").html(couponName);
                    $(".offerApplied").html(res.data.coupon_amount);

                    sessionStorage.setItem('coupon_amount', res.data.coupon_amount);
                    sessionStorage.setItem('appliedCoupon', couponName);
                    updateTotalAmount();
                    // Update wallet popup if it's open
                    updateWalletPopupIfOpen();
                    toastr.success(res.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                        }
                    });
                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                            sessionStorage.removeItem('coupon_amount');
                            sessionStorage.removeItem('appliedCoupon');
                            updateTotalAmount();
                            window.location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                toastr.error(error, 'Error')
            });
    });

    function applyOffers(couponName, amt) {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'applyoffer']) ?>",
                type: 'POST',
                data: {'coupon_code': couponName, 'subTotal': amt},
                success: function (response) {
                    if (response.status) {


                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    $(document).ready(function () {
        // Check sessionStorage for appliedCoupon and coupon_amount
        let appliedCoupon = sessionStorage.getItem('appliedCoupon');
        let couponAmount = sessionStorage.getItem('coupon_amount');

        if (appliedCoupon && couponAmount) {
            // Update UI with stored values
            $(".appliedCoupon").html(appliedCoupon);
            $(".couponName").val(appliedCoupon);
            $(".offerApplied").html(couponAmount);
        } else {
            // Reset UI if no values in sessionStorage
            $(".appliedCoupon").html('');
            $(".offerApplied").html('0');
        }
    });
</script>
<script>
    $(document).ready(function () {
        updateTotalAmount();
    });

    function updateTotalAmount() {
        // Retrieve stored values
        let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
        let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
        let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
        let walletUsed = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
        let subAmount = <?= $totalPrice ?>;
        let paybycredit = <?php echo $payByCreditActive; ?>;

        if(paybycredit == true){
            sessionStorage.removeItem('appliedCoupon');
            sessionStorage.removeItem('coupon_amount');
            sessionStorage.removeItem('redeemPoint');
            sessionStorage.removeItem('delivery_charge');
            sessionStorage.removeItem('walletAmount');
            walletUsed = 0;
        }

        // Calculate total amount with all discounts and charges
        let totalAmount = subAmount - (couponAmount + redeemPoint + walletUsed) + delivery_charge;

        // Ensure total amount is not negative
        if (totalAmount < 0) {
            totalAmount = 0;
        }

        sessionStorage.setItem('final_amount', totalAmount);
        $('.totalAmount').html(formatAmount(totalAmount));
        $('#payment-confirm').html("Pay " + formatAmount(totalAmount) + " FCFA");
        $(".delivery-state").html(delivery_charge ? formatAmount(delivery_charge) : '-');
    }

</script>
<script>
    $(document).ready(function () {
        // When the radio button for "Pickup from showroom" is selected
        $('#pickup-from-showroom').on('change', function () {
            if ($(this).prop('checked')) {
                $('#pickup-div').fadeIn(300);  // Show Pickup div with fade effect
                $('#delivery-div').hide();  // Hide Delivery div

                // Add pulse animation to the selected option
                const label = $(this).next('.delivery-option-content');
                label.addClass('pulse-animation');
                setTimeout(() => {
                    label.removeClass('pulse-animation');
                }, 500);
            }
        });

        // When the radio button for "Deliver to address" is selected
        $('#deliver-to-address').on('change', function () {
            if ($(this).prop('checked')) {
                $('#delivery-div').fadeIn(300);  // Show Delivery div with fade effect
                $('#pickup-div').hide();  // Hide Pickup div

                // Add pulse animation to the selected option
                const label = $(this).next('.delivery-option-content');
                label.addClass('pulse-animation');
                setTimeout(() => {
                    label.removeClass('pulse-animation');
                }, 500);
            }
        });

        // Make sure clicking on the label selects the radio button
        $('.delivery-option-content').on('click', function(e) {
            // Select the radio button
            const radio = $(this).prev('input[type="radio"]');
            radio.prop('checked', true).trigger('change');
        });

        // Set the proper initial state without triggering change events
        let deliveryOption = $('input[name="delivery_option"]:checked').val();
        if (deliveryOption === 'pickup') {
            $('#pickup-div').show();
            $('#delivery-div').hide();
        } else {
            $('#delivery-div').show();
            $('#pickup-div').hide();
        }


    });

    // Client-side showroom data handling with infinite scroll
    let allShowrooms = []; // Store all showrooms data
    let filteredShowrooms = [];
    let displayedCount = 0;
    const itemsPerBatch = 9; // Number of showrooms to load per batch
    let isLoading = false;
    let allLoaded = false;

    // Load all showroom data at once
    $(document).ready(function() {
        // Show loading indicator with animation
        $('#loading-message').show();

        // Fetch all showroom data at once
        $.ajax({
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'getCartShowRoomAddress']) ?>",
            method: 'post',
            data: {
                search_str: '',
                city_name: ''
            },
            dataType: 'json',
            success: function(response) {
                $('#loading-message').hide();

                if (response.status === 'success') {
                    // Store all showrooms for client-side filtering
                    allShowrooms = response.data;
                    filteredShowrooms = [...allShowrooms];

                    // Initial render
                    filterShowrooms('');

                    // Setup scroll event for infinite scrolling
                    setupInfiniteScroll();
                } else {
                    $('#showroom-list').html('<div style="padding:20px; text-align:center;">No showrooms found.</div>');
                }
            },
            error: function() {
                $('#loading-message').hide();
                $('#showroom-list').html('<div style="padding:20px; text-align:center; color:red;">Failed to load showrooms. Please try again.</div>');
            }
        });

        // Focus on search input when clicking the search container
        $('.showroom-search-container').on('click', function() {
            $('#showroom-search').focus();
        });
    });

    // Setup infinite scroll
    function setupInfiniteScroll() {
        // Add scroll event listener to the parent container
        $('#pickup-div').on('scroll', function() {
            const scrollHeight = $(this)[0].scrollHeight;
            const scrollTop = $(this).scrollTop();
            const clientHeight = $(this).height();

            // If we're near the bottom (within 200px) and not currently loading
            if (!isLoading && !allLoaded && scrollTop + clientHeight > scrollHeight - 200) {
                loadMoreShowrooms();
            }
        });

        // Also listen for window scroll for cases where the container doesn't have its own scrollbar
        $(window).on('scroll', function() {
            if (!$('#pickup-div').is(':visible')) return;

            const rect = document.getElementById('showroom-list').getBoundingClientRect();
            const windowHeight = window.innerHeight;

            // If the bottom of the showroom list is visible and we're not loading
            if (!isLoading && !allLoaded && rect.bottom <= windowHeight + 200) {
                loadMoreShowrooms();
            }
        });
    }

    // Filter showrooms based on search term
    function filterShowrooms(searchTerm) {
        searchTerm = searchTerm.toLowerCase().trim();

        if (searchTerm === '') {
            // If no search term, use all showrooms
            filteredShowrooms = [...allShowrooms];
        } else {
            // Filter showrooms based on search term
            filteredShowrooms = allShowrooms.filter(showroom =>
                (showroom.name && showroom.name.toLowerCase().includes(searchTerm)) ||
                (showroom.address && showroom.address.toLowerCase().includes(searchTerm)) ||
                (showroom.city && showroom.city.city_name && showroom.city.city_name.toLowerCase().includes(searchTerm))
            );
        }

        // Reset display state
        displayedCount = 0;
        allLoaded = false;

        // Clear the current list
        $('#showroom-list').empty();

        // Load the first batch
        loadMoreShowrooms();
    }

    // Load more showrooms
    function loadMoreShowrooms() {
        if (isLoading || allLoaded) return;

        isLoading = true;

        // Show loading indicator at the bottom
        if ($('#load-more-indicator').length === 0) {
            $('#showroom-list').after('<div id="load-more-indicator" style="text-align:center; padding:15px;"><i class="fa fa-spinner fa-spin"></i> Loading more showrooms...</div>');
        } else {
            $('#load-more-indicator').show();
        }

        // Simulate network delay for smoother UX (remove in production if not needed)
        setTimeout(function() {
            const startIndex = displayedCount;
            const endIndex = Math.min(startIndex + itemsPerBatch, filteredShowrooms.length);
            const selectedShowroom = sessionStorage.getItem('showroom_id') || '';

            if (startIndex >= filteredShowrooms.length) {
                // All showrooms have been loaded
                allLoaded = true;
                $('#load-more-indicator').hide();
                isLoading = false;
                return;
            }

            const showroomsToShow = filteredShowrooms.slice(startIndex, endIndex);

            if (showroomsToShow.length === 0) {
                if (startIndex === 0) {
                    $('#showroom-list').html('<div style="padding:20px; width:100%; text-align:center; grid-column: 1 / -1;">No showrooms match your search.</div>');
                }
                allLoaded = true;
                $('#load-more-indicator').hide();
                isLoading = false;
                return;
            }

            // Build HTML for this batch
            let showroomHTML = '';

            // Create HTML for each showroom card
            showroomsToShow.forEach(showroom => {
                const isSelected = selectedShowroom == showroom.id ? 'selected' : '';
                showroomHTML += `
                    <div class="showroom-item ${isSelected}" data-showroom-id="${showroom.id}">
                        <div class="showroom-item-content">
                            <div class="showroom-item-header">
                                <div class="showroom-radio-container">
                                    <input type="radio" class="showroom-radio" name="showroom"
                                           id="showroom-${showroom.id}" value="${showroom.id}"
                                           ${isSelected ? 'checked' : ''}>
                                    <label class="pop-up-name" for="showroom-${showroom.id}">
                                        ${showroom.name || 'Unnamed Showroom'}
                                        <span class="pop-up-showroom-type">Showroom</span>
                                    </label>
                                </div>
                                <div class="showroom-actions">
                                    <a href="https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(showroom.address || '')}"
                                       target="_blank" class="view-map-btn" title="View on Map">
                                        <i class="fa fa-map"></i>
                                        <span class="action-text">Map</span>
                                    </a>
                                </div>
                            </div>
                            <div class="showroom-details">
                                <div class="showroom-line">
                                    <i class="fa fa-store showroom-icon"></i>
                                    ${showroom.address || 'Address not available'}
                                </div>
                                <div class="showroom-line">
                                    <i class="fa fa-city showroom-icon"></i>
                                    ${showroom.city ? showroom.city.city_name : 'N/A'}
                                </div>
                                <div class="showroom-phone">
                                    <i class="fa fa-phone showroom-icon"></i>
                                    <a href="tel:${showroom.contact_number || ''}" class="showroom-phone-link">
                                        <span>${showroom.contact_number || 'N/A'}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Append to the DOM (not replace)
            $('#showroom-list').append(showroomHTML);

            // Update displayed count
            displayedCount = endIndex;

            // Check if all items have been loaded
            if (displayedCount >= filteredShowrooms.length) {
                allLoaded = true;
            }

            // Hide loading indicator
            $('#load-more-indicator').hide();
            isLoading = false;
        }, 300); // Small delay for better UX
    }

    // Search input handler with debounce
    let searchTimeout;
    $('#showroom-search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            filterShowrooms($(this).val());
        }, 300); // 300ms debounce delay
    });

    // Make showroom item clickable and highlight selection
    $(document).on('click keypress', '.showroom-item', function (e) {
        if (e.type === 'click' || (e.type === 'keypress' && (e.which === 13 || e.which === 32))) {
            // Remove selection from all
            $('.showroom-item').removeClass('selected');
            // Add selection to clicked
            $(this).addClass('selected');
            // Set radio checked
            $(this).find('.showroom-radio').prop('checked', true);

            // Store in sessionStorage
            const showroomId = $(this).data('showroom-id');
            sessionStorage.setItem('showroom_id', showroomId);
            sessionStorage.setItem('customer_address_id', '');
            sessionStorage.setItem('city_id', '');
            sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'pickup');

            // Find the showroom name for better user feedback
            const showroomName = $(this).find('.pop-up-name').text().trim();

            // Show a confirmation toast
            if (typeof toastr !== 'undefined') {
                toastr.success('Showroom selected successfully', '', {
                    timeOut: 2000,
                    progressBar: true
                });
            }

            // Trigger change event for delivery logic if needed
            $(this).find('.showroom-radio').trigger('change');
        }
    });
</script>

<script>
    // checkout page start
    $(document).ready(function () {

        // Show the popup
        $('#ax-open-popup').on('click', function () {
            $('#ax-popup-overlay').fadeIn();
            $('#ax-popup').fadeIn();
        });

        // Close the popup
        $('#ax-close-popup, #ax-popup-overlay').on('click', function () {
            $('#ax-popup-overlay').fadeOut();
            $('.ax-popup').fadeOut();
        });

        let delivery_mode_type = sessionStorage.getItem('delivery_mode_type') || '';



        $('.delivery_mode_type').on('change', function () {
            sessionStorage.setItem('delivery_mode_type', $(this).val());
        });

        if (delivery_mode_type === 'express') {
            // Set checked without triggering click event
            $('input[name="delivery_mode_type"][value="express"]').prop('checked', true);
            sessionStorage.setItem('delivery_mode_type', 'express');
        } else {
            // Set checked without triggering click event
            $('input[name="delivery_mode_type"][value="standard"]').prop('checked', true);
            sessionStorage.setItem('delivery_mode_type', 'standard');
        }

        let checkDeliveryTypeCheckedCheckbox = sessionStorage.getItem('checkDeliveryTypeCheckedCheckbox') || '';

        if (checkDeliveryTypeCheckedCheckbox === 'pickup') {
            // Set checked without triggering click event
            $('#pickup-from-showroom').prop('checked', true);
            // Show/hide appropriate divs manually
            $('#pickup-div').show();
            $('#delivery-div').hide();
            let showroom_id = sessionStorage.getItem('showroom_id') || '';
            if(showroom_id){
                setTimeout(function () {
                    $('#container').append('<input type="radio" name="showroom" value="' + showroom_id + '" id="showroom-"' + showroom_id + '"">');
                    $('input[name="showroom"][value="' + showroom_id + '"]').prop('checked', true);
                }, 1000); // Adjust this to match your load delay


            }
        } else if (checkDeliveryTypeCheckedCheckbox === 'delivered') {
            // Set checked without triggering click event
            $('#deliver-to-address').prop('checked', true);
            // Show/hide appropriate divs manually
            $('#delivery-div').show();
            $('#pickup-div').hide();
            let customer_address_id = sessionStorage.getItem('customer_address_id') || '';
            if(customer_address_id){
                // Set checked without triggering click event
                $('input[name="address"][value="' + customer_address_id + '"]').prop('checked', true);
            }
        }

    });
</script>
<script>
    $(document).on('click', '#payment-confirm', function () {
        // Select the checked radio button
        const checkedRadio = $('input[name="method"]:checked');

        if (!checkedRadio.length) { 
            toastr.warning('Please select a payment method', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }

        let delivery_option = $('input[name="delivery_option"]:checked').val();
        // alert($('input[name="address"]:checked').length);
        if(delivery_option == 'delivery'){
            if($('input[name="address"]:checked').length == 0){
                toastr.warning('Please select a delivery address', '', {
                timeOut: 3000,
                progressBar: true
            });
                return;
            }
        } else if(delivery_option == 'pickup'){
            if($('input[name="showroom"]:checked').length == 0){
                toastr.warning('<?= __('Please select a showroom to pickup from.') ?>', '', {
                timeOut: 3000,
                progressBar: true
            });
                return;
            }
        }

        // Check if mobile number is required and provided for MTN MoMo
        const paymentMethod = checkedRadio.val();
        let mobileNo = '';

        if (paymentMethod === 'MTN MoMo') {
            const countryCode = $('#ax-country-select').val().trim();
            const phoneNumber = $('#ax-mobile-input').val().trim();

            if (!phoneNumber) {
                toastr.warning('Please enter your mobile number for MTN MoMo payment', '', {
                    timeOut: 3000,
                    progressBar: true
                });
                $('#ax-mobile-input-error').text('Please enter your mobile number').show();
                return;
            }

            // Basic validation for mobile number format
            if (!/^\d{10,15}$/.test(phoneNumber)) {
                toastr.warning('Please enter a valid mobile number (10-15 digits)', '', {
                    timeOut: 3000,
                    progressBar: true
                });
                $('#ax-mobile-input-error').text('Please enter a valid mobile number (10-15 digits)').show();
                return;
            }

            // Format the mobile number with country code
            mobileNo =  phoneNumber;
        }

        // Get cart items from order summary section
        const cartItems = [];
        let calculatedTotal = 0;

        $('.my-order-item-details').each(function () {
            const itemContainer = $(this);
            const itemId = itemContainer.find('.updateCartItem').first().data('item-id');
            const itemName = itemContainer.find('.my-order-item-details-title').text().trim();
            const qty = parseInt(itemContainer.find('.cartItemQty').text().trim(), 10);
            const price = Math.max(0, parseFloat(itemContainer.find('.cart_items_list_price').val()));
            const category_name = itemContainer.find('.cart_items_list_category_name').val();
            const product_id = itemContainer.find('.cart_items_list_id').val();
            const brand_name = itemContainer.find('.cart_items_list_brand_name').val();

            if (itemId && qty && price) {
                const itemTotal = Math.max(0, qty * price); // Ensure item total is not negative
                cartItems.push({
                    cart_item_id: itemId,
                    product_id: product_id,
                    quantity: qty,
                    itemName: itemName,
                    price: price,
                    category_name: category_name,
                    brand_name: brand_name,
                    item_total: itemTotal
                });
                calculatedTotal += itemTotal;
            }
        });

        if (cartItems.length === 0) {
            toastr.warning('Your cart is empty', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        // Get coupon code if applied
        const couponCode = $('.couponName').val();
        // Get redeem points if applied
        const redeemPoints = $('.loyaltyPointsCheck').is(':checked') ? Math.max(0, lytPoint) : 0;
        // Get the total price from PHP
        const serverTotal = Math.max(0, <?= $totalPrice ?>);
        // Verify if calculated total matches server total (both should be non-negative)
        console.log('calculatedTotal:', calculatedTotal);
        console.log('serverTotal:', serverTotal);
        if (Math.abs(calculatedTotal - serverTotal) > 0.01 || calculatedTotal < 0 || serverTotal < 0) {
            toastr.error('Price mismatch detected. Please refresh the page.', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        const createOrder = '<?= $this->Url->build(['controller' => 'Account', 'action' => 'createOrder']) ?>';
        const inputPointVal = parseFloat($(".radeem-input").val());
        const delivery_mode = $('input[name="delivery_option"]:checked').val();

        const orderInfo = {
            wallet: parseFloat(sessionStorage.getItem('walletAmount')) || 0,
            delivery_mode_type: sessionStorage.getItem('delivery_mode_type'),
            payment_method: paymentMethod,
            cart_items: cartItems,
            coupon_code: couponCode,
            redeem_points: redeemPoints,
            total_amount: serverTotal,
            sub_amount: serverTotal - (redeemPoints || 0),
            mobile_no: mobileNo,
            final_amount: parseFloat(sessionStorage.getItem('final_amount')),
            delivery_mode,
            showroom_id: sessionStorage.getItem('showroom_id'),
            customer_address_id: sessionStorage.getItem('customer_address_id'),
            inputPointVal,
        };

        // Disable the button and show the loader
        const $button = $(this);
        $button.prop('disabled', true).addClass('loading');



        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'begin_checkout',
            ecommerce: {
                items: cartItems.map(item => ({
                    item_id: item.product_id,
                    item_name: item.name,
                    price: parseFloat(item.item_total).toFixed(2),
                    item_category: item.category_name,
                    item_brand: item.brand_name,
                    quantity: item.quantity
                })),
                ...(typeof cartCoupon !== 'undefined' && cartCoupon && { coupon: cartCoupon }),
                ...(typeof cartTotal !== 'undefined' && cartTotal && {
                    value: parseFloat(cartTotal).toFixed(2),
                    currency: 'FCFA'
                })
            }
        });

        ajaxCall('POST', createOrder, orderInfo)
            .then(response => {
                // Hide the loader
                $button.removeClass('loading');

                if (response.status === 'success') {
                    toastr.success(response.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                            if (response.data.payment_method === "Wave") {
                                window.location.href = response.wave_launch_url;
                            } else {
                                window.location.href = response.redirect_url.startsWith('http')
                                    ? response.redirect_url
                                    : searchAjaxUrl + response.redirect_url;
                            }
                        }
                    });
                } else {
                    // Re-enable the button on error
                    $button.prop('disabled', false);

                    toastr.error(response.message, '', {
                        timeOut: 3000,
                        progressBar: true
                    });
                }
            })
            .catch(error => {
                // Hide the loader and re-enable the button on error
                $button.removeClass('loading').prop('disabled', false);

                console.error('Order creation error:', error);
                toastr.error('An error occurred while processing your order', '', {
                    timeOut: 3000,
                    progressBar: true
                });
            });

    });

    // Show/hide mobile number input based on payment method
    $(document).on('change', 'input[name="method"]', function () {
        const selectedMethod = $(this).val();
        console.log('Payment method changed to:', selectedMethod);

        // Force hide the mobile input with multiple approaches
        $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');

        if (selectedMethod === 'MTN MoMo') {
            // Force show for MTN MoMo
            $('#momo-mobile-input').show().css('display', 'flex').addClass('show-input').attr('style', 'display: flex !important');
            console.log('MTN MoMo selected, showing mobile input');
        } else {
            // Force hide for other methods
            $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');
            // Clear any error messages when hiding the input
            $('#ax-mobile-input-error').hide();
            console.log('Other method selected, hiding mobile input');
        }

        // Debug check
        setTimeout(function() {
            console.log('Mobile input display style:', $('#momo-mobile-input').css('display'));
            console.log('Mobile input has show-input class:', $('#momo-mobile-input').hasClass('show-input'));
            console.log('Mobile input style attribute:', $('#momo-mobile-input').attr('style'));
        }, 100);
    });

    // Add click handler for payment method labels to ensure the change event is triggered
    $(document).on('click', '.pay-with-card-i-l label', function() {
        const radioId = $(this).attr('for');
        $('#' + radioId).prop('checked', true).trigger('change');
    });

    // Handle input on mobile number field to clear error message
    $(document).on('input', '#ax-mobile-input', function() {
        $('#ax-mobile-input-error').hide();
    });

    // Handle country code change
    $(document).on('change', '#ax-country-select', function() {
        $('#ax-mobile-input-error').hide();
    });

    // Trigger change event on page load for default selected method
    $(document).ready(function () {
        // Initialize Select2 for country code dropdowns
        try {
            // Initialize Select2 for MTN MoMo country code dropdown
            $('#ax-country-select').select2({
                minimumResultsForSearch: 0,
                allowClear: false,
                placeholder: 'Select'
            });

            // Set country code based on PHP variable or default to 237
            var countryCode = '<?= $mtn_country_code ?>' || '225';
            $('#ax-country-select').val(countryCode).trigger('change');
            console.log('Set country code to: ' + countryCode);

            // Initialize Select2 for address form country code dropdown
            $('#address-country-select').select2({
                minimumResultsForSearch: 0,
                allowClear: false,
                placeholder: 'Select',
                dropdownParent: $('body') // Attach to body to avoid positioning issues
            });

            // Only set default country code if no value is already selected
            // This preserves any existing country code from a saved address
            if (!$('#address-country-select').val() || $('#address-country-select').val() === '') {
                $('#address-country-select').val('237').trigger('change');
                console.log('Set default address country code to 237 (no previous value)');
            } else {
                // Make sure to trigger change event to properly update the Select2 display
                $('#address-country-select').trigger('change');
                console.log('Keeping existing address country code: ' + $('#address-country-select').val());
            }

            // Format country code options
            function formatCountryCode(state) {
                if (!state.id) return state.text;
                return $('<span style="font-weight: 500;">+' + state.id + '</span>');
            }

            console.log('Select2 initialized for country code dropdowns');
        } catch (e) {
            console.error('Error initializing Select2:', e);
        }

        // Check if MTN MoMo is selected and show the mobile input if it is
        const selectedMethod = $('input[name="method"]:checked').val();
        console.log('Initial payment method:', selectedMethod);

        // Force hide mobile input by default
        $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');

        if (selectedMethod === 'MTN MoMo') {
            // Force show for MTN MoMo
            $('#momo-mobile-input').show().css('display', 'flex').addClass('show-input').attr('style', 'display: flex !important');
            console.log('MTN MoMo selected initially, showing mobile input');
        } else {
            // Force hide for other methods
            $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');
            console.log('Other method selected initially, hiding mobile input');
        }

        // Debug check
        setTimeout(function() {
            console.log('Initial mobile input display style:', $('#momo-mobile-input').css('display'));
            console.log('Initial mobile input has show-input class:', $('#momo-mobile-input').hasClass('show-input'));
            console.log('Initial mobile input style attribute:', $('#momo-mobile-input').attr('style'));
        }, 100);

        // Manually trigger change event for the selected payment method
        $('input[name="method"]:checked').trigger('change');
    });

    // Single event handler for all delivery-related changes
    // Using a debounce variable to prevent multiple simultaneous calls
    let deliveryUpdateTimeout = null;

    // Wallet amount changes are handled in the popup functionality above


    $(document).on('change', 'input[name="delivery_mode_type"],input[name="delivery_option"], input[name="showroom"], input[name="address"]', function () {
        // Skip delivery charge calculation during initial page load
        if (window.initialPageLoad) {
            console.log('Skipping delivery charge calculation during initial page load');
            return;
        }

        // If we're already calculating, don't start another calculation
        if (deliveryUpdateTimeout) {
            clearTimeout(deliveryUpdateTimeout);
            console.log('Cleared pending delivery charge calculation');
        }

        // Set a short timeout to debounce multiple rapid changes
        deliveryUpdateTimeout = setTimeout(function() {
            // Clear the timeout reference
            deliveryUpdateTimeout = null;
            let delivery_mode = $('input[name="delivery_option"]:checked').val();
            const selectedElement = $(event.target);
            const selectedLabel = $('label[for="' + selectedElement.attr('id') + '"]').text();
            const deliveryCharges = '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getDeliveryChargeFromCityNew']) ?>';
        
            let triggeredName = selectedElement.attr('name');
            console.log('Delivery option changed:', triggeredName);

            if (triggeredName === 'address') {
                sessionStorage.setItem('city_id', selectedElement.data('city-id'));
                sessionStorage.setItem('customer_address_id', $('input[name="address"]:checked').val());
                sessionStorage.setItem('showroom_id', '');
            } else if (triggeredName === 'showroom') {
                sessionStorage.setItem('showroom_id', $('input[name="showroom"]:checked').val());
                sessionStorage.setItem('customer_address_id', '');
                sessionStorage.setItem('city_id', '');
            }

            if (delivery_mode === 'pickup') {
                sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'pickup');
            } else if (delivery_mode === 'delivery') {
                sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'delivered');
            }

            let customer_address_id = '';
            if (delivery_mode === 'pickup') {
                $('input[name="address"]').prop('checked', false);
                customer_address_id = 0;
            } else {
                $('input[name="showroom"]').prop('checked', false);
                customer_address_id = selectedElement.data('city-id') ?? $('input[name="address"]:checked').val();
            }

            if(triggeredName === 'delivery_mode_type'){
                customer_address_id = sessionStorage.getItem('city_id');
            }

            const jsondata = {
                cityId: customer_address_id,
                delivery_mode: $(".delivery_mode_type:checked").val(),
                weightQuantityArray: <?php echo json_encode($weightQuantityArray); ?>,
                sizeQuantityArray: <?php echo json_encode($sizeQuantityArray); ?>,
            }

            // Call our improved function to calculate delivery charge
            calculateDeliveryCharge();
        }, 300); // 300ms debounce delay
    });

    async function ajaxCall(method, url, data = null) {
        try {
            const options = {
                dataType: 'json',
                method: method.toUpperCase(),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
            };

            if (data && method.toUpperCase() !== 'GET') {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(url, options);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json(); // Assuming the response is JSON
        } catch (error) {
            console.error('Error in AJAX call:', error.message);
            throw error;
        }
    }
</script>


<script>
function checkAndSelectRadio() {
    var cityId = sessionStorage.getItem('city_id');
    var customerAddressId = sessionStorage.getItem('customer_address_id');
    var deliveryCharge = sessionStorage.getItem('delivery_charge');

    if (cityId && customerAddressId) {
        // Find the radio button where value matches customerAddressId and data-city-id matches cityId
        var selectedRadio = $("input[name='address'][data-city-id='" + cityId + "'][value='" + customerAddressId + "']");

        if (selectedRadio.length > 0) {
            // Set checked without triggering change event
            selectedRadio.prop('checked', true);
        }
    }

    // Set delivery charge in .delivery-state
    if (deliveryCharge !== null) {
        $(".delivery-state").html(formatAmount(deliveryCharge));
    }
}


    // Removed duplicate document.ready function for delivery charge

</script>

<?php $this->end(); ?>
<script>
// Check if jQuery is loaded
function checkJquery() {
    if (window.jQuery) {
        // jQuery is loaded, initialize our code
        initializeCheckout();
    } else {
        // jQuery is not loaded yet, wait and try again
        setTimeout(checkJquery, 100);
    }
}

// Define storage object first to avoid reference errors
window.storage = {
    get(key, fallback = null) {
        let v = sessionStorage.getItem(key);
        if (v === null || v === undefined) return fallback;
        if (!isNaN(v) && v !== '') return parseFloat(v);
        return v;
    },
    set(key, val) {
        sessionStorage.setItem(key, val);
    },
    remove(key) {
        sessionStorage.removeItem(key);
    }
};

// Global variables to control delivery charge calculations
window.initialPageLoad = true;
window.deliveryChargeCallCount = 0;
window.maxInitialCalls = 1; // Only allow one call during page load
window.deliveryChargeCalculated = false;

// Completely disable automatic delivery charge calculations on page load
// We'll manually trigger it once when needed
console.log('Page load started, delivery charge calculations disabled');

// Set a timeout to reset the flag after page load is complete
setTimeout(function() {
    window.initialPageLoad = false;
    console.log('Page load complete, delivery charge calculations will be enabled');

    // Only calculate if it hasn't been calculated yet
    if (!window.deliveryChargeCalculated) {
        console.log('Performing initial delivery charge calculation');
      //  calculateDeliveryChargeOnce();
    }
}, 1500); // Increased timeout to ensure page is fully loaded

// Function to calculate delivery charge only once during page load
function calculateDeliveryChargeOnce22() {
    // If we've already calculated the delivery charge, don't do it again
    if (window.deliveryChargeCalculated) {
        console.log('Delivery charge already calculated, skipping');
        return;
    }

    // Mark as calculated to prevent further calls
    window.deliveryChargeCalculated = true;

    // Get the delivery mode and customer address ID
    let delivery_mode = $('input[name="delivery_option"]:checked').val();
    let customer_address_id = '';

    if (delivery_mode === 'pickup') {
        customer_address_id = 0;
    } else {
        let selectedAddress = $('input[name="address"]:checked');
        customer_address_id = selectedAddress.data('city-id') || '';
    }

    // Only proceed if we have valid data
    if (!customer_address_id && delivery_mode !== 'pickup') {
        console.log('Skipping delivery charge calculation - no valid address selected');
        return;
    }

    // Get the delivery charge endpoint and data
    const deliveryCharges = '<?= $this->Url->build(["controller" => "Account", "action" => "getDeliveryChargeFromCityNew"]) ?>';
    const jsondata = {
        cityId: customer_address_id,
        delivery_mode: $(".delivery_mode_type:checked").val(),
        weightQuantityArray: <?php echo json_encode($weightQuantityArray); ?>,
        sizeQuantityArray: <?php echo json_encode($sizeQuantityArray); ?>,
    };
 
    // Show a loading indicator
    $(".delivery-state").html('<small>Calculating...</small>');

    // Make the AJAX call
    $.ajax({
        headers: {'X-CSRF-Token': '<?= $this->request->getAttribute("csrfToken") ?>'},
        url: deliveryCharges,
        type: 'POST',
        data: jsondata,
        success: function(response) {
            if (response.status === 'success') {
                storage.set('delivery_charge', response.total_delivery_charge);
                $(".delivery-state").text(formatAmount(response.total_delivery_charge));
                updateTotalAmount();
                console.log('Initial delivery charge calculated:', response.total_delivery_charge);
            } else {
                storage.set('delivery_charge', 0);
                $(".delivery-state").text('Free');
                updateTotalAmount();
                console.log('Initial delivery charge: Free shipping');
            }
        },
        error: function(error) {
            console.error('Delivery charge error:', error);
            $(".delivery-state").text('Error');
        }
    });
}

// Function to calculate delivery charge for user-initiated changes
function calculateDeliveryCharge() {
    // Increment the call count
    window.deliveryChargeCallCount++;

    // During page load, limit the number of calls
    if (window.initialPageLoad && window.deliveryChargeCallCount > window.maxInitialCalls) {
        console.log(`Skipping delivery charge calculation - too many calls during page load (${window.deliveryChargeCallCount})`);
        return;
    }

    // Get the delivery mode and customer address ID
    let delivery_mode = $('input[name="delivery_option"]:checked').val();
    let customer_address_id = '';

    if (delivery_mode === 'pickup') {
        customer_address_id = 0;
    } else {
        let selectedAddress = $('input[name="address"]:checked');
        customer_address_id = selectedAddress.data('city-id') || '';
    }

    // Only proceed if we have valid data
    if (!customer_address_id && delivery_mode !== 'pickup') {
        console.log('Skipping delivery charge calculation - no valid address selected');
        return;
    }

    // Get the delivery charge endpoint and data
    const deliveryCharges = '<?= $this->Url->build(["controller" => "Account", "action" => "getDeliveryChargeFromCityNew"]) ?>';
    const jsondata = {
        cityId: customer_address_id,
        delivery_mode: $(".delivery_mode_type:checked").val(),
        weightQuantityArray: <?php echo json_encode($weightQuantityArray); ?>,
        sizeQuantityArray: <?php echo json_encode($sizeQuantityArray); ?>,
    };

    console.log('Calculating delivery charge with data:', jsondata);

    // Show a loading indicator
    $(".delivery-state").html('<small>Calculating...</small>');

    // Make the AJAX call
    $.ajax({
        headers: {'X-CSRF-Token': '<?= $this->request->getAttribute("csrfToken") ?>'},
        url: deliveryCharges,
        type: 'POST',
        data: jsondata,
        success: function(response) {
            if (response.status === 'success') {
                const prevCharge = storage.get('delivery_charge', 0);
                const newCharge = response.total_delivery_charge;

                // Only update if the charge has changed
                if (prevCharge != newCharge) {
                    storage.set('delivery_charge', newCharge);
                    $(".delivery-state").text(formatAmount(newCharge));
                    updateTotalAmount();

                    // Only show notification if this is not during page load
                    if (!window.initialPageLoad) {
                        toastr.success('Shipping charge updated', '', {
                            timeOut: 2000,
                            progressBar: true
                        });
                    }
                } else {
                    // Just update the display without notification
                    $(".delivery-state").text(formatAmount(newCharge));
                }

                console.log('Delivery charge calculated:', newCharge);
            } else {
                storage.set('delivery_charge', 0);
                $(".delivery-state").text('Free');
                updateTotalAmount();
                console.log('Free shipping applied');
            }
        },
        error: function(error) {
            console.error('Delivery charge error:', error);
            $(".delivery-state").text('Error');
        }
    });
}

// Start checking for jQuery when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add a direct fix for the mobile number input and Select2 initialization
    setTimeout(function() {
        // Check if jQuery is loaded
        if (window.jQuery) {
            // Get the selected payment method
            var selectedMethod = jQuery('input[name="method"]:checked').val();
            console.log('DOMContentLoaded payment method:', selectedMethod);

            // Force hide the mobile input
            if (selectedMethod !== 'MTN MoMo') {
                jQuery('#momo-mobile-input').hide().css('display', 'none').attr('style', 'display: none !important');
                console.log('Forced hide mobile input on DOMContentLoaded');
            }

            // Add a global click handler for payment methods
            jQuery(document).on('click', 'input[name="method"]', function() {
                var method = jQuery(this).val();
                console.log('Payment method clicked:', method);

                if (method === 'MTN MoMo') {
                    jQuery('#momo-mobile-input').show().css('display', 'flex').attr('style', 'display: flex !important');
                } else {
                    jQuery('#momo-mobile-input').hide().css('display', 'none').attr('style', 'display: none !important');
                }
            });

            // Initialize Select2 for country code dropdowns if Select2 is available
            if (jQuery.fn.select2) {
                // Initialize Select2 for MTN MoMo country code dropdown
                jQuery('#ax-country-select').select2({
                    width: '100%',
                    dropdownAutoWidth: true,
                    minimumResultsForSearch: 0,
                    allowClear: false,
                    placeholder: 'Select',
                    dropdownCssClass: 'country-code-dropdown',
                    dropdownParent: jQuery('body'), // Attach to body to avoid positioning issues
                    templateResult: formatCountryCode,
                    templateSelection: formatCountryCode
                });

                // Set country code based on PHP variable or default to 237
                var countryCode = '<?= $mtn_country_code ?>' || '237';
                jQuery('#ax-country-select').val(countryCode).trigger('change');
                console.log('Set country code to: ' + countryCode + ' in DOMContentLoaded');

                // Initialize Select2 for address form country code dropdown
                jQuery('#address-country-select').select2({
                    width: '100%',
                    dropdownAutoWidth: true,
                    minimumResultsForSearch: 0,
                    allowClear: false,
                    placeholder: 'Select',
                    dropdownCssClass: 'country-code-dropdown',
                    dropdownParent: jQuery('body'), // Attach to body to avoid positioning issues
                    templateResult: formatCountryCode,
                    templateSelection: formatCountryCode
                });

                // Set default country code (237 for Cameroon)
                if (!jQuery('#address-country-select').val()) {
                    jQuery('#address-country-select').val('237').trigger('change');
                    console.log('Set default country code to 237 for address in DOMContentLoaded');
                }

                // Format country code options
                function formatCountryCode(state) {
                    if (!state.id) return state.text;
                    return jQuery('<span style="font-weight: 500;">+' + state.id + '</span>');
                }

                console.log('Select2 initialized on DOMContentLoaded');
            } else {
                console.warn('Select2 not available on DOMContentLoaded');
            }
        }
    }, 500);

    // Continue with normal initialization
    checkJquery();
});

// --- Utility Functions ---
function formatAmount(amount) {
    if (isNaN(amount)) return '0';
    return amount.toString().split('.')[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
}
function updateTotalAmount() {
    let couponAmount = storage.get('coupon_amount', 0);
    let redeemPoint = storage.get('redeemPoint', 0);
    let delivery_charge = storage.get('delivery_charge', 0);

    // Handle non-numeric delivery charge (like 'Free')
    if (isNaN(delivery_charge) || delivery_charge === 'Free') {
        delivery_charge = 0;
    }

    let subAmount = <?= $totalPrice ?>;
    let paybycredit = <?php echo $payByCreditActive; ?>;
    if (paybycredit) {
        storage.remove('coupon_amount');
        storage.remove('redeemPoint');
        storage.remove('delivery_charge');
        couponAmount = redeemPoint = delivery_charge = 0;
    }

    // Log the values for debugging
    console.log('Total calculation:', {
        subAmount,
        couponAmount,
        redeemPoint,
        delivery_charge
    });
    let totalAmount = subAmount - (couponAmount + redeemPoint) + delivery_charge;
    storage.set('final_amount', totalAmount);
    $('.totalAmount').html(formatAmount(totalAmount));
    $('#payment-confirm').html("Pay " + formatAmount(totalAmount) + " FCFA");
    $(".delivery-state").html(delivery_charge ? formatAmount(delivery_charge) : '-');
}

// Main initialization function that will run when jQuery is loaded
function initializeCheckout() {
    // Initialize Select2 for country code dropdowns
    try {
        // Initialize Select2 for MTN MoMo country code dropdown
        if ($.fn.select2) {
            $('#ax-country-select').select2({
                minimumResultsForSearch: 0,
                allowClear: false,
                placeholder: 'Select'
            });

            // Set country code based on PHP variable or default to 237
            var countryCode = '<?= $mtn_country_code ?>' || '237';
            $('#ax-country-select').val(countryCode).trigger('change');
            console.log('Set country code to: ' + countryCode + ' in initializeCheckout');

            // Initialize Select2 for address form country code dropdown
            $('#address-country-select').select2({
                minimumResultsForSearch: 0,
                allowClear: false,
                placeholder: 'Select'
            });

            // Set default country code (237 for Cameroon)
            if (!$('#address-country-select').val()) {
                $('#address-country-select').val('237').trigger('change');
                console.log('Set default country code to 237 for address in initializeCheckout');
            }

            // Format country code options
            function formatCountryCode(state) {
                if (!state.id) return state.text;
                return $('<span style="font-weight: 500;">+' + state.id + '</span>');
            }

            console.log('Select2 initialized in initializeCheckout');
        } else {
            console.warn('Select2 not available in initializeCheckout');
        }
    } catch (e) {
        console.error('Error initializing Select2 in initializeCheckout:', e);
    }
    // --- Coupon UI ---
    let appliedCoupon = storage.get('appliedCoupon', '');
    let couponAmount = storage.get('coupon_amount', 0);

    // Update coupon display based on whether a coupon is applied
    if (appliedCoupon && appliedCoupon !== '') {
        $(".appliedCoupon").html(appliedCoupon);
        $(".couponName").val(appliedCoupon);
        $(".offerApplied").html(couponAmount || '0');
        $(".coupon-applied").show();
        $(".no-coupon").hide();
    } else {
        $(".appliedCoupon").html('');
        $(".couponName").val('');
        $(".offerApplied").html('0');
        $(".coupon-applied").hide();
        $(".no-coupon").show();
    }

    // --- Loyalty Points UI ---
    let redeemPoint = storage.get('redeemPoint', 0);
    $(".lyt-reedem-point").html(redeemPoint);
    $(".lytPoints").val(redeemPoint);
    $(".radeem-input").val(redeemPoint);

    // --- Delivery Mode ---
    let delivery_mode_type = storage.get('delivery_mode_type', 'standard');
    $(`input[name="delivery_mode_type"][value="${delivery_mode_type}"]`).prop('checked', true);

    // --- Delivery Option ---
    let deliveryType = storage.get('checkDeliveryTypeCheckedCheckbox', 'pickup');
    if (deliveryType === 'pickup') {
        // Set checked without triggering change event
        $('#pickup-from-showroom').prop('checked', true);
        // Show/hide appropriate divs manually
        $('#pickup-div').show();
        $('#delivery-div').hide();

        let showroom_id = storage.get('showroom_id', '');
        if (showroom_id) {
            $(`input[name="showroom"][value="${showroom_id}"]`).prop('checked', true);
        }
    } else {
        // Set checked without triggering change event
        $('#deliver-to-address').prop('checked', true);
        // Show/hide appropriate divs manually
        $('#delivery-div').show();
        $('#pickup-div').hide();

        let customer_address_id = storage.get('customer_address_id', '');
        if (customer_address_id) {
           $(`input[name="address"][value="${customer_address_id}"]`).prop('checked', true);
        }
    }

    // --- Delivery Charge --- (handled below)

    // --- Update total on load without recalculating delivery charge ---
    // Use the stored delivery charge value without making an API call
    let storedDeliveryCharge = storage.get('delivery_charge', 0);
    if (storedDeliveryCharge) {
        $(".delivery-state").text(formatAmount(storedDeliveryCharge));
    } else {
        $(".delivery-state").text('-');
    }

    // Update the total amount without triggering delivery charge calculation
    updateTotalAmount();

    // Call checkAndSelectRadio to set the correct radio buttons without triggering change events
    checkAndSelectRadio();

    // Initialize payment method display
    const selectedPaymentMethod = $('input[name="method"]:checked').val();
  //  console.log('Payment method initialized:', selectedPaymentMethod);

    // Force hide mobile input by default
    $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');

    if (selectedPaymentMethod === 'MTN MoMo') {
        // Force show for MTN MoMo
        $('#momo-mobile-input').show().css('display', 'flex').addClass('show-input').attr('style', 'display: flex !important');
    //    console.log('MTN MoMo selected in initialization, showing mobile input');
    } else {
        // Force hide for other methods
        $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');
     //   console.log('Other method selected in initialization, hiding mobile input');
    }

    // Debug check
    setTimeout(function() {
        // console.log('Initialization mobile input display style:', $('#momo-mobile-input').css('display'));
        // console.log('Initialization mobile input has show-input class:', $('#momo-mobile-input').hasClass('show-input'));
        // console.log('Initialization mobile input style attribute:', $('#momo-mobile-input').attr('style'));
    }, 100);

    // Manually trigger change event for the selected payment method
    $('input[name="method"]:checked').trigger('change');

// --- Coupon Apply/Remove ---
$(document).on('click', '.checkCouponCode', async function () {
    let couponName = $(".couponName").val();
    if (!couponName || couponName.trim() === '') {
        toastr.warning('Please enter a coupon code');
        return;
    }

    try {
        let res = await applyOffers(couponName, <?= $totalPrice ?>);
        if (res.status == 'success' || res.status == 200) {
            // Save coupon data
            storage.set('appliedCoupon', couponName);
            storage.set('coupon_amount', res.data.coupon_amount);

            // Update UI
            $(".appliedCoupon").html(couponName);
            $(".offerApplied").html(res.data.coupon_amount);
            $(".coupon-applied").show();
            $(".no-coupon").hide();

            updateTotalAmount();

            // Immediately adjust wallet amount for optimal discount distribution
            adjustWalletForOptimalDiscounts();

            toastr.success(res.message);
        } else {
            // Clear coupon data
            storage.remove('coupon_amount');
            storage.remove('appliedCoupon');

            // Update UI
            $(".appliedCoupon").html('');
            $(".offerApplied").html('0');
            $(".coupon-applied").hide();
            $(".no-coupon").show();

            updateTotalAmount();
            toastr.warning(res.message);
        }
    } catch (e) {
        console.error('Error applying coupon:', e);
        toastr.error('Error applying coupon');
    }
});
$(document).on('click', '.removeCoupon', function () {
    // Clear coupon data
    storage.remove('coupon_amount');
    storage.remove('appliedCoupon');

    // Update UI
    $(".appliedCoupon").html('');
    $(".offerApplied").html('0');
    $(".couponName").val('');
    $(".coupon-applied").hide();
    $(".no-coupon").show();

    updateTotalAmount();
    // Update wallet popup if it's open
    updateWalletPopupIfOpen();
    toastr.success("Coupon removed successfully!", '', {
        timeOut: 3000,
        progressBar: true
    });
});

// --- Loyalty Points Redeem ---
$(".radeem-btn").on("click", async function () {
    let inputPoint = parseFloat($(".radeem-input").val());
    let final_amount_data = storage.get('final_amount', 0);
    let newam = final_amount_data - inputPoint;
    try {
        let response = await $.ajax({
            headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
            url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'loyaltyPointVerify']) ?>",
            type: 'POST',
            data: {redeem_points: inputPoint, final_amount: newam}
        });
        if (response.status == 'success') {
            $(".lyt-reedem-point").html(inputPoint);
            $(".lytPoints").val(inputPoint);
            storage.set('redeemPoint', inputPoint);
            closePopup();
            updateTotalAmount();
            toastr.success(response.message);
        } else {
            storage.set('redeemPoint', 0);
            $(".lyt-reedem-point").html('0');
            $(".lytPoints").val('0');
            updateTotalAmount();
            toastr.warning(response.message);
        }
    } catch (e) {
        toastr.error('Error redeeming points');
    }
});


// --- Delivery Mode/Option/Address/Showroom Change ---
$(document).on('change', 'input[name="delivery_mode_type"],input[name="delivery_option"], input[name="showroom"], input[name="address"]', async function (event) {
    let delivery_mode = $('input[name="delivery_option"]:checked').val();
    let triggeredName = $(event.target).attr('name');
    if (triggeredName === 'address') {
        storage.set('city_id', $(this).data('city-id'));
        storage.set('customer_address_id', $('input[name="address"]:checked').val());
        storage.set('showroom_id', '');
    } else if (triggeredName === 'showroom') {
        storage.set('showroom_id', $('input[name="showroom"]:checked').val());
        storage.set('customer_address_id', '');
        storage.set('city_id', '');
    }
    if (delivery_mode === 'pickup') {
        storage.set('checkDeliveryTypeCheckedCheckbox', 'pickup');
    } else if (delivery_mode === 'delivery') {
        storage.set('checkDeliveryTypeCheckedCheckbox', 'delivered');
    }
    let customer_address_id = '';
    if (delivery_mode === 'pickup') {
        $('input[name="address"]').prop('checked', false);
        customer_address_id = 0;
    } else {
        $('input[name="showroom"]').prop('checked', false);
        customer_address_id = $(this).data('city-id') ?? $('input[name="address"]:checked').val();
    }
    if (triggeredName === 'delivery_mode_type') {
        customer_address_id = storage.get('city_id');
    }
    // Get delivery charge via AJAX
    const deliveryCharges = '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getDeliveryChargeFromCityNew']) ?>';
    const jsondata = {
        cityId: customer_address_id,
        delivery_mode: $(".delivery_mode_type:checked").val(),
        weightQuantityArray: <?php echo json_encode($weightQuantityArray); ?>,
        sizeQuantityArray: <?php echo json_encode($sizeQuantityArray); ?>,
    };
            // if(delivery_mode === 'delivery'){
            // sessionStorage.removeItem('showroom_id');
            // }else{
            // sessionStorage.removeItem('customer_address_id');
            // sessionStorage.removeItem('city_id');
            // }
    try {
        let response = await $.ajax({
            headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
            url: deliveryCharges,
            type: 'POST',
            data: jsondata
        });
        if (response.status === 'success') {

            storage.set('delivery_charge', response.total_delivery_charge);
            $(".delivery-state").text(formatAmount(response.total_delivery_charge));
            updateTotalAmount();
            if(response.showroom==false){
                toastr.success(response.message);
            }
        } else {
            storage.set('delivery_charge', 0);
            $(".delivery-state").text('Free');
            updateTotalAmount();
            toastr.warning(response.message);
        }
    } catch (e) {
        toastr.warning('Please select a delivery address');
        // toastr.error('Error fetching delivery charge');
    }
});

// --- Payment Confirm ---
$(document).on('click', '#payment-confirm', async function () {
    // read delivery_option

});

// --- Helper AJAX Functions ---

function deleteToCart(cart_item_id) {
    return $.ajax({
        headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
        url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'deleteCartItem']) ?>",
        type: 'POST',
        data: {cart_item_id}
    });
}
function applyOffers(couponName, amt) {
    return $.ajax({
        headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
        url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'applyoffer']) ?>",
        type: 'POST',
        data: {'coupon_code': couponName, 'subTotal': amt}
    });
}
function closePopup() {
    $('#popup, #overlay').hide();
}

// Close the initializeCheckout function
}

// Enhanced Select2 initialization for searchable-select
$(document).ready(function() {
    // Initialize Select2 for searchable-select elements with improved styling
    $('.searchable-select').select2({
        width: '100%',
        dropdownAutoWidth: true,
        minimumResultsForSearch: 0,
        allowClear: false,
        placeholder: 'Select',
        dropdownCssClass: 'country-code-dropdown',
        dropdownParent: $('body'),
        templateResult: function(option) {
            if (!option.id) return option.text;

            // Get the option text which contains the country name
            var text = $(option.element).text();
            var parts = text.split('(');
            var countryCode = parts[0].trim();
            var countryName = parts.length > 1 ? parts[1].replace(')', '').trim() : '';

            // Create a container with both code and country
            var $container = $(
                '<div class="select2-country-option">' +
                    '<div class="select2-country-code">' + countryCode + '</div>' +
                    '<div class="select2-country-name">' + countryName + '</div>' +
                '</div>'
            );

            return $container;
        },
        templateSelection: function(option) {
            if (!option.id) return option.text;
            return $('<span style="font-weight: 500; font-size: 14px;">+' + option.id + '</span>');
        }
    });

    // Set the country code based on the PHP variable
    var countryCode = '<?= $mtn_country_code ?>' || '237';
    $('#ax-country-select').val(countryCode).trigger('change');

    // Format country code options for better display
    function formatCountryOption(option) {
        if (!option.id) return option.text;

        // Get country name from data attribute or extract from text
        var countryName = '';

        // First try to get from data-country attribute
        var dataCountry = $(option.element).attr('data-country');
        if (dataCountry) {
            countryName = dataCountry;
        } else {
            // Try to extract from small tag
            var smallTag = $(option.element).find('small');
            if (smallTag.length > 0) {
                countryName = smallTag.text().replace(/[\(\)]/g, '');
            } else {
                // If no small tag, try to extract from the text content
                var text = $(option.element).text();
                var match = text.match(/\+\d+\s*\((.+?)\)/);
                if (match && match[1]) {
                    countryName = match[1];
                }
            }
        }

        // Create a cleaner display with proper spacing and font size
        return $('<span style="display: flex; align-items: center;">' +
                 '<span style="font-weight: 500; font-size: 14px; min-width: 45px;">+' + option.id + '</span>' +
                 '<span style="color: #333; font-size: 13px; margin-left: 8px;">' + countryName + '</span>' +
                 '</span>');
    }

    // Ensure dropdown position is correct
    $('.searchable-select').on('select2:open', function() {
        // Add a small delay to ensure the dropdown is fully rendered
        setTimeout(function() {
            // Add custom class to the dropdown for additional styling
            $('.select2-dropdown').addClass('select2-dropdown-improved');

            // Ensure dropdown has solid background
            $('.select2-results__options').css({
                'background-color': '#fff',
                'max-height': '250px'
            });

            // Force the dropdown to be wide enough for country names
            $('.select2-dropdown').css({
                'min-width': '250px',
                'width': 'auto !important',
                'background-color': '#fff'
            });

            // Fix any display issues with the dropdown
            $('.select2-results').css({
                'display': 'block',
                'background-color': '#fff'
            });
        }, 10);
    });

    // Fix any issues with the dropdown after it's opened
    $(document).on('mouseenter', '.select2-results__option', function() {
        // Ensure country names are visible on hover
        $(this).find('.select2-country-name').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });
    });
});
</script>
