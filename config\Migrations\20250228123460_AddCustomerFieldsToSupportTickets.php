<?php
use Migrations\AbstractMigration;

class AddCustomerFieldsToSupportTickets extends AbstractMigration
{
    public function change()
    {
        $table = $this->table('support_tickets');

        $table->addColumn('customer_id', 'integer', [
            'null' => true,
            'default' => null,
            'signed' => false, // Ensure it's unsigned
            'after' => 'id',
        ]);

        $table->addColumn('customer_email', 'string', [
            'limit' => 255,
            'null' => true,
            'default' => null,
            'after' => 'customer_id',
        ]);
        $table->addColumn('copy_to', 'text', [
            'null' => true,
            'default' => null,
            'after' => 'customer_email',
            'comment' => 'Stores multiple email addresses separated by commas',
        ]);
        $table->addForeignKey('customer_id', 'customers', 'id', [
            'delete' => 'SET_NULL',
            'update' => 'CASCADE',
            'constraint' => 'fk_support_tickets_customers', // Naming the constraint explicitly
        ]);

        $table->update();
    }
}
