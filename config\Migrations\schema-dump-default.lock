a:6:{s:10:"categories";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:10:"categories";s:11:" * _columns";a:16:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"meta_title";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"meta_keyword";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"meta_description";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"icon";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"url_key";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:56:"slug : category name with hypen auto generate and stored";s:8:"baseType";N;s:9:"precision";N;}s:9:"parent_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:27:"A=Active,Inactive,D=Deleted";s:8:"baseType";N;s:9:"precision";N;}s:10:"menu_order";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:16:"fashion_category";a:9:{s:4:"type";s:12:"smallinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"created";a:7:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"modified";a:7:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}s:12:"top_category";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:2:"No";s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"is_featured_category";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:2:"No";s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:16:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:11:"description";s:4:"text";s:10:"meta_title";s:4:"text";s:12:"meta_keyword";s:4:"text";s:16:"meta_description";s:4:"text";s:4:"icon";s:6:"string";s:7:"url_key";s:6:"string";s:9:"parent_id";s:7:"integer";s:6:"status";s:6:"string";s:10:"menu_order";s:7:"integer";s:16:"fashion_category";s:12:"smallinteger";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";s:12:"top_category";s:6:"string";s:20:"is_featured_category";s:6:"string";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:15:"utf8_unicode_ci";}s:13:" * _temporary";b:0;}s:7:"modules";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:7:"modules";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}s:7:"updated";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:7:"created";s:8:"datetime";s:7:"updated";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_general_ci";}s:13:" * _temporary";b:0;}s:5:"roles";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:5:"roles";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}s:7:"updated";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:7:"created";s:8:"datetime";s:7:"updated";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_general_ci";}s:13:" * _temporary";b:0;}s:8:"settings";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:8:"settings";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:19:"theme_primary_color";a:8:{s:4:"type";s:6:"string";s:6:"length";i:25;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:21:"theme_secondary_color";a:8:{s:4:"type";s:6:"string";s:6:"length";i:25;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"theme_tertiary_color";a:8:{s:4:"type";s:6:"string";s:6:"length";i:25;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:19:"theme_primary_color";s:6:"string";s:21:"theme_secondary_color";s:6:"string";s:20:"theme_tertiary_color";s:6:"string";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_general_ci";}s:13:" * _temporary";b:0;}s:5:"users";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:5:"users";s:11:" * _columns";a:11:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"first_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"last_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"email";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"password";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"mobile";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"role_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"department";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"1";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"modified";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}}s:11:" * _typeMap";a:11:{s:2:"id";s:7:"integer";s:10:"first_name";s:6:"string";s:9:"last_name";s:6:"string";s:5:"email";s:6:"string";s:8:"password";s:6:"string";s:6:"mobile";s:6:"string";s:7:"role_id";s:7:"integer";s:10:"department";s:6:"string";s:6:"status";s:7:"boolean";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:5:"email";a:3:{s:4:"type";s:6:"unique";s:7:"columns";a:1:{i:0;s:5:"email";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_general_ci";}s:13:" * _temporary";b:0;}s:7:"widgets";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:7:"widgets";s:11:" * _columns";a:18:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:5:"title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"content";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"count";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:24:"Number of items/products";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"is_featured";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:2:"No";s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"is_deal";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:2:"No";s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"product_preference";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"start_date";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"end_date";a:7:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:13:"display_order";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:31:"A=Active, I=Inactive, D=Deleted";s:8:"baseType";N;s:9:"precision";N;}s:8:"order_by";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:2:"-1";s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"is_mobile";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:3:"Yes";s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"is_web";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:3:"Yes";s:7:"collate";s:15:"utf8_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:7:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"modified";a:7:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:19:"current_timestamp()";s:7:"comment";s:0:"";s:8:"baseType";N;}}s:11:" * _typeMap";a:18:{s:2:"id";s:7:"integer";s:5:"title";s:6:"string";s:4:"type";s:6:"string";s:7:"content";s:4:"text";s:5:"count";s:7:"integer";s:11:"is_featured";s:6:"string";s:7:"is_deal";s:6:"string";s:18:"product_preference";s:6:"string";s:11:"category_id";s:7:"integer";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:13:"display_order";s:7:"integer";s:6:"status";s:6:"string";s:8:"order_by";s:6:"string";s:9:"is_mobile";s:6:"string";s:6:"is_web";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:11:"category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:14:"widgets_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:10:"references";a:2:{i:0;s:10:"categories";i:1;s:2:"id";}s:6:"update";s:8:"restrict";s:6:"delete";s:8:"restrict";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:15:"utf8_unicode_ci";}s:13:" * _temporary";b:0;}}