<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Core\Configure;
use Cake\Core\Configure\Engine\PhpConfig;
use Cake\Core\Exception\Exception;
use Cake\I18n\Time;
//use Cake\Http\Response;
//use Cake\Http\Exception\BadRequestException;
//use Cake\Http\Exception\NotFoundException;
use Cake\View\JsonView;
use Cake\Utility\Security;
use Cake\Routing\Router;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;

class PaymentsController extends AppController
{
	public function initialize(): void
    {
        parent::initialize();
        $this->Authentication->addUnauthenticatedActions(['success','error','wavePaymentSuccess', 'wavePaymentError']);
        $this->Transactions = $this->fetchTable('Transactions');
        $this->Orders = $this->fetchTable('Orders');
        $this->PaymentMethodSettings = $this->fetchTable('PaymentMethodSettings');
        $this->loadComponent('Wave');
        $this->viewBuilder()->setLayout('web');
    }

    public function success() {
        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('success');
    }
    public function error() {
        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('error');
    }

    public function wavePaymentSuccess() {

    	$data = $this->request->getQuery();
        $client_referenceID = $data['client_reference'];

	    $transaction = $this->Transactions->find()
                ->where(['wave_client_referenceID' => $client_referenceID])
                ->first();
        $checkout_sessionID = $transaction['wave_checkout_sessionID'];

        $result = $this->Wave->retrieveCheckout($checkout_sessionID);

        //update order/transactions
        $transactionID = $result['transaction_id'];
        $checkout_status = $result['checkout_status'];
        $payment_status = 'Paid';
        $transaction->wave_checkout_status = $checkout_status;
        $transaction->payment_status = $payment_status;
        $transaction->transactionID = $transactionID;
        $this->Transactions->save($transaction);

        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('wave_payment_success');
    }

    public function wavePaymentError() {

    	$data = $this->request->getQuery();
        $client_referenceID = $data['client_reference'];

    	$transaction = $this->Transactions->find()
                ->where(['wave_client_referenceID' => $client_referenceID])
                ->first();
        $checkout_sessionID = $transaction['wave_checkout_sessionID'];

        $result = $this->Wave->retrieveCheckout($checkout_sessionID);

        //update order/transactions
        //$transactionID = $result['transaction_id'];
        $checkout_status = $result['checkout_status'];
        $payment_status = 'Failed';
        $transaction->wave_checkout_status = $checkout_status;
        $transaction->payment_status = $payment_status;
        if($result['last_payment_error']['message']){
        	$transaction->reason = $result['last_payment_error']['message'];
    	}
        $this->Transactions->save($transaction);

        if($checkout_status == 'expired') {
            $updateOrder = $this->Orders->get($transaction['order_id']);
            $updateOrder->status = 'Expired';
            $this->Orders->save($updateOrder);
        }

        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('wave_payment_error');
    }
}
