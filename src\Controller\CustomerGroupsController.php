<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;

/**
 * CustomerGroups Controller
 *
 * @property \App\Model\Table\CustomerGroupsTable $CustomerGroups
 */
class CustomerGroupsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }

    public function index()
    {
        $query = $this->CustomerGroups->find()->where(['status !=' => 'D'])->applyOptions(['order' => ['name' => 'ASC']]);;
        $customerGroups = $query->all();
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');

        $this->set(compact('customerGroups', 'status', 'statusMap'));
    }

    /**
     * View method
     *
     * @param string|null $id Customer Group id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $customerGroup = $this->CustomerGroups->get($id, contain: ['CustomerGroupMappings']);
        $statuses = Configure::read('Constants.STATUS');
        $this->set(compact('customerGroup','statuses'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $customerGroup = $this->CustomerGroups->newEmptyEntity();
        if ($this->request->is('post')) {
            $customerGroup = $this->CustomerGroups->patchEntity($customerGroup, $this->request->getData());
            if ($this->CustomerGroups->save($customerGroup)) {
                $this->Flash->success(__('The customer group has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The customer group could not be saved. Please, try again.'));
        }
        $this->set(compact('customerGroup'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Customer Group id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $customerGroup = $this->CustomerGroups->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $customerGroup = $this->CustomerGroups->patchEntity($customerGroup, $this->request->getData());
            if ($this->CustomerGroups->save($customerGroup)) {
                $this->Flash->success(__('The customer group has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The customer group could not be saved. Please, try again.'));
        }
        $statuses = Configure::read('Constants.STATUS');
        $this->set(compact('customerGroup', 'statuses'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Customer Group id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $customerGroup = $this->CustomerGroups->get($id);
        $response = ['success' => false, 'message' => 'The Customer Group could not be deleted. Please, try again.'];
        if ($customerGroup) {
            if ($this->CustomerGroups->delete($customerGroup)) {
                $response = ['success' => true, 'message' => 'The Customer Group has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Customer Group could not be deleted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Customer Group does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
