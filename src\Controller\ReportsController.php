<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;
use Cake\Core\Configure;
use DateTime;
use DateInterval;
use DatePeriod;

class ReportsController extends AppController
{
    protected $Roles;
    protected $Orders;
    protected $Customers;
    protected $Users;
    protected $Products;
    protected $Showrooms;
    protected $Transactions;
    protected $SupplierPayment;
    protected $Suppliers;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $SupplierProducts;
    protected $ShowroomStocks;
    protected $OrderItems;
    protected $Categories;
    protected $ProductCategories;
    protected $ShowroomExpenses;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin'); // Use the admin layout
        $this->loadComponent('Media');

        $this->Roles = $this->fetchTable('Roles');
        $this->Orders = $this->fetchTable('Orders');
        $this->Customers = $this->fetchTable('Customers');
        $this->Users = $this->fetchTable('Users');
        $this->Products = $this->fetchTable('Products');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->SupplierPayment = $this->fetchTable('SupplierPayment');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->ShowroomStocks = $this->fetchTable('ShowroomStocks');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Categories = $this->fetchTable('Categories');
        $this->ProductCategories = $this->fetchTable('ProductCategories');
        $this->ShowroomExpenses = $this->fetchTable('ShowroomExpenses');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'add']);
    }

    public function index()
    {
        $user = $this->Authentication->getIdentity();

        if(!empty($user))
        {
            $role = $this->Roles->get($user->role_id);

            if (strtolower($role->name) == 'admin') {
                // Admin dashboard
                return $this->redirect(['action' => 'adminDashboard']);

            } elseif (strtolower($role->name) == 'showroom') {
                // Showroom Manager dashboard
                return $this->redirect(['action' => 'showroomManagerDashboard']);
            } elseif (strtolower($role->name) == 'supervisor') {
                // Showroom Manager dashboard
                return $this->redirect(['action' => 'supervisorManagerDashboard']);
            }
        }
        else
        {
            return $this->redirect(['controller' => 'Users', 'action' => 'login']);
        }
    }

    public function orderReport()
    {
        $requested_user = $this->Authentication->getIdentity();
        $conditions = [];

        // Initialize $showrooms as an empty array
        $showrooms = [];
        $readonly = false; // Set the default value for readonly
        $selectedShowroomId = null; // Store the selected showroom ID

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'showroom manager') {
                // Fetch the showroom managed by this user
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $selectedShowroomId = $managerShowroom->id; // Store the selected showroom
                    $conditions['Orders.showroom_id'] = $managerShowroom->id;
                    // Filter showrooms for the showroom manager
                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id' => $managerShowroom->id, 'Showrooms.status' => 'A'])
                        ->all();
                    $readonly = true; // Set the field as readonly for showroom manager
                }

            } elseif (strtolower($role->name) === 'showroom supervisor') {
                // Fetch all showrooms supervised by this user
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->all()
                    ->extract('id')
                    ->toList();

                if (!empty($supervisorShowrooms)) {
                    $conditions['Orders.showroom_id IN'] = $supervisorShowrooms;
                    // Filter showrooms for the showroom supervisor
                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id IN' => $supervisorShowrooms, 'Showrooms.status' => 'A'])
                        ->all();
                }

            } else {
                // Admins will have access to all active showrooms
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->all();
            }
        }

        $currentDate = new \DateTime();
        $startDate = (clone $currentDate)->modify('-5 months')->format('Y-m-01');

        // Find top 5 showrooms based on total order count over the past 6 months
        $topShowrooms = $this->Showrooms->find()
            ->select([
                'Showrooms.id',
                'Showrooms.name',
                'total_orders' => $this->Orders->find()->func()->count('*')
            ])
            ->matching('Orders', function ($q) use ($startDate, $conditions) {
                return $q->where(array_merge($conditions, [
                    'Orders.status !=' => 'Cancelled',
                    'Orders.order_date >=' => $startDate
                ]));
            })
            ->where(['Showrooms.status' => 'A'])
            ->group(['Showrooms.id', 'Showrooms.name'])
            ->order(['total_orders' => 'DESC'])
            ->limit(5)
            ->toArray();

        $orderData = [];
        foreach ($topShowrooms as $showroom) {
            $orders = $this->Orders->find()
                ->select([
                    'month' => 'MONTH(Orders.order_date)',
                    'year' => 'YEAR(Orders.order_date)',
                    'order_count' => $this->Orders->find()->func()->count('*')
                ])
                ->where(array_merge(['Orders.showroom_id' => $showroom->id], $conditions, [
                    'Orders.status !=' => 'Cancelled',
                    'Orders.order_date >=' => $startDate
                ]))
                ->group(['year', 'month'])
                ->order(['year' => 'ASC', 'month' => 'ASC'])
                ->toArray();

            $orderData[$showroom->name] = $orders;
        }

        $orderTrendsgraphData = [];
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $currentDate = new \DateTime();
        $monthLabels = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = (clone $currentDate)->modify("-$i months");
            $monthLabels[] = $months[$date->format('n') - 1]; 
        }

        foreach ($orderData as $showroomName => $orders) {
            $orderCounts = array_fill(0, 6, 0);

            foreach ($orders as $order) {
                $orderMonthIndex = array_search($months[$order['month'] - 1], $monthLabels);
                if ($orderMonthIndex !== false) {
                    $orderCounts[$orderMonthIndex] += $order['order_count'];
                }
            }

            $orderTrendsgraphData[] = [
                'name' => $showroomName,
                'data' => $orderCounts
            ];
        }

        $OrderDetails = $this->Orders->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'full_name' => $this->Customers->Users->find()->func()->concat([
                    "Users.first_name" => 'identifier',
                    ' ' ,
                    "Users.last_name" => 'identifier',
                ]),
                'phone_number' => $this->Customers->Users->find()->func()->concat([
                    "Customers.phone_number" => 'identifier',
                ]),
                'Orders.total_amount',
                'Orders.status',
                'Orders.payment_method',
                'Orders.order_date',
                'Orders.order_type',
                'Orders.delivery_mode',
                'quantity' => $this->OrderItems->find()->func()->sum('OrderItems.quantity'),
                'transaction_status' => 'Transactions.payment_status',
                'showroom_name' => 'Showrooms.name'
            ])
            ->innerJoinWith('Customers.Users')
            ->leftJoinWith('OrderItems')
            ->leftJoinWith('Transactions')
            ->leftJoinWith('Showrooms')
            ->where($conditions) // Apply ACL conditions
            ->group(['Orders.id'])
            ->order(['Orders.id' => 'DESC'])
            ->toArray();

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');    
        $ordertypestatuses = Configure::read('Constants.ORDER_TYPE_STATUSES');    
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : ''; 

        $this->set(compact(
            'showrooms', 
            'readonly',
            'selectedShowroomId',
            'orderTrendsgraphData', 
            'monthLabels', 
            'OrderDetails', 
            'orderstatuses', 
            'paymentstatuses', 
            'ordertypestatuses', 
            'dateFormat', 
            'timeFormat', 
            'currencySymbol', 
            'decimalSeparator', 
            'thousandSeparator'
        ));   
    }

    public function filterGraphData()
    {
        $this->request->allowMethod(['post']);

        $startDate = $this->request->getData('startDate');
        $endDate = $this->request->getData('endDate');
        $months = $this->request->getData('months');
        $showroom_id = $this->request->getData('showroom_id');
        $order_status = $this->request->getData('order_status');  
        $payment_status = $this->request->getData('payment_status');  
        $order_type = $this->request->getData('order_type');  
        $delivery_mode = $this->request->getData('delivery_mode');  

        // Get the number of months specified (3 or 6)
        $numberOfMonths = count($months);

        // Fetch showrooms
        if ($showroom_id === '' || $showroom_id === null) {
            $showrooms = $this->Showrooms->find()
                ->select([
                    'Showrooms.id',
                    'Showrooms.name',
                    'total_orders' => $this->Orders->find()->func()->count('*')
                ])
                ->leftJoinWith('Orders', function ($q) use ($startDate, $endDate, $order_status, $order_type) {
                    $q = $q->where([
                        'DATE(Orders.order_date) >=' => $startDate,
                        'DATE(Orders.order_date) <=' => $endDate
                    ]);
                    if (!empty($order_status)) {
                        if (strtolower($order_status) === 'cancelled') {
                            $q->where(['Orders.status' => 'Cancelled']);
                        } else {
                            $q->where(['Orders.status' => $order_status]);
                        }
                    } else {
                        $q->where(['Orders.status !=' => 'Cancelled']);
                    }
                    if (!empty($order_type)) {
                        $q->where(['Orders.order_type' => $order_type]);
                    }
                    return $q;
                })
                ->where(['Showrooms.status' => 'A'])
                ->group(['Showrooms.id', 'Showrooms.name'])
                ->order(['total_orders' => 'DESC'])
                ->limit(5)
                ->toArray();
        } else {
            $showrooms = $this->Showrooms->find()
                ->where([
                    'Showrooms.id' => $showroom_id,
                    'Showrooms.status' => __('A')
                ])
                ->all()
                ->toArray();
        }

        $orderData = [];

        foreach ($showrooms as $showroom) {
            $query = $this->Orders->find()
                ->select([
                    'month' => 'MONTH(Orders.order_date)',
                    'year' => 'YEAR(Orders.order_date)',
                    'order_count' => $this->Orders->find()->func()->count('*')
                ])
                ->where([
                    'Orders.showroom_id' => $showroom->id,
                    'DATE(Orders.order_date) >=' => $startDate,
                    'DATE(Orders.order_date) <=' => $endDate
                ]);

            if (!empty($order_status)) {
                if (strtolower($order_status) === 'cancelled') {
                    $query->where(['Orders.status' => 'Cancelled']);
                } else {
                    $query->where(['Orders.status' => $order_status]);
                }
            } else {
                $query->where(['Orders.status !=' => 'Cancelled']);
            } 

            if (!empty($order_type)) {
                $query->where(['Orders.order_type' => $order_type]);
            }

            if (!empty($delivery_mode)) {
                $query->where(['Orders.delivery_mode' => $delivery_mode]);
            }

            if (!empty($payment_status)) {
                $query->innerJoinWith('Transactions', function ($q) use ($payment_status) {
                    return $q->where(['Transactions.payment_status' => $payment_status]);
                });
            }

            $orders = $query
                ->group(['year', 'month'])
                ->order(['year' => 'ASC', 'month' => 'ASC'])
                ->toArray();

            $orderData[$showroom->name] = $orders;
        }

        $monthLabels = [];
        $currentDate = new \DateTime();
        $monthLabels[] = $currentDate->format('M');
        for ($i = 1; $i < $numberOfMonths; $i++) {
            $currentDate->modify('-1 month');
            $monthLabels[] = $currentDate->format('M');
        }
        $monthLabels = array_reverse($monthLabels);

        $orderTrendsgraphData = [];
        foreach ($orderData as $showroomName => $orders) {
            $orderCounts = array_fill(0, count($monthLabels), 0);

            foreach ($orders as $order) {
                $orderMonthAbbr = date('M', mktime(0, 0, 0, $order['month'], 1));
                $orderMonthIndex = array_search($orderMonthAbbr, $monthLabels);

                if ($orderMonthIndex !== false) {
                    $orderCounts[$orderMonthIndex] += $order['order_count'];
                }
            }

            $orderTrendsgraphData[] = [
                'name' => $showroomName,
                'data' => $orderCounts
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'graph_data' => $orderTrendsgraphData
        ]));
    }

    public function storePerformanceReport()
    {

        $requested_user = $this->Authentication->getIdentity();
        $conditions = [];
        $expense_conditions = [];
        $showroom_expense_conditions = [];

        // Initialize $showrooms as an empty array
        $showrooms = [];
        $readonly = false; // Set the default value for readonly
        $selectedShowroomId = null; // Store the selected showroom ID

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'showroom manager') {
                // Fetch the showroom managed by this user
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $selectedShowroomId = $managerShowroom->id; // Store the selected showroom
                    $conditions['Orders.showroom_id'] = $managerShowroom->id;
                    $expense_conditions['SupplierPayment.showroom_id'] = $managerShowroom->id;
                    $showroom_expense_conditions['ShowroomExpenses.showroom_id'] = $managerShowroom->id;

                    // Filter showrooms for the showroom manager
                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id' => $managerShowroom->id, 'Showrooms.status' => 'A'])
                        ->all();
                    $readonly = true; // Set the field as readonly for showroom manager
                }

            } elseif (strtolower($role->name) === 'showroom supervisor') {
                // Fetch all showrooms supervised by this user
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->all()
                    ->extract('id')
                    ->toList();

                if (!empty($supervisorShowrooms)) {
                    $conditions['Orders.showroom_id IN'] = $supervisorShowrooms;
                    $expense_conditions['SupplierPayment.showroom_id IN'] = $supervisorShowrooms;
                    $showroom_expense_conditions['ShowroomExpenses.showroom_id IN'] = $supervisorShowrooms;

                    // Filter showrooms for the showroom supervisor
                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id IN' => $supervisorShowrooms, 'Showrooms.status' => 'A'])
                        ->all();
                }

            } else {
                // Admins will have access to all active showrooms
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->all();
            }
        }

        // echo '<pre>';print_r($conditions);die;

        // Get current month start and end date
        $currentMonthStart = FrozenTime::now()->startOfMonth();
        $currentMonthEnd = FrozenTime::now()->endOfMonth();

        // Fetch sales data for each showroom within the selected period
        $salesQuery = $this->Orders->find()
            ->select([
                'showroom_id',
                'Showrooms.name',
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
            ])
            // ->where([
            //     'Orders.order_date >=' => $currentMonthStart,
            //     'Orders.order_date <=' => $currentMonthEnd,
            //     'Orders.status !=' => 'Cancelled',  // Exclude canceled orders
            //     'Orders.showroom_id IS NOT' => null
            // ])
            ->where($conditions + [
                'Orders.order_date >=' => $currentMonthStart,
                'Orders.order_date <=' => $currentMonthEnd,
                'Orders.status !=' => 'Cancelled'
            ])
            ->group('Orders.showroom_id')
            ->contain(['Showrooms'])
            ->orderDesc('total_sales') // Sort by total sales in descending order
            ->limit(5)
            ->toArray(); 

        // echo '<pre>';print_r($salesQuery);die;

        // Fetch expenses data for each showroom within the selected period
        $expensesQuery = $this->SupplierPayment->find()
            ->select([
                'showroom_id',
                'Showrooms.name',
                'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
            ])
            ->where($expense_conditions + [
                'SupplierPayment.created >=' => $currentMonthStart,
                'SupplierPayment.created <=' => $currentMonthEnd
            ])
            ->group('SupplierPayment.showroom_id')
            ->contain(['Showrooms'])
            ->toArray();

        // echo '<pre>';print_r($expensesQuery);die;

        // Initialize an empty associative array for merged data
        $mergedData = [];

        // Merge sales data
        foreach ($salesQuery as $sale) {
            $showroomId = $sale->showroom_id;
            $mergedData[$showroomId] = [
                'name' => $sale->showroom->name ?? 'Unknown Showroom',
                'total_sales' => $sale->total_sales / 1000,  // Convert to thousands if needed
                'total_expenses' => 0  // Default to 0 for now
            ];
        }

        $showroomExpensesQuery1 = $this->ShowroomExpenses->find()
            ->select([
                'showroom_id',
                'name',
                'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
            ])
            ->where($showroom_expense_conditions + [
                'ShowroomExpenses.created >=' => $currentMonthStart,
                'ShowroomExpenses.created <=' => $currentMonthEnd,
                // 'ShowroomExpenses.showroom_id IS NOT' => null,
                'ShowroomExpenses.status' => 'A'
            ])
            ->group('ShowroomExpenses.showroom_id')
            ->toArray();

        // Prepare a lookup for showroom expenses
        $showroomExpenses = [];
        foreach ($showroomExpensesQuery1 as $expense) {
            $showroomExpenses[$expense->showroom_id] = $expense->total_expenses;
        }

        // Merge SupplierPayment expenses
        foreach ($expensesQuery as $expense) {
            $showroomId = $expense->showroom_id;

            if (!isset($mergedData[$showroomId])) {
                $mergedData[$showroomId] = [
                    'name' => $expense->showroom->name,
                    'total_sales' => 0, // Default to 0 if no sales data
                    'total_expenses' => 0
                ];
            }

            // Add SupplierPayment expenses
            $mergedData[$showroomId]['total_expenses'] += $expense->total_expenses / 1000;
        }

        // Merge ShowroomExpenses separately
        foreach ($showroomExpensesQuery1 as $expense) {
            $showroomId = $expense->showroom_id;

            if (!isset($mergedData[$showroomId])) {
                $mergedData[$showroomId] = [
                    'name' => $expense->showroom->name ?? $expense->name,
                    'total_sales' => 0, // Default to 0 if no sales data
                    'total_expenses' => 0
                ];
            }

            // Add ShowroomExpenses
            $mergedData[$showroomId]['total_expenses'] += $expense->total_expenses / 1000;
        }

        // Prepare data for chart
        $showrooms = [];
        $salesData = [];
        $expensesData = [];

        foreach ($mergedData as $data) {
            $showrooms[] = $data['name'];
            $salesData[] = $data['total_sales'];
            $expensesData[] = $data['total_expenses'];
        }

        /** PROFIT CODE **/

        // Initialize an associative array for merged data and profit margin calculation
        $profitMergedData = [];

        // Merge sales data
        foreach ($salesQuery as $sale) {
            $showroomId = $sale->showroom_id;
            $profitMergedData[$showroomId] = [
                'name' => $sale->showroom->name ?? 'Unknown Showroom',
                'total_sales' => $sale->total_sales / 1000,  // Convert to thousands if needed
                'total_expenses' => 0,  // Default to 0 for now
                'profit_margin' => 0   // Placeholder for profit margin calculation
            ];
        }

        // Merge expenses data and calculate profit margin
        foreach ($expensesQuery as $expense) {
            $showroomId = $expense->showroom_id;
            if (isset($profitMergedData[$showroomId])) {
                // If the showroom exists in both queries, add expenses and calculate profit margin
                $profitMergedData[$showroomId]['total_expenses'] = $expense->total_expenses / 1000;  // Convert to thousands if needed
            } else {
                // If only expenses exist for a showroom, initialize sales as 0
                $profitMergedData[$showroomId] = [
                    'name' => $expense->showroom->name,
                    'total_sales' => 0,
                    'total_expenses' => $expense->total_expenses / 1000,
                    'profit_margin' => 0
                ];
            }
        }

        // Calculate profit margin for each showroom
        foreach ($profitMergedData as $showroomId => $data) {
            $profitMergedData[$showroomId]['profit_margin'] = $data['total_sales'] - $data['total_expenses'];
        }

        // Prepare data for the chart
        $profitShowrooms = [];
        $profitMargins = [];

        foreach ($profitMergedData as $data) {
            $profitShowrooms[] = $data['name'];
            $profitMargins[] = $data['profit_margin'];
        }

        /** ORDER REPORTS SALES AND EXPENSES TABLE **/
        // Fetch total sales and number of orders for each showroom within the selected period
        $tableSalesQuery = $this->Orders->find()
            ->select([
                'showroom_id',
                'Showrooms.name',
                'Cities.city_name',
                // 'Showrooms.city', // Assuming there's a city field in the Showrooms table
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount'),
                'order_count' => $this->Orders->find()->func()->count('Orders.id')
            ])
            ->where($conditions + [
                // 'Orders.order_date >=' => $currentMonthStart,
                // 'Orders.order_date <=' => $currentMonthEnd,
                'Orders.status !=' => 'Cancelled',
                // 'Orders.showroom_id IS NOT' => null
            ])
            ->group('Orders.showroom_id')
            ->contain(['Showrooms.Cities'])
            ->toArray();

        // Merge sales data and initialize other fields
        foreach ($tableSalesQuery as $sale) {
            $showroomId = $sale->showroom_id;
            $tableMergedData[$showroomId] = [
                'name' => $sale->showroom->name,
                'city' => $sale->showroom->city->city_name, // Assuming city_name is in associated City table
                'total_sales' => $sale->total_sales,
                'order_count' => $sale->order_count,
                'total_expenses' => 0, // Default to 0, updated if expenses are found
                'profit_margin' => 0, // Placeholder for profit margin
                'average_sale' => $sale->order_count > 0 ? $sale->total_sales / $sale->order_count : 0
            ];
        }

        // Fetch expenses data from SupplierPayment for each showroom within the selected period
        $supplierExpensesQuery = $this->SupplierPayment->find()
            ->select([
                'showroom_id',
                'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
            ])
            ->where($expense_conditions + [
                // 'SupplierPayment.created >=' => $currentMonthStart,
                // 'SupplierPayment.created <=' => $currentMonthEnd,
                // 'SupplierPayment.showroom_id IS NOT' => null // Ensure it's linked to a showroom
            ])
            ->group('SupplierPayment.showroom_id')
            ->toArray();

        // Fetch expenses data from ShowroomExpenses for each showroom within the selected period
        $showroomExpensesQuery = $this->ShowroomExpenses->find()
            ->select([
                'showroom_id',
                'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
            ])
            ->where($showroom_expense_conditions + [
                // 'ShowroomExpenses.created >=' => $currentMonthStart,
                // 'ShowroomExpenses.created <=' => $currentMonthEnd,
                // 'ShowroomExpenses.showroom_id IS NOT' => null,
                'ShowroomExpenses.status' => 'A'
            ])
            ->group('ShowroomExpenses.showroom_id')
            ->toArray();

        // Combine expenses from both sources
        $combinedExpenses = [];
        foreach ($supplierExpensesQuery as $supplierExpense) {
            $showroomId = $supplierExpense->showroom_id;
            $combinedExpenses[$showroomId] = [
                'showroom_id' => $showroomId,
                'total_expenses' => $supplierExpense->total_expenses
            ];
        }

        foreach ($showroomExpensesQuery as $showroomExpense) {
            $showroomId = $showroomExpense->showroom_id;
            if (isset($combinedExpenses[$showroomId])) {
                $combinedExpenses[$showroomId]['total_expenses'] += $showroomExpense->total_expenses;
            } else {
                $combinedExpenses[$showroomId] = [
                    'showroom_id' => $showroomId,
                    'total_expenses' => $showroomExpense->total_expenses
                ];
            }
        }

        // Merge combined expenses data with sales/profit data
        foreach ($combinedExpenses as $expense) {
            $showroomId = $expense['showroom_id'];
            if (isset($tableMergedData[$showroomId])) {
                $tableMergedData[$showroomId]['total_expenses'] = $expense['total_expenses'];
            } else {
                // Skip entries with no sales data (name or city would be unknown)
                continue;
            }
        }

        // Calculate profit margin for each showroom
        foreach ($tableMergedData as $showroomId => &$data) {
            $data['profit_margin'] = $data['total_sales'] - $data['total_expenses'];
        }

        // Filter out entries with "Unknown" name or city
        $tableMergedData = array_filter($tableMergedData, function ($data) {
            return $data['name'] !== 'Unknown' && $data['city'] !== 'Unknown';
        });

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : ''; 
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('showrooms', 'salesData', 'expensesData', 'profitShowrooms', 'profitMargins', 'tableMergedData', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));

    }

    // public function filterStorePerformanceGraphData()
    // {
    //     $startDate = $this->request->getData('startDate');
    //     $endDate = $this->request->getData('endDate');

    //     // Fetch top 5 showrooms based on total sales
    //     $salesQuery = $this->Orders->find()
    //         ->select([
    //             'showroom_id',
    //             'Showrooms.name',
    //             'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //         ])
    //         ->where([
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate,
    //             'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //             'Orders.showroom_id IS NOT' => null
    //         ])
    //         ->group('Orders.showroom_id')
    //         ->contain(['Showrooms'])
    //         ->orderDesc('total_sales') // Sort by total sales in descending order
    //         ->limit(5) // Limit to top 5 showrooms
    //         ->toArray();

    //     // Fetch expenses data for the top 5 showrooms
    //     $showroomIds = array_column($salesQuery, 'showroom_id');

    //     if (!empty($showroomIds)) {

    //         $expensesQuery = $this->SupplierPayment->find()
    //             ->select([
    //                 'showroom_id',
    //                 'Showrooms.name',
    //                 'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
    //             ])
    //             ->where([
    //                 'SupplierPayment.created >=' => $startDate,
    //                 'SupplierPayment.created <=' => $endDate,
    //                 'SupplierPayment.showroom_id IN' => $showroomIds // Only for top 5 showrooms
    //             ])
    //             ->group('SupplierPayment.showroom_id')
    //             ->contain(['Showrooms'])
    //             ->toArray();

    //         // Fetch expenses from ShowroomExpenses
    //         $showroomExpensesQuery = $this->ShowroomExpenses->find()
    //             ->select([
    //                 'showroom_id',
    //                 'name',
    //                 'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
    //             ])
    //             ->where([
    //                 'ShowroomExpenses.created >=' => $startDate,
    //                 'ShowroomExpenses.created <=' => $endDate,
    //                 'ShowroomExpenses.showroom_id IN' => $showroomIds,
    //                 'ShowroomExpenses.status' => 'A'
    //             ])
    //             ->group('ShowroomExpenses.showroom_id')
    //             ->toArray();

    //     } else {

    //         $expensesQuery = [];
    //         $showroomExpensesQuery = [];
    //     }

    //     // Prepare a lookup for showroom expenses
    //     $showroomExpenses = [];
    //     foreach ($showroomExpensesQuery as $expense) {
    //         $showroomExpenses[$expense->showroom_id] = $expense->total_expenses;
    //     }


    //     // Initialize an empty associative array for merged data
    //     $mergedData = [];

    //     // Merge sales data
    //     foreach ($salesQuery as $sale) {
    //         $showroomId = $sale->showroom_id;
    //         $mergedData[$showroomId] = [
    //             'name' => $sale->showroom->name,
    //             'total_sales' => $sale->total_sales / 1000, // Convert to thousands
    //             'total_expenses' => 0 // Default to 0
    //         ];
    //     }

    //     // Merge SupplierPayment expenses
    //     foreach ($expensesQuery as $expense) {
    //         $showroomId = $expense->showroom_id;

    //         if (!isset($mergedData[$showroomId])) {
    //             $mergedData[$showroomId] = [
    //                 'name' => $expense->showroom->name,
    //                 'total_sales' => 0, // Default to 0 if no sales data
    //                 'total_expenses' => 0
    //             ];
    //         }

    //         // Add SupplierPayment expenses
    //         $mergedData[$showroomId]['total_expenses'] += $expense->total_expenses / 1000;
    //     }

    //     // Merge ShowroomExpenses separately
    //     foreach ($showroomExpensesQuery as $expense) {
    //         $showroomId = $expense->showroom_id;

    //         if (!isset($mergedData[$showroomId])) {
    //             $mergedData[$showroomId] = [
    //                 'name' => $expense->showroom->name ?? $expense->name,
    //                 'total_sales' => 0, // Default to 0 if no sales data
    //                 'total_expenses' => 0
    //             ];
    //         }

    //         // Add ShowroomExpenses
    //         $mergedData[$showroomId]['total_expenses'] += $expense->total_expenses / 1000;
    //     }

    //     // Prepare data for the chart
    //     $showrooms = [];
    //     $salesData = [];
    //     $expensesData = [];
    //     $profitShowrooms = [];
    //     $profitMargins = [];

    //     foreach ($mergedData as $data) {
    //         $showrooms[] = $data['name'];
    //         $salesData[] = $data['total_sales'];
    //         $expensesData[] = $data['total_expenses'];
    //         $profitShowrooms[] = $data['name']; // Add to profit showrooms
    //         $profitMargins[] = $data['total_sales'] - $data['total_expenses']; // Calculate profit margin
    //     }

    //     // Prepare the response
    //     $responseData = [
    //         'showrooms' => $showrooms,
    //         'salesData' => $salesData,
    //         'expensesData' => $expensesData,
    //         'profitShowrooms' => $profitShowrooms,
    //         'profitMargins' => $profitMargins
    //     ];

    //     // Return JSON response
    //     return $this->response->withType('application/json')->withStringBody(json_encode($responseData));
    // }

    public function filterStorePerformanceGraphData()
    {
        $requested_user = $this->Authentication->getIdentity();
        $conditions = [];
        $expense_conditions = [];
        $showroom_expense_conditions = [];
        $showrooms = [];
        $readonly = false;
        $selectedShowroomId = null;

        $startDate = $this->request->getData('startDate');
        $endDate = $this->request->getData('endDate');

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'showroom manager') {
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $selectedShowroomId = $managerShowroom->id;
                    $conditions['Orders.showroom_id'] = $managerShowroom->id;
                    $expense_conditions['SupplierPayment.showroom_id'] = $managerShowroom->id;
                    $showroom_expense_conditions['ShowroomExpenses.showroom_id'] = $managerShowroom->id;

                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id' => $managerShowroom->id, 'Showrooms.status' => 'A'])
                        ->all();
                    $readonly = true;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {

                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->enableHydration(false) // So we get arrays, not entities
                    ->all()
                    ->toList();

                $supervisorShowrooms = array_column($supervisorShowrooms, 'id');

                if (!empty($supervisorShowrooms)) {
                    $conditions['Orders.showroom_id IN'] = $supervisorShowrooms;
                    $expense_conditions['SupplierPayment.showroom_id IN'] = $supervisorShowrooms;
                    $showroom_expense_conditions['ShowroomExpenses.showroom_id IN'] = $supervisorShowrooms;

                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id IN' => $supervisorShowrooms, 'Showrooms.status' => 'A'])
                        ->all();
                }
            } else {
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->all();
            }
        }

        // Fetch top 5 showrooms by sales
        $salesQuery = $this->Orders->find()
            ->select([
                'showroom_id',
                'Showrooms.name',
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.status !=' => 'Cancelled',
            ])
            ->group('Orders.showroom_id')
            ->contain(['Showrooms'])
            ->orderDesc('total_sales')
            ->limit(5)
            ->toArray();

        // Restrict future queries to only these top 5
        $showroomIds = array_column($salesQuery, 'showroom_id');

        if (!empty($showroomIds)) {
            $expense_conditions['SupplierPayment.showroom_id IN'] = $showroomIds;
            $showroom_expense_conditions['ShowroomExpenses.showroom_id IN'] = $showroomIds;

            $expensesQuery = $this->SupplierPayment->find()
                ->select([
                    'showroom_id',
                    'Showrooms.name',
                    'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
                ])
                ->where($expense_conditions + [
                    'SupplierPayment.created >=' => $startDate,
                    'SupplierPayment.created <=' => $endDate
                ])
                ->group('SupplierPayment.showroom_id')
                ->contain(['Showrooms'])
                ->toArray();

            $showroomExpensesQuery = $this->ShowroomExpenses->find()
                ->select([
                    'showroom_id',
                    'name',
                    'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
                ])
                ->where($showroom_expense_conditions + [
                    'ShowroomExpenses.created >=' => $startDate,
                    'ShowroomExpenses.created <=' => $endDate,
                    'ShowroomExpenses.status' => 'A'
                ])
                ->group('ShowroomExpenses.showroom_id')
                ->toArray();
        } else {
            $expensesQuery = [];
            $showroomExpensesQuery = [];
        }

        // Merge data
        $mergedData = [];

        foreach ($salesQuery as $sale) {
            $showroomId = $sale->showroom_id;
            $mergedData[$showroomId] = [
                'name' => $sale->showroom->name ?? 'Unknown Showroom',
                'total_sales' => $sale->total_sales / 1000,
                'total_expenses' => 0
            ];
        }

        foreach ($expensesQuery as $expense) {
            $showroomId = $expense->showroom_id;
            if (!isset($mergedData[$showroomId])) {
                $mergedData[$showroomId] = [
                    'name' => $expense->showroom->name ?? 'Unknown Showroom',
                    'total_sales' => 0,
                    'total_expenses' => 0
                ];
            }
            $mergedData[$showroomId]['total_expenses'] += $expense->total_expenses / 1000;
        }

        foreach ($showroomExpensesQuery as $expense) {
            $showroomId = $expense->showroom_id;
            if (!isset($mergedData[$showroomId])) {
                $mergedData[$showroomId] = [
                    'name' => $expense->name ?? 'Unknown Showroom',
                    'total_sales' => 0,
                    'total_expenses' => 0
                ];
            }
            $mergedData[$showroomId]['total_expenses'] += $expense->total_expenses / 1000;
        }

        // Format response
        $showrooms = [];
        $salesData = [];
        $expensesData = [];
        $profitShowrooms = [];
        $profitMargins = [];

        foreach ($mergedData as $data) {
            $showrooms[] = $data['name'];
            $salesData[] = $data['total_sales'];
            $expensesData[] = $data['total_expenses'];
            $profitShowrooms[] = $data['name'];
            $profitMargins[] = $data['total_sales'] - $data['total_expenses'];
        }

        $responseData = [
            'showrooms' => $showrooms,
            'salesData' => $salesData,
            'expensesData' => $expensesData,
            'profitShowrooms' => $profitShowrooms,
            'profitMargins' => $profitMargins
        ];

        return $this->response->withType('application/json')->withStringBody(json_encode($responseData));
    }

    public function saleTrendsReport()
    {
        $requested_user = $this->Authentication->getIdentity();
        $conditions = [];
        $showroom_conditions = [];
        $showrooms = [];
        $readonly = false;
        $selectedShowroomId = null;

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'showroom manager') {
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $selectedShowroomId = $managerShowroom->id;
                    $conditions['Orders.showroom_id'] = $managerShowroom->id;
                    $showroom_conditions['Showrooms.id'] = $managerShowroom->id;

                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id' => $managerShowroom->id, 'Showrooms.status' => 'A'])
                        ->all();
                    $readonly = true;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {

                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->enableHydration(false) // So we get arrays, not entities
                    ->all()
                    ->toList();

                $supervisorShowrooms = array_column($supervisorShowrooms, 'id');

                if (!empty($supervisorShowrooms)) {
                    $conditions['Orders.showroom_id IN'] = $supervisorShowrooms;
                    $showroom_conditions['Showrooms.id IN'] = $supervisorShowrooms;

                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id IN' => $supervisorShowrooms, 'Showrooms.status' => 'A'])
                        ->all();
                }
            } else {
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->all();
            }
        }

        // Get current month start and end date
        $currentMonthStart = FrozenTime::now()->startOfMonth();
        $currentMonthEnd = FrozenTime::now()->endOfMonth();

        // Fetch the total number of orders grouped by order type (Online, Showroom)
        $orderCountsByType = $this->Orders->find()
            ->select([
                'order_type',
                'order_count' => $this->Orders->find()->func()->count('*')
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $currentMonthStart,
                'Orders.order_date <=' => $currentMonthEnd,
                'Orders.status !=' => 'Cancelled'  // Exclude canceled orders if necessary
            ])
            ->group('Orders.order_type')
            ->toArray();

        // Initialize variables to store order counts for Online and Showroom
        $onlineOrderCount = 0;
        $showroomOrderCount = 0;

        // Assign order counts based on order type
        foreach ($orderCountsByType as $order) {
            if ($order->order_type === 'Online') {
                $onlineOrderCount = $order->order_count;
            } elseif ($order->order_type === 'Showroom') {
                $showroomOrderCount = $order->order_count;
            }
        }

        // Calculate total orders and percentages
        $totalOrders = $onlineOrderCount + $showroomOrderCount;
        $onlineOrderPercent = $totalOrders > 0 ? ($onlineOrderCount / $totalOrders) * 100 : 0;
        $showroomOrderPercent = $totalOrders > 0 ? ($showroomOrderCount / $totalOrders) * 100 : 0;


        /** GET SHOWROOM SALES TREND GRAPH DATA **/
        // Define the date range
        $startDate = (new \DateTime('first day of -5 months'))->format('Y-m-d');
        $endDate = (new \DateTime('last day of this month'))->format('Y-m-d');

        // Initialize arrays with zero values to handle empty months
        $showroomSalemonths = [];
        $onlineCounts = [];
        $showroomCounts = [];
        $totalCounts = [];

        // Loop to get the last six months' names in order
        for ($i = 5; $i >= 0; $i--) {
            $date = new \DateTime("first day of -$i months");
            $monthName = $date->format('M');  // Format as "Jan", "Feb", etc.
            $showroomSalemonths[] = $monthName;
            $onlineCounts[$monthName] = 0;
            $showroomCounts[$monthName] = 0;
            $totalCounts[$monthName] = 0;
        }

        // Query for Online order counts by month over the last six months
        $onlineOrders = $this->Orders->find()
            ->select([
                'month' => 'DATE_FORMAT(Orders.order_date, "%b")',
                'online_count' => $this->Orders->find()->func()->count('*')
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $currentMonthStart,
                'Orders.order_date <=' => $currentMonthEnd,
                'Orders.order_type' => 'Online',
                'Orders.status !=' => 'Cancelled'
            ])
            ->group('month')
            ->order(['Orders.order_date' => 'ASC'])
            ->toArray();

        // Query for Showroom order counts by month over the last six months
        $showroomOrders = $this->Orders->find()
            ->select([
                'month' => 'DATE_FORMAT(Orders.order_date, "%b")',
                'showroom_count' => $this->Orders->find()->func()->count('*')
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $currentMonthStart,
                'Orders.order_date <=' => $currentMonthEnd,
                'Orders.order_type' => 'Showroom',
                'Orders.status !=' => 'Cancelled'
            ])
            ->group('month')
            ->order(['Orders.order_date' => 'ASC'])
            ->toArray();

        // Merge online order counts into the initialized array
        foreach ($onlineOrders as $order) {
            $month = $order->month;
            $onlineCounts[$month] = $order->online_count;
            $totalCounts[$month] += $order->online_count;  // Add to total count
        }

        // Merge showroom order counts into the initialized array
        foreach ($showroomOrders as $order) {
            $month = $order->month;
            $showroomCounts[$month] = $order->showroom_count;
            $totalCounts[$month] += $order->showroom_count;  // Add to total count
        }

        // Convert associative arrays to indexed arrays for JavaScript
        $onlineCounts = array_values($onlineCounts);
        $showroomCounts = array_values($showroomCounts);
        $totalCounts = array_values($totalCounts);

        // Find top 5 showrooms based on total order count over the past 6 months
        $showrooms = $this->Showrooms->find()
            ->select([
                'Showrooms.id',
                'Showrooms.name',
                'total_orders' => $this->Orders->find()->func()->count('*')
            ])
            ->leftJoinWith('Orders', function ($q) use ($startDate) {
                return $q->where([
                    'Orders.status !=' => 'Cancelled',
                    'Orders.order_date >=' => $startDate
                ]);
            })
            ->where($showroom_conditions + ['Showrooms.status' => 'A'])
            ->group(['Showrooms.id', 'Showrooms.name'])
            ->order(['total_orders' => 'DESC'])
            ->limit(5)
            ->toArray();

        $orderData = [];
        foreach ($showrooms as $showroom) {
            $orders = $this->Orders->find()
                ->select([
                    'month' => 'MONTH(Orders.order_date)',
                    'year' => 'YEAR(Orders.order_date)',
                    'order_count' => $this->Orders->find()->func()->count('*')
                ])
                ->where([
                    'Orders.showroom_id' => $showroom->id,
                    'Orders.status !=' => 'Cancelled',
                    'Orders.order_date >=' => $currentMonthStart
                ])
                ->group(['year', 'month'])
                ->order(['year' => 'ASC', 'month' => 'ASC'])
                ->toArray();

            $orderData[$showroom->name] = $orders;
        }    

        $orderTrendsgraphData = [];
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $currentDate = new \DateTime();
        $monthLabels = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = (clone $currentDate)->modify("-$i months");
            $monthLabels[] = $months[$date->format('n') - 1]; // Get month name and year
        }

        foreach ($orderData as $showroomName => $orders) {
            $orderCounts = array_fill(0, 6, 0); // Initialize an array with 6 slots (one for each month)
            
            foreach ($orders as $order) {
                // Get the month index for the current order (0 = current month, 1 = previous month, etc.)
                $orderMonthIndex = array_search($months[$order['month'] - 1], $monthLabels);
                if ($orderMonthIndex !== false) {
                    $orderCounts[$orderMonthIndex] += $order['order_count'];
                }
            }

            $orderTrendsgraphData[] = [
                'name' => $showroomName,
                'data' => $orderCounts
            ];
        }

        $this->set(compact('onlineOrderPercent', 'showroomOrderPercent', 'showroomSalemonths', 'onlineCounts', 'showroomCounts', 'totalCounts', 'monthLabels', 'orderTrendsgraphData'));
    }

    public function filterSaleTrendsGraphData()
    {

        $requested_user = $this->Authentication->getIdentity();
        $conditions = [];
        $showroom_conditions = [];
        $showrooms = [];
        $readonly = false;
        $selectedShowroomId = null;

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'showroom manager') {
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $selectedShowroomId = $managerShowroom->id;
                    $conditions['Orders.showroom_id'] = $managerShowroom->id;
                    $showroom_conditions['Showrooms.id'] = $managerShowroom->id;

                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id' => $managerShowroom->id, 'Showrooms.status' => 'A'])
                        ->all();
                    $readonly = true;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {

                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->enableHydration(false) // So we get arrays, not entities
                    ->all()
                    ->toList();

                $supervisorShowrooms = array_column($supervisorShowrooms, 'id');

                if (!empty($supervisorShowrooms)) {
                    $conditions['Orders.showroom_id IN'] = $supervisorShowrooms;
                    $showroom_conditions['Showrooms.id IN'] = $supervisorShowrooms;

                    $showrooms = $this->Showrooms->find()
                        ->where(['Showrooms.id IN' => $supervisorShowrooms, 'Showrooms.status' => 'A'])
                        ->all();
                }
            } else {
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->all();
            }
        }

        $startDate = $this->request->getData('startDate');
        $endDate = $this->request->getData('endDate');

        // Fetch the total number of orders grouped by order type (Online, Showroom)
        $orderCountsByType = $this->Orders->find()
            ->select([
                'order_type',
                'order_count' => $this->Orders->find()->func()->count('*')
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.status !=' => 'Cancelled'  // Exclude canceled orders if necessary
            ])
            ->group('Orders.order_type')
            ->toArray();

        // Initialize variables to store order counts for Online and Showroom
        $onlineOrderCount = 0;
        $showroomOrderCount = 0;

        // Assign order counts based on order type
        foreach ($orderCountsByType as $order) {
            if ($order->order_type === 'Online') {
                $onlineOrderCount = $order->order_count;
            } elseif ($order->order_type === 'Showroom') {
                $showroomOrderCount = $order->order_count;
            }
        }

        // Calculate total orders and percentages
        $totalOrders = $onlineOrderCount + $showroomOrderCount;
        $onlineOrderPercent = $totalOrders > 0 ? ($onlineOrderCount / $totalOrders) * 100 : 0;
        $showroomOrderPercent = $totalOrders > 0 ? ($showroomOrderCount / $totalOrders) * 100 : 0;

        // Initialize arrays for each month with zero values to handle empty months

        $saleStartDate = new \DateTime($startDate);
        $saleEndDate = new \DateTime($endDate);

        // Initialize the arrays for months and counts
        $showroomSalemonths = [];
        $onlineCounts = [];
        $showroomCounts = [];
        $totalCounts = [];

        // Generate months between startDate and endDate
        $interval = new \DateInterval('P1M');
        $period = new \DatePeriod($saleStartDate, $interval, $saleEndDate->modify('first day of next month'));

        foreach ($period as $date) {
            $monthName = $date->format('M');
            $showroomSalemonths[] = $monthName;
            $onlineCounts[$monthName] = 0;
            $showroomCounts[$monthName] = 0;
            $totalCounts[$monthName] = 0;
        }

        // Query for Online order counts by month
        $onlineOrders = $this->Orders->find()
            ->select([
                'month' => 'DATE_FORMAT(Orders.order_date, "%b")',
                'online_count' => $this->Orders->find()->func()->count('*')
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.order_type' => 'Online',
                'Orders.status !=' => 'Cancelled'
            ])
            ->group('month')
            ->order(['Orders.order_date' => 'ASC'])
            ->toArray();

        // Query for Showroom order counts by month
        $showroomOrders = $this->Orders->find()
            ->select([
                'month' => 'DATE_FORMAT(Orders.order_date, "%b")',
                'showroom_count' => $this->Orders->find()->func()->count('*')
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.order_type' => 'Showroom',
                'Orders.status !=' => 'Cancelled'
            ])
            ->group('month')
            ->order(['Orders.order_date' => 'ASC'])
            ->toArray();

        // Merge online order counts into the initialized array
        foreach ($onlineOrders as $order) {
            $month = $order->month;
            $onlineCounts[$month] = $order->online_count;
            $totalCounts[$month] += $order->online_count;  // Add to total count
        }

        // Merge showroom order counts into the initialized array
        foreach ($showroomOrders as $order) {
            $month = $order->month;
            $showroomCounts[$month] = $order->showroom_count;
            $totalCounts[$month] += $order->showroom_count;  // Add to total count
        }

        // Convert associative arrays to indexed arrays for JavaScript
        $onlineCounts = array_values($onlineCounts);
        $showroomCounts = array_values($showroomCounts);
        $totalCounts = array_values($totalCounts);

        /** SHOWROOM SALES COMPARISON **/
        $months = $this->request->getData('months');

        $numberOfMonths = count($months);

        // Find top 5 showrooms based on total order count over the past 6 months
        $showrooms = $this->Showrooms->find()
            ->select([
                'Showrooms.id',
                'Showrooms.name',
                'total_orders' => $this->Orders->find()->func()->count('*')
            ])
            ->leftJoinWith('Orders', function ($q) use ($startDate) {
                return $q->where([
                    'Orders.status !=' => 'Cancelled',
                    'Orders.order_date >=' => $startDate
                ]);
            })
            ->where($showroom_conditions + ['Showrooms.status' => 'A'])
            ->group(['Showrooms.id', 'Showrooms.name'])
            ->order(['total_orders' => 'DESC'])
            ->limit(5)
            ->toArray();

        $orderData = [];

        foreach ($showrooms as $showroom) {
            // Fetch orders for each showroom within the specified date range and order status
            $query = $this->Orders->find()
                ->select([
                    'month' => 'MONTH(Orders.order_date)',
                    'year' => 'YEAR(Orders.order_date)',
                    'order_count' => $this->Orders->find()->func()->count('*')
                ])
                ->where([
                    'Orders.showroom_id' => $showroom->id,
                    'DATE(Orders.order_date) >=' => $startDate,
                    'DATE(Orders.order_date) <=' => $endDate
                ]);

            // Group by month and year and order by them
            $orders = $query
                ->group(['year', 'month'])
                ->order(['year' => 'ASC', 'month' => 'ASC'])
                ->toArray();

            $orderData[$showroom->name] = $orders;
        }

        // Create month labels based on the number of months specified
        $monthLabels = [];
        // Get the current date
        $currentDate = new \DateTime();

        // Include the current month in the labels first
        $monthLabels[] = $currentDate->format('M'); // Format: "10 2024"
        for ($i = 1; $i < $numberOfMonths; $i++) {
            $currentDate->modify('-1 month'); // Move to the previous month
            $monthLabels[] = $currentDate->format('M');
        }

        $monthLabels = array_reverse($monthLabels);

        $orderTrendsgraphData = [];
        foreach ($orderData as $showroomName => $orders) {
            // Initialize the order counts array with zero for each month label
            $orderCounts = array_fill(0, count($monthLabels), 0);

            foreach ($orders as $order) {
                // Create a string for the order month in the format of month abbreviation (e.g., "Oct")
                $orderMonthAbbr = date('M', mktime(0, 0, 0, $order['month'], 1)); // Convert month number to abbreviation

                // Check if the order month abbreviation exists in the monthLabels
                $orderMonthIndex = array_search($orderMonthAbbr, $monthLabels);

                if ($orderMonthIndex !== false) {
                    $orderCounts[$orderMonthIndex] += $order['order_count']; // Increment the count for the respective month
                }
            }

            $orderTrendsgraphData[] = [
                'name' => $showroomName,
                'data' => $orderCounts // Order counts mapped to the respective months
            ];
        }

        // Prepare the response
        $responseData = [
            'onlineOrderPercent' => $onlineOrderPercent,
            'showroomOrderPercent' => $showroomOrderPercent,
            'showroomSalemonths' => $showroomSalemonths,
            'onlineCounts' => $onlineCounts,
            'showroomCounts' => $showroomCounts,
            'totalCounts' => $totalCounts,
            'monthLabels' => $monthLabels,
            'orderTrendsgraphData' => $orderTrendsgraphData
        ];

        // Return JSON response
        return $this->response->withType('application/json')->withStringBody(json_encode($responseData));
    }

    public function salePersonPerformanceReport()
    {
        $requested_user = $this->Authentication->getIdentity();

        // Get user role
        $userWithRole = $this->Users->get($requested_user->id, [
            'contain' => ['Roles']
        ]);
        $userRole = $userWithRole->role->name;

        // echo '<pre>';print_r($userRole);die;

        $salespersons = [];
        $salespersonIds = [];
        $conditions = [];
        $showroom_conditions = [];

        if (strtolower($userRole) === 'showroom manager') {
            $managerShowroom = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_manager' => $requested_user->id])
                ->first();

            if ($managerShowroom) {
                $selectedShowroomId = $managerShowroom->id;
                $conditions['Orders.showroom_id'] = $managerShowroom->id;
                $showroom_conditions['Showrooms.id'] = $managerShowroom->id;

                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.id' => $managerShowroom->id, 'Showrooms.status' => 'A'])
                    ->first();

                // echo "<pre>";print_r($showrooms->id);die;

                $readonly = true;

                // Get salespersons for the manager's showroom
                $salespersons = $this->Showrooms->ShowroomUsers->find()
                    ->where([
                        'ShowroomUsers.showroom_id' => $showrooms->id,
                        'ShowroomUsers.status' => 'A'
                    ])
                    // ->contain([
                    //     'Users' => function ($q) {
                    //         return $q->contain(['Roles'])
                    //             ->where(['Roles.name' => 'Sales Person']);
                    //     }
                    // ])
                    ->all();

                // echo "<pre>";print_r($salespersons);die;
            }

        } elseif (strtolower($userRole) === 'showroom supervisor') {

            $supervisorShowrooms = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_supervisor' => $requested_user->id])
                ->enableHydration(false)
                ->all()
                ->toList();

            $supervisorShowroomIds = array_column($supervisorShowrooms, 'id');

            if (!empty($supervisorShowroomIds)) {
                $conditions['Orders.showroom_id IN'] = $supervisorShowroomIds;
                $showroom_conditions['Showrooms.id IN'] = $supervisorShowroomIds;

                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.id IN' => $supervisorShowroomIds, 'Showrooms.status' => 'A'])
                    ->all();

                // Get only users with role "Sales Person" assigned to the supervisor's showrooms
                $salespersons = $this->Showrooms->ShowroomUsers->find()
                    ->where([
                        'ShowroomUsers.showroom_id IN' => $supervisorShowroomIds,
                        'ShowroomUsers.status' => 'A'
                    ])
                    // ->contain([
                    //     'Users' => function ($q) {
                    //         return $q->contain(['Roles'])
                    //             ->where(['Roles.name' => 'Sales Person']);
                    //     }
                    // ])
                    ->all();
            }
        }

        // echo '<pre>';print_r($conditions);die;

        if (!empty($salespersons)) {
            foreach ($salespersons as $sp) {
                if (!empty($sp->user_id)) {
                    $salespersonIds[] = $sp->user_id;
                }
            }
        }

        // echo '<pre>';print_r($salespersonIds);die;

        // Get current month start and end date
        $currentMonthStart = FrozenTime::now()->startOfMonth();
        $currentMonthEnd = FrozenTime::now()->endOfMonth();

        // $salesData = $this->Orders->find()
        //     ->select([
        //         'sales_person_id',
        //         'full_name' => $this->Orders->find()->func()->concat([
        //             'SalesPersons.first_name' => 'identifier',
        //             ' ',
        //             'SalesPersons.last_name' => 'identifier'
        //         ]),
        //         'order_count' => $this->Orders->find()->func()->count('*')
        //     ])
        //     ->contain(['SalesPersons']) // Use the defined alias
        //     ->where([
        //         'Orders.order_date >=' => $currentMonthStart,
        //         'Orders.order_date <=' => $currentMonthEnd,
        //         'Orders.order_type' => 'Showroom',
        //         'Orders.status !=' => 'Cancelled'
        //     ])
        //     ->group(['sales_person_id'])
        //     ->order(['order_count' => 'DESC'])
        //     ->toArray();

        $salesDataQuery = $this->Orders->find()
            ->select([
                'sales_person_id',
                'full_name' => $this->Orders->find()->func()->concat([
                    'SalesPersons.first_name' => 'identifier',
                    ' ',
                    'SalesPersons.last_name' => 'identifier'
                ]),
                'order_count' => $this->Orders->find()->func()->count('*')
            ])
            ->contain([
                'SalesPersons' => function ($q) {
                    return $q->select(['id', 'first_name', 'last_name']);
                }
            ])
            ->where($conditions + [
                'Orders.order_date >=' => $currentMonthStart,
                'Orders.order_date <=' => $currentMonthEnd,
                'Orders.order_type' => 'Showroom',
                'Orders.status !=' => 'Cancelled',
                (!empty($salespersonIds) ? ['Orders.sales_person_id IN' => $salespersonIds] : [])
            ])
            ->group(['Orders.sales_person_id'])
            ->order(['order_count' => 'DESC']);

        $salesData = $salesDataQuery->toArray();

        // echo "<pre>";print_r($salesData);die;

        // Prepare data for the chart
        $salesPersons = [];
        $orderCounts = [];
        foreach ($salesData as $data) {
            $salesPersons[] = !empty($data->full_name) ? $data->full_name : 'Unknown';
            $orderCounts[] = $data->order_count;
        }

        $salesTableData = $this->Orders->find()
            ->select([
                'sales_person_name' => $this->Orders->find()->func()->concat([
                    'SalesPersons.first_name' => 'identifier',
                    ' ',
                    'SalesPersons.last_name' => 'identifier'
                ]),
                'showroom_name' => 'Showrooms.name',
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount'), // Replace 'total_amount' if needed
                'total_orders' => $this->Orders->find()->func()->count('*')
            ])
            ->contain(['SalesPersons', 'Showrooms']) // Use 'SalesPersons' instead of 'Users'
            ->where($conditions + [
                // 'Orders.order_date >=' => $currentMonthStart,
                // 'Orders.order_date <=' => $currentMonthEnd,
                'Orders.order_type' => 'Showroom',
                'Orders.status !=' => 'Cancelled',
                // Filter by salespersonIds if not empty
                (!empty($salespersonIds) ? ['Orders.sales_person_id IN' => $salespersonIds] : [])
            ])
            ->group(['sales_person_id', 'Showrooms.id'])
            ->order(['total_sales' => 'DESC']) // Order by highest sales
            ->toArray();

        foreach ($salesTableData as $data) {
            // Calculate average sales value and handle division by zero
            $data->average_sales_value = $data->total_orders ? ($data->total_sales / $data->total_orders) : 0;
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('salesPersons', 'orderCounts', 'salesTableData', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }

    public function filterSalesPersonPerformance()
    {
        $requested_user = $this->Authentication->getIdentity();

        // Get user role
        $userWithRole = $this->Users->get($requested_user->id, [
            'contain' => ['Roles']
        ]);
        $userRole = $userWithRole->role->name;

        // echo '<pre>';print_r($userRole);die;

        $salespersons = [];
        $salespersonIds = [];
        $conditions = [];
        $showroom_conditions = [];

        if (strtolower($userRole) === 'showroom manager') {
            $managerShowroom = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_manager' => $requested_user->id])
                ->first();

            if ($managerShowroom) {
                $selectedShowroomId = $managerShowroom->id;
                $conditions['Orders.showroom_id'] = $managerShowroom->id;
                $showroom_conditions['Showrooms.id'] = $managerShowroom->id;

                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.id' => $managerShowroom->id, 'Showrooms.status' => 'A'])
                    ->first();

                // echo "<pre>";print_r($showrooms->id);die;

                $readonly = true;

                // Get salespersons for the manager's showroom
                $salespersons = $this->Showrooms->ShowroomUsers->find()
                    ->where([
                        'ShowroomUsers.showroom_id' => $showrooms->id,
                        'ShowroomUsers.status' => 'A'
                    ])
                    // ->contain([
                    //     'Users' => function ($q) {
                    //         return $q->contain(['Roles'])
                    //             ->where(['Roles.name' => 'Sales Person']);
                    //     }
                    // ])
                    ->all();

                // echo "<pre>";print_r($salespersons);die;
            }

        } elseif (strtolower($userRole) === 'showroom supervisor') {

            $supervisorShowrooms = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_supervisor' => $requested_user->id])
                ->enableHydration(false)
                ->all()
                ->toList();

            $supervisorShowroomIds = array_column($supervisorShowrooms, 'id');

            if (!empty($supervisorShowroomIds)) {
                $conditions['Orders.showroom_id IN'] = $supervisorShowroomIds;
                $showroom_conditions['Showrooms.id IN'] = $supervisorShowroomIds;

                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.id IN' => $supervisorShowroomIds, 'Showrooms.status' => 'A'])
                    ->all();

                // Get only users with role "Sales Person" assigned to the supervisor's showrooms
                $salespersons = $this->Showrooms->ShowroomUsers->find()
                    ->where([
                        'ShowroomUsers.showroom_id IN' => $supervisorShowroomIds,
                        'ShowroomUsers.status' => 'A'
                    ])
                    // ->contain([
                    //     'Users' => function ($q) {
                    //         return $q->contain(['Roles'])
                    //             ->where(['Roles.name' => 'Sales Person']);
                    //     }
                    // ])
                    ->all();
            }
        }

        // echo '<pre>';print_r($conditions);die;

        if (!empty($salespersons)) {
            foreach ($salespersons as $sp) {
                if (!empty($sp->user_id)) {
                    $salespersonIds[] = $sp->user_id;
                }
            }
        }

        $startDate = $this->request->getData('startDate');
        $endDate = $this->request->getData('endDate');

        // Query for total order sales per sales person within the date range

        $salesData = $this->Orders->find()
            ->select([
                'sales_person_id',
                'full_name' => $this->Orders->find()->func()->concat([
                    'SalesPersons.first_name' => 'identifier',
                    ' ',
                    'SalesPersons.last_name' => 'identifier'
                ]),
                'order_count' => $this->Orders->find()->func()->count('*')
            ])
            ->contain(['SalesPersons'])  // Use 'SalesPersons' instead of 'Users'
            ->where($conditions + [
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.order_type' => 'Showroom',
                'Orders.status !=' => 'Cancelled',  // Exclude cancelled orders
                (!empty($salespersonIds) ? ['Orders.sales_person_id IN' => $salespersonIds] : [])
            ])
            ->group(['sales_person_id'])
            ->order(['order_count' => 'DESC'])  // Order by highest sales
            ->toArray();

        // Prepare data for the chart
        $salesPersons = [];
        $orderCounts = [];
        foreach ($salesData as $data) {
            $salesPersons[] = !empty($data->full_name) ? $data->full_name : 'Unknown';
            $orderCounts[] = $data->order_count;
        }

        // Prepare the response
        $responseData = [
            'salesPersons' => $salesPersons,
            'orderCounts' => $orderCounts
        ];

        // Return JSON response
        return $this->response->withType('application/json')->withStringBody(json_encode($responseData));
    }

}
