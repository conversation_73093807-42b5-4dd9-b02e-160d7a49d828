<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;
use Cake\Log\Log;
use Cake\Utility\Security;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class DriversController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Users;
    protected $Drivers;
    protected $Shipments;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Users = $this->fetchTable('Users');
        $this->Drivers = $this->fetchTable('Drivers');
        $this->Shipments = $this->fetchTable('Shipments');
    }
    
    public function index()
    {
        // Query the Drivers table and join with Users to get driver names
        $drivers = $this->Drivers->find()
            ->select(['Drivers.id', 'Users.first_name', 'Users.last_name', 'Users.email', 'Drivers.status'])
            ->where(['Users.status IN' => ['A', 'I'], 'Users.user_type' => 'Driver'])
            ->contain(['Users'])->toArray();

        $this->set(compact('drivers'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        // Fetch all driver data filtered by driver_id
        $driver = $this->Drivers->find()
            ->where(['Drivers.id' => $id])
            ->contain(['Users'])
            ->first();

        if (!empty($driver->driver_photo)) {
                $driver->driver_photo = $this->Media->getCloudFrontURL($driver->driver_photo);
        }

        $this->set(compact('driver'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $driver = $this->Drivers->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            try {
                // Insert into Users table
                $user = $this->Users->newEmptyEntity();

                $plainPassword = Security::randomString(10);
                $password = $plainPassword;

                $user = $this->Users->patchEntity($user, [
                    'first_name' => $data['first_name'],
                    'last_name' => $data['last_name'],
                    'email' => $data['email'],
                    'password' => $password,
                    'country_code' => $data['contact_country_code'],
                    'mobile_no' => $data['contact_number'],
                    'status' => 'A', // Assuming default status is 'A'
                    'user_type' => 'Driver' // Assuming user_type for drivers
                ]);

                if ($this->Users->save($user)) {

                    $userId = $user->id; // Get the newly inserted user ID

                    $allowedFormats = Configure::read('Constants.DRIVER_IMAGE_TYPE');
                    $maxWebImageSize = Configure::read('Constants.DRIVER_IMAGE_SIZE') * 1024 * 1024;

                    if (!empty($data['image']) && $data['image']->getError() === UPLOAD_ERR_OK) {
                        $web_image = $data['image'];
                        $webImageName = trim($web_image->getClientFilename());
                        $webImageSize = $web_image->getSize();
                        $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                        if (!in_array($webImageExt, $allowedFormats)) {
                            return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                        }

                        if ($webImageSize > $maxWebImageSize) {
                            return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                        }

                        list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                        $minWidth = Configure::read('Constants.DRIVER_IMAGE_MIN_WIDTH');
                        $maxWidth = Configure::read('Constants.DRIVER_IMAGE_MAX_WIDTH');
                        $minHeight = Configure::read('Constants.DRIVER_IMAGE_MIN_HEIGHT');
                        $maxHeight = Configure::read('Constants.DRIVER_IMAGE_MAX_HEIGHT');

                        if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                            return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                        }

                        if (!empty($webImageName)) {
                            $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $filePath = Configure::read('Constants.DRIVER_IMAGE');
                            $folderPath = $uploadFolder . $filePath;
                            $targetdir = WWW_ROOT . $folderPath;
                            $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                            $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                            if ($uploadResult === 'Success') {
                                $data['image'] = $uploadFolder . $webImageFile;
                            }
                        }
                    }
                    else
                    {
                        $data['image'] = "";
                    }

                    // Insert into Drivers table
                    $driver = $this->Drivers->newEmptyEntity();
                    $driver = $this->Drivers->patchEntity($driver, [
                        'user_id' => $userId,
                        'driver_photo' => $data['image'],
                        'address' => $data['address'],
                        'status' => 'A' // Assuming default status is 'A'
                    ]);

                    if ($this->Drivers->save($driver)) {

                        $this->sendWelcomeEmailToDriver($user, $plainPassword);

                        $this->Flash->success(__('The driver has been saved.'));
                        return $this->redirect(['action' => 'index']);

                    } else {
                        throw new \Exception('Could not save driver details.');
                    }
                } else {
                    throw new \Exception('Could not save user details.');
                }
            } catch (\Exception $e) {
                
                $this->Flash->error(__('The driver could not be saved. Please, try again.'));
            }
        }

        $imageSize = Configure::read('Constants.DRIVER_IMAGE_SIZE');
        $webImageMinWidth = Configure::read('Constants.DRIVER_IMAGE_MIN_WIDTH');
        $webImageMaxWidth = Configure::read('Constants.DRIVER_IMAGE_MAX_WIDTH');
        $webImageMinHeight = Configure::read('Constants.DRIVER_IMAGE_MIN_HEIGHT');
        $webImageMaxHeight = Configure::read('Constants.DRIVER_IMAGE_MAX_HEIGHT');
        $webImageType = Configure::read('Constants.DRIVER_IMAGE_TEXT_TYPE');
        $webImageTypeText = Configure::read('Constants.DRIVER_IMAGE_TEXT_TYPE');

        $file_acceptance_msg = __('Only '.$webImageTypeText.' files are allowed. Max size: '.$imageSize.'MB. Dimensions: '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight.'');

        $this->set([
            'webImageSize' => Configure::read('Constants.DRIVER_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.DRIVER_IMAGE_JS_TYPE'),
            'webImageMinWidth' => Configure::read('Constants.DRIVER_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.DRIVER_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.DRIVER_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.DRIVER_IMAGE_MAX_HEIGHT'),
        ]);
        
        $this->set(compact('driver', 'file_acceptance_msg'));
    }

    private function sendWelcomeEmailToDriver($user, $password)
    {
        $toEmails = [$user->email];

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No email found to send welcome email to driver ID: " . $user->id);
            return;
        }

        $emailData = [
            'name' => $user->first_name . ' ' . $user->last_name,
            'email' => $user->email,
            'password' => $password,
            'greeting' => 'Dear ' . $user->first_name . ',',
        ];

        $subject = "Welcome to the Driver App";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'welcome_driver', // template name in templates/email/html/
            $emailData
        );
    }


    /**
     * Edit method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    
    public function edit($id = null)
    {
        $driver = $this->Drivers->get($id, ['contain' => ['Users']]);

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            try {
                // Update Users table
                $driver->user = $this->Drivers->Users->patchEntity($driver->user, [
                    'first_name' => $data['first_name'],
                    'last_name' => $data['last_name'],
                    'email' => $data['email'],
                    'country_code' => $data['contact_country_code'],
                    'mobile_no' => $data['contact_number'],
                    'status' => $data['status']
                ]);

                if ($this->Drivers->Users->save($driver->user)) {
                    

                    $allowedFormats = Configure::read('Constants.SHOWROOM_IMAGE_TYPE');
                    $maxWebImageSize = Configure::read('Constants.SHOWROOM_IMAGE_SIZE') * 1024 * 1024;

                    if (!empty($data['image']) && $data['image']->getError() === UPLOAD_ERR_OK) {
                        $web_image = $data['image'];
                        $webImageName = trim($web_image->getClientFilename());
                        $webImageSize = $web_image->getSize();
                        $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                        if (!in_array($webImageExt, $allowedFormats)) {
                            return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                        }

                        if ($webImageSize > $maxWebImageSize) {
                            return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                        }

                        list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                        $minWidth = Configure::read('Constants.SHOWROOM_IMAGE_MIN_WIDTH');
                        $maxWidth = Configure::read('Constants.SHOWROOM_IMAGE_MAX_WIDTH');
                        $minHeight = Configure::read('Constants.SHOWROOM_IMAGE_MIN_HEIGHT');
                        $maxHeight = Configure::read('Constants.SHOWROOM_IMAGE_MAX_HEIGHT');

                        if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                            return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                        }

                        if (!empty($webImageName)) {
                            $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $filePath = Configure::read('Constants.SHOWROOM_IMAGE');
                            $folderPath = $uploadFolder . $filePath;
                            $targetdir = WWW_ROOT . $folderPath;
                            $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                            $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                            if ($uploadResult === 'Success') {
                                $data['image'] = $uploadFolder . $webImageFile;
                            }
                        }
                    }
                    else
                    {
                        $data['image'] = $driver->driver_photo;
                    }

                    // Update Drivers table
                    $driver = $this->Drivers->patchEntity($driver, [
                        'driver_photo' => $data['image'],
                        'address' => $data['address'],
                        'status' => $data['status']
                    ]);

                    if ($this->Drivers->save($driver)) {
                        
                        $this->Flash->success(__('The driver has been updated.'));
                        return $this->redirect(['action' => 'index']);

                    } else {
                        throw new \Exception('Could not update driver details.');
                    }
                } else {
                    throw new \Exception('Could not update user details.');
                }
            } catch (\Exception $e) {
                
                $this->Flash->error(__('The driver could not be updated. Please, try again.'));
            }
        }

        $imageSize = Configure::read('Constants.DRIVER_IMAGE_SIZE');
        $webImageMinWidth = Configure::read('Constants.DRIVER_IMAGE_MIN_WIDTH');
        $webImageMaxWidth = Configure::read('Constants.DRIVER_IMAGE_MAX_WIDTH');
        $webImageMinHeight = Configure::read('Constants.DRIVER_IMAGE_MIN_HEIGHT');
        $webImageMaxHeight = Configure::read('Constants.DRIVER_IMAGE_MAX_HEIGHT');
        $webImageType = Configure::read('Constants.DRIVER_IMAGE_TEXT_TYPE');
        $webImageTypeText = Configure::read('Constants.DRIVER_IMAGE_TEXT_TYPE');

        $file_acceptance_msg = __('Only '.$webImageTypeText.' files are allowed. Max size: '.$imageSize.'MB. Dimensions: '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight.'');

        if (!empty($driver->driver_photo)) {
                $driver->driver_photo = $this->Media->getCloudFrontURL($driver->driver_photo);
        }

        $this->set([
            'webImageSize' => Configure::read('Constants.DRIVER_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.DRIVER_IMAGE_JS_TYPE'),
            'webImageMinWidth' => Configure::read('Constants.DRIVER_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.DRIVER_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.DRIVER_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.DRIVER_IMAGE_MAX_HEIGHT'),
        ]);

        $this->set(compact('driver', 'file_acceptance_msg'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The driver could not be deleted. Please, try again.'];

        try {
            // Fetch the driver record
            $driver = $this->Drivers->get($id, ['contain' => ['Users']]);

            // Check if the driver_id exists in the Shipments table with a delivery_status other than 'delivered'
            $undeliveredShipments = $this->Shipments->find()
                ->where([
                    'driver_id' => $driver->id,
                    'delivery_status !=' => 'delivered',
                ])
                ->count();

            if ($undeliveredShipments > 0) {
                $response['message'] = 'The driver cannot be deleted as there are undelivered shipments assigned.';
            } else {
                
                // Mark the driver record as deleted
                $driver->status = 'D';

                // Ensure the associated user is updated if it exists
                if (!empty($driver->user)) {
                    $user = $driver->user;
                    $user->status = 'D';

                    // Save the user record bypassing validation
                    if (!$this->Drivers->Users->save($user, ['checkRules' => false, 'validate' => false])) {
                        throw new \Exception('Failed to update the associated user status: ' . json_encode($user->getErrors()));
                    }
                }

                // Save the driver record
                if ($this->Drivers->save($driver)) {
                    $response = ['success' => true, 'message' => 'The driver has been marked as deleted.'];
                }
                
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }


    // public function delete($id = null)
    // {
    //     $this->request->allowMethod(['post', 'delete']);

    //     $response = ['success' => false, 'message' => 'The driver could not be deleted. Please, try again.'];

    //     try {

    //         // Fetch the driver record
    //         $driver = $this->Drivers->get($id, ['contain' => ['Users']]);

    //         // Mark the driver record as deleted
    //         $driver->status = 'D';

    //         // Update the associated user status
    //         if ($driver->user) {
    //             $driver->user->status = 'D'; // Set the user's status to 'D'
    //         }

    //         // Save both driver and user records
    //         if ($this->Drivers->save($driver) && $this->Drivers->Users->save($driver->user)) {
    //             $response = ['success' => true, 'message' => 'The driver has been marked as deleted.'];
    //         }

    //     } catch (\Exception $e) {
    //         $response['message'] = $e->getMessage();
    //     }

    //     if ($this->request->is('ajax')) {
    //         $this->response = $this->response->withType('application/json');
    //         $this->response = $this->response->withStringBody(json_encode($response));
    //         return $this->response;
    //     } else {
    //         if ($response['success']) {
    //             $this->Flash->success($response['message']);
    //         } else {
    //             $this->Flash->error($response['message']);
    //         }
    //         return $this->redirect(['action' => 'index']);
    //     }
    // }
}
