<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;
use Cake\Routing\Router;
use Cake\I18n\FrozenDate;


class SupplierPaymentController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    // protected $Showrooms;
    // protected $ZoneShowrooms;

    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $SupplierProducts;
    protected $Suppliers;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->Suppliers = $this->fetchTable('Suppliers');

        // $this->Showrooms = $this->fetchTable('Showrooms');
        // $this->ZoneShowrooms = $this->fetchTable('ZoneShowrooms');
    }
    
    // public function index()
    // {
    //     $zones = $this->SupplierPayment->find()
    //         ->where(['Zones.status IN' => ['A', 'I']])
    //         ->contain([
    //             'Showrooms' => function ($q) {
    //                 return $q->select(['Showrooms.id', 'Showrooms.name']);
    //             }
    //         ])
    //         ->order(['Zones.name' => 'ASC'])->toArray();

    //     $this->set(compact('zones'));
    // }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $supplier_payment = $this->SupplierPayment->get($id, contain: [
            'Suppliers', 'SupplierPurchaseOrders', 'Showrooms'
        ]);                

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        // Format the amount
        $supplier_payment->amount = number_format((float)$supplier_payment->amount, 0, '', $thousandSeparator) . ' ' . $currencySymbol;

        if ($supplier_payment->cheque_copy) {
            
            $supplier_payment->cheque_copy = $this->Media->getCloudFrontURL($supplier_payment->cheque_copy);
        }

        if ($supplier_payment->receipt) {
            
            $supplier_payment->receipt = $this->Media->getCloudFrontURL($supplier_payment->receipt);
        }

        if ($supplier_payment->payment_date) {
            
            $supplier_payment->payment_date = $supplier_payment->payment_date->format('Y-m-d');
        }

        $response = [
            'status' => 'success',
            'supplier_payment' => $supplier_payment
        ];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */

    // public function add()
    // {
    //     $this->request->allowMethod(['post', 'ajax']);

    //     if ($this->request->is('post')) {

    //         $currencyConfig = Configure::read('Settings.Currency.format');
    //         $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //         $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //         $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //         $data = $this->request->getData();
    //         $supplierId = $data['supplier_id'];

    //         $selectedBills = $data['bills'] ?? []; // Get selected bill IDs as an array
    //         $billAmounts = $data['bill_amounts'] ?? [];
    //         $savedPayments = [];
    //         $errors = [];

    //         foreach ($selectedBills as $billId) {

    //             $amount = isset($billAmounts[$billId]) ? (float) $billAmounts[$billId] : 0;

    //             if ($amount <= 0) {
    //                 $errors[] = __('Invalid amount for bill ID {0}', $billId);
    //                 continue;
    //             }

    //             $supplier_payment = $this->SupplierPayment->newEmptyEntity();

    //             $paymentData = [
    //                 'supplier_id' => $supplierId,
    //                 'showroom_id' => $data['showroom_id'] ?? null, 
    //                 'supplier_purchase_order_id' => $billId,
    //                 'payment_mode' => $data['payment_mode'],
    //                 'amount' => $amount,
    //                 'payment_date' => $data['payment_date'],
    //                 'cheque_no' => $data['cheque_no'] ?? null,
    //                 'cheque_date' => $data['cheque_date'] ?? null,
    //                 'bank_name' => $data['bank_name'] ?? null,
    //                 'payee_name' => $data['payee_name'] ?? null,
    //                 'receipt_number' => $data['receipt_number'] ?? null,
    //             ];

    //             if (!empty($data['cheque_copy'])) {
    //                 $chequeCopy = $data['cheque_copy'];
    //                 if ($chequeCopy->getError() === UPLOAD_ERR_OK) {
    //                     $fileName = trim($chequeCopy->getClientFilename());
    //                     if (!empty($fileName)) {
    //                         $imageTmpName = $chequeCopy->getStream()->getMetadata('uri');
    //                         $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
    //                         $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
    //                         $filePath = Configure::read('Constants.SUPPLIER_PAYMENT_CHEQUE_COPY');
    //                         $folderPath = $uploadFolder . $filePath;
    //                         $targetdir = WWW_ROOT . $folderPath;
    //                         $ext = pathinfo($fileName, PATHINFO_EXTENSION);
    //                         $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
    //                         $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
    //                         if ($uploadResult === 'Success') {
    //                             $paymentData['cheque_copy'] = $folderPath . $imageFile;
    //                         }
    //                     }
    //                 }
    //             }

    //             if (!empty($data['receipt'])) {
    //                 $chequeCopy = $data['receipt'];
    //                 if ($chequeCopy->getError() === UPLOAD_ERR_OK) {
    //                     $fileName = trim($chequeCopy->getClientFilename());
    //                     if (!empty($fileName)) {
    //                         $imageTmpName = $chequeCopy->getStream()->getMetadata('uri');
    //                         $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
    //                         $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
    //                         $filePath = Configure::read('Constants.SUPPLIER_PAYMENT_RECEIPT');
    //                         $folderPath = $uploadFolder . $filePath;
    //                         $targetdir = WWW_ROOT . $folderPath;
    //                         $ext = pathinfo($fileName, PATHINFO_EXTENSION);
    //                         $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
    //                         $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
    //                         if ($uploadResult === 'Success') {
    //                             $paymentData['receipt'] = $folderPath . $imageFile;
    //                         }
    //                     }
    //                 }
    //             }

    //             $supplier_payment = $this->SupplierPayment->patchEntity($supplier_payment, $paymentData);
    //             if ($this->SupplierPayment->save($supplier_payment)) {
    //                 $savedPayments[] = $supplier_payment;
                    
    //                 // Update Purchase Order Status
    //                 $orderRequest = $this->SupplierPurchaseOrders->get($billId);
    //                 $orderRequest->payment_status = __('Paid');
    //                 $this->SupplierPurchaseOrders->save($orderRequest);
    //             } else {
    //                 $errors[] = __('Failed to save payment for bill ID {0}', $billId);
    //             }
    //         }

    //         if (!empty($savedPayments)) {

    //             $query_supplier_payment = $this->SupplierPayment->find()
    //                 ->where(['SupplierPayment.supplier_id' => $supplierId])
    //                 ->contain([
    //                     'Suppliers','SupplierPurchaseOrders'
    //                 ])
    //                 ->order(['SupplierPayment.created' => 'DESC'])->toArray();                                   

    //             $dateFormat = Configure::read('Settings.DATE_FORMAT');
    //             $timeFormat = Configure::read('Settings.TIME_FORMAT');
    //             $supplier_payments = [];
    //             $i = 1;
    //             foreach ($query_supplier_payment as $payment) {

    //                 $paymentStatusMap = [
    //                     __('Paid') => ['label' => __('Paid'), 'class' => 'col-green'],
    //                     __('Pending') => ['label' => __('Pending'), 'class' => 'col-blue']
    //                 ];

    //                 $payment_status = $paymentStatusMap[$payment->supplier_purchase_order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

    //                 // $date = $payment->supplier_purchase_order->payment_due_date->format($dateFormat . ' ' . $timeFormat);

    //                 // $due_date = date('Y-m-d', strtotime($date. ' + '.$payment->supplier->credit_period.' days'));

    //                 if (!empty($payment->supplier_purchase_order->payment_due_date)) {
    //                     $date = $payment->supplier_purchase_order->payment_due_date->format($dateFormat . ' ' . $timeFormat);
    //                     $due_date = date('Y-m-d', strtotime($date . ' + ' . ($payment->supplier->credit_period ?? 0) . ' days'));
    //                 } else {
    //                     $due_date = 'N/A'; // No due date if `payment_due_date` is missing
    //                 }

    //                 $supplier_payments[] = [
    //                     'supplier' => h($payment->supplier->name),
    //                     'bill_no' => h($payment->supplier_purchase_order->bill_no),
    //                     'payment_date' => $payment->payment_date->format($dateFormat . ' ' . $timeFormat),
    //                     'amount' => h(number_format((float)$payment->amount, 0, '', $thousandSeparator)).' '.$currencySymbol,
    //                     'due_date' => h($due_date),
    //                     'payment_status' => '<div class="badge-outline ' . h($payment_status['class']) . '">' . h($payment_status['label']) . '</div>',
    //                     'actions' => '
    //                         <a onClick="openViewSupplierPaymentModal(' . $payment->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'
    //                 ];

    //             }    

    //             $query_purchase_order = $this->SupplierPurchaseOrders->find()
    //                             ->where(['SupplierPurchaseOrders.supplier_id' => $supplierId])
    //                             ->where(['SupplierPurchaseOrders.status IN' => ['A', 'P', 'I']])
    //                             ->order(['SupplierPurchaseOrders.created' => 'DESC'])->toArray();  
    //             $purchase_orders = [];
    //             $i = 1;
    //             foreach ($query_purchase_order as $order) {

    //                 $statusMap = [
    //                 'A' => ['label' => __('Active'), 'class' => 'col-green'],
    //                 'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
    //                 'P' => ['label' => __('Pending'), 'class' => 'col-blue']
    //                 ];

    //                 $status = $statusMap[$order->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

    //                 $paymentStatusMap = [
    //                     'Paid' => ['label' => __('Paid'), 'class' => 'col-green'],
    //                     'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
    //                 ];

    //                 $payment_status = $paymentStatusMap[$order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

    //                 $deliveredStatusMap = [
    //                     'Delivered' => ['label' => __('Delivered'), 'class' => 'col-green'],
    //                     'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
    //                 ];

    //                 $delivery_status = $deliveredStatusMap[$order->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];


    //                 $purchase_orders[] = [
    //                     'order_date' => $order->order_date->format($dateFormat . ' ' . $timeFormat),
    //                     'bill_no' => h($order->bill_no),
    //                     'supp_bill_no' => h($order->supplier_bill_no),
    //                     'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
    //                     'payment_status' => '<div class="badge-outline ' . h($payment_status['class']) . '">' . h($payment_status['label']) . '</div>',
    //                     'delivery_status' => '<div class="badge-outline ' . h($delivery_status['class']) . '">' . h($delivery_status['label']) . '</div>',
    //                     'actions' => '
    //                         <a onClick="openViewPurchaseOrderModal(' . $order->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'.
    //                         '<a onClick="openEditPurchaseOrderModal('.$order->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
    //                         '<a href="' . Router::url(['controller' => 'SupplierPurchaseOrders', 'action' => 'delete', $order->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'.
    //                         '<a data-toggle="tooltip" title="Verify" onclick="openVerifyModal('.$order->id.')" data-id="'.$order->id.'"><i class="fa fa-check" aria-hidden="true"></i></a>'
    //                 ];

    //             }

    //             $query = $this->SupplierPayment->find()
    //                 ->select([
    //                     'total_amount' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
    //                 ])
    //                 ->where(['SupplierPayment.supplier_id' => $supplierId])
    //                 ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
    //                     return $q->where(['SupplierPurchaseOrders.payment_status' => __('Paid')]);
    //                 })
    //                 ->first();
        
    //             // Get the total amount from the result, set to 0 if no result
    //             $totalPaymentsDone = $query ? $query->total_amount : 0.00;

    //             // Format the total payments done to two decimal places
    //             $totalPaymentsDoneFormatted = number_format((float)$totalPaymentsDone, 0, '', $thousandSeparator).' '.$currencySymbol; 

    //             /**  TOTAL PENDING AMOUNT **/

    //             $pendingBills = $this->SupplierPurchaseOrders->find()
    //                 ->contain([
    //                     'SupplierPurchaseOrdersItems' => function ($q) {
    //                         return $q->contain(['Products'])
    //                              ->select([
    //                                 'SupplierPurchaseOrdersItems.product_id',
    //                                 'SupplierPurchaseOrdersItems.approved_quantity',
    //                                 'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
    //                             ]);
    //                     }
    //                 ])
    //                 ->where([
    //                     'SupplierPurchaseOrders.payment_status' => 'Pending',
    //                     'SupplierPurchaseOrders.status' => 'A',
    //                     'SupplierPurchaseOrders.supplier_id' => $supplierId
    //                 ])
    //                 ->all();

    //             $totalPendingPayments = 0;    
    //             foreach ($pendingBills as $purchaseOrder) {

    //                 foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {

    //                     // print_r($purchaseOrder->supplier_id.'----'.$product->product_id).'\n';

    //                     // Fetch supplier price
    //                     $supplierPrice = $this->SupplierProducts->find()
    //                         ->select(['supplier_price'])
    //                         ->where([
    //                             'supplier_id' => $purchaseOrder->supplier_id,
    //                             'product_id' => $product->product_id
    //                         ])
    //                         ->first();

    //                     // Calculate total pending amount for each product
    //                     if ($supplierPrice) {
    //                         $totalPendingPayments += $supplierPrice->supplier_price * $product->approved_quantity;
    //                     }
    //                 }
    //             }

    //             // Output the total pending payments
    //             $totalPendingPaymentsFormatted = number_format((float)$totalPendingPayments, 0, '', $thousandSeparator).' '.$currencySymbol;

    //             // Fetch pending purchase orders
    //             $pendingOrders = $this->SupplierPurchaseOrders->find()
    //                 ->where([
    //                     'payment_status' => 'Pending', 
    //                     'status' => 'A',
    //                     'supplier_id' => $supplierId
    //                 ])
    //                 ->all();

    //             $totalPendingDue = 0;
    //             $today = new FrozenDate();
    //             $supplier_data = $this->Suppliers->get($supplierId);
    //             // Loop through each pending order
    //             foreach ($pendingOrders as $order) {
    //                 // Credit 5 days to payment_due_date
    //                 // $currentDueDate = new FrozenDate($order->payment_due_date);
    //                 // $newDueDate = $currentDueDate->addDays($supplier_data->credit_period);

    //                 if (!empty($order->payment_due_date)) {
    //                     $currentDueDate = new FrozenDate($order->payment_due_date);
    //                     $newDueDate = $currentDueDate->addDays($supplier_data->credit_period);
    //                 } else {
    //                     // Handle the case where payment_due_date is null
    //                     $newDueDate = null; // or set a default date if applicable
    //                 }


    //                 if ($today > $newDueDate) {
    //                     // Fetch the approved products for the current order
    //                     $approvedProducts = $this->SupplierPurchaseOrdersItems->find()
    //                         ->where(['supplier_purchase_order_id' => $order->id])
    //                         ->all();

    //                     // Calculate total due for this order
    //                     foreach ($approvedProducts as $product) {
    //                         // Fetch the supplier price for the product
    //                         $supplierProduct = $this->SupplierProducts->find()
    //                             ->where(['supplier_id' => $order->supplier_id, 'product_id' => $product->product_id])
    //                             ->first();

    //                         if ($supplierProduct) {
    //                             // Calculate the pending due for this product
    //                             $pendingDue = $supplierProduct->supplier_price * $product->approved_quantity;
    //                             $totalPendingDue += $pendingDue;
    //                         }
    //                     }
    //                 }
    //             }

    //             // Display the total pending due
    //             $totalPendingDueFormatted = number_format((float)$totalPendingDue, 0, '', $thousandSeparator).' '.$currencySymbol;

    //             $this->set([
    //                 'supplier_payments' => $supplier_payments,
    //                 '_serialize' => ['supplier_payments'],
    //             ]);

    //             return $this->response->withType('application/json')->withStringBody(json_encode(['status' => __('success'), 'data' => $supplier_payments, 'purchase_orders' => $purchase_orders, 'totalPaymentsDoneFormatted' => $totalPaymentsDoneFormatted, 'totalPendingPaymentsFormatted' => $totalPendingPaymentsFormatted, 'totalPendingDueFormatted' => $totalPendingDueFormatted]));

    //             // return $this->response->withType('application/json')->withStringBody(json_encode([
    //             //     'status' => __('success'),
    //             //     'message' => __('Supplier payments have been saved successfully.'),
    //             //     'data' => $savedPayments
    //             // ]));
    //         } else {
    //             return $this->response->withType('application/json')->withStringBody(json_encode([
    //                 'status' => __('error'),
    //                 'message' => __('Failed to save supplier payments.'),
    //                 'errors' => $errors
    //             ]));
    //         }
    //     }
    // }

    public function add()
    {
        $this->request->allowMethod(['post', 'ajax']);

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            $supplierId = $data['supplier_id'];
            $showroomId = $data['showroom_id'] ?? null;
            $totalPayment = (float) $data['amount'];

            if ($totalPayment <= 0) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['error' => __('Invalid payment amount.')]));
            }

            if (!empty($data['cheque_copy']) && $data['cheque_copy'] instanceof \Laminas\Diactoros\UploadedFile) {
                $chequeCopy = $data['cheque_copy'];
                if ($chequeCopy->getError() === UPLOAD_ERR_OK) {
                    $fileName = trim($chequeCopy->getClientFilename());
                    if (!empty($fileName)) {
                        $imageTmpName = $chequeCopy->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.SUPPLIER_PAYMENT_CHEQUE_COPY');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult === 'Success') {
                            $data['cheque_copy'] = $folderPath . $imageFile;
                        } else {
                            $data['cheque_copy'] = null;
                        }
                    } else {
                        $data['cheque_copy'] = null;
                    }
                } else {
                    $data['cheque_copy'] = null;
                }
            }
            else
            {
                $data['cheque_copy'] = null;
            }

            if (!empty($data['receipt']) && $data['receipt'] instanceof \Laminas\Diactoros\UploadedFile) {
                $chequeCopy = $data['receipt'];
                if ($chequeCopy->getError() === UPLOAD_ERR_OK) {
                    $fileName = trim($chequeCopy->getClientFilename());
                    if (!empty($fileName)) {
                        $imageTmpName = $chequeCopy->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.SUPPLIER_PAYMENT_RECEIPT');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult === 'Success') {
                            $data['receipt'] = $folderPath . $imageFile;
                        }
                    }
                }
            }
            else
            {
                $data['receipt'] = null;
            }

            // Step 1: Fetch eligible orders (Partially Paid first, then Pending), with status A
            $pendingOrders = $this->SupplierPurchaseOrders->find()
                ->where([
                    'supplier_id' => $supplierId,
                    'payment_status IN' => ['Partially Paid', 'Pending'],
                    'status' => 'A'
                ])
                ->order([
                    "FIELD(payment_status, 'Partially Paid', 'Pending')", // Prioritize Partially Paid
                    'payment_due_date' => 'ASC'
                ])
                ->all();

            $remainingAmount = $totalPayment;
            $savedPayments = [];

            foreach ($pendingOrders as $order) {
                // Step 2: Calculate actual total using SupplierProducts table
                $orderItems = $this->SupplierPurchaseOrdersItems->find()
                    ->where(['supplier_purchase_order_id' => $order->id])
                    ->toArray();

                $actualTotal = 0;

                foreach ($orderItems as $item) {
                    $supplierPrice = null;

                    if (!empty($item->product_variant_id)) {
                        $supplierProduct = $this->SupplierProducts->find()
                            ->select(['supplier_price'])
                            ->where([
                                'supplier_id' => $supplierId,
                                'product_variant_id' => $item->product_variant_id,
                                'status' => 'A'
                            ])
                            ->first();

                        if ($supplierProduct) {
                            $supplierPrice = $supplierProduct->supplier_price;
                        }
                    }

                    if ($supplierPrice === null) {
                        $supplierProduct = $this->SupplierProducts->find()
                            ->select(['supplier_price'])
                            ->where([
                                'supplier_id' => $supplierId,
                                'product_id' => $item->product_id,
                                'status' => 'A'
                            ])
                            ->first();

                        if ($supplierProduct) {
                            $supplierPrice = $supplierProduct->supplier_price;
                        }
                    }

                    if ($supplierPrice !== null) {
                        $actualTotal += $supplierPrice * $item->approved_quantity;
                    }
                }

                // Step 3: Calculate already paid
                $alreadyPaid = $this->SupplierPayment->find()
                    ->where(['supplier_purchase_order_id' => $order->id])
                    ->select(['sum' => 'SUM(amount)'])
                    ->first()->sum ?? 0;

                $dueAmount = $actualTotal - $alreadyPaid;

                if ($dueAmount <= 0) {
                    continue;
                }

                $paymentAmount = min($dueAmount, $remainingAmount);

                // Step 4: Create payment entry
                $supplierPayment = $this->SupplierPayment->newEntity([
                    'supplier_id' => $supplierId,
                    'showroom_id' => $showroomId,
                    'supplier_purchase_order_id' => $order->id,
                    'payment_mode' => $data['payment_mode'],
                    'amount' => $paymentAmount,
                    'payment_date' => $data['payment_date'],
                    'cheque_no' => $data['cheque_no'] ?? null,
                    'cheque_copy' => $data['cheque_copy'] ?? null,
                    'cheque_date' => $data['cheque_date'] ?? null,
                    'bank_name' => $data['bank_name'] ?? null,
                    'payee_name' => $data['payee_name'] ?? null,
                    'receipt_number' => $data['receipt_number'] ?? null,
                    'receipt' => $data['receipt'] ?? null,
                ]);

                if ($this->SupplierPayment->save($supplierPayment)) {
                    $savedPayments[] = $supplierPayment;
                    $remainingAmount -= $paymentAmount;

                    // Step 5: Update payment status
                    $totalPaid = $alreadyPaid + $paymentAmount;

                    if ($totalPaid >= $actualTotal) {
                        $order->payment_status = 'Paid';
                    } elseif ($totalPaid > 0) {
                        $order->payment_status = 'Partially Paid';
                    }

                    $this->SupplierPurchaseOrders->save($order);
                }

                if ($remainingAmount <= 0) {
                    break;
                }
            }

            if (!empty($savedPayments)) {

                $query_supplier_payment = $this->SupplierPayment->find()
                    ->where(['SupplierPayment.supplier_id' => $supplierId])
                    ->contain([
                        'Suppliers','SupplierPurchaseOrders'
                    ])
                    ->order(['SupplierPayment.created' => 'DESC'])->toArray();                                   

                $currencyConfig = Configure::read('Settings.Currency.format');
                $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
                $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
                $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

                $dateFormat = Configure::read('Settings.DATE_FORMAT');
                $timeFormat = Configure::read('Settings.TIME_FORMAT');
                $supplier_payments = [];
                $i = 1;
                foreach ($query_supplier_payment as $payment) {

                    $paymentStatusMap = [
                        __('Paid') => ['label' => __('Paid'), 'class' => 'col-green'],
                        __('Partially Paid') => ['label' => __('Partially Paid'), 'class' => 'col-orange'],
                        __('Pending') => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $payment_status = $paymentStatusMap[$payment->supplier_purchase_order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                    // $date = $payment->supplier_purchase_order->payment_due_date->format($dateFormat . ' ' . $timeFormat);

                    // $due_date = date('Y-m-d', strtotime($date. ' + '.$payment->supplier->credit_period.' days'));

                    if (!empty($payment->supplier_purchase_order->payment_due_date)) {
                        $date = $payment->supplier_purchase_order->payment_due_date->format($dateFormat . ' ' . $timeFormat);
                        $due_date = date('Y-m-d', strtotime($date . ' + ' . ($payment->supplier->credit_period ?? 0) . ' days'));
                    } else {
                        $due_date = 'N/A'; // No due date if `payment_due_date` is missing
                    }

                    $supplier_payments[] = [
                        'supplier' => h($payment->supplier->name),
                        'bill_no' => h($payment->supplier_purchase_order->bill_no),
                        'payment_date' => $payment->payment_date->format($dateFormat . ' ' . $timeFormat),
                        'amount' => h(number_format((float)$payment->amount, 0, '', $thousandSeparator)).' '.$currencySymbol,
                        'due_date' => h($due_date),
                        'payment_status' => '<div class="badge-outline ' . h($payment_status['class']) . '">' . h($payment_status['label']) . '</div>',
                        'actions' => '
                            <a onClick="openViewSupplierPaymentModal(' . $payment->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'
                    ];

                }    

                $query_purchase_order = $this->SupplierPurchaseOrders->find()
                                ->where(['SupplierPurchaseOrders.supplier_id' => $supplierId])
                                ->where(['SupplierPurchaseOrders.status IN' => ['A', 'P', 'I']])
                                ->order(['SupplierPurchaseOrders.created' => 'DESC'])->toArray();  
                $purchase_orders = [];
                $i = 1;
                foreach ($query_purchase_order as $order) {

                    $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                    'P' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $status = $statusMap[$order->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

                    $paymentStatusMap = [
                        'Paid' => ['label' => __('Paid'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $payment_status = $paymentStatusMap[$order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                    $deliveredStatusMap = [
                        'Delivered' => ['label' => __('Delivered'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $delivery_status = $deliveredStatusMap[$order->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];


                    $purchase_orders[] = [
                        'order_date' => $order->order_date->format($dateFormat . ' ' . $timeFormat),
                        'bill_no' => h($order->bill_no),
                        'supp_bill_no' => h($order->supplier_bill_no),
                        'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                        'payment_status' => '<div class="badge-outline ' . h($payment_status['class']) . '">' . h($payment_status['label']) . '</div>',
                        'delivery_status' => '<div class="badge-outline ' . h($delivery_status['class']) . '">' . h($delivery_status['label']) . '</div>',
                        'actions' => '
                            <a onClick="openViewPurchaseOrderModal(' . $order->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'.
                            '<a onClick="openEditPurchaseOrderModal('.$order->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                            '<a href="' . Router::url(['controller' => 'SupplierPurchaseOrders', 'action' => 'delete', $order->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'.
                            '<a data-toggle="tooltip" title="Verify" onclick="openVerifyModal('.$order->id.')" data-id="'.$order->id.'"><i class="fa fa-check" aria-hidden="true"></i></a>'
                    ];

                }

                $query = $this->SupplierPayment->find()
                    ->select([
                        'total_amount' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
                    ])
                    ->where(['SupplierPayment.supplier_id' => $supplierId])
                    ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
                        return $q->where(['SupplierPurchaseOrders.payment_status IN' => ['Partially Paid', 'Paid']]);
                    })
                    ->first();
        
                // Get the total amount from the result, set to 0 if no result
                $totalPaymentsDone = $query ? $query->total_amount : 0.00;

                // Format the total payments done to two decimal places
                $totalPaymentsDoneFormatted = number_format((float)$totalPaymentsDone, 0, '', $thousandSeparator).' '.$currencySymbol; 

                /**  TOTAL PENDING AMOUNT **/

                $pendingBills = $this->SupplierPurchaseOrders->find()
                    ->contain([
                        'SupplierPurchaseOrdersItems' => function ($q) {
                            return $q->contain(['Products'])
                                 ->select([
                                    'SupplierPurchaseOrdersItems.product_id',
                                    'SupplierPurchaseOrdersItems.approved_quantity',
                                    'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
                                ]);
                        }
                    ])
                    ->where([
                        'SupplierPurchaseOrders.payment_status IN' => ['Partially Paid', 'Pending'],
                        'SupplierPurchaseOrders.status' => 'A',
                        'SupplierPurchaseOrders.supplier_id' => $supplierId
                    ])
                    ->all();

                $totalPendingPayments = 0;    
                foreach ($pendingBills as $purchaseOrder) {
                    $orderTotal = 0;

                    foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {
                        $conditions = [
                            'supplier_id' => $purchaseOrder->supplier_id,
                            'product_id' => $product->product_id,
                            'status' => 'A'
                        ];

                        if (!empty($product->product_variant_id)) {
                            $conditions['product_variant_id'] = $product->product_variant_id;
                        }

                        $supplierPrice = $this->SupplierProducts->find()
                            ->select(['supplier_price'])
                            ->where($conditions)
                            ->first();

                        if ($supplierPrice) {
                            $orderTotal += $supplierPrice->supplier_price * $product->approved_quantity;
                        }
                    }

                    // Fetch total paid for this order
                    $alreadyPaid = $this->SupplierPayment->find()
                        ->where(['supplier_purchase_order_id' => $purchaseOrder->id])
                        ->select(['sum' => 'SUM(amount)'])
                        ->first()->sum ?? 0;

                    $pendingAmount = $orderTotal - $alreadyPaid;
                    if ($pendingAmount > 0) {
                        $totalPendingPayments += $pendingAmount;
                    }
                }

                // Output the total pending payments
                $totalPendingPaymentsFormatted = number_format((float)$totalPendingPayments, 0, '', $thousandSeparator).' '.$currencySymbol;

                // Fetch pending purchase orders
                $pendingOrders = $this->SupplierPurchaseOrders->find()
                    ->where([
                        'payment_status IN' => ['Partially Paid', 'Pending'],
                        'status' => 'A',
                        'supplier_id' => $supplierId
                    ])
                    ->all();

                $totalPendingDue = 0;
                $today = new FrozenDate();
                $supplier_data = $this->Suppliers->get($supplierId);
                // Loop through each pending order
                foreach ($pendingOrders as $order) {
                    if (!empty($order->payment_due_date)) {
                        $currentDueDate = new FrozenDate($order->payment_due_date);
                    } else {
                        $currentDueDate = null;
                    }

                    if ($currentDueDate) {
                        $newDueDate = $currentDueDate->addDays($supplier_data->credit_period);
                    } else {
                        $newDueDate = null;
                    }

                    if ($today > $newDueDate) {
                        $orderTotal = 0;

                        // Fetch the approved products for the current order
                        $approvedProducts = $this->SupplierPurchaseOrdersItems->find()
                            ->where(['supplier_purchase_order_id' => $order->id])
                            ->all();

                        // Calculate total for approved quantities
                        foreach ($approvedProducts as $product) {
                            
                            $conditions = [
                                'supplier_id' => $order->supplier_id,
                                'product_id' => $product->product_id,
                                'status' => 'A'
                            ];

                            // Include product_variant_id if available
                            if (!empty($product->product_variant_id)) {
                                $conditions['product_variant_id'] = $product->product_variant_id;
                            }

                            $supplierProduct = $this->SupplierProducts->find()
                                ->where($conditions)
                                ->first();

                            if ($supplierProduct) {
                                $orderTotal += $supplierProduct->supplier_price * $product->approved_quantity;
                            }
                        }

                        // Fetch total paid amount for this order
                        $alreadyPaid = $this->SupplierPayment->find()
                            ->where(['supplier_purchase_order_id' => $order->id])
                            ->select(['sum' => 'SUM(amount)'])
                            ->first()->sum ?? 0;

                        $pendingDue = $orderTotal - $alreadyPaid;

                        if ($pendingDue > 0) {
                            $totalPendingDue += $pendingDue;
                        }
                    }
                }

                // Display the total pending due
                $totalPendingDueFormatted = number_format((float)$totalPendingDue, 0, '', $thousandSeparator).' '.$currencySymbol;

                $this->set([
                    'supplier_payments' => $supplier_payments,
                    '_serialize' => ['supplier_payments'],
                ]);

                return $this->response->withType('application/json')->withStringBody(json_encode(['status' => __('success'), 'data' => $supplier_payments, 'purchase_orders' => $purchase_orders, 'totalPaymentsDoneFormatted' => $totalPaymentsDoneFormatted, 'totalPendingPaymentsFormatted' => $totalPendingPaymentsFormatted, 'totalPendingDueFormatted' => $totalPendingDueFormatted]));

            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => __('error'),
                    'message' => __('Failed to save supplier payments.'),
                    'errors' => $errors
                ]));
            }

            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['error' => __('No eligible purchase orders found for payment.')]));
        }
    }


}
