<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Core\Configure;
use Cake\Core\Configure\Engine\PhpConfig;
use Cake\Core\Exception\Exception;
use Cake\I18n\Time;
//use Cake\Http\Response;
//use Cake\Http\Exception\BadRequestException;
//use Cake\Http\Exception\NotFoundException;
use Cake\View\JsonView;
use Cake\Utility\Security;
use Cake\Routing\Router;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;

class WebhooksController extends AppController
{
	public function initialize(): void
    {
        parent::initialize();
        $this->Authentication->addUnauthenticatedActions(['mtnWebhookListener','waveWebhookListener', 'testWebhook']);
        $this->Transactions = $this->fetchTable('Transactions');
        $this->Orders = $this->fetchTable('Orders');
        $this->PaymentMethodSettings = $this->fetchTable('PaymentMethodSettings');
    }

    public function mtnWebhookListener()
    {
        // Disable automatic rendering of a view template
        $this->autoRender = false;

        $this->request->allowMethod(['post']); // Allow only POST requests

        // Get the raw POST data
        //$rawPayload = file_get_contents("php://input");
        $rawPayload = $this->request->getBody()->getContents();
        $data = json_decode($rawPayload, true);

        // Example logic: Log the payload
        $logFile = LOGS . 'mtn_webhook.log';
        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - ' . $rawPayload . PHP_EOL, FILE_APPEND);
        
		// Check if the notification contains a status update
		if (isset($data['status']) && isset($data['externalId'])) {
		    
		    $status = $data['status'];
		    $externalId = $data['externalId'];
		    $payerId = $data['payerId'];
            $financialTransactionId = $data['financialTransactionId'] ?? null;
            $reason = $data['reason'] ?? null;

		    // Process status update
		    if ($status === 'SUCCESSFUL') {
		        $payment_status = 'Paid';
		    } elseif ($status === 'FAILED') {
		       $payment_status = 'Failed';
		    } else {
		        $payment_status = 'Pending';
		    }
		    $transaction = $this->Transactions->find()
		    	->where(['momo_externalID' => $externalId])
		    	->first();

            if ($transaction) {

            	$transaction->payment_status = $payment_status;
                $transaction->transactionID = $financialTransactionId;
                $transaction->momo_payerID = $payerId;
                $transaction->reason = $reason;
                $this->Transactions->save($transaction);
            }
		} else {
		    //echo "Invalid data received.";
		}
    }

    public function waveWebhookListener(){

        // Disable automatic rendering of a view template
        $this->autoRender = false;
        $this->request->allowMethod(['post']); // Allow only POST requests

        // This key is given to you by a Wave Representative when we register
        // your webhook URL. (This is different from the API key)
        $webhook_secret = $this->PaymentMethodSettings->find()
            ->select(['value'])
            ->where(['attribute' => 'wave_webhook_secret'])
            ->first();
        $wave_webhook_secret = $webhook_secret['value'];

        // # This header is sent by Wave in each webhook POST request.
        // (The plain HTTP header is 'Wave-Signature')
        $wave_signature = $_SERVER['HTTP_WAVE_SIGNATURE'];

        // The body of the request, as a plain string, not yet parsed to JSON:
        $webhook_body = file_get_contents('php://input');

        $webhook_json = $this->webhook_verify_signature_and_decode($wave_webhook_secret, $wave_signature, $webhook_body);
        if($webhook_json) {
            // You can now proceed to use the webhook data to process the request:
            $webhook_type = $webhook_json->type;
            $webhook_data = $webhook_json->data;

            $checkout_sessionID = $webhook_data['id'];  
            $checkout_status = $webhook_data['checkout_status'];          

            $transaction = $this->Transactions->find()
                    ->where(['checkout_sessionID' => $checkout_sessionID])
                    ->first();            

            if($webhook_type == 'checkout.session.completed') {

                //transaction update 
                $transactionID = $webhook_data['transaction_id'];              
                $payment_status = 'Paid';
                if ($transaction) {
                    $transaction->checkout_status = $checkout_status;
                    $transaction->payment_status = $payment_status;
                    $transaction->transactionID = $transactionID;
                    $this->Transactions->save($transaction);
                }
            } elseif ($webhook_type == 'checkout.session.payment_failed') {

                //order and transaction update
                $payment_status = 'Failed';
                if ($transaction) {
                    $transaction->checkout_status = $checkout_status;
                    $transaction->payment_status = $payment_status;
                    $transaction->reason = $webhook_data['last_payment_error']['message'];
                    $this->Transactions->save($transaction);
                }
                if($checkout_status == 'expired') {
                    $updateOrder = $this->Orders->get($transaction['order_id']);
                    $updateOrder->status = 'Expired';
                    $this->Orders->save($updateOrder);
                }
            } else {
                
            }

        } else {
            $error = 'Invalid webhook signature';
            $logFile = LOGS . 'wave_webhook.log';
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - ' . $error . PHP_EOL, FILE_APPEND);
        }        
    }

    protected function webhook_verify_signature_and_decode($wave_webhook_secret, $wave_signature, $webhook_body) {
       
        // Uncomment var_dump if you need to debug the 3 input values:
        // var_dump($wave_webhook_secret, $wave_signature, $webhook_body);

        $parts = explode(",", $wave_signature);
        $timestamp = explode("=", $parts[0])[1];

        $signatures = array();
        foreach (array_slice($parts, 1) as $signature) {
            $signatures[] = explode("=", $signature)[1];
        }

        $computed_hmac = hash_hmac("sha256", $timestamp . $webhook_body, $wave_webhook_secret);
        $valid = in_array($computed_hmac, $signatures);

        if($valid) {
            # The request is valid, you can proceed using the decoded contents.
            return json_decode($webhook_body);
        } else {
            return null;
        }
    }

    // test webhook
    public function testWebhook() {

        $this->autoRender = false;
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, 'https://babiken.com360degree.com/Webhooks/waveWebhookListener');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);

        $data = array(
          'test_key' => 'test_value'
        );

        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($ch);
        var_dump($result);
        if (curl_errno($ch)) {
          echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);

    }

}