<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Transaction Entity
 *
 * @property int $id
 * @property int $order_id
 * @property string $invoice_number
 * @property string $transaction_number
 * @property \Cake\I18n\DateTime $transaction_date
 * @property string $amount
 * @property string $payment_method
 * @property string $payment_status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Order $order
 */
class Transaction extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'order_id' => true,
        'invoice_number' => true,
        'transaction_number' => true,
        'transaction_fee' => true,
        'transaction_date' => true,
        'amount' => true,
        'payment_method' => true,
        'payment_status' => true,
        'created' => true,
        'modified' => true,
        'order' => true,
    ];
}
