<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Showroom> $showrooms
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-daterangepicker/daterangepicker.css') ?>">
<style type="text/css">
    .modal-backdrop {
        background-color : transparent !important;
        position: relative !important;
    }
</style>
<?php $this->end(); ?>

<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item active"><?= __('Shipment') ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Manage Shipment") ?></h4>
                <div class="card-header-form">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>"
                            id="customSearchBox" />
                        <div class="input-group-btn">
                            <button class="btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <?php if ($canAdd): ?>
                            <a href="<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'add']) ?>"
                                class="btn m-r-15">
                                <i class="fas fa-plus"></i>
                                <?= __("Create Shipment") ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?= $this->Url->build(['controller' => 'ShipmentsAssignments', 'action' => 'index']) ?>"
                                class="btn m-r-15">
                                <i class="fas fa-plus"></i>
                                <?= __("Assign Shipment") ?>
                            </a>
                        <button class="btn menu-toggle" type="submit">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    </div>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <div class="d-flex">

                        <div class="form-group d-flex align-items-center m-l-20">
                            <label style="width: 50px !important;" class="mr-2"><?= __('City:') ?></label>
                            <select class="form-control select2" id="filterCity">
                                <option><?= __('Select City') ?></option>
                                <?php foreach ($cities as $city): ?>
                                    <option value="<?= $city->city_name; ?>">
                                        <?= htmlspecialchars($city->city_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group d-flex align-items-center m-l-20">
                            <label for="status" style="width: 100px;" class="mr-2"><?= __('Shipment Status:') ?></label>
                            <select name="status" id="filterStatus" class="form-control form-select">
                                <option value=""><?= __('Filter By Status') ?></option>
                                <option value="Pending"><?= __('Pending') ?></option>
                                <option value="Picked up"><?= __('Picked up') ?></option>
                                <option value="Pickup Failed"><?= __('Pickup Failed') ?></option>
                                <option value="Return Pickup"><?= __('Return Pickup') ?></option>
                                <option value="Out for Delivery"><?= __('Out for Delivery') ?></option>
                                <option value="Partially Delivered"><?= __('Partially Delivered') ?></option>
                                <option value="Delivered"><?= __('Delivered') ?></option>
                                <option value="Failed"><?= __('Failed') ?></option>
                                <option value="Returned"><?= __('Returned') ?></option>
                            </select>
                        </div>

                        <div class="form-group d-flex align-items-center m-l-20">
                            <label class="mr-2"><?= __('Select Date:') ?></label>
                            <input type="text" class="form-control" id="filterDate" placeholder="Select Date">
                        </div>
                        
                        <div class="form-group ms-4">
                            <button class="btn btn-primary shipment_filter" id="filter">
                                <i class="fa fa-filter" aria-hidden="true"></i>
                            </button>
                            <button type="reset" class="btn btn-primary reset_shipment_filter"><i
                                    class="fas fa-redo-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="shipmentTable" role="grid">
                        <thead>
                            <tr>
                                <th><?= __('Shipment Id') ?></th>
                                <th><?= __('Order IDs') ?></th>
                                <th><?= __('From') ?></th>
                                <th><?= __('City') ?></th>
                                <th><?= __('Zone') ?></th>
                                <th><?= __('Municipality') ?></th>
                                <th><?= __('Driver/Delivery Partner') ?></th>
                                <th><?= __('Total Cash Collected') ?></th>
                                <th><?= __('Created Date') ?></th>
                                <th><?= __('Driver/Delivery Partner Assigned') ?></th>
                                <th><?= __('Express Delivery') ?>
                                <th id="status"><?= __('Status') ?></th>
                                <th class="actions"><?= __('Actions') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($shipments as $shipment): ?>

                                <?php
                                    // Collect all unique city, zone, and municipality names from shipment orders
                                    $cities = [];
                                    $zones = [];
                                    $municipalities = [];

                                    if (!empty($shipment->shipment_orders)) {
                                        foreach ($shipment->shipment_orders as $order) {
                                            if (!empty($order->city) && !empty($order->city->city_name)) {
                                                $cities[] = $order->city->city_name;
                                            }
                                            if (!empty($order->zone) && !empty($order->zone->name)) {
                                                $zones[] = $order->zone->name;
                                            }
                                            if (!empty($order->municipality) && !empty($order->municipality->name)) {
                                                $municipalities[] = $order->municipality->name;
                                            }
                                        }
                                    }

                                    $cityNames = implode(', ', array_unique($cities));
                                    $zoneNames = implode(', ', array_unique($zones));
                                    $municipalityNames = implode(', ', array_unique($municipalities));
                                ?>

                                <?php
                                    $isExpress = false;
                                    if (!empty($shipment->shipment_orders)) {
                                        foreach ($shipment->shipment_orders as $shipmentOrder) {
                                            if (!empty($shipmentOrder->order) && $shipmentOrder->order->delivery_mode_type === 'express') {
                                                $isExpress = true;
                                                break;
                                            }
                                        }
                                    }
                                ?>

                                <tr <?= $isExpress ? 'style="background-color: #ffe6e6 !important;"' : '' ?>>
                                    <td>
                                        <a href="javascript:void(0);" class="expand-toggle" data-id="<?= $this->Number->format($shipment->id) ?>">
                                            <i class="fa fa-plus-circle text-success"></i>
                                        </a>
                                        <?= $this->Number->format($shipment->id) ?>
                                    </td>
                                    <td>
                                        <?= !empty($shipment->shipment_orders) 
                                            ? h(implode(', ', array_column($shipment->shipment_orders, 'order_id'))) 
                                            : 'N/A' ?>
                                    </td>
                                    <td>
                                        <?= !empty($shipment->sender_name) ? h($shipment->sender_name) : 'N/A' ?>
                                    </td>
                                    <td>
                                        <?= !empty($cityNames) ? h($cityNames) : 'N/A' ?>
                                    </td>
                                    <td>
                                        <?= !empty($zoneNames) ? h($zoneNames) : 'N/A' ?>
                                    </td>
                                    <td>
                                        <?= !empty($municipalityNames) ? h($municipalityNames) : 'N/A' ?>
                                    </td>
                                    <td>
                                        <?php
                                        if (!empty($shipment['_matchingData']['Drivers']['id'])) {

                                            // Display Driver's full name
                                            echo h($shipment['_matchingData']['Users']->first_name . ' ' . $shipment['_matchingData']['Users']->last_name) ;
                                        } elseif (!empty($shipment->partner_name)) {
                                            // Display Delivery Partner's name
                                            echo h($shipment->partner_name);
                                        } else {
                                            // Display 'N/A' if neither is available
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        // Check if shipment_orders exist
                                        if (!empty($shipment->shipment_orders)) {
                                            // Extract 'cash_collected' values and sum them up
                                            $totalCashCollected = array_sum(array_column($shipment->shipment_orders, 'cash_collected'));
                                        } else {
                                            $totalCashCollected = 0;
                                        }

                                        echo h(number_format($totalCashCollected, 0, '', $thousandSeparator)).' '.$currencySymbol;
                                        ?>
                                    </td>
                                    <td>
                                        <?= $shipment->created ? h($shipment->created->format('Y-m-d')) : 'N/A' ?>
                                    </td>
                                    <td>
                                        <?php
                                        if (!empty($shipment['_matchingData']['Drivers']['id'])) { ?>
                                            
                                            <?php if(strtolower($shipment->delivery_status) !== 'picked up') { ?>

                                                <?php if(strtolower($shipment->delivery_status) !== 'return pickup') { ?>
                                                <button class="btn btn-sm assign-shipment-btn" 
                                                    data-shipment-id="<?= $shipment->id ?>"
                                                    data-bs-toggle="modal" data-bs-target="#assignShipmentModal">
                                                    <?= __('Assign') ?>
                                                </button>
                                                <?php } else {
                                                    echo h('Return Pickup');
                                                    }
                                                ?>

                                            <?php } else {
                                                echo h('Picked up');
                                                }
                                            ?>

                                        <?php } elseif (!empty($shipment->partner_name)) { ?>
                                            
                                            <?php if(strtolower($shipment->delivery_status) !== 'picked up') { ?>

                                                <?php if(strtolower($shipment->delivery_status) !== 'return pickup') { ?>
                                                <button class="btn btn-sm assign-shipment-btn" 
                                                    data-shipment-id="<?= $shipment->id ?>"
                                                    data-bs-toggle="modal" data-bs-target="#assignShipmentModal">
                                                    <?= __('Assign') ?>
                                                </button>
                                                <?php } else {
                                                    echo h('Return Pickup');
                                                    }
                                                ?>

                                            <?php } else {
                                                echo h('Picked up');
                                                }
                                            ?>

                                        <?php } else {
                                            // Display 'N/A' if neither is available
                                            echo h('No');
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?= $isExpress ? 'Yes' : 'No' ?>
                                    </td>
                                    <td>
                                        <?php

                                            $statusMap = [
                                                'Pending'    => ['label' => 'Pending', 'class' => 'col-blue'],      // Grey for waiting (neutral)
                                                'Picked up'  => ['label' => 'Picked up', 'class' => 'col-cyan'],      // Cyan for "picked / collected"
                                                'Pickup Failed'     => ['label' => 'Pickup Failed', 'class' => 'col-deep-orange'],
                                                'Return Pickup'    => ['label' => 'Return Pickup', 'class' => 'col-blue'],      // Grey for waiting (neutral)
                                                'Out for Delivery' => ['label' => 'Out for Delivery', 'class' => 'col-purple'],   // Purple for movement
                                                'Partially Delivered' => ['label' => 'Partially Delivered', 'class' => 'col-grey'],
                                                'Delivered'  => ['label' => 'Delivered', 'class' => 'col-green'],     // Green for success
                                                'Failed'     => ['label' => 'Failed', 'class' => 'col-deep-orange'],  // Deep orange for serious failure
                                                'Returned'   => ['label' => 'Returned', 'class' => 'col-brown'],      // Brown for return back
                                            ];

                                        $status = $statusMap[$shipment->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <div class="badge-outline <?= $status['class'] ?>">
                                            <?= h($status['label']) ?>
                                        </div>
                                    </td>
                                    <td class="actions">
                                        <?php if ($canView): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'view', $shipment->id]) ?>"
                                            class="" data-toggle="tooltip" title="View">
                                            <i class="far fa-eye m-r-10"></i>
                                        </a>
                                        <?php endif; ?>
                                        <?php if ($canEdit): ?>
                                            <?php if ($shipment->delivery_status === 'Pending'): ?>
                                            <a href="<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'edit', $shipment->id]) ?>"
                                                class="" data-toggle="tooltip" title="Edit">
                                                <i class="fas fa-pencil-alt m-r-10"></i>
                                            </a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <?php if ($canDelete): ?>
                                            <?php if ($shipment->can_delete): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'delete', $shipment->id]) ?>"
                                                    class="delete-btn" data-toggle="tooltip" title="Delete"
                                                    data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                    <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?> 
                                        <?php endif; ?>    
                                    </td>
                                </tr>

                                <tr class="details-row" style="display: none;">
                                    <td colspan="10">
                                        <table class="table table-sm table-bordered">
                                            <thead class="table-secondary">
                                                <tr>
                                                    <th><?= __('Order ID') ?></th>
                                                    <th><?= __('Customer Name') ?></th>
                                                    <th><?= __('Product SKU') ?></th>
                                                    <th><?= __('Product Name') ?></th>
                                                    <th><?= __('Attributes/Variants') ?></th>
                                                    <th><?= __('Quantity in Shipment') ?></th>
                                                    <th><?= __('Delivery Status') ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>

                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="assignShipmentModal" tabindex="-1" aria-labelledby="assignShipmentLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="assignShipmentLabel"><?= __('Assign Shipment') ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <?= $this->Form->create(null, ['id' => 'assignShipmentForm', 'novalidate' => true, 'type' => 'post']); ?>

            <div class="modal-body">
                <?= $this->Form->hidden('shipment_id', ['id' => 'shipmentIdToAssign']) ?>
                <p><?= __('Are you sure you want to mark this shipment as') ?> <strong><?= __('Picked up') ?></strong>?</p>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                <button type="button" class="btn" id="confirmAssignBtn"><?= __('Assign') ?></button>
            </div>

            <?= $this->Form->end(); ?>

        </div>
    </div>
</div>


<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-daterangepicker/daterangepicker.js'); ?>"></script>
<script>

    $(document).ready(function() {
        // When Assign button is clicked
        $('.assign-shipment-btn').click(function() {
            var shipmentId = $(this).data('shipment-id');
            $('#shipmentIdToAssign').val(shipmentId);
        });

        // When Confirm button in modal is clicked
        $(document).on('click', '#confirmAssignBtn', function () {
            const shipmentId = $('#shipmentIdToAssign').val();

            if (shipmentId) {
                
                $('#confirmAssignBtn').attr('disabled', 'disabled');

                $.ajax({
                    url: '<?= $this->Url->build(["controller" => "Shipment", "action" => "assignShipment"]) ?>',
                    type: 'POST',
                    data: JSON.stringify({ shipment_id: shipmentId }),
                    contentType: "application/json",
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute("csrfToken") ?>'
                    },
                    success: function (response) {
                        if (response.success) {
                            swal({
                                title: '<?= __('Success') ?>',
                                text: '<?= __('Shipment marked as picked up!') ?>',
                                icon: 'success'
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            $('#confirmAssignBtn').removeAttr('disabled');
                            swal('<?= __('Error') ?>', response.message || '<?= __('Failed to assign shipment.') ?>', 'error');
                        }
                    },
                    error: function () {
                        $('#confirmAssignBtn').removeAttr('disabled');
                        swal('<?= __('Error') ?>', '<?= __('Something went wrong. Please try again.') ?>', 'error');
                    }
                });
            }
        });

    });

    // Remove any .details-row before initializing DataTables
    $("#shipmentTable tbody .details-row").remove();

    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#shipmentTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount
    });

    $(document).on("click", ".expand-toggle", function () {
        
        let icon = $(this).find("i");
        let row = $(this).closest("tr");
        let shipmentId = $(this).data("id");
        let detailsRow = row.next(".details-row");

        if (detailsRow.length === 0) {
            // Create and insert details-row dynamically
            let newRow = `
                <tr class="details-row">
                    <td colspan="11">
                        <table class="table table-sm table-bordered details-table">
                            <thead class="table-secondary">
                                <tr>
                                    <th><?= __('Order ID') ?></th>
                                    <th><?= __('Customer Name') ?></th>
                                    <th><?= __('Product SKU') ?></th>
                                    <th><?= __('Product Name') ?></th>
                                    <th><?= __('Variant Name') ?></th>
                                    <th><?= __('Attribute Name') ?></th>
                                    <th><?= __('Quantity in Shipment') ?></th>
                                    <th><?= __('Delivery Status') ?></th>
                                    <th><?= __('Delivery Date') ?></th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </td>
                </tr>`;
            row.after(newRow);
            detailsRow = row.next(".details-row");

            // Fetch data via AJAX
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'fetchOrderItemsByShipmentId']) ?>',
                type: "POST",
                data: { shipment_id: shipmentId },
                headers: { 'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>' },
                success: function (response) {
                    if (response.success) {
                        let tbody = detailsRow.find("tbody");
                        tbody.empty();

                        $.each(response.data, function (index, item) {
                            let newRow = `<tr>
                                <td>${item.order_id}</td>
                                <td>${item.customer_name}</td>
                                <td>${item.sku}</td>
                                <td>${item.product_name}</td>
                                <td>${item.variants}</td>
                                <td>
                                  ${
                                    item.attribute_name && item.attribute_value
                                      ? `${item.attribute_name} : ${item.attribute_value}`
                                      : 'N/A'
                                  }
                                </td>
                                <td>${item.ordered_quantity}</td>
                                <td>${item.delivery_status}</td>
                                <td>${item.delivery_status_date}</td>
                            </tr>`;

                            tbody.append(newRow);
                        });

                        detailsRow.show();
                        icon.removeClass("fa-plus-circle text-success").addClass("fa-minus-circle text-danger");
                    } else {
                        swal('<?= __('Error') ?>', '<?= __('No data found!') ?>', 'error');
                    }
                },
                error: function () {
                    swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
                }
            });

        } else {
            detailsRow.toggle();
            icon.toggleClass("fa-plus-circle text-success fa-minus-circle text-danger");
        }
    });

    // Initialize date picker with a range selection feature
    $("#filterDate").daterangepicker({
        autoUpdateInput: false,
        locale: {
            format: "YYYY-MM-DD",
            cancelLabel: "Clear"
        }
    });

    // Update input field when a date range is selected
    $("#filterDate").on('apply.daterangepicker', function (ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    });

    // Clear input field when cancel is clicked
    $("#filterDate").on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
    });

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    $('.shipment_filter').on('click', function () {

        // var delivery_mode = $("#filterMode option:selected").val();
        var city = $("#filterCity option:selected").val();
        var shipment_status = $("#filterStatus option:selected").val();
        // var municipality = $("#filterMunicipality option:selected").val();
        // var showroom = $("#filterShowroom option:selected").val();
        // var customer_group = $("#filterCustomerGroup option:selected").val();
        var dateRange = $("#filterDate").val();

        table.search('').columns().search('');

        $(".details-row").hide(); // Hide all expanded rows
        $(".order-checkbox").prop("checked", false);
        $(".assign-checkbox").prop("checked", false);

        $("#shipmentTable tbody .details-row").remove();

        if (city && city !== '<?= __("Select City") ?>') {
            table.column(3).search(city, true, false, false).draw();
        }

        if (shipment_status) {
            table.column(11).search(shipment_status, true, false, false).draw();
        }

        // Remove previous date filtering
        $.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(function (fn) {
            return !fn.name || fn.name !== "expectedDeliveryDateFilter";
        });

        // Apply Expected Delivery Date Filter
        if (dateRange) {
            var dates = dateRange.split(" - ");
            var fromDate = new Date(dates[0]);
            var toDate = new Date(dates[1]);

            // Normalize times for accurate filtering
            fromDate.setHours(0, 0, 0, 0);
            toDate.setHours(23, 59, 59, 999);

            $.fn.dataTable.ext.search.push(function expectedDeliveryDateFilter(settings, data, dataIndex) {
                var expectedDeliveryDateStr = data[8].trim(); // Column index 10 (Expected Delivery Date)
                
                // Convert expected delivery date to JavaScript Date object
                var expectedDeliveryDate = new Date(expectedDeliveryDateStr);

                if (!isNaN(expectedDeliveryDate)) {
                    return expectedDeliveryDate >= fromDate && expectedDeliveryDate <= toDate;
                }
                return false; // If date is invalid, exclude the row
            });

            table.draw();
        }

    });

    $('.reset_shipment_filter').on('click', function () {

            table.search('').columns().search('').draw();

            $(".details-row").hide(); // Hide all expanded rows
            $(".order-checkbox").prop("checked", false);
            $(".assign-checkbox").prop("checked", false);

            $('#filterCity').val("Select City").trigger('change');
            $('#filterStatus').val("").trigger('change');

            // Reset Date Filter
            $('#filterDate').val("").trigger('change');

            // Remove the Expected Delivery Date filter from DataTable
            $.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(function (fn) {
                return !fn.name || fn.name !== "expectedDeliveryDateFilter";
            });

            table.draw();

        });

</script>
<?php $this->end(); ?>
