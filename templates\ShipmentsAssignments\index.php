<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Showroom> $showrooms
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style type="text/css">
    .select2-container--default .select2-selection--single.is-invalid {
        border-color: #dc3545 !important;
        border-width: 1px;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    .error-msg {
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 4px;
        display: block;
    }
</style>
<?php $this->end(); ?>

<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item"><a
                    href="<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'index']) ?>"><?= __('Shipment') ?></a>
            </li>
        <li class="breadcrumb-item active"><?= __('Assign Shipment to Driver/Delivery Partner') ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Assign Shipment to Driver/Delivery Partner") ?></h4>
            </div>
            <div class="card-body">

                <div class="row g-3 mb-3">
                    
                    <div class="col-md-2">
                        <label class="fw-bold" for="orderType"><?= __('Order Type:') ?></label>
                        <select id="filterMode" class="form-control select2">
                            <option value=""><?= __('All') ?></option>
                            <option value="Yes"><?= __('Express Delivery') ?></option>
                            <option value="No"><?= __('Standard Delivery') ?></option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="fw-bold"><?= __('City:') ?></label>
                        <select class="form-control select2" id="filterCity">
                            <option><?= __('Select City') ?></option>
                            <?php foreach ($cities as $city): ?>
                                <option value="<?= $city->city_name; ?>">
                                    <?= htmlspecialchars($city->city_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="fw-bold"><?= __('Zone:') ?></label>
                        <select class="form-control select2" id="filterZone">
                            <option><?= __('Select Zone') ?></option>
                            <?php foreach ($zones as $zone): ?>
                                <option value="<?= $zone->name; ?>">
                                    <?= htmlspecialchars($zone->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="fw-bold"><?= __('Municipality:') ?></label>
                        <select class="form-control select2" id="filterMunicipality">
                            <option><?= __('Select Municipality') ?></option>
                            <?php foreach ($municipalities as $municipality): ?>
                                <option value="<?= $municipality->name; ?>">
                                    <?= htmlspecialchars($municipality->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="fw-bold"><?= __('Showroom:') ?></label>
                        <select class="form-control select2" id="filterShowroom">
                            <option><?= __('Select Showroom') ?></option>
                            <?php foreach ($showrooms as $showroom): ?>
                                <option value="<?= $showroom->name; ?>">
                                    <?= htmlspecialchars($showroom->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="fw-bold"><?= __('Warehouse:') ?></label>
                        <select class="form-control select2" id="filterWarehouse">
                            <option><?= __('Select Warehouse') ?></option>
                            <?php foreach ($warehouses as $warehouse): ?>
                                <option value="<?= $warehouse->name; ?>">
                                    <?= htmlspecialchars($warehouse->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label></label>
                        <button class="btn btn-primary shipment_filter" id="filter">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                        </button>

                        <button type="reset" class="btn btn-primary reset_shipment_filter"><i class="fas fa-redo-alt"></i></button>
                    </div>

                </div>

                <div class="form-group row">
                    <label class="col-sm-2 col-form-label fw-bold"><?= __('Delivery Type') ?></label>
                    <div class="col-sm-5">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="delivery_type" id="deliveryTypePartner" value="delivery_partner" checked>
                            <label class="form-check-label" for="deliveryTypePartner"><?= __('Delivery Partner') ?></label>
                        </div>
                        <div class="form-check form-check-inline" id="driverOption">
                            <input class="form-check-input" type="radio" name="delivery_type" id="deliveryTypeDriver" value="driver">
                            <label class="form-check-label" for="deliveryTypeDriver"><?= __('Driver') ?></label>
                        </div>
                    </div>
                </div>

                <div class="form-group row toDriverSelectDiv">
                    <label for="driverSelect" class="col-sm-2 col-form-label fw-bold"><?= __("To Driver") ?> <sup class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <select class="form-control form-select select2 d-flex align-items-center" id="driverSelect" name="Driver">
                            <option><?= __('-- Select Driver --') ?></option>
                            <?php foreach ($drivers as $driver): ?>
                                <option value="<?= $driver->id; ?>">
                                    <?= htmlspecialchars($driver->user->first_name.' '.$driver->user->last_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span id="driver_error" style="color: #dc3545;display: none;"><?= __('Select Driver') ?></span>
                    </div>
                </div>

                <div class="form-group row toPartnerSelectDiv">
                    <label for="partnerSelect" class="col-sm-2 col-form-label fw-bold"><?= __("To Delivery Partner") ?> <sup class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <select class="form-control form-select select2 d-flex align-items-center" id="partnerSelect" name="Delivery Partner">
                            <option><?= __('-- Select Delivery Partner --') ?></option>
                            <?php foreach ($delivery_partners as $partner): ?>
                                <option value="<?= $partner->id; ?>">
                                    <?= htmlspecialchars($partner->partner_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span id="partner_error" style="color: #dc3545;display: none;"><?= __('Select Delivery Partner') ?></span>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped" id="shipmentTable" role="grid">
                    <thead>
                        <tr>
                            <th><?= __('Select') ?></th>
                            <th><?= __('Shipment Id') ?></th>
                            <th><?= __('Order ID') ?></th>
                            <th><?= __('From') ?></th>
                            <th><?= __('City') ?></th>
                            <th><?= __('Zone') ?></th>
                            <th><?= __('Municipality') ?></th>
                            <th><?= __('Express Order') ?></th>
                            <th id="status"><?= __('Status') ?></th>
                            <th class="actions"><?= __('Actions') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($shipments as $shipment): ?>

                            <?php
                                // Collect all unique city, zone, and municipality names from shipment orders
                                $cities = [];
                                $zones = [];
                                $municipalities = [];
                                $hasExpressDelivery = false;

                                if (!empty($shipment->shipment_orders)) {
                                    foreach ($shipment->shipment_orders as $order) {
                                        if (!empty($order->city) && !empty($order->city->city_name)) {
                                            $cities[] = $order->city->city_name;
                                        }
                                        if (!empty($order->zone) && !empty($order->zone->name)) {
                                            $zones[] = $order->zone->name;
                                        }
                                        if (!empty($order->municipality) && !empty($order->municipality->name)) {
                                            $municipalities[] = $order->municipality->name;
                                        }
                                        if (!empty($order->order) && $order->order->delivery_mode_type === 'express') {
                                            $hasExpressDelivery = true;
                                        }
                                    }
                                }

                                $cityNames = implode(', ', array_unique($cities));
                                $zoneNames = implode(', ', array_unique($zones));
                                $municipalityNames = implode(', ', array_unique($municipalities));
                                $deliveryModeText = $hasExpressDelivery ? 'Yes' : 'No';
                            ?>

                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input expand order-checkbox" name="shipment_id[]" value="<?= h($shipment->id) ?>">
                                </td>
                                <td>
                                    <?= $this->Number->format($shipment->id) ?>
                                </td>
                                <td>
                                    <?= !empty($shipment->shipment_orders) 
                                        ? h(implode(', ', array_column($shipment->shipment_orders, 'order_id'))) 
                                        : 'N/A' ?>
                                </td>
                                <td>
                                    <?= !empty($shipment->sender_name) ? h($shipment->sender_name) : 'N/A' ?>
                                </td>
                                <td>
                                    <?= !empty($cityNames) ? h($cityNames) : 'N/A' ?>
                                </td>
                                <td>
                                    <?= !empty($zoneNames) ? h($zoneNames) : 'N/A' ?>
                                </td>
                                <td>
                                    <?= !empty($municipalityNames) ? h($municipalityNames) : 'N/A' ?>
                                </td>
                                <td><?= h($deliveryModeText) ?></td>
                                <td>
                                    <?php
                                        $statusMap = [
                                            'Pending'    => ['label' => 'Pending', 'class' => 'col-blue'],      // Grey for waiting (neutral)
                                            'Picked Up'  => ['label' => 'Picked Up', 'class' => 'col-cyan'],      // Cyan for "picked / collected"
                                            'Pickup Failed'     => ['label' => 'Pickup Failed', 'class' => 'col-deep-orange'],
                                            'Return Pickup'    => ['label' => 'Return Pickup', 'class' => 'col-blue'],
                                            'Out for Delivery' => ['label' => 'Out for Delivery', 'class' => 'col-purple'],   // Purple for movement
                                            'Partially Delivered' => ['label' => 'Partially Delivered', 'class' => 'col-grey'],
                                            'Delivered'  => ['label' => 'Delivered', 'class' => 'col-green'],     // Green for success
                                            'Failed'     => ['label' => 'Failed', 'class' => 'col-deep-orange'],  // Deep orange for serious failure
                                            'Returned'   => ['label' => 'Returned', 'class' => 'col-brown'],      // Brown for return back
                                        ];

                                    $status = $statusMap[$shipment->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                    ?>
                                    <div class="badge-outline <?= $status['class'] ?>">
                                        <?= h($status['label']) ?>
                                    </div>
                                </td>
                                <td class="actions">
                                    <a href="javascript:void(0);" class="expand-toggle" data-id="<?= $this->Number->format($shipment->id) ?>">
                                        <i class="fa fa-plus-circle text-success"></i>
                                    </a>
                                </td>
                            </tr>

                            <tr class="details-row" style="display: none;">
                                <td colspan="10">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-secondary">
                                            <tr>
                                                <th><?= __('Order ID') ?></th>
                                                <th><?= __('Customer Name') ?></th>
                                                <th><?= __('Product SKU') ?></th>
                                                <th><?= __('Product Name') ?></th>
                                                <th><?= __('Attributes/Variants') ?></th>
                                                <th><?= __('Quantity in Shipment') ?></th>
                                                <th><?= __('Delivery Status') ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>

                        <?php endforeach; ?>
                    </tbody>
                </table>
                </div>
                <button class="btn btn-success mt-3" id="assignShipmentBtn"><?= __('Assign Shipment') ?></button>
            </div>
    </div>
</div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>

    // Remove any .details-row before initializing DataTables
    $("#shipmentTable tbody .details-row").remove();

    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#shipmentTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount
    });

    // Initialize based on the currently checked radio button
    var selectedValue = $('input[name="delivery_type"]:checked').val();
    toggleSelectBox(selectedValue);

    // Handle radio button change event to toggle select boxes
    $('input[name="delivery_type"]').change(function() {
        var selectedValue = $('input[name="delivery_type"]:checked').val();
        toggleSelectBox(selectedValue);
    });

    // Function to toggle between showroom and warehouse dropdowns
    function toggleSelectBox(value) {        

        // Uncheck all shipment checkboxes
        $('input[name="shipment_id[]"]').prop('checked', false);

        if (value === '<?= __('delivery_partner') ?>') {

            $('.toPartnerSelectDiv').show();
            $('.toDriverSelectDiv').hide();

            $('#partnerSelect').attr('name','delivery_partner_id');
            $('#driverSelect').removeAttr('name');

            // Filter out rows with "Abidjan" in city column (column 4)
            table.column(4).search('^(?!.*Abidjan).*$', true, false).draw();

        } else if (value === '<?= __('driver') ?>') {

            $('.toPartnerSelectDiv').hide();
            $('.toDriverSelectDiv').show();

            $('#driverSelect').attr('name','driver_id');
            $('#partnerSelect').removeAttr('name');

            // Clear city filter if switching back to driver mode
            table.column(4).search('', true, false).draw();
        }
    }

    $(document).on("click", ".expand-toggle", function () {
        
        let icon = $(this).find("i");
        let row = $(this).closest("tr");
        let shipmentId = $(this).data("id");
        let detailsRow = row.next(".details-row");

        if (detailsRow.length === 0) {
            // Create and insert details-row dynamically
            let newRow = `
                <tr class="details-row">
                    <td colspan="11">
                        <table class="table table-sm table-bordered details-table">
                            <thead class="table-secondary">
                                <tr>
                                    <th><?= __('Order ID') ?></th>
                                    <th><?= __('Customer Name') ?></th>
                                    <th><?= __('Product SKU') ?></th>
                                    <th><?= __('Product Name') ?></th>
                                    <th><?= __('Variant Name') ?></th>
                                    <th><?= __('Attribute Name') ?></th>
                                    <th><?= __('Quantity in Shipment') ?></th>
                                    <th><?= __('Delivery Status') ?></th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </td>
                </tr>`;
            row.after(newRow);
            detailsRow = row.next(".details-row");

            // Fetch data via AJAX
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'fetchOrderItemsByShipmentId']) ?>',
                type: "POST",
                data: { shipment_id: shipmentId },
                headers: { 'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>' },
                success: function (response) {
                    if (response.success) {
                        let tbody = detailsRow.find("tbody");
                        tbody.empty();

                        $.each(response.data, function (index, item) {
                            let newRow = `<tr>
                                <td>${item.order_id}</td>
                                <td>${item.customer_name}</td>
                                <td>${item.sku}</td>
                                <td>${item.product_name}</td>
                                <td>${item.variants}</td>
                                <td>
                                  ${
                                    item.attribute_name && item.attribute_value
                                      ? `${item.attribute_name} : ${item.attribute_value}`
                                      : 'N/A'
                                  }
                                </td>
                                <td>${item.ordered_quantity}</td>
                                <td>${item.delivery_status}</td>
                            </tr>`;

                            tbody.append(newRow);
                        });

                        detailsRow.show();
                        icon.removeClass("fa-plus-circle text-success").addClass("fa-minus-circle text-danger");
                    } else {
                        swal('<?= __('Error') ?>', '<?= __('No data found!') ?>', 'error');
                    }
                },
                error: function () {
                    swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
                }
            });

        } else {
            detailsRow.toggle();
            icon.toggleClass("fa-plus-circle text-success fa-minus-circle text-danger");
        }
    });

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    $('.shipment_filter').on('click', function () {

        var delivery_mode = $("#filterMode option:selected").val();
        var city = $("#filterCity option:selected").val();
        var zone = $("#filterZone option:selected").val();
        var municipality = $("#filterMunicipality option:selected").val();
        var showroom = $("#filterShowroom option:selected").val();
        var warehouse = $("#filterWarehouse option:selected").val();

        table.search('').columns().search('');

        $(".details-row").hide(); // Hide all expanded rows
        $(".order-checkbox").prop("checked", false);
        $(".assign-checkbox").prop("checked", false);

        $("#shipmentTable tbody .details-row").remove();

        if (delivery_mode && delivery_mode !== '') {
            table.column(7).search(delivery_mode, true, false, false).draw();
        }

        if (city && city !== '<?= __("Select City") ?>') {
            table.column(4).search(city, true, false, false).draw();
        }

        if (zone && zone !== '<?= __("Select Zone") ?>') {
            table.column(5).search(zone, true, false, false).draw();
        }

        if (municipality && municipality !== '<?= __("Select Municipality") ?>') {
            table.column(6).search(municipality, true, false, false).draw();
        }

        if (showroom && showroom !== '<?= __("Select Showroom") ?>') {
            table.column(3).search(showroom, true, false, false).draw();
        }

        if (warehouse && warehouse !== '<?= __("Select Warehouse") ?>') {
            table.column(3).search(warehouse, true, false, false).draw();
        }

        if (city && city.toLowerCase().includes('abidjan')) {
            $('input[name="delivery_type"][value="delivery_partner"]').prop('disabled', true);
            $('input[name="delivery_type"][value="driver"]').prop('checked', true).trigger('change');
        } else {
            $('input[name="delivery_type"][value="delivery_partner"]').prop('disabled', false);
        }

    });

    $('.reset_shipment_filter').on('click', function () {

        table.search('').columns().search('').draw();

        $(".details-row").hide(); // Hide all expanded rows
        $(".order-checkbox").prop("checked", false);
        $(".assign-checkbox").prop("checked", false);

        $('#filterMode').val("").trigger('change');
        $('#filterCity').val("Select City").trigger('change');
        $('#filterZone').val("Select Zone").trigger('change');
        $('#filterMunicipality').val("Select Municipality").trigger('change');
        $('#filterShowroom').val("Select Showroom").trigger('change');
        $('#filterWarehouse').val("Select Warehouse").trigger('change');

        // ✅ Reset radio buttons
        $('input[name="delivery_type"]').prop('disabled', false); // Enable both
        $('#driverRadio').prop('checked', true).trigger('change');  

        table.draw();

    });

    $("#assignShipmentBtn").click(function (event) {

        event.preventDefault();

        let isValid = true;
        $('#driver_error, #partner_error').hide();
        $('#shipment_error').remove();

        if ($('input[name="shipment_id[]"]:checked').length === 0) {
            isValid = false;
            swal('<?= __('Error') ?>', '<?= __('Please select at least one shipment.') ?>', 'error');
        }

        const deliveryType = $('input[name="delivery_type"]:checked').val();
        const deliveryId = deliveryType === 'driver' ? $("#driverSelect").val() : $("#partnerSelect").val();

        if (deliveryType === 'driver') {
            if (!deliveryId || deliveryId === '<?= __('-- Select Driver --') ?>') {
                isValid = false;
                $('#driver_error').show();
                $('#driverSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');
            }
        } else {
            if (!deliveryId || deliveryId === '<?= __('-- Select Delivery Partner --') ?>') {
                isValid = false;
                $('#partner_error').show();
                $('#partnerSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');
            }
        }

        if (!isValid) return;

        const shipment_ids = $('input[name="shipment_id[]"]:checked').map(function () {
            return $(this).val();
        }).get();

        swal({
            title: '<?= __('Confirm Assign Shipment') ?>',
            text: '<?= __('Are you sure you want to assign the shipment?') ?>',
            icon: 'warning',
            buttons: ['Cancel', 'Yes, Assign'],
            dangerMode: true,
        }).then((willAssign) => {
            if (willAssign) {
                $("#assignShipmentBtn").attr('disabled', 'disabled');

                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'ShipmentsAssignments', 'action' => 'assignShipment']) ?>',
                    method: "POST",
                    data: JSON.stringify({
                        delivery_type: deliveryType === 'driver' ? 'Driver' : 'Delivery Partner',
                        deliveryId: deliveryId,
                        shipment_ids: shipment_ids
                    }),
                    contentType: "application/json",
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        if (response.status === 'partial') {
                            swal('<?= __('Partial Success') ?>', `<?= __('Some shipments failed.') ?> IDs: ${response.failed_ids.join(', ')}`, 'warning');
                        } else {
                            swal('<?= __('Success') ?>', response.message, 'success').then(() => {
                                window.location.href = '<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'index']) ?>';
                            });
                        }
                    },
                    error: function (xhr) {
                        $("#assignShipmentBtn").removeAttr('disabled');
                        let errorMsg = '<?= __('Error assigning shipment.') ?>';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }

                        swal('<?= __('Error') ?>', errorMsg, 'error');
                    }
                });
            }
        });
    });



</script>
<?php $this->end(); ?>
