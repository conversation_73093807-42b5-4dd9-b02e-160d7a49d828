<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Showroom $showroom
 * @var \Cake\Collection\CollectionInterface|string[] $cities
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/intlTelInput/css/intlTelInput.min.css') ?>">
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/intlTelInput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?= h($mapApiKey) ?>&libraries=places"></script>
<style type="text/css">
    .is-invalid {
        border: 1px solid red !important;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 ********* 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    select.is-invalid {
        border-color: #dc3545 !important;
    }

    .error {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 4px;
    }
    .iti--allow-dropdown
    {
        width: 100%;
    }
    #map {

        height: 400px;
        width: 100%;
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Showrooms', 'action' => 'index']) ?>"><?= __('Showrooms') ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __('Add') ?></li>
    </ul>
    <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
        <small class="p-10 fw-bold"><?= __('BACK') ?></small>
        <span class="rotate me-2">⤣</span>
    </button>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20"><?= __('Add Showroom') ?></h6>
            <?php echo $this->Form->create($showroom, ['id' => 'add', 'novalidate' => true, 'type' => 'file']); ?>
            <?php if (!empty($showroom->getErrors())): ?>
                <div class="validation-errors">
                    <?php foreach ($showroom->getErrors() as $field => $errors): ?>
                        <div class="field-errors">
                            <strong><?php echo h(ucwords($field)); ?>:</strong>
                            <ul>
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo h($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label fw-bold"><?= __('Showroom Name') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name',
                        'placeholder' => 'Showroom Name',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="supervisor" class="col-sm-2 col-form-label fw-bold"><?= __('Supervisor') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <select data-live-search="true" id="supervisor" name="showroom_supervisor" class="form-control select2">
                        <option value=""><?= __('Select Supervisor') ?></option>
                        <?php if(!empty($supervisors)) { foreach ($supervisors as $supervisor): ?>
                            <option value="<?= h($supervisor['id']) ?>"><?= h($supervisor['first_name'].' '.$supervisor['last_name']) ?></option> 
                        <?php endforeach; } ?>
                    </select>
                </div>
            </div>

            <div class="form-group row">
                <label for="city_id" class="col-sm-2 col-form-label fw-bold"><?= __('Location') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('city_id', 
                    ['id' => 'city_id_model',
                        'type' => 'select',
                        'empty' => 'Select Location',
                        'label' => false,
                        'div' => false,
                        'title' => 'Select Location',
                        'options' => $cities,
                        'data-live-search' => "true",
                        'class' => 'form-control select2'
                    ]);
                    ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="municipality_id" class="col-sm-2 col-form-label fw-bold"><?= __('Municipality') ?></label>
                <div class="col-sm-5 main-field">
                    <select class="form-control select2" name="municipality_id" id="filterMunicipality">
                        <option value=""><?= __('Select Municipality') ?></option>
                        <?php foreach ($municipalities as $municipality): ?>
                            <option value="<?= $municipality->id; ?>">
                                <?= htmlspecialchars($municipality->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="form-group row">
                <label for="address" class="col-sm-2 col-form-label fw-bold"><?= __('Address') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <input type="hidden" id="latitude" name="latitude">
                    <input type="hidden" id="longitude" name="longitude">
                    <?php echo $this->Form->control('address', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'address',
                        'placeholder' => 'Address',
                        'label' => false,
                        'readonly' => true
                    ]); ?>
                    <div id="map" class="mt-2"></div>
                </div>
            </div>

            <div class="form-group row">
                <label for="area_sq_mts" class="col-sm-2 col-form-label fw-bold"><?= __('Area Sq mts') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('area_sq_mts', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'area_sq_mts',
                        'placeholder' => 'Area Sq mts',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                    <label for="web-image" class="col-sm-2 col-form-label fw-bold"><?= __('Image') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('image[]', [
                            'type' => 'file',
                            'class' => 'form-control',
                            'id' => 'imageInput',
                            'placeholder' => __('Web Image'),
                            'accept' => implode(',', $webImageType),
                            'data-max-size' => '10MB',
                            'multiple' => 'multiple',
                            'label' => false
                        ]); ?>
                        <span><?= $file_acceptance_msg ?></span>
                        <div id="previeContainer">
                            <ul id="imagePreviewContainer">

                            </ul>
                        </div>
                    </div>
                </div>

            <div class="form-group row">
                <label for="email" class="col-sm-2 col-form-label fw-bold"><?= __('Email or Emails') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('email', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'email',
                        'placeholder' => 'Enter email addresses, separated by commas',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="contact_number" class="col-sm-2 col-form-label fw-bold"><?= __('Phone') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <input id="contactNumberCountryCode" type="hidden" name="contact_country_code">
                    <?php echo $this->Form->control('contact_number', [
                        'type' => 'tel',
                        'class' => 'form-control tel',
                        'id' => 'contact_number',
                        'placeholder' => 'Phone Number',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="Showroom_timing" class="col-sm-2 col-form-label fw-bold"><?= __('Showroom Timings') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('showroom_timing', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'Showroom_timing',
                        'placeholder' => 'Eg: “7 am to 9pm, Monday to Saturday”',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="showroom_manager" class="col-sm-2 col-form-label fw-bold"><?= __('Showroom Manager') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <select data-live-search="true" id="showroom_manager" name="showroom_manager" class="form-control select2">
                        <option value=""><?= __('Select Showroom Manager') ?></option>
                        <?php if(!empty($showroom_managers)) { foreach ($showroom_managers as $manager): ?>
                            <option value="<?= h($manager['id']) ?>"><?= h($manager['first_name'].' '.$manager['last_name']) ?></option> 
                        <?php endforeach; } ?>
                    </select>
                </div>
            </div>

            <div class="form-group row">
                <label for="min_product_quantity" class="col-sm-2 col-form-label fw-bold"><?= __('Minimum Product Quantity') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('min_product_quantity', [
                        'type' => 'number',
                        'min' => '1',
                        'class' => 'form-control',
                        'id' => 'min_product_quantity',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
            <div class="col-sm-10 offset-sm-2">
                <button type="submit" class="btn"><?= __('Save') ?></button>
                <button type="reset" id="resetButton" class="btn"><?= __('Reset') ?></button>
            </div>
        </div>
        </div>
        </form>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script>


    let map;
    let marker;
    let geocoder;

    function initMap() {
        // Initialize map at a default location
        const defaultLocation = { lat: 5.359952, lng: -4.008256 }; // Abidjan, Ivory Coast

        // Initialize map
        map = new google.maps.Map(document.getElementById("map"), {
            center: defaultLocation,
            zoom: 14,
        });

        // Initialize geocoder
        geocoder = new google.maps.Geocoder();

        // Add a draggable marker
        marker = new google.maps.Marker({
            position: defaultLocation,
            map: map,
            draggable: true,
        });

        // Fetch address on marker drag
        marker.addListener("dragend", () => {
            const position = marker.getPosition();
            updateLatLngInputs(position.lat(), position.lng());
            fetchAddress(position.lat(), position.lng());
        });

        // Set default values in hidden fields
        document.getElementById("latitude").value = defaultLocation.lat;
        document.getElementById("longitude").value = defaultLocation.lng;

        // Fetch address on initial load
        fetchAddress(defaultLocation.lat, defaultLocation.lng);
    }

    function updateLatLngInputs(lat, lng) {
        document.getElementById("latitude").value = lat;
        document.getElementById("longitude").value = lng;
    }

    function fetchAddress(lat, lng) {
        const latlng = { lat: parseFloat(lat), lng: parseFloat(lng) };

        geocoder.geocode({ location: latlng }, (results, status) => {
            if (status === "OK") {
                if (results[0]) {
                    // Display the address in the input field
                    document.getElementById("address").value = results[0].formatted_address;
                    console.log("Address:", results[0].formatted_address);
                } else {
                    console.log("No results found");
                    document.getElementById("address").value = "No address found";
                }
            } else {
                console.log("Geocoder failed due to: " + status);
                document.getElementById("address").value = "Error fetching address";
            }
        });
    }

    // Initialize the map when the page loads
    window.onload = initMap;

    $(document).ready(function() {
        $('#zoneSelect').on('change', function() {
            const selectedOption = $(this).find(':selected');
            const supervisorName = selectedOption.data('supervisor');
            $('#supervisorName').val(supervisorName || '');
        });
    });

    //FAX 
    var inputFax = document.querySelector("#contact_number");
    var itiFax = window.intlTelInput(inputFax, {
        initialCountry: "ci",
        separateDialCode: true,
        preferredCountries: ["ci", "us", "gb"],
        utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
    });


    function updateFaxCountryCode() {

        $('#contactNumberCountryCode').val(itiFax.getSelectedCountryData().dialCode);
    }

    $('#contactNumberCountryCode').val(itiFax.getSelectedCountryData().dialCode);

    inputFax.addEventListener("countrychange", updateFaxCountryCode);

    let allFiles = [];

    document.getElementById('imageInput').addEventListener('change', function (event) {

        var files = Array.from(event.target.files);

        if(files.length == 0)
        {
            allFiles = [];
            renderPreviews();
            return false;
        }

        var validFiles = [];
        var invalidFiles = [];

        let processedFiles = 0;
        let totalFiles = files.length;

        // let dimension = <?= $webImageMinWidth ?>+'x'+<?= $webImageMaxWidth ?>+' and '+<?= $webImageMinHeight ?>+'x'+<?= $webImageMaxHeight ?>

        files.forEach(function(file, index) {

            var fileExtension = file.name.split('.').pop().toLowerCase();
            var allowedExtensions = ['jpg', 'jpeg', 'png'];

            // Check if the file has a valid extension
            if (allowedExtensions.includes(fileExtension)) {
                var img = new Image();
                img.src = URL.createObjectURL(file);

                img.onload = function () {
                    var width = img.naturalWidth;
                    var height = img.naturalHeight;
                    var fileSize = file.size / 1024 / 1024;  // Convert file size to MB

                    // Validate file size (max 10MB) and dimensions (width: 300-400px, height: 200-320px)
                    if (fileSize <= <?= $webImageSize ?> && width >= <?= $webImageMinWidth ?> && width <= <?= $webImageMaxWidth ?> && height >= <?= $webImageMinHeight ?> && height <= <?= $webImageMaxHeight ?>) {
                        validFiles.push(file);  // Add to valid files array
                    } else {

                        invalidFiles.push({file: file.name, reason: '<?= __('Image dimensions should be between '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight) ?>'});
                    }

                    processedFiles++;

                    // When all files are processed, update the file input and show alerts
                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }

                };

                img.onerror = function () {
                    invalidFiles.push({file: file, reason: '<?= __('Unable to load image') ?>'});
                    processedFiles++;

                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }
                };
            }
            else
            {

                invalidFiles.push({file: file.name, reason: '<?= __('Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.') ?>'});

                processedFiles++;

                if (processedFiles === totalFiles) {
                    finalizeFileProcessing(validFiles, invalidFiles);
                }
            }
        });

    });

    // Finalize file processing
    function finalizeFileProcessing(validFiles, invalidFiles) {


        var html = "<ul>";
        for(var i=0; i < invalidFiles.length; i++)
        {
           html += `<li>${invalidFiles[i].file} - ${invalidFiles[i].reason}</li>`;
        }
        html += '</ul>';

        const wrapper = document.createElement('div');
        wrapper.innerHTML = html;

        if(invalidFiles.length > 0)
        {
            swal({
                title: "<?= __("Error") ?>",
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: "true"
            });
        }

        var dataTransfer = new DataTransfer();

        validFiles.forEach(function(file) {
            dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
        });

        document.getElementById('imageInput').files = dataTransfer.files;  // Update input files

        let newFiles = validFiles;
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();

    }

    // Function to update the file input with only valid files
    function updateFileInput(validFiles) {

        var dataTransfer = new DataTransfer();

        validFiles.forEach(function(file) {
            dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
        });

        document.getElementById('imageInput').files = dataTransfer.files;  // Update input files

        let newFiles = validFiles;
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();
    }

    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);

            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            if (file.url) {
                li.innerHTML = `
                        <img src="${file.url}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
            } else {
                let reader = new FileReader();
                reader.onload = function (e) {
                    li.innerHTML = `
                        <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            }

            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function (e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            allFiles.splice(index, 1);

            //Remove file from input
            var dataTransfer = new DataTransfer();

            allFiles.forEach(function(file) {
                dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
            });

            document.getElementById('imageInput').files = dataTransfer.files;

            renderPreviews();
        }
    });



    $(document).ready(function () {
        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });

        $('#resetButton').on('click', function() {
            $('#city_id_model').val(null).trigger('change');
            $('#showroom_manager').val(null).trigger('change');
            $('#supervisor').val(null).trigger('change');
        });
    });

    $(document).ready(function () {

        $.validator.addMethod("multiemail", function(value, element) {
            if (this.optional(element)) {
                return true;
            }

            var emails = value.split(','), // Split by comma
                valid = true;

            // Regex pattern for email validation
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            // Validate each email
            for (var i = 0; i < emails.length; i++) {
                emails[i] = emails[i].trim(); // Trim whitespace
                if (!emailPattern.test(emails[i])) {
                    valid = false;
                    break;
                }
            }

            return valid;
        }, "<?= __('Please enter valid email addresses separated by a comma.') ?>");

        $.validator.addMethod("pattern", function (value, element, param) {
            if (this.optional(element)) {
                return true;
            }
            if (typeof param === "string") {
                param = new RegExp("^(?:" + param + ")$");
            }
            return param.test(value);
        }, "<?= __('Invalid format.') ?>");

        $.validator.addMethod("validPhoneContact", function(value, element) {
            return itiFax.isValidNumber(); // Use intlTelInput's isValidNumber method
        }, "<?= __('Please enter a valid contact number') ?>");

        $("#add").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true
                },
                'showroom_supervisor': {
                    required: true
                },
                'address': {
                    required: true
                },
                'email': {
                    required: true,
                    multiemail: true
                },
                'contact_number': {
                    required: true,
                    validPhoneContact: true
                },
                'city_id': {
                    required: true
                },
                'showroom_manager': {
                    required: true
                }
            },
            messages: {
                'name': {
                    required: "<?= __('Please enter showroom name') ?>"
                },
                'showroom_supervisor': {
                    required: "<?= __('Please select a supervisor') ?>"
                },
                'address': {
                    required: "<?= __('Please enter address') ?>"
                },
                'email': {
                    required: "<?= __('Please enter email') ?>",
                    multiemail: "<?= __('Please enter valid email addresses separated by a comma.') ?>"
                },
                'contact_number': {
                    required: "<?= __('Please enter contact number') ?>",
                },
                'city_id': {
                    required: "<?= __('Please select a location') ?>"
                },
                'showroom_manager': {
                    required: "<?= __('Please select a showroom manager') ?>"
                }
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');

                // If it's a select2 or select box, also add to the container
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').addClass('is-invalid');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');

                // Remove from select2 or select container too
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').removeClass('is-invalid');
                }
            },
            submitHandler: function (form) {
                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            }
        });
    });
</script>
<?php $this->end(); ?>
