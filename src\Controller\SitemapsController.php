<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Inflector;
use Cake\View\XmlView;

class SitemapsController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout(null);
        $this->Authentication->addUnauthenticatedActions(['index', 'products', 'categories', 'brands', 'pages']);
        
        $this->Brands = $this->fetchTable('Brands');
        $this->Categories = $this->fetchTable('Categories');
        $this->Products = $this->fetchTable('Products');
        $this->ContentPages = $this->fetchTable('ContentPages');
    }

    public function viewClasses(): array
    {
        return [XmlView::class];
    }

    // 🗂️ Sitemap Index
    public function index()
    {
        
    }

    // 📦 Products Sitemap
    public function products()
    {        
        $products = $this->Products->find()
            ->select(['id', 'url_key', 'modified'])
            ->where(['status' => 'A']) // Only active products
            ->all();

        $this->set(compact('products'));
        //$this->viewBuilder()->setOption('serialize', ['products']);
    }

    // 🏷️ Categories Sitemap
    public function categories()
    {        
        $categories = $this->Categories->find()
            ->select(['id', 'url_key', 'modified'])
            ->where(['status' => 'A'])
            ->all();

        $this->set(compact('categories'));
        //$this->viewBuilder()->setOption('serialize', ['categories']);        
    }

    // 🔥 Brands Sitemap
    public function brands()
    {
        $brands = $this->Brands->find()
            ->select(['id', 'url_key', 'modified'])
            ->where(['status' => 'A'])
            ->all();

        $this->set(compact('brands'));
        //$this->viewBuilder()->setOption('serialize', ['brands']);
    }

    // 📄 Content Pages Sitemap
    public function pages()
    {
        $pages = $this->Brands->find()
            ->select(['id', 'url_key', 'modified'])
            ->where(['status' => 'A'])
            ->all();

        $this->set(compact('ContentPages'));
        //$this->viewBuilder()->setOption('serialize', ['pages']);
    }
}
