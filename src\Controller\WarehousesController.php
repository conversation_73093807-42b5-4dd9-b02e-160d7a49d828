<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\ORM\TableRegistry;
use Cake\ORM\Query;
use Cake\I18n\FrozenDate;

/**
 * Warehouses Controller
 *
 * @property \App\Model\Table\WarehousesTable $Warehouses
 */
class WarehousesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    protected \App\Model\Table\CitiesTable $Cities;
    protected \App\Model\Table\MunicipalitiesTable $Municipalities;
    protected \App\Model\Table\UsersTable $Users;
    protected \App\Model\Table\ProductStocksTable $ProductStocks;
    protected \App\Model\Table\StockMovementsTable $StockMovements;
    protected \App\Model\Table\RolesTable $Roles;
    protected \App\Model\Table\ProductsTable $Products;
    protected \App\Model\Table\ProductVariantsTable $ProductVariants;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $StockReturns;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Cities = $this->fetchTable('Cities');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->Users = $this->fetchTable('Users');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->Roles = $this->fetchTable('Roles');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->StockReturns = $this->fetchTable('StockReturns');
    }

    public function index()
    {
        $query = $this->Warehouses->find()
            ->contain(['Managers', 'Cities', 'Municipalities'])
            ->leftJoinWith('ProductStocks', function ($q) {
                return $q->select([
                    'total_stock' => $q->func()->sum('ProductStocks.quantity + ProductStocks.reserved_stock + ProductStocks.defective_stock')
                ]);
            })
            ->where(['Warehouses.status !=' => 'D'])
            ->group('Warehouses.id')
            ->applyOptions(['order' => ['Warehouses.name' => 'ASC']]);
        $warehouses = $query->all();

        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $TotalProductItems = 0;
        $InStockProducts = 0;
        $LowStockProduct = 0;
        $DeliveriesScheduled = 0;
        $TotalProducts = $this->Products->find()
            ->where(['Products.status !=' => 'D'])->toArray();

        $TotalProductVaraints = $this->ProductVariants->find()
            ->where(['ProductVariants.status !=' => 'D'])->toArray();

        $TotalProducts = count($TotalProducts) + count($TotalProductVaraints);

        $InStockProducts = $this->ProductStocks->find()
            ->select([
                'product_id',
                'product_variant_id',
                'product_attribute_id',
                'total_quantity' => 'ProductStocks.quantity',
                'min_product_quantity' => 'Categories.min_product_quantity'
            ])
            ->join([
                'Warehouses' => [
                    'table' => 'warehouses',
                    'type' => 'INNER',
                    'conditions' => 'ProductStocks.warehouse_id = Warehouses.id',
                ],
                'Products' => [
                    'table' => 'products',
                    'type' => 'INNER',
                    'conditions' => 'Products.id = ProductStocks.product_id',
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => 'ProductStocks.product_variant_id = ProductVariants.id',
                ],
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => 'ProductCategories.product_id = ProductStocks.product_id',
                ],
                'Categories' => [
                    'table' => 'categories',
                    'type' => 'LEFT',
                    'conditions' => 'Categories.id = ProductCategories.category_id',
                ],
            ])
            ->group([
                'Warehouses.id',
                'ProductStocks.id',
                'Products.id',
                'ProductVariants.id',
                'Categories.id',
            ])
            ->where(['Warehouses.status !=' => 'D'])
            ->distinct(['Products.id', 'ProductVariants.id'])
            ->enableHydration(false)
            ->toArray();


        $InStockProduct = 0;
        $LowStockProduct = 0;

        foreach ($InStockProducts as $StockProduct) {
            $InStockProduct++;
            if ($StockProduct['total_quantity'] < $StockProduct['min_product_quantity']) {
                $LowStockProduct++;
            }
        }


        $DeliveriesScheduled = $this->StockMovements->find()
            ->select([
                'unique_count' => $this->StockMovements->find()->func()->count('*')
            ])
            ->where([
                'StockMovements.movement_type' => 'Incoming',
                'StockMovements.warehouse_id IS NOT' => null,
                'DATE(StockMovements.movement_date)' => date('Y-m-d')
            ])
            ->enableHydration(false)
            ->first();

        $DeliveriesScheduled = $DeliveriesScheduled['unique_count'] ?? 0;

        $requested_user = $this->Authentication->getIdentity();
        $deliveryCount = 0;

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);
            $roleName = strtolower($role->name);
            $today = FrozenDate::today();
            $shipmentOrdersTable = TableRegistry::getTableLocator()->get('ShipmentOrders');

            if (in_array($roleName, ['warehouse manager', 'warehouse assistant'])) {
                $warehouse = null;

                if ($roleName === 'warehouse manager') {
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['manager_id' => $requested_user->id])
                        ->first();
                } elseif ($roleName === 'warehouse assistant') {
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['assistant_id' => $requested_user->id])
                        ->first();
                }

                if (!empty($warehouse)) {
                    $warehouseId = $warehouse->id;

                    $deliveryCount = $shipmentOrdersTable->find()
                        ->matching('Shipments', function ($q) use ($warehouseId) {
                            return $q->where([
                                'Shipments.sender_type' => 'Warehouse',
                                'Shipments.senderID' => $warehouseId,
                                'Shipments.status' => 'A'
                            ]);
                        })
                        ->where([
                            'DATE(ShipmentOrders.expected_delivery_date)' => $today->format('Y-m-d'),
                            'ShipmentOrders.status' => 'A'
                        ])
                        ->count();
                }

            } elseif ($roleName === 'admin') {
                // Admin sees all today's delivery
                $deliveryCount = $shipmentOrdersTable->find()
                    ->matching('Shipments', function ($q) {
                        return $q->where(['Shipments.status' => 'A']);
                    })
                    ->where([
                        'DATE(ShipmentOrders.expected_delivery_date)' => $today->format('Y-m-d'),
                        'ShipmentOrders.status' => 'A'
                    ])
                    ->count();
            }
        }

        $this->set(compact('warehouses', 'status', 'statusMap', 'TotalProducts', 'InStockProduct', 'LowStockProduct', 'DeliveriesScheduled', 'deliveryCount'));
    }

    /**
     * View method
     *
     * @param string|null $id Warehouse id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $warehouse = $this->Warehouses->get($id, contain: ['Managers', 'Assistants', 'Cities', 'Municipalities']);
        $statuses = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $status = Configure::read('Constants.STATUS');

        // $users = $this->Users->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'first_name'
        // ])->where(['status' => 'A'])->all()->toArray();

        // if (empty($users)) {
        //     $users  = ['' => 'No users available'];
        // }

        $roles = $this->Roles->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->where(['status' => 'A'])
            ->order(['name' => 'ASC'])
            ->all()
            ->toArray();


        if (empty($roles)) {
            $roles  = ['' => 'No roles available'];
        }

        $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
            ->where([
                'SupplierPurchaseOrders.delivery_status' => 'Delivered',
                'SupplierPurchaseOrders.id_deliver_to' => $id,
                'SupplierPurchaseOrders.status' => 'A'
            ])
            ->all();

        $purchase_order_bills = [];

        // Loop through categories
        foreach ($supplierPurchaseOrders as $purchaseOrder) {
            $purchase_order_bills[$purchaseOrder->id] = $purchaseOrder->bill_no;
        }

        $stock_returns = $this->StockReturns->find()
            ->where([
                'StockReturns.return_from' => 'Warehouse',
                'StockReturns.return_id_from' => $id
            ])
            ->leftJoinWith('StockRequests.SupplierPurchaseOrders')
            ->leftJoin(
                ['Suppliers' => 'suppliers'],
                ['StockReturns.return_id_to = Suppliers.id']
            )
            ->select([
                'StockReturns.id',
                'StockReturns.return_id_to',
                'StockReturns.return_date',
                'StockReturns.created',
                'SupplierPurchaseOrders.bill_no',
                'Suppliers.name', // Fetch supplier name
            ])
            ->order(['StockReturns.created' => 'DESC'])
            ->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('warehouse', 'statuses', 'statusMap', 'status', 'roles', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'purchase_order_bills', 'stock_returns', 'dateFormat', 'timeFormat'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $warehouse = $this->Warehouses->newEmptyEntity();
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $phoneNumber = $this->request->getData('warehouse_phone_no');
            $countryCode = $this->request->getData('warehouse_country_code');

            $phone = str_replace($countryCode, '', $phoneNumber);
            if (strpos($phone, '+') === 0) {
                $phone = ltrim($phone, '+');
            }
            $data['country_code'] = $countryCode;
            $data['warehouse_phone_no'] = $phone;

            $warehouse = $this->Warehouses->patchEntity($warehouse, $data);
            if ($this->Warehouses->save($warehouse)) {
                $this->Flash->success(__('The warehouse has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The warehouse could not be saved. Please, try again.'));
        }
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])->all()->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No City available'];
        }

        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($municipalities)) {
            $municipalities = ['' => 'No Municipality available'];
        }

        $assignedManagers = $this->Warehouses->find()
            ->select(['manager_id'])
            ->where(['status' => 'A'])
            ->distinct()
            ->enableHydration(false)
            ->all()
            ->map(fn($row) => $row['manager_id'])
            ->toArray();

        // Filter out null/empty values
        $assignedManagers = array_filter($assignedManagers, fn($id) => !empty($id));

        $conditions = [
            'role_id' => Configure::read('Constants.MANAGER_ROLE_ID'),
            'status' => 'A'
        ];

        if (!empty($assignedManagers)) {
            $conditions['id NOT IN'] = $assignedManagers;
        }

        $users = $this->Users->find('list', [
            'keyField' => 'id',
            'valueField' => function ($row) {
                return $row->first_name . ' ' . $row->last_name;
            }
        ])
            ->where($conditions)
            ->all()
            ->toArray();

        if (empty($users)) {
            $users = ['' => 'No Manager available'];
        }

        $assignedAssistants = $this->Warehouses->find()
            ->select(['assistant_id'])
            ->where(['status' => 'A'])
            ->distinct()
            ->enableHydration(false)
            ->all()
            ->map(fn($row) => $row['assistant_id'])
            ->toArray();

        // Filter out null/empty values
        $assignedAssistants = array_filter($assignedAssistants, fn($id) => !empty($id));

        $conditions = [
            'role_id' => Configure::read('Constants.WAREHOUSE_ASSISTANT_ROLE_ID'),
            'status' => 'A'
        ];

        if (!empty($assignedAssistants)) {
            $conditions['id NOT IN'] = $assignedAssistants;
        }

        $assistant = $this->Users->find('list', [
            'keyField' => 'id',
            'valueField' => function ($row) {
                return $row->first_name . ' ' . $row->last_name;
            }
        ])
            ->where($conditions)
            ->all()
            ->toArray();

        if (empty($assistant)) {
            $assistant = ['' => 'No Warehouse Assistant available'];
        }

        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');

        // $users = $this->Warehouses->Users->find('list', limit: 200)->all();
        $this->set(compact('warehouse', 'users', 'cities', 'municipalities', 'ABIDJAN_CITY_ID', 'assistant'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Warehouse id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $warehouse = $this->Warehouses->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            $phoneNumber = $this->request->getData('warehouse_phone_no');
            $countryCode = $this->request->getData('warehouse_country_code');

            $phone = str_replace($countryCode, '', $phoneNumber);
            if (strpos($phone, '+') === 0) {
                $phone = ltrim($phone, '+');
            }
            $data['country_code'] = $countryCode;
            $data['warehouse_phone_no'] = $phone;

            $warehouse = $this->Warehouses->patchEntity($warehouse, $data);
            if ($this->Warehouses->save($warehouse)) {
                $this->Flash->success(__('The warehouse has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The warehouse could not be saved. Please, try again.'));
        }
        // $users = $this->Warehouses->Users->find('list', limit: 200)->all();
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])->all()->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No City available'];
        }

        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($municipalities)) {
            $municipalities = ['' => 'No Municipality available'];
        }

        $assignedManagers = $this->Warehouses->find()
            ->select(['manager_id'])
            ->where(['status' => 'A', 'id <>' => $id])
            ->distinct()
            ->enableHydration(false)
            ->all()
            ->map(fn($row) => $row['manager_id'])
            ->toArray();

        // Filter out null/empty values
        $assignedManagers = array_filter($assignedManagers, fn($id) => !empty($id));

        $conditions = [
            'role_id' => Configure::read('Constants.MANAGER_ROLE_ID'),
            'status' => 'A'
        ];

        if (!empty($assignedManagers)) {
            $conditions['id NOT IN'] = $assignedManagers;
        }

        $users = $this->Users->find('list', [
            'keyField' => 'id',
            'valueField' => function ($row) {
                return $row->first_name . ' ' . $row->last_name;
            }
        ])
            ->where($conditions)
            ->all()
            ->toArray();

        if (empty($users)) {
            $users = ['' => 'No Manager available'];
        }


        $assignedAssistants = $this->Warehouses->find()
            ->select(['assistant_id'])
            ->where(['status' => 'A', 'id <>' => $id])
            ->distinct()
            ->enableHydration(false)
            ->all()
            ->map(fn($row) => $row['assistant_id'])
            ->toArray();

        // Filter out null/empty values
        $assignedAssistants = array_filter($assignedAssistants, fn($id) => !empty($id));

        $conditions = [
            'role_id' => Configure::read('Constants.WAREHOUSE_ASSISTANT_ROLE_ID'),
            'status' => 'A'
        ];

        if (!empty($assignedAssistants)) {
            $conditions['id NOT IN'] = $assignedAssistants;
        }

        $assistant = $this->Users->find('list', [
            'keyField' => 'id',
            'valueField' => function ($row) {
                return $row->first_name . ' ' . $row->last_name;
            }
        ])
            ->where($conditions)
            ->all()
            ->toArray();

        if (empty($assistant)) {
            $assistant = ['' => 'No Warehouse Assistant available'];
        }

        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');
        $statuses = Configure::read('Constants.STATUS');

        $this->set(compact('warehouse', 'users', 'cities', 'municipalities', 'ABIDJAN_CITY_ID', 'statuses', 'assistant'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Warehouse id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $warehouse = $this->Warehouses->get($id);
        $response = ['success' => false, 'message' => 'The Warehouse could not be deleted. Please, try again.'];
        if ($warehouse) {
            if ($this->ProductStocks->exists(['warehouse_id' => $warehouse->id])) {
                $response = ['success' => false, 'message' => 'Cannot delete this warehouse as it has active product stock, try again.'];
            } else {
                if ($this->Warehouses->delete($warehouse)) {
                    $response = ['success' => true, 'message' => 'The Warehouse has been deleted.'];
                } else {
                    $response = ['success' => false, 'message' => 'The Warehouse could not be deleted. Please, try again.'];
                }
            }
        } else {
            $response = ['success' => false, 'message' => 'The Warehouse does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
