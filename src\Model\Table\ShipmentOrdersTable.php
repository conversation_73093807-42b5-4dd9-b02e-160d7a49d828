<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * ShipmentOrders Model
 *
 * @property \App\Model\Table\ShipmentsTable&\Cake\ORM\Association\BelongsTo $Shipments
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\BelongsTo $Orders
 * @property \App\Model\Table\DriversTable&\Cake\ORM\Association\BelongsTo $Drivers
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\CustomerAddressesTable&\Cake\ORM\Association\BelongsTo $CustomerAddresses
 * @property \App\Model\Table\CitiesTable&\Cake\ORM\Association\BelongsTo $Cities
 * @property \App\Model\Table\ZonesTable&\Cake\ORM\Association\BelongsTo $Zones
 * @property \App\Model\Table\MunicipalitiesTable&\Cake\ORM\Association\BelongsTo $Municipalities
 * @property \App\Model\Table\ShipmentOrderItemsTable&\Cake\ORM\Association\HasMany $ShipmentOrderItems
 *
 * @method \App\Model\Entity\ShipmentOrder newEmptyEntity()
 * @method \App\Model\Entity\ShipmentOrder newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\ShipmentOrder> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\ShipmentOrder get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\ShipmentOrder findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\ShipmentOrder patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\ShipmentOrder> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\ShipmentOrder|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\ShipmentOrder saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\ShipmentOrder>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ShipmentOrder>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ShipmentOrder>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ShipmentOrder> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ShipmentOrder>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ShipmentOrder>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ShipmentOrder>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ShipmentOrder> deleteManyOrFail(iterable $entities, array $options = [])
 */
class ShipmentOrdersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('shipment_orders');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->belongsTo('Shipments', [
            'foreignKey' => 'shipment_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Orders', [
            'foreignKey' => 'order_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Drivers', [
            'foreignKey' => 'driver_id',
        ]);
        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('CustomerAddresses', [
            'foreignKey' => 'customer_address_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Zones', [
            'foreignKey' => 'zone_id',
        ]);
        $this->belongsTo('Municipalities', [
            'foreignKey' => 'municipality_id',
        ]);
        $this->hasMany('ShipmentOrderItems', [
            'foreignKey' => 'shipment_order_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->notEmptyString('shipment_id');

        $validator
            ->nonNegativeInteger('order_id')
            ->notEmptyString('order_id');

        $validator
            ->integer('driver_id')
            ->allowEmptyString('driver_id');

        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->nonNegativeInteger('customer_address_id')
            ->notEmptyString('customer_address_id');

        $validator
            ->nonNegativeInteger('city_id')
            ->notEmptyString('city_id');

        $validator
            ->integer('zone_id')
            ->allowEmptyString('zone_id');

        $validator
            ->integer('municipality_id')
            ->allowEmptyString('municipality_id');

        $validator
            ->date('expected_delivery_date')
            ->allowEmptyDate('expected_delivery_date');

        $validator
            ->date('actual_delivery_date')
            ->allowEmptyDate('actual_delivery_date');

        $validator
            ->nonNegativeInteger('delivery_attempts')
            ->allowEmptyString('delivery_attempts');

        $validator
            ->boolean('is_expedited')
            ->allowEmptyString('is_expedited');

        $validator
            ->decimal('shipping_cost')
            ->allowEmptyString('shipping_cost');

        $validator
            ->scalar('order_delivery_status')
            ->allowEmptyString('order_delivery_status');

        $validator
            ->dateTime('delivery_status_date')
            ->notEmptyDateTime('delivery_status_date');

        $validator
            ->decimal('cash_collected')
            ->allowEmptyString('cash_collected');

        $validator
            ->scalar('proof_of_delivery')
            ->maxLength('proof_of_delivery', 255)
            ->allowEmptyString('proof_of_delivery');

        $validator
            ->scalar('driver_comments')
            ->allowEmptyString('driver_comments');

        $validator
            ->scalar('special_instructions')
            ->allowEmptyString('special_instructions');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    // public function buildRules(RulesChecker $rules): RulesChecker
    // {
    //     $rules->add($rules->existsIn(['shipment_id'], 'Shipments'), ['errorField' => 'shipment_id']);
    //     $rules->add($rules->existsIn(['order_id'], 'Orders'), ['errorField' => 'order_id']);
    //     $rules->add($rules->existsIn(['driver_id'], 'Drivers'), ['errorField' => 'driver_id']);
    //     $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);
    //     $rules->add($rules->existsIn(['customer_address_id'], 'CustomerAddresses'), ['errorField' => 'customer_address_id']);
    //     $rules->add($rules->existsIn(['city_id'], 'Cities'), ['errorField' => 'city_id']);
    //     $rules->add($rules->existsIn(['zone_id'], 'Zones'), ['errorField' => 'zone_id']);
    //     $rules->add($rules->existsIn(['municipality_id'], 'Municipalities'), ['errorField' => 'municipality_id']);

    //     return $rules;
    // }
    
    //S
    public function changeDeliveryStatus ($shipment_order_id, $data)
    {       
        $delivery_entity = $this->get($shipment_order_id);        
        $statusupdate['order_delivery_status'] = $data['delivery_status'];
        if($data['cash_collected']){
            $statusupdate['cash_collected'] = $data['cash_collected'];
        }
        $statusupdate['delivery_status_date'] = $data['delivery_status_date'];
        if($data['actual_delivery_date']){
            $statusupdate['actual_delivery_date'] = $data['actual_delivery_date'];
        }        
        $delivery_entity = $this->patchEntity($delivery_entity, $statusupdate);
        $res = $this->save($delivery_entity);
        
        if($res) {
            return true;
        } else {
            return false;
        }        
    }

    //S
    public function orderDetail($shipmentOrderId)
    {
        $ordersQuery = $this->find()
            ->contain([
                'Orders' => function ($q) {
                    return $q->contain([
                        'Customers' => [
                            'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']]
                        ],
                        'CustomerAddresses' => function ($q2) {
                            return $q2->select([
                                    'CustomerAddresses.id',
                                    'CustomerAddresses.city_id',
                                    'CustomerAddresses.name',
                                    'CustomerAddresses.type',
                                    'CustomerAddresses.address_line1',
                                    'CustomerAddresses.address_line2',
                                    'CustomerAddresses.house_no',
                                    'CustomerAddresses.landmark',
                                    'CustomerAddresses.zipcode',
                                    'CustomerAddresses.phone_no1',
                                    'CustomerAddresses.phone_no2'
                                ])
                                ->contain([
                                    'Cities' => ['fields' => ['id', 'city_name']],
                                    'Municipalities' => ['fields' => ['id', 'name']]
                                ]);
                        },
                        'Showrooms' => function ($q3) {
                            return $q3->select([
                                    'Showrooms.id',
                                    'Showrooms.city_id',
                                    'Showrooms.name',
                                    'Showrooms.address',
                                    'Showrooms.area_sq_mts',
                                    'Showrooms.image',
                                    'Showrooms.email',
                                    'Showrooms.contact_country_code',
                                    'Showrooms.contact_number',
                                    'Showrooms.showroom_timing'
                                ])
                                ->contain([
                                    'Cities' => ['fields' => ['id', 'city_name']],
                                    'ShowroomManager' => ['fields' => ['first_name', 'last_name']],
                                    'ShowroomSupervisor' => ['fields' => ['first_name', 'last_name']]
                                ]);
                        },
                        'Transactions',
                        'Offers' => ['fields' => ['offer_name', 'offer_code']],
                    ]);
                },
                'ShipmentOrderItems' => [
                    'OrderItems' => [
                        'Products' => ['fields' => ['id', 'name', 'reference_name']],
                        'ProductVariants',
                        'OrderTrackingHistories' => [
                            'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                        ]
                    ]
                ]
            ])
            ->where(['ShipmentOrders.id' => $shipmentOrderId])
            ->first();

            return $ordersQuery;
    }

    //S
    public function listAssignedOrder($driverId, $date, $searchTerm = null) {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'ShipmentOrders.id',
                'ShipmentOrders.shipment_id',
                'ShipmentOrders.order_id',
                'ShipmentOrders.expected_delivery_date',
                'ShipmentOrders.order_delivery_status',
                'ShipmentOrders.delivery_status_date',
                'Shipments.sender_type',
                'Shipments.senderID',
                'sender_name' => 'COALESCE(Warehouses.name, Showrooms.name)',
                'warehouse_id' => 'Warehouses.id',
                'warehouse_latitude' => 'Warehouses.latitude',
                'warehouse_longitude' => 'Warehouses.longitude',
                'showroom_id' => 'Showrooms.id',
                'showroom_latitude' => 'Showrooms.latitude',
                'showroom_longitude' => 'Showrooms.longitude',
                'order_item_count' => 'COUNT(DISTINCT ShipmentOrderItems.order_item_id)'
            ])
            ->join([
                'Shipments' => [
                    'table' => 'shipments',
                    'type' => 'INNER',
                    'conditions' => 'ShipmentOrders.shipment_id = Shipments.id'
                ],
                'ShipmentOrderItems' => [
                    'table' => 'shipment_order_items',
                    'type' => 'LEFT',
                    'conditions' => 'ShipmentOrderItems.shipment_order_id = ShipmentOrders.id'
                ]
            ])
            ->leftJoin(
                ['Warehouses' => 'warehouses'],
                ['Shipments.sender_type' => 'Warehouse', 'Shipments.senderID = Warehouses.id']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['Shipments.sender_type' => 'Showroom', 'Shipments.senderID = Showrooms.id']
            )
            ->contain([
                'Orders' => [
                    'fields' => ['Orders.id', 'Orders.order_number', 'Orders.status', 'Orders.payment_method',
                     'Orders.delivery_mode_type', 'Orders.created'],

                      'OrderItems' => [
                        'fields' => ['OrderItems.id', 'OrderItems.order_id'],
                    ]                 
                ],
               
                'Customers' => [
                    'fields' => ['id','profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'mobile_no']],
                ],
                'CustomerAddresses' => [ 
                    'fields' => ['customer_id', 'city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode', 'latitude', 'longitude', 'phone_no1', 'phone_no2'],                   
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ]
            ])
            ->where([
                    'ShipmentOrders.driver_id' => $driverId,
                    //'ShipmentOrders.order_delivery_status' => 'Pending'
                    //'ShipmentOrders.expected_delivery_date' =>$date
                ])
            ->group(['ShipmentOrders.id'])
            ->order(['Shipments.created' => 'DESC'])
            ->enableHydration(false);

            // Apply search term filter
            if (!empty($searchTerm)) {
                $ordersQuery->andWhere([
                    'OR' => [
                        'Orders.order_number LIKE' => '%' . $searchTerm . '%',
                        'Users.first_name LIKE' => '%' . $searchTerm . '%',
                        'Users.last_name LIKE' => '%' . $searchTerm . '%',
                        'Users.mobile_no LIKE' => '%' . $searchTerm . '%'
                    ]
                ]);
            }    

        // Execute the query
        $orders = $ordersQuery->all();
       
        // Iterate through the orders to count OrderItems

        /* foreach ($orders as $order) {
            // Ensure we are accessing the correct array structure for order_items
            $order_item_count = 0;
            
            // Check if 'Orders' and 'OrderItems' are set in the structure
            if (isset($order['order']['order_items'])) {
                $order_item_count = count($order['order']['order_items']);
            }

            // Add the count to the order
           $order['order']['order_item_count'] = $order_item_count;
        }*/

        return $orders->toArray();
    } 

    //S
    public function listDeliveredOrder($driverId, $filter_order_status, $filter_payment_status, $filter_sdate, $time_period, $startDate, $endDate, $searchTerm, $page, $limit) {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'ShipmentOrders.id',
                'ShipmentOrders.shipment_id',
                'ShipmentOrders.order_id',
                'ShipmentOrders.expected_delivery_date',
                'ShipmentOrders.actual_delivery_date',
                'ShipmentOrders.order_delivery_status',
                'ShipmentOrders.delivery_status_date',
                'Shipments.sender_type',
                'Shipments.senderID',
                'sender_name' => 'COALESCE(Warehouses.name, Showrooms.name)',
                'warehouse_id' => 'Warehouses.id',
                'warehouse_latitude' => 'Warehouses.latitude',
                'warehouse_longitude' => 'Warehouses.longitude',
                'showroom_id' => 'Showrooms.id',
                'showroom_latitude' => 'Showrooms.latitude',
                'showroom_longitude' => 'Showrooms.longitude'
            ])
            ->join([
                'Shipments' => [
                    'table' => 'shipments',
                    'type' => 'INNER',
                    'conditions' => 'ShipmentOrders.shipment_id = Shipments.id'
                ]
            ])
            ->leftJoin(
                ['Warehouses' => 'warehouses'],
                ['Shipments.sender_type' => 'Warehouse', 'Shipments.senderID = Warehouses.id']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['Shipments.sender_type' => 'Showroom', 'Shipments.senderID = Showrooms.id']
            )
            ->contain([
                'Orders' => [
                    'fields' => ['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.status', 'Orders.payment_method', 'Orders.delivery_mode_type', 'Orders.created'],

                   /* 'OrderItems' => [
                        'fields' => ['OrderItems.id', 'OrderItems.order_id', 'OrderItems.quantity', 'OrderItems.status', 'OrderItems.total_price', 'OrderItems.price'],
                        'Products' => ['fields' => ['id', 'name', 'reference_name', 'sales_price','promotion_price']],
                        'ProductVariants'  => ['fields' => ['id','product_id','variant_name','reference_name','sku']],
                    ],*/
                    
                    'Transactions',
                    'Offers' => ['fields' => ['offer_name', 'offer_code']],                
                ],
                
                'ShipmentOrderItems' => [
                    'OrderItems' => [
                        'Products' => ['fields' => ['id', 'name', 'reference_name', 'sales_price','promotion_price']],
                        'ProductVariants',
                        'OrderTrackingHistories' => [
                            'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                        ]
                    ]
                ],

                'Customers' => [
                    'fields' => ['id','profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']],
                ],

                'CustomerAddresses' => [ 
                    'fields' => ['customer_id', 'city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode','phone_no1', 'phone_no2'],                   
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ]
            ])          
            ->where([
                    'Shipments.driver_id' => $driverId/*,
                    'ShipmentOrders.order_delivery_status' => 'Delivered'*/
                ])
            ->order(['ShipmentOrders.id' => 'DESC'])
            ->enableHydration(false);

            // Apply search term filter
            if (!empty($searchTerm)) {
                $ordersQuery->andWhere([
                    'OR' => [
                        'Orders.order_number LIKE' => '%' . $searchTerm . '%',
                        'Users.first_name LIKE' => '%' . $searchTerm . '%',
                        'Users.last_name LIKE' => '%' . $searchTerm . '%',
                        'Users.mobile_no LIKE' => '%' . $searchTerm . '%'
                    ]
                ]);
            }

            // Apply filters if present
            if (!empty($filter_order_status)) {
                //$ordersQuery->where(['Orders.status' => $filter_order_status]);
                $ordersQuery->where(['ShipmentOrders.order_delivery_status' => $filter_order_status]);
            } else {
                $ordersQuery->where(['ShipmentOrders.order_delivery_status' => 'Delivered']); 
            }

            if (!empty($filter_payment_status)) {
                //$ordersQuery->where(['Transactions.payment_status' => $filter_payment_status]);
                $ordersQuery->matching('Orders.Transactions', function ($q) use ($filter_payment_status) {
                    return $q->where(['Transactions.payment_status' => $filter_payment_status]);
                });
            }

            //Filter by date
            if (!empty($filter_sdate)) {
                $ordersQuery->where(['DATE(ShipmentOrders.delivery_status_date)' => $filter_sdate]);
            }

            //Filter by time period
            if (!empty($time_period)) {
                $ordersQuery->where([
                    'ShipmentOrders.delivery_status_date >=' => $startDate,
                    'ShipmentOrders.delivery_status_date <=' => $endDate,
                ]);
            }

        // Execute the query
        $orders = $ordersQuery->all();
       
        // Iterate through the orders to count OrderItems
        foreach ($orders as $order) {
            // Ensure we are accessing the correct array structure for order_items
            $order_item_count = 0;
            
            // Check if 'Orders' and 'OrderItems' are set in the structure
            if (isset($order['shipment_order_items']['order_item'])) {
                $order_item_count = count($order['shipment_order_items']['order_item']);
            }

            // Add the count to the order
           $order['order']['order_item_count'] = $order_item_count;
        }

        return $orders->toArray();
    }

    //S
    public function cashDeliveredOrder($driverId, $page, $limit) {

        $current_date = date('Y-m-d');
        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'ShipmentOrders.id',
                'ShipmentOrders.shipment_id',
                'ShipmentOrders.order_id',
                'ShipmentOrders.expected_delivery_date',
                'ShipmentOrders.actual_delivery_date',
            ])
            ->contain([
                'Orders' => [
                    'fields' => ['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.status', 'Orders.payment_method', 'Orders.delivery_mode_type', 'Orders.created'],

                   /* 'OrderItems' => [
                        'fields' => ['OrderItems.id', 'OrderItems.order_id', 'OrderItems.quantity', 'OrderItems.status', 'OrderItems.total_price', 'OrderItems.price'],
                        'Products' => ['fields' => ['id', 'name', 'reference_name', 'sales_price','promotion_price']],
                        'ProductVariants'  => ['fields' => ['id','product_id','variant_name','reference_name','sku']],
                    ],*/

                    'Transactions'                 
                ],
                'ShipmentOrderItems' => [
                    'OrderItems' => [
                        'Products' => ['fields' => ['id', 'name', 'reference_name', 'sales_price','promotion_price']],
                        'ProductVariants',
                        'OrderTrackingHistories' => [
                            'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                        ]
                    ]
                ],
                'Customers' => [
                    'fields' => ['id','profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']],
                ],
                'CustomerAddresses' => [ 
                    'fields' => ['customer_id', 'city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode','phone_no1', 'phone_no2'],                   
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ]
            ])
            /*->join(['Transactions' => [
                'table' => 'transactions',
                'type' => 'INNER',
                'conditions' => 'Transactions.order_id = Orders.id'
            ]
            ])*/
            ->where([
                    'ShipmentOrders.driver_id' => $driverId,
                    'ShipmentOrders.order_delivery_status' => 'Delivered',
                    'DATE(ShipmentOrders.delivery_status_date)' => $current_date,
                    'ShipmentOrders.cash_collected' => 1
                ])
            ->order(['ShipmentOrders.id' => 'DESC'])
            ->enableHydration(false);

        // Execute the query
        $orders = $ordersQuery->all();
        // Iterate through the orders to count OrderItems
        foreach ($orders as $order) {
            // Ensure we are accessing the correct array structure for order_items
            $order_item_count = 0;
            
            // Check if 'Orders' and 'OrderItems' are set in the structure
            if (isset($order['shipment_order_items']['order_item'])) {
                $order_item_count = count($order['shipment_order_items']['order_item']);
            }

            // Add the count to the order
           $order['order']['order_item_count'] = $order_item_count;
        }
        return $orders->toArray();
    }

    //S
    public function cashOnHand($driverId) {

        $current_date = date('Y-m-d');
        $totalAmount = $this->find()
        ->select([
            'total' => $this->find()->func()->sum('ROUND(Orders.total_amount, 2)') // Round to 2 decimals
        ])
        ->innerJoinWith('Orders') // Using CakePHP's association method
        ->where([
            'ShipmentOrders.driver_id' => $driverId,
            'ShipmentOrders.order_delivery_status' => 'Delivered',
            'DATE(ShipmentOrders.delivery_status_date)' => $current_date,
            'ShipmentOrders.cash_collected' => 1
        ])
        ->first();
        return $totalAmount;
    }  

    //S    
    public function orderAutocomplete($queryStr)
    {
        $query = $this->find()
            ->select([
                'id' => 'ShipmentOrders.id',
                'order_id' => 'Orders.id',
                'order_number' => 'Orders.order_number',
                'customer_name' => 'CONCAT(Users.first_name, " ", Users.last_name)',
                'mobile_no' => 'Users.mobile_no'
            ])
            ->contain([
                'Orders',
                'Customers.Users'
            ])
            ->where([
                'OR' => [
                    'Orders.order_number LIKE' => '%' . $queryStr . '%',
                    'Users.first_name LIKE' => '%' . $queryStr . '%',
                    'Users.last_name LIKE' => '%' . $queryStr . '%',
                    'Users.mobile_no LIKE' => '%' . $queryStr . '%'
                ]
            ])
            //->limit(10)
            ->order(['Orders.order_number' => 'DESC']);

        return $query->toArray();
    }

    //S
    public function shipmentOrderDetail($id)
    {
        $ordersQuery = $this->find()
            ->select([
                'ShipmentOrders.id',
                'ShipmentOrders.shipment_id',
                'ShipmentOrders.order_id',
                'ShipmentOrders.expected_delivery_date',
                'ShipmentOrders.order_delivery_status',
                'ShipmentOrders.delivery_status_date'                
            ])           
            ->contain([                
                'Orders' => [
                    'fields' => [
                        'Orders.id', 'Orders.order_number',
                        'Orders.payment_method', 'Orders.delivery_mode_type', 'Orders.order_date'
                    ]
                ],
                'Customers' => [
                'fields' => ['id', 'profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'mobile_no']]
                ],
                'CustomerAddresses' => [
                    'fields' => [
                        'customer_id', 'city_id', 'name', 'type', 'address_line1', 'address_line2',
                        'house_no', 'landmark', 'zipcode', 'latitude', 'longitude', 'phone_no1', 'phone_no2'
                    ],
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ],
                'ShipmentOrderItems' => [
                    'OrderItems' => [
                        'fields' => [
                            'OrderItems.id', 'OrderItems.order_id'
                        ],
                        // optionally contain Product or Variant if needed
                    ]
                ]                              
            ])
            ->where([
                'ShipmentOrders.id' => $id
            ])
            ->first();

        return $ordersQuery->toArray();
    }

    //S
    public function detailByShipmentId($shipment_id)
    {
        $ordersQuery = $this->find()
            ->select([
                'ShipmentOrders.id',
                'ShipmentOrders.shipment_id',
                'ShipmentOrders.order_id',
                'ShipmentOrders.expected_delivery_date',
                'ShipmentOrders.order_delivery_status',
                'ShipmentOrders.delivery_status_date'                
            ])           
            ->contain([                
                'Orders' => [
                    'fields' => [
                        'Orders.id', 'Orders.order_number',
                        'Orders.payment_method', 'Orders.delivery_mode_type', 'Orders.order_date'
                    ]
                ],
                'Customers' => [
                'fields' => ['id', 'profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'mobile_no']]
                ],
                'CustomerAddresses' => [
                    'fields' => [
                        'customer_id', 'city_id', 'name', 'type', 'address_line1', 'address_line2',
                        'house_no', 'landmark', 'zipcode', 'latitude', 'longitude', 'phone_no1', 'phone_no2'
                    ],
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ],
                'ShipmentOrderItems' => [
                    'OrderItems' => [
                        'fields' => [
                            'OrderItems.id', 'OrderItems.order_id'
                        ],
                        // optionally contain Product or Variant if needed
                    ]
                ]                              
            ])
            ->where([
                'ShipmentOrders.shipment_id' => $shipment_id
            ])
            ->all();

        return $ordersQuery->toArray();
    }

}
