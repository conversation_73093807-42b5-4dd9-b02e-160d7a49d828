<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class StocksController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $ProductStocks;
    protected $Warehouses;
    protected $Showrooms;
    protected $Products;
    protected $Roles;
    protected $ProductAttributes;
    protected $SupplierProducts;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Products = $this->fetchTable('Products');
        $this->Roles = $this->fetchTable('Roles');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
    }

    public function index()
    {

        $requested_user = $this->Authentication->getIdentity();

        $conditions = [];
        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'showroom manager') {

                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    
                    $managerShowroomId = $managerShowroom->id;

                    $conditions['ProductStocks.showroom_id'] = $managerShowroomId;
                }

            } elseif (strtolower($role->name) === 'warehouse manager') {

                $warehouse = $this->Warehouses->find()
                    ->select(['id'])
                    ->where(['manager_id' => $requested_user->id])
                    ->first();

                if (!empty($warehouse)) {

                    $warehouseId = $warehouse->id;
                    $conditions['ProductStocks.warehouse_id'] = $warehouseId;
                }
            } elseif (strtolower($role->name) === 'warehouse assistant') {

                $warehouse = $this->Warehouses->find()
                    ->select(['id'])
                    ->where(['assistant_id' => $requested_user->id])
                    ->first();

                if (!empty($warehouse)) {

                    $warehouseId = $warehouse->id;
                    $conditions['ProductStocks.warehouse_id'] = $warehouseId;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {
                
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->all() // Fetch results
                    ->extract('id') // Extract IDs
                    ->toList(); // Convert to an array


                if (!empty($supervisorShowrooms)) {
                    $conditions['ProductStocks.showroom_id IN'] = $supervisorShowrooms;
                }
            }  
        } 

        $productStocks = $this->ProductStocks->find()
            ->select([
                'ProductStocks.id',  // In Stock
                'ProductStocks.quantity',  // In Stock
                'ProductStocks.product_attribute_id',  // In Stock
                'ProductStocks.warehouse_id',  // In Stock
                'ProductStocks.showroom_id',  // In Stock
                'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),  // Either Showroom or Warehouse name
                'Products.name',  // Product name
                'Products.reference_name',  // Product name
                'ProductVariants.variant_name',  // Product variant name (if exists)
                'SKU' => $this->ProductStocks->find()->func()->coalesce([
                    new IdentifierExpression('ProductVariants.sku'), 
                    new IdentifierExpression('Products.sku')
                ])
            ])
            ->contain([
                'Showrooms' => [
                    'fields' => ['Showrooms.id', 'Showrooms.name'],
                ],
                'Warehouses' => [
                    'fields' => ['Warehouses.id', 'Warehouses.name'],
                ],
                'Products' => [
                    'fields' => ['Products.id', 'Products.name', 'Products.purchase_price', 'Products.sku'],
                ],
                'ProductVariants' => [
                    'fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.purchase_price', 'ProductVariants.sku'],
                ]
            ])
            ->where($conditions)
            ->order(['ProductStocks.id' => 'DESC'])
            ->toArray();

        
        foreach ($productStocks as &$stock) {

            // Initialize an attributes array in each item
            $stock->attributes = [];

            if ($stock->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $stock->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $stock->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }

            // Check if product_variant_id is available and query supplier_products accordingly
            if ($stock->product_variant_id) {
                // Fetch supplier_price from supplier_products based on product_variant_id
                $supplierProduct = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_variant_id' => $stock->product_variant_id])
                    ->first();

                // Assign the supplier_price if found
                if ($supplierProduct) {
                    $stock->supplier_price = $supplierProduct->supplier_price;
                } else {
                    // If not found for product_variant_id, fallback to product_id
                    $supplierProductFallback = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_id' => $stock->product->id])
                        ->first();
                    
                    $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
                }
            } else {
                // If no product_variant_id, fallback to the product's supplier_price
                $supplierProductFallback = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_id' => $stock->product->id])
                    ->first();
                
                $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
            }

            // Calculate the value (quantity * supplier_price)
            $stock->value = $stock->supplier_price * $stock->quantity;

            // You can access other fields similarly as before
            $productId = $stock->product->id ?? null;

            // if ($productId) {
            //     // Query to fetch min_product_quantity for the current product
            //     $categoryData = $this->Products->find()
            //         ->select(['Categories.min_product_quantity'])
            //         ->leftJoinWith('ProductCategories.Categories')
            //         ->where(['Products.id' => $productId])
            //         ->first();

            //     // Assign min_product_quantity if available
            //     $stock->min_product_quantity = $categoryData['_matchingData']['Categories']['min_product_quantity'] ?? 'N/A';
            // } else {
            //     $stock->min_product_quantity = 'N/A';
            // }

            if ($productId) {
                if (!empty($stock->warehouse_id)) {
                    // Fetch min_product_quantity from Categories (via ProductCategories)
                    $categoryData = $this->Products->find()
                        ->select(['Categories.min_product_quantity'])
                        ->leftJoinWith('ProductCategories.Categories')
                        ->where(['Products.id' => $productId])
                        ->first();

                    $stock->min_product_quantity = $categoryData['_matchingData']['Categories']['min_product_quantity'] ?? 'N/A';

                } elseif (!empty($stock->showroom_id)) {
                    // Fetch min_product_quantity from the Showrooms table
                    $showroom = $this->Showrooms->find()
                        ->select(['min_product_quantity'])
                        ->where(['id' => $stock->showroom_id])
                        ->first();

                    $stock->min_product_quantity = $showroom ? $showroom->min_product_quantity : 'N/A';

                } else {
                    $stock->min_product_quantity = 'N/A';
                }
            } else {
                $stock->min_product_quantity = 'N/A';
            }       
        }


        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('productStocks', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'role'));
    }

    public function viewWarehouseStock($id = null)
    {

        $productStocks = $this->ProductStocks->find()
            ->select([
                'ProductStocks.id',  
                'ProductStocks.product_attribute_id',  
                'ProductStocks.quantity',  
                'ProductStocks.reserved_stock',  
                'ProductStocks.defective_stock',  
                'ProductStocks.service_center_stock',  
                'ProductStocks.purchased_stock',  
                'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),  // Either Showroom or Warehouse name
                'Products.name',  // Product name
                'Products.reference_name',  // Product name
                'ProductVariants.variant_name',  // Product variant name (if exists)
                'SKU' => $this->ProductStocks->find()->func()->coalesce([
                    new IdentifierExpression('ProductVariants.sku'), 
                    new IdentifierExpression('Products.sku')
                ])
            ])
            ->contain([
                'Showrooms' => [
                    'fields' => ['Showrooms.id', 'Showrooms.name'],
                ],
                'Warehouses' => [
                    'fields' => ['Warehouses.id', 'Warehouses.name'],
                ],
                'Products' => [
                    'fields' => ['Products.id', 'Products.name', 'Products.purchase_price', 'Products.sku'],
                ],
                'ProductVariants' => [
                    'fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.purchase_price', 'ProductVariants.sku'],
                ]
            ])
            ->where(['ProductStocks.warehouse_id' => $id])
            ->order(['ProductStocks.id' => 'DESC'])
            ->toArray();

        foreach ($productStocks as &$stock) {
            // Initialize an attributes array in each item
            $stock->attributes = [];

            if ($stock->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $stock->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $stock->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }

            // Fetch supplier_price based on product_variant_id or product_id
            $productId = $stock->product->id ?? null;
            $productVariantId = $stock->product_variant_id ?? null;

            if ($productVariantId) {
                // Fetch supplier_price for product_variant_id
                $supplierProduct = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_variant_id' => $productVariantId])
                    ->first();
                
                // Assign supplier_price if found
                if ($supplierProduct) {
                    $stock->supplier_price = $supplierProduct->supplier_price;
                } else {
                    // Fallback to product_id if not found for product_variant_id
                    $supplierProductFallback = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_id' => $productId])
                        ->first();
                    
                    $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
                }
            } else {
                // If no product_variant_id, fetch supplier_price using product_id
                $supplierProductFallback = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_id' => $productId])
                    ->first();
                
                $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
            }

            // Calculate value (quantity * supplier_price)
            $stock->value = $stock->supplier_price * $stock->quantity;

            // Query to fetch min_product_quantity for the current product
            if ($productId) {
                $categoryData = $this->Products->find()
                    ->select(['Categories.min_product_quantity'])
                    ->leftJoinWith('ProductCategories.Categories')
                    ->where(['Products.id' => $productId])
                    ->first();

                // Assign min_product_quantity if available
                $stock->min_product_quantity = $categoryData['_matchingData']['Categories']['min_product_quantity'] ?? 'N/A';
            } else {
                $stock->min_product_quantity = 'N/A';
            }
        }

        $warehouse = $this->Warehouses->find()
                    ->where(['Warehouses.id' => $id])
                    ->first();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $requested_user = $this->Authentication->getIdentity();
        
        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);
        }

        $this->set(compact('productStocks', 'currencySymbol', 'warehouse', 'decimalSeparator', 'thousandSeparator', 'role'));
    }

    public function viewShowroomStock($id = null)
    {

        $productStocks = $this->ProductStocks->find()
            ->select([
                'ProductStocks.id', 
                'ProductStocks.showroom_id', 
                'ProductStocks.product_attribute_id', 
                'ProductStocks.quantity',  
                'ProductStocks.reserved_stock',  
                'ProductStocks.defective_stock',  
                'ProductStocks.service_center_stock',  
                'ProductStocks.purchased_stock',  
                'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),  // Either Showroom or Warehouse name
                'Products.name',  // Product name
                'Products.reference_name',  // Product name
                'ProductVariants.variant_name',  // Product variant name (if exists)
                'SKU' => $this->ProductStocks->find()->func()->coalesce([
                    new IdentifierExpression('ProductVariants.sku'), 
                    new IdentifierExpression('Products.sku')
                ])
            ])
            ->contain([
                'Showrooms' => [
                    'fields' => ['Showrooms.id', 'Showrooms.name'],
                ],
                'Warehouses' => [
                    'fields' => ['Warehouses.id', 'Warehouses.name'],
                ],
                'Products' => [
                    'fields' => ['Products.id', 'Products.name', 'Products.purchase_price', 'Products.sku'],
                ],
                'ProductVariants' => [
                    'fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.purchase_price', 'ProductVariants.sku'],
                ]
            ])
            ->where(['ProductStocks.showroom_id' => $id])
            ->order(['ProductStocks.id' => 'DESC'])
            ->toArray();

        foreach ($productStocks as &$stock) {
            // Initialize an attributes array in each item
            $stock->attributes = [];

            if ($stock->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $stock->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $stock->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }

            // Fetch supplier_price based on product_variant_id or product_id
            $productId = $stock->product->id ?? null;
            $productVariantId = $stock->product_variant_id ?? null;

            if ($productVariantId) {
                // Fetch supplier_price for product_variant_id
                $supplierProduct = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_variant_id' => $productVariantId])
                    ->first();
                
                // Assign supplier_price if found
                if ($supplierProduct) {
                    $stock->supplier_price = $supplierProduct->supplier_price;
                } else {
                    // Fallback to product_id if not found for product_variant_id
                    $supplierProductFallback = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_id' => $productId])
                        ->first();
                    
                    $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
                }
            } else {
                // If no product_variant_id, fetch supplier_price using product_id
                $supplierProductFallback = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_id' => $productId])
                    ->first();
                
                $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
            }

            // Calculate value (quantity * supplier_price)
            $stock->value = $stock->supplier_price * $stock->quantity;

            // Query to fetch min_product_quantity for the current product
            if ($productId) {
                // $categoryData = $this->Products->find()
                //     ->select(['Categories.min_product_quantity'])
                //     ->leftJoinWith('ProductCategories.Categories')
                //     ->where(['Products.id' => $productId])
                //     ->first();

                // // Assign min_product_quantity if available
                // $stock->min_product_quantity = $categoryData['_matchingData']['Categories']['min_product_quantity'] ?? 'N/A';

                // Fetch min_product_quantity from the Showrooms table
                $showroom = $this->Showrooms->find()
                    ->select(['min_product_quantity'])
                    ->where(['id' => $stock->showroom_id])
                    ->first();

                $stock->min_product_quantity = $showroom ? $showroom->min_product_quantity : 'N/A';


            } else {
                $stock->min_product_quantity = 'N/A';
            }
        }

        $showroom = $this->Showrooms->find()
                    ->where(['Showrooms.id' => $id])
                    ->first();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);
        }

        $this->set(compact('productStocks', 'currencySymbol', 'showroom', 'decimalSeparator', 'thousandSeparator', 'role'));
    }

}
