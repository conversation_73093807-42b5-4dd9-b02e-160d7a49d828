<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Showroom $showroom
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>
    .select2-container--default .select2-selection--single.is-invalid {
        border-color: #dc3545 !important;
        border-width: 1px;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    .error-msg {
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 4px;
        display: block;
    }
    .btn {
        background-color: transparent !important;
        color: #0d839b !important;
        font-weight: bolder;
    }
    .btn:hover, .btn-primary:hover, .btn:focus, .btn-primary:focus {
        background-color:  transparent !important;
        color: #0d839b !important;
    }

    .card {
        border: 1px solid #0d839b;
        background-color: #fff !important;
        padding-left: 10px !important;
        /*        border-top-right-radius: 10px!important;*/
        border-bottom-right-radius: 10px!important;
        border-bottom-left-radius: 10px!important;
        margin-bottom: 0px !important;
    }

    .section-body .card .card-body {
        border-top-right-radius: 10px!important;
        border-bottom-right-radius: 10px!important;
        border-bottom-left-radius: 10px!important;
    }

    #General_id {
        position: relative;
        top: 2px;
    }

    p{
        color: black !important;
    }

    label {
        color: black !important;
        font-weight: 900 !important;
    }

    .active_tab {
        background-color: #fff !important;
        color: orange !important;
        border-top-right-radius: 10px!important;
        border-top-left-radius: 10px!important;
        border-bottom-left-radius: 0px!important;
        border-bottom-right-radius: 0px!important;
        width: 130px;
        position: relative;
        top: 6px;
        z-index:9;
        border-left:1px solid teal !important;
        border-top:1px solid teal !important;
        border-right:1px solid teal !important;
    }

    .active_tab:hover{

        border-color: 1px solid teal !important;
        color: orange !important;
        background: white !important;
        border: 1px solid #0d839b !important;
        border-bottom: 2px solid #fff !important;
        border-top-right-radius: 10px!important;
        border-top-left-radius: 10px!important;
        border-bottom-left-radius: 0px!important;
        border-bottom-right-radius: 0px!important;
    }

    .btn:not(.btn-social):not(.btn-social-icon):active,
    .btn:not(.btn-social):not(.btn-social-icon):focus,
    .btn:not(.btn-social):not(.btn-social-icon):hover {
        border-color: teal !important;
        background-color: #fff;       
        border-bottom: 3px solid #fff !important;
    }

    .btn:not(.btn-social):not(.btn-social-icon):hover
    { 
        border: none;
        background-color: transparent;
        border-bottom: none !important; 
    }

    .btn:not(.btn-social):not(.btn-social-icon) { 
        border: none; 
        background-color:
    } 

    .btn:not(.btn-social):not(.btn-social-icon):hover { 
        background-color: transparent;
    }

    .table:not(.table-sm) thead th {
        background-color: #FDF3E8 !important;
        color: black !important;
    }

    .admin_btn:hover{
        background-color: #f77f00 !important;
        color: white !important;
    }

    .admin_btn
    {
        color: white !important;
        background-color: #0d839b !important;
    }

    .dataTables_paginate
    {
        float: inline-end;
    }

    .modal-backdrop {
        position: relative !important;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }

    .switch input { 
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      -webkit-transition: .4s;
      transition: .4s;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      -webkit-transition: .4s;
      transition: .4s;
    }

    input:checked + .slider {
      background-color: #2196F3;
    }

    input:focus + .slider {
      box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
      -webkit-transform: translateX(26px);
      -ms-transform: translateX(26px);
      transform: translateX(26px);
    }

    /* Rounded sliders */
    .slider.round {
      border-radius: 34px;
    }

    .slider.round:before {
      border-radius: 50%;
    }

    #supplier-product-filter-body-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #supplier-product-filter-body-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #supplier-product-filter-body-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #supplier-product-filter-body-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #supplier-product-filter-body-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #supplier-product-filter-body-container .input-group .btn {
        box-shadow: unset !important;
    }

    #supplier-product-filter-body-container .btn:focus,
    #supplier-product-filter-body-container .btn:hover,
    #supplier-product-filter-body-container .btn.active {
        background-color: #f77f00 !important;
    }

    #purchase-order-filter-body-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #purchase-order-filter-body-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #purchase-order-filter-body-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #purchase-order-filter-body-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #purchase-order-filter-body-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #purchase-order-filter-body-container .input-group .btn {
        box-shadow: unset !important;
    }

    #purchase-order-filter-body-container .btn:focus,
    #purchase-order-filter-body-container .btn:hover,
    #purchase-order-filter-body-container .btn.active {
        background-color: #f77f00 !important;
    }


    #return-order-filter-body-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #return-order-filter-body-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #return-order-filter-body-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #return-order-filter-body-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #return-order-filter-body-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #return-order-filter-body-container .input-group .btn {
        box-shadow: unset !important;
    }

    #return-order-filter-body-container .btn:focus,
    #return-order-filter-body-container .btn:hover,
    #return-order-filter-body-container .btn.active {
        background-color: #f77f00 !important;
    }

    #supplier-payments-filter-body-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #supplier-payments-filter-body-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #supplier-payments-filter-body-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #supplier-payments-filter-body-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #supplier-payments-filter-body-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #supplier-payments-filter-body-container .input-group .btn {
        box-shadow: unset !important;
    }

    #supplier-payments-filter-body-container .btn:focus,
    #supplier-payments-filter-body-container .btn:hover,
    #supplier-payments-filter-body-container .btn.active {
        background-color: #f77f00 !important;
    }

    .delete-img-btn1 {
        position: relative;
        /* top: -46px; */
        bottom: 40px;
        left: 35px;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        border: none;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-button {
        position: absolute;
        top: 42px;
        right: 102px;
        background-color: rgba(0, 0, 0, 0.5) !important;
        color: white !important;
        border: none !important;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        cursor: pointer !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .cheque-remove-button {
        position: absolute;
        top: 690px;
        right: 592px;
        background-color: rgba(0, 0, 0, 0.5) !important;
        color: white !important;
        border: none !important;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        cursor: pointer !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .receipt-remove-button {
        position: absolute;
        top: 476px;
        right: 595px;
        background-color: rgba(0, 0, 0, 0.5) !important;
        color: white !important;
        border: none !important;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        cursor: pointer !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .input-group-text, select.form-control:not([size]):not([multiple]), .form-control:not(.form-control-sm):not(.form-control-lg) {
    font-size: 13px !important;
    }

    #purchaseOrderProductTable {
        width: 100% !important;
    }

    .modal-backdrop {
        background-color : transparent !important
    }

    #select2-payment-bill-select-container,
    #select2-payment-showrooms-select-container,
    #select2-payment-mode-container,
    #select2-bill-select-container,
    #select2-showrooms-select-container,
    #select2-product-id-container,
    #select2-warehouses-select-container,
    #select2-return-showrooms-select-container{
    display: flex;
    align-items: center;
    width: 328px;
   }

   #select2-product-id-container {
    display: flex;
    align-items: center;
    width: 328px;
   }

   .color-teal {
    color:teal !important;
    float:right;
   }

   #select2-product_option-container,
   #select2-po_variant_id-container,
   #select2-po-attribute-dropdown-container {
    display: flex;
    align-items: center;
   }

   .back-clr{
    background-color:#FDF3E8 !important;
   }

   #select2-product_option-container, #select2-po_variant_id-container {
    max-width: 150px !important;
   }

   .select2-container{
        width: 100% !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered{
        line-height: 40px;
    }

    td {
        input {
            max-width:70px;
        }
    }

    #purchaseOrderProductTableHeader {
    background-color: #FDF3E8 !important;
    color: black !important;
}
</style>
<?php $this->end(); ?>
<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item active">
                <a href="<?= $this->Url->build(['controller' => 'Suppliers', 'action' => 'index']) ?>"><?= __('Suppliers') ?></a>
            </li>
            <li class="breadcrumb-item active"><?= __('View') ?></li>
        </ul>
        <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
            <small class="p-10 fw-bold"><?= __('BACK') ?></small>
            <span class="rotate me-2">⤣</span>
        </button>
    </div>
</section>

<div class="d-flex justify-content-between">
    
    <a onclick="openTab(event, 'Supplier_info')" class="btn m-t-5 tablinks active_tab" style="width:200px;" id="General_id"><?= __('Supplier Info') ?></a>

    <a onclick="openTab(event, 'Supplier_product')" class="btn m-t-5 m-b-5 tablinks" style="border-radius: 10px;width:200px;padding: 4px;"><?= __('Supplier Products') ?></a>

    <a onclick="openTab(event, 'Order_request')" class="btn m-t-5 m-b-5 tablinks" style="border-radius: 10px;width:200px;padding: 4px;"><?= __('Purchase Order Request') ?></a>

    <!-- <a onclick="openTab(event, 'Return_request')" class="btn m-t-5 m-b-5 tablinks" style="border-radius: 10px;width:200px;padding: 4px;" id="return_order_tab">< ?= __('Return Request') ?></a> -->

    <a onclick="openTab(event, 'Supplier_payment')" class="btn m-t-5 m-b-5 tablinks" style="border-radius: 10px;width:200px;padding: 4px;"><?= __('Payments') ?></a>

</div>

<div id="Supplier_info" class="tabcontent" style="display: block;">
    <section id="view-product-product-details" class="section-body">

        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
                <div class="card">
                    <div class="card-body pb-0">
                        <div class="form-group row">
                            <label for="category" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Supplier Name') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="category" name="category" class="ps-5"><?php echo !empty($supplier->name) ? h($supplier->name) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="sub-category" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Point Of Contact Name') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="sub-category" name="sub-category" class="ps-5"><?php echo !empty($supplier->contact_name) ? h($supplier->contact_name) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="product-size" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Point Of Contact Phone') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="product-size" name="product-size" class="ps-5"><?php echo !empty($supplier->phone_number) ? h($supplier->phone_number) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="brand" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Point Of Contact Email') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="brand" name="brand" class="ps-5"><?php echo !empty($supplier->contact_email) ? h($supplier->contact_email) : '-'; ?></p>
                            </div>
                        </div>
                       
                        <div class="form-group row">
                            <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Fax') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="start-date" name="start-date" class="ps-5"><?php echo !empty($supplier->fax) ? '+'.h($supplier->fax_country_code).' '.h($supplier->fax) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('After Sale Support Number') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="start-date" name="start-date" class="ps-5"><?php echo !empty($supplier->support_number) ? '+'.h($supplier->support_country_code).' '.h($supplier->support_number) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="availability-date" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Credit Period') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="availability-date" name="availability-date" class="ps-5"><?php echo !empty($supplier->credit_period) ? h($supplier->credit_period) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="availability-date" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Address') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="availability-date" name="availability-date" class="ps-5"><?php echo !empty($supplier->address) ? h($supplier->address) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="product-images" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('City') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="product-images" name="product-images" class="ps-5"><?php echo !empty($supplier->city) ? h($supplier->city) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="supplier-reference" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Pin Code') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="supplier-reference" name="supplier-reference" class="ps-5"><?php echo !empty($supplier->pincode) ? h($supplier->pincode) : '-'; ?></p>
                            </div>
                        </div>
                        <!-- <div class="form-group row">
                            <label for="supplier-name" class="col-sm-2 col-form-label fw-bold  ps-5">< ?= __('Pending Payment') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="supplier-name" name="supplier-name" class="ps-5">-</p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="supplier-name" class="col-sm-2 col-form-label fw-bold  ps-5">< ?= __('Due Payment') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="supplier-name" name="supplier-name" class="ps-5">-</p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="supplier-name" class="col-sm-2 col-form-label fw-bold  ps-5">< ?= __('Last Payment Date') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="supplier-name" name="supplier-name" class="ps-5">-</p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="supplier-name" class="col-sm-2 col-form-label fw-bold  ps-5">< ?= __('Last Payment Amount') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="supplier-name" name="supplier-name" class="ps-5">-</p>
                            </div>
                        </div> -->
                        <div class="form-group row">
                            <label for="supplier-name" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Status') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p id="supplier-name" name="supplier-name" class="ps-5"><?php if(!empty($supplier->status)) {

                                        $status = $supplier->status;

                                        $statusLabel = ($status === 'A') ? 'Active' : 
                                                       (($status === 'I') ? 'Inactive' : 'Deleted');

                                        echo $statusLabel;

                                } ?></p>
                            </div>
                        </div>
                    </div>
                                
                </div>
            </div>
        </div>
    </section>
</div>

<div id="Supplier_product" class="tabcontent" style="display: none;">
    <section id="view-product-product-details" class="section-body">

        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4 style="color: black !important;"><?= __('Supplier Products') ?></h4>
                    <div class="card-header-form">
                        <form>
                            <div class="input-group" style="margin-left:17px;">
                                <input type="text" class="form-control search-control" placeholder="Search" id="customSupplierProductSearchBox">
                                <div class="input-group-btn">
                                    <button class="btn admin_btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <?php if ($canAdd): ?>
                                    <a class="btn view-add-supplier-product-modal btn-primary admin_btn m-r-15">
                                        <i class="fas fa-plus"></i>
                                        <?= __('Add Supplier Products') ?>
                                    </a>
                                    <a class="btn view-upload-supplier-product-modal btn-primary admin_btn m-r-15">
                                        <i class="fas fa-upload"></i>
                                        <?= __('Upload CSV') ?>
                                    </a>
                                <?php endif; ?>
                                <button
                                    class="btn admin_btn btn-primary supplier-product-menu-toggle fw-bold"
                                    type="submit">
                                    <i
                                        class="fas fa-filter"                 
                                    ></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div id="supplier-product-filter-body-container">
                    <div class="" style="margin-top: 0px !important;margin-right: 0px !important;">
                        <form method="get" accept-charset="utf-8" class="form-inline filter-rating attribute"
                            id="filter-search" action="">
                            <div class="d-flex card-body">
                                <div class="d-flex align-items-center m-l-20"
                                >
                                    <label class="m-r-5" for="status" style="width: max-content;"><?= __('Status:') ?></label>
                                    <select name="status" id="filterSupplierProductStatus" class="form-select form-control" style="width:150px;">
                                        <option value=""><?= __('Filter By Status') ?></option>
                                        <option value="A"><?= __('Active') ?></option>
                                        <option value="I"><?= __('Inactive') ?></option>
                                    </select>
                                </div>
                                <div class="d-flex">
                                    <button type="button" style="margin-right: 0px !important;" class="m-l-20 admin_btn btn supplier_product_filter">
                                        <i class="fa fa-filter" aria-hidden="true"></i>
                                    </button>
                                    <button type="reset" class="m-l-10 admin_btn btn reset_supplier_product_filter"><i class="fas fa-redo-alt"></i></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body" style="padding-top: 0px;">
                    <div class="table-responsive">
                        <div id="table-attributes_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                            <table class="table table-striped dataTable no-footer" id="supplierProductTable" role="grid" aria-describedby="table-attributes_info">
                                <thead>
                                    <tr>
                                        <th><?= __('Id') ?></th>
                                        <th><?= __('Supplier Name') ?></th>
                                        <th><?= __('SKU Id') ?></th>
                                        <th><?= __('Product Name') ?></th>
                                        <th><?= __('Product Variant Name') ?></th>
                                        <th><?= __('Supplier Price') ?></th>
                                        <th><?= __('Product Image') ?></th>
                                        <th id="supplierProductStatus"><?= __('Status') ?></th>
                                        <th class="actions"><?= __('Actions') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($supplier_products as $supplier_product): ?>
                                    <tr>
                                        <td><?php echo !empty($supplier_product->id) ? $this->Number->format($supplier_product->id) : 'N/A'; ?></td>
                                        <td><?php echo !empty($supplier_product->supplier->name) ? h($supplier_product->supplier->name) : 'N/A'; ?></td>
                                        <td><?php echo !empty($supplier_product->product->sku) ? h($supplier_product->product->sku) : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($supplier_product->product->name) ? h($supplier_product->product->name) : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($supplier_product->product_variant->variant_name) ? h($supplier_product->product_variant->variant_name) : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($supplier_product->supplier_price) ? h(number_format($supplier_product->supplier_price, 0,'', $thousandSeparator)).' '.$currencySymbol : 'N/A'; ?></td>
                                        <td><img src="<?= $supplier_product->product->product_image ?>" style="height: 40px;width: 40px;">
                                        </td> 
                                        <td>
                                            <?php
                                                $statusMap = [
                                                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                                                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                                                    'D' => ['label' => __('Deleted'), 'class' => 'col-red']
                                                ];

                                                $status = $statusMap[$supplier_product->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $status['class'] ?>">
                                                    <?= h($status['label']) ?>
                                                </div>
                                        </td>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a target="_blank" href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'view', $supplier_product->product->id]) ?>"
                                                class="" data-toggle="tooltip" title="<?= __("View") ?>"><i
                                                    class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>
                                                <a style="cursor: pointer;" onclick="openEditSupplierProductModal(<?= $supplier_product->id ?>)" data-toggle="tooltip" data-id="<?= $supplier_product->id ?>" title="Edit"><i
                                                        class="fas fa-pencil-alt m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canDelete): ?>            
                                                <a href="<?= $this->Url->build(['controller' => 'SupplierProducts', 'action' => 'delete', $supplier_product->id]) ?>"
                                                    class="delete-btn" data-toggle="tooltip" title="Delete"
                                                    data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                        <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?>    
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            </div>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
    </section>

    <div id="upload-supplier-product-modal" style="margin-top: 100px;" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <div class="d-flex">
                    <h5 class="modal-title"><?= __("Upload Supplier Product") ?></h5>
                    <a class="btn admin_btn m-l-40 download_csv_format admin_btn"><i class="fas fa-download"></i> <?= __('Download Sample Format') ?></a>
                </div>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container">
                    <form id="upload_supplier_product_form"> 

                        <div class="form-group row">
                            <label for="supplier_id" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Name") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <?php echo $this->Form->control('supplier_id', [
                                    'options' => $suppliers,
                                    'default' => $supplier->id,
                                    'disabled' => true, 
                                    'empty' => __('Select Supplier'), 
                                    'class' => 'form-control',
                                    'data-live-search' => "true",
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="upload_supplier_error"><?= __('Please select supplier') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product_id" class="col-sm-2 col-form-label fw-bold"><?= __("Upload File") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="file" id="csv_file" name="csv_file" class="form-control" accept=".csv">
                                <span style="display:none;color: #dc3545;" id="upload_file_error"><?= __('Please select a file') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-10 offset-sm-2">
                                <button style="background-color: #0d839b !important;color: white !important" type="submit" class="btn upload_supplier_product_btn admin_btn"><?= __('Save') ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>

    <div id="add-supplier-product-modal" style="margin-top: 100px;" class="modal" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-90">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Add Supplier Products") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container">
                    <form id="add_supplier_product_form"> 

                        <div class="form-group row">
                            <label for="supplier_id" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Name") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <?php echo $this->Form->control('supplier_id', [
                                    'options' => $suppliers,
                                    'default' => $supplier->id,
                                    'disabled' => true, 
                                    'empty' => __('Select Supplier'), 
                                    'class' => 'form-control',
                                    'data-live-search' => "true",
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="supplier_error"><?= __('Please select supplier') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product_id" class="col-sm-2 col-form-label fw-bold"><?= __("Product Name") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('product_id', [
                                    'options' => $products,
                                    'empty' => __('Select Product'), 
                                    'class' => 'form-control select2',
                                    'data-live-search' => "true",
                                    'label' => false,
                                    'id' => 'supplier_product_id'
                                ]); ?>
                                <span class="error-msg" style="display:none;color: #dc3545;" id="product_error"><?= __('Please select product') ?></span>
                            </div>
                        </div>

                        <!-- Product Variant Selection (Dynamically Populated) -->
                        <div class="form-group row">
                            <label for="supplier_product_variant_id" class="col-sm-2 col-form-label fw-bold"><?= __("Product Variant") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <select id="supplier_product_variant_id" name="product_variant_id" class="form-control select2">
                                    <option value=""><?= __('Select Variant') ?></option>
                                </select>
                                <span style="display:none;color: #dc3545;" id="variant_error"><?= __('Please select a variant') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_price" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Price") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('supplier_price', ['type' => 'number', 'class' => 'form-control', 'label' => false]); ?>
                                <span style="display:none;color: #dc3545;" id="supplier_price_error"><?= __('Please enter a supplier price') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-10 offset-sm-2">
                                <button style="background-color: #0d839b !important;color: white !important" type="button" onclick="saveSupplierProducts()" class="btn add_supplier_product_btn admin_btn"><?= __('Save') ?></button>
                                <button style="background-color: #0d839b !important;color: white !important" type="reset" id="resetButton" class="btn admin_btn"><?= __('Reset') ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>

    <div id="edit-supplier-product-modal" style="margin-top: 100px;" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Edit Supplier Products") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container">
                    <form id="edit_supplier_product_form"> 

                        <div class="form-group row">
                            <label for="supplier_id" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Name") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <?php echo $this->Form->control('supplier_id', [
                                    'options' => $suppliers,
                                    'default' => $supplier->id,
                                    'disabled' => true,
                                    'empty' => __('Select Supplier'), 
                                    'class' => 'form-control edit_supplier_id', 
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="edit_supplier_error"><?= __('Please select supplier') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product_id" class="col-sm-2 col-form-label fw-bold"><?= __("Product Name") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" id="supplier_product_id_value" name="product_id">
                                <?php echo $this->Form->control('product_id', [
                                    'options' => $products, 
                                    'disabled' => true,
                                    'empty' => __('Select Product'), 
                                    'class' => 'form-control edit_product_id', 
                                    'label' => false,
                                    'data-live-search' => "true"
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="edit_product_error"><?= __('Please select product') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_product_variant_id" class="col-sm-2 col-form-label fw-bold"><?= __("Product Variant") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" id="edit_supplier_product_variant_id" name="product_variant_id">
                                <?php echo $this->Form->control('productvariantid', [
                                    'type' => 'text',
                                    'disabled' => true, 
                                    'class' => 'form-control supplier_product_variant_id_text', 
                                    'label' => false,
                                    'data-live-search' => "true"
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_price" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Price") ?> <sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('supplier_price', ['type' => 'number', 'class' => 'form-control edit_supplier_price', 'label' => false]); ?>
                                <span style="display:none;color: #dc3545;" id="edit_supplier_price_error"><?= __('Please enter a supplier price') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __('Status') ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select edit_supplier_product_status',
                                    'id' => 'status',
                                    'options' => [
                                        'A' => 'Active',
                                        'I' => 'Inactive'
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-10 offset-sm-2 edit_supplier_product_footer">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>
</div>

<div id="Order_request" class="tabcontent" style="display: none;">
    <section id="view-product-product-details" class="section-body">

        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4 style="color: black !important;"><?= __('Purchase Order Request') ?></h4>
                    <div class="card-header-form">
                        <form>
                            <div class="input-group" style="margin-left:17px;">
                                <input type="text" class="form-control search-control" placeholder="Search" id="customPurchaseOrderSearchBox">
                                <div class="input-group-btn">
                                    <button class="btn admin_btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <?php if ($canAdd): ?>
                                    <a class="btn view-add-purchase-order-modal btn-primary admin_btn m-r-15">
                                        <i class="fas fa-plus"></i>
                                        <?= __('Add Order Request') ?>
                                    </a>
                                <?php endif; ?>
                                <button
                                    class="btn admin_btn btn-primary purchase-order-menu-toggle fw-bold"
                                    type="button">
                                    <i
                                        class="fas fa-filter"                 
                                    ></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div id="purchase-order-filter-body-container" style="display: none;">
                    <div class="" style="margin-top: 0px !important;margin-right: 0px !important;">
                        <form method="get" accept-charset="utf-8" class="form-inline filter-rating attribute"
                            id="filter-search" action="">
                            <div class="d-flex card-body">
                                <div class="d-flex align-items-center m-l-20 col-sm-5">
                                    <label class="m-r-5 col-sm-4" for="payment_status"><?= __('Payment Status:') ?></label>
                                    <select name="payment_status" id="filterPaymentStatus" class="form-control form-select">
                                        <option value=""><?= __("Filter By Payment Status") ?></option>
                                        <option value="Paid"><?= __("Paid") ?>
                                        </option>
                                        <option value="Pending"><?= __("Pending") ?>
                                        </option>
                                    </select>
                                </div>
                                <div class="d-flex align-items-center m-l-20">
                                    <label class="m-r-5" for="status"><?= __('Status:') ?></label>
                                    <select name="status" id="filterPurchaseOrderStatus" class="form-control form-select">
                                        <option value=""><?= __('Filter By Status') ?></option>
                                        <option value="A"><?= __('Active') ?></option>
                                        <option value="I"><?= __('Inactive') ?></option>
                                    </select>
                                </div>
                                <div class="d-flex">
                                    <button type="button" style="margin-right: 0px !important;" class="m-l-20 admin_btn btn purchase_order_filter">
                                        <i class="fa fa-filter" aria-hidden="true"></i>
                                    </button>
                                    <button type="reset" class="m-l-10 admin_btn btn reset_purchase_order_filter"><i class="fas fa-redo-alt"></i></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body" style="padding-top: 0px;">
                    <div class="table-responsive">
                        <div id="table-attributes_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                            <table class="table table-striped dataTable no-footer" id="purchaseOrderTable" role="grid" aria-describedby="table-attributes_info">
                                <thead>
                                    <tr>
                                        <th><?= __('Order Date') ?></th>
                                        <th><?= __('Bill No.') ?></th>
                                        <th><?= __('Supplier Bill No.') ?></th>
                                        <th id="purchaseOrderStatus"><?= __('Status') ?></th>
                                        <th id="payment_status"><?= __('Payment Status') ?></th>
                                        <th id="delivery_status"><?= __('Delivery Status') ?></th>
                                        <th class="actions"><?= __('Actions') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($order_request as $order): ?>
                                    <tr>
                                        <td><?php echo !empty($order->order_date) ? $order->order_date->format($dateFormat . ' ' . $timeFormat) : 'N/A'; ?></td>
                                        <td><?php echo !empty($order->bill_no) ? h($order->bill_no) : 'N/A'; ?></td>
                                        <td><?php echo !empty($order->supplier_bill_no) ? h($order->supplier_bill_no) : 'N/A'; ?>
                                        </td>
                                        <td>
                                            <?php
                                                $statusMap = [
                                                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                                                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                                                    'P' => ['label' => __('Pending'), 'class' => 'col-blue']
                                                ];

                                                $status = $statusMap[$order->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $status['class'] ?>">
                                                    <?= h($status['label']) ?>
                                                </div>
                                        </td>
                                        <td>
                                            <?php
                                                $paymentStatusMap = [
                                                    'Paid' => ['label' => __('Paid'), 'class' => 'col-green'],
                                                    'Partially Paid' => ['label' => __('Partially Paid'), 'class' => 'col-orange'],
                                                    'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                                                ];

                                                $payment_status = $paymentStatusMap[$order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $payment_status['class'] ?>">
                                                    <?= h($payment_status['label']) ?>
                                                </div>
                                        </td>
                                        <td>
                                            <?php
                                                $deliveredStatusMap = [
                                                    'Delivered' => ['label' => __('Delivered'), 'class' => 'col-green'],
                                                    'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                                                ];

                                                $delivery_status = $deliveredStatusMap[$order->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $delivery_status['class'] ?>">
                                                    <?= h($delivery_status['label']) ?>
                                                </div>
                                        </td>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a onclick="openViewPurchaseOrderModal(<?= $order->id ?>)" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>    
                                                <!-- <a onclick="openEditPurchaseOrderModal(< ?= $order->id ?>)" data-toggle="tooltip" title="Edit"><i
                                                        class="fas fa-pencil-alt m-r-10"></i></a> -->
                                            <?php endif; ?>
                                            <?php if ($canDelete): ?>            
                                                <!-- <a href="< ?= $this->Url->build(['controller' => 'SupplierPurchaseOrders', 'action' => 'delete', $order->id]) ?>"
                                                    class="delete-btn" data-toggle="tooltip" title="Delete"
                                                    data-delete-confirmation="< ?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="< ?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="< ?= addslashes($deleteFailMessage); ?>">
                                                        <i class="far fa-trash-alt"></i>
                                                </a> -->
                                            <?php endif; ?>    
                                                <!-- <a data-toggle="tooltip" title="Verify" onclick="openVerifyModal(< ?= $order->id ?>)"><i class="fa fa-check" aria-hidden="true"></i></a> -->
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            </div>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
    </section>

    <div id="add-purchase-order-modal" style="margin-top: 50px;" class="modal" role="dialog">
      <div class="modal-dialog" style="max-width: 1020px !important;" role="document">
        <div class="modal-content m-l-120">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Add Purchase Order Request") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container m-l-20">
                    <form id="add_purchase_order_form"> 

                        <div class="form-group row">
                            <label for="order_date" class="col-sm-2 col-form-label fw-bold"><?= __("Order Date") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <?php echo $this->Form->control('order_date', [
                                    'type' => 'date',
                                    'class' => 'form-control',
                                    'id' => 'order_date',
                                    'placeholder' => __('Order Date'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="order_date_error"><?= __('Please choose order date') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Bill No.") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('bill_no', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'bill_no',
                                    'placeholder' => __('Bill No.'),
                                    'maxlength' => 100,  // Max length limit
                                    // 'readonly' => true,
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="bill_no_error"><?= __('Please enter bill no.') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Bill No.") ?></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('supplier_bill_no', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'supplier_bill_no',
                                    'placeholder' => __('Supplier Bill No.'),
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'status',
                                    'options' => [
                                        'P' => 'Pending',
                                        'A' => 'Active',
                                        'I' => 'Inactive'
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="delivery_status" class="col-sm-2 col-form-label fw-bold"><?= __("Delivery Status") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('delivery_status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'delivery_status',
                                    'options' => [
                                        'Pending' => 'Pending',
                                        'Delivered' => 'Delivered'
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="delivery_address" class="col-sm-2 col-form-label fw-bold"><?= __("Delivery Address") ?> <sup class="text-danger font-11">*</sup></label>
                            
                            <div class="col-sm-5 d-flex main-field">
                                <div class="form-check mr-3">
                                    <input class="form-check-input" style="width: 10px;height: 10px;" type="radio" id="yes" name="deliver_to" value="Showroom" checked>
                                    <label class="form-check-label" for="yes">
                                        <?= __('Showroom') ?>
                                    </label>
                                </div>
                                <div class="form-check mr-3">
                                    <input class="form-check-input" style="width: 10px;height: 10px;" type="radio" id="no" name="deliver_to" value="Warehouse">
                                    <label class="form-check-label" for="no">
                                        <?= __('Warehouse') ?>
                                    </label>
                                </div>
                            </div>                   
                        </div>

                        <div class="form-group row">
                            <label for="" class="col-sm-2 col-form-label fw-bold"></label>
                            <div class="col-sm-5 main-field">
                                <div id="showrooms-select-div"> 
                                <?php
                                echo $this->Form->control(
                                    'id_deliver_to',
                                    [
                                        'id' => 'showrooms-select',
                                        'type' => 'select',
                                        'empty' => 'Select Showroom',
                                        'label' => false,
                                        'div' => false,
                                        'title' => __('Select Showroom'),
                                        'options' => $showrooms,
                                        'data-live-search' => "true",
                                        'class' => 'form-control select2'
                                    ]
                                );
                                ?>
                                <span style="display:none;color: #dc3545;" id="showroom_error"><?= __('Please select showroom') ?></span>
                                </div>

                                <div id="warehouses-select-div" style="display: none;">
                                    <?php
                                    echo $this->Form->control(
                                        'id_deliver_to',
                                        [
                                            'id' => 'warehouses-select',
                                            'type' => 'select',
                                            'empty' => 'Select Warehouse',
                                            'label' => false,
                                            'div' => false,
                                            'title' => __('Select Warehouse'),
                                            'options' => $warehouses,
                                            'data-live-search' => "true",
                                            'style' => 'display:none;',
                                            'class' => 'form-control select2'
                                        ]
                                    );
                                    ?>
                                    <span style="display:none;color: #dc3545;" id="warehouse_error"><?= __('Please select warehouse') ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="required_delivery_date" class="col-sm-2 col-form-label fw-bold"><?= __("Required Delivery Date") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('required_delivery_date', [
                                    'type' => 'date',
                                    'class' => 'form-control',
                                    'id' => 'required_delivery_date',
                                    'placeholder' => __('Required Delivery Date'),
                                    'value' => date('Y-m-d'),
                                    'min' => date('Y-m-d'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="required_delivery_date_error"><?= __('Please choose required delivery date') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="payment_status" class="col-sm-2 col-form-label fw-bold"><?= __("Payment Status") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('payment_status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'payment_status',
                                    'options' => [
                                        'Pending' => 'Pending',
                                        'Paid' => 'Paid'
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="payment_due_date" class="col-sm-2 col-form-label fw-bold"><?= __("Payment Due Date") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('payment_due_date', [
                                    'type' => 'date',
                                    'class' => 'form-control',
                                    'id' => 'payment_due_date',
                                    'placeholder' => __('Payment Due Date'),
                                    'value' => date('Y-m-d'),
                                    'min' => date('Y-m-d'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="payment_due_date_error"><?= __('Please choose payment due date') ?></span>
                            </div>
                            <div class="col-sm-7 m-t-20">
                                <p class="color-teal"><?= __('Credit Period:') ?> <?= h($supplier->credit_period) ?> <?= __('Days') ?></p>
                            </div>
                        </div>

                        <h6 class="m-b-20" style="color: #004958"><?= __("Add Products") ?></h6>
                        <table id="add_product_table" class="add_product_table table-responsive table table-striped dataTable no-footer">
                          <thead style="backgroundColor:#FDF3E8">
                            <tr class="back-clr">
                              <td class="fw-bold"><?= __("Product") ?></td>
                              <td class="fw-bold"><?= __("Product Variant") ?></td>
                              <td class="fw-bold"><?= __("Product Attribute") ?></td>
                              <td class="fw-bold"><?= __("SKU") ?></td>
                              <td class="fw-bold"><?= __("Supplier") ?></td>
                              <td class="fw-bold"><?= __("Quantity") ?></td>
                              <td class="fw-bold"><?= __("Image") ?></td>
                              <td class="fw-bold"><?= __("Action") ?></td>
                            </tr>
                          </thead>  
                          <tbody>
                            <tr>
                              <td>
                                 <select id="product_option" data-live-search="true" class="form-control product_option select2">
                                    <option value="0">Select Product</option>
                                    <?php foreach ($supplier_products_active as $product): ?>
                                        <option data-product-supplier="<?= h($product->supplier->name) ?>" data-product-image="<?= h($product->product->product_image) ?>" data-product-sku="<?= h($product->product->sku) ?>" value="<?= h($product->product->id) ?>"><?= h($product->product->name) ?></option> 
                                    <?php endforeach; ?>
                                </select><br>
                                <span id="product_option_error" style="color: #dc3545;"></span> 
                              </td>

                              <td>
                                  <?php 
                                        echo $this->Form->control('variant_id', [
                                        'id' => 'po_variant_id',
                                        'type' => 'select',
                                        'label' => false,
                                        'empty' => __('Select Variant'),
                                        'options' => [],
                                        'class' => 'form-control select2'
                                    ]);
                                  ?>
                              </td>

                              <td>
                                <?php 
                                        echo $this->Form->control('attribute_id', [
                                        'id' => 'po-attribute-dropdown',
                                        'type' => 'select',
                                        'label' => false,
                                        'empty' => __('Select Attribute'),
                                        'options' => [],
                                        'class' => 'form-control select2'
                                    ]);
                                  ?>

                              </td>

                              <td>
                                    <span id="product_sku"></span>
                              </td>

                              <td>
                                    <span id="product_supplier"></span>
                              </td>

                              <td>
                                  <input type="number" min="1" class="form-control product_quantity" id="product_quantity">
                                <span id="product_quantity_error" style="color: #dc3545;"></span>
                              </td>

                              <td>
                                  <img id="product_image" style="height: 40px;width: auto;"></img>
                              </td>
                              
                              <td>
                                  <button type="button" id="updateQuest" class="btn admin_btn btn-md updateQuest"><?= __("Add") ?> </button>
                              </td>

                              </td>
                            </tr>
                          </tbody>                 
                        </table>

                        <div class="form-group row">
                            <div class="col-sm-12">
                                <button style="background-color: #0d839b !important;color: white !important;float: right;position: relative;right: 30px;" type="button" onclick="savePurchaseOrderRequest()" class="btn add_purchase_order_btn admin_btn"><?= __('Save') ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>

    <div id="view-purchase-order-modal" style="margin-top: 50px;" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("View Purchase Order Request") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container view_purchase_order_container">
                    
                </div>
            </div>
        </div>
      </div>  
    </div>

    <div id="edit-purchase-order-modal" style="margin-top: 50px;" class="modal" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Edit Purchase Order Request") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container edit_purchase_order_container m-l-20">
                    <form id="edit_purchase_order_form">
                        <div class="form-group row">
                            <label for="order_date" class="col-sm-2 col-form-label fw-bold"><?= __("Order Date") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <?php echo $this->Form->control('order_date', [
                                    'type' => 'date',
                                    'class' => 'form-control',
                                    'id' => 'edit_order_date',
                                    'placeholder' => __('Order Date'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="edit_order_date_error"><?= __('Please choose order date') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Bill No.") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('bill_no', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'edit_bill_no',
                                    'placeholder' => __('Bill No.'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="edit_bill_no_error"><?= __('Please enter bill no.') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Bill No.") ?></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('supplier_bill_no', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'edit_supplier_bill_no',
                                    'placeholder' => __('Supplier Bill No.'),
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'edit_status',
                                    'options' => [
                                        'P' => 'Pending',
                                        'A' => 'Active',
                                        'I' => 'Inactive'
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="delivery_status" class="col-sm-2 col-form-label fw-bold"><?= __("Delivery Status") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('delivery_status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'edit_delivery_status',
                                    'options' => [
                                        'Pending' => 'Pending',
                                        'Delivered' => 'Delivered'
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="delivery_address" class="col-sm-2 col-form-label fw-bold"><?= __("Delivery Address") ?> <sup class="text-danger font-11">*</sup></label>
                            
                            <div class="col-sm-5 d-flex main-field">
                                <div class="form-check mr-3">
                                    <input class="form-check-input edit_delivery" style="width: 10px;height: 10px;" onchange="toggleSelectBoxEdit(this)" type="radio" id="yes1" name="deliver_to" value="Showroom">
                                    <label class="form-check-label" for="yes1">
                                        Showroom
                                    </label>
                                </div>
                                <div class="form-check mr-3">
                                    <input class="form-check-input edit_delivery" style="width: 10px;height: 10px;" onchange="toggleSelectBoxEdit(this)" type="radio" id="no1" name="deliver_to" value="Warehouse">
                                    <label class="form-check-label" for="no1">
                                        Warehouse
                                    </label>
                                </div>
                            </div>                   
                        </div>

                        <div class="form-group row">
                            <label for="" class="col-sm-2 col-form-label fw-bold"></label>
                            <div class="col-sm-5 main-field">
                                <div id="showrooms-select-edit-div">
                                    <?php
                                    echo $this->Form->control(
                                        'id_deliver_to',
                                        [
                                            'id' => 'showrooms-select-edit',
                                            'type' => 'select',
                                            'empty' => 'Select Showroom',
                                            'label' => false,
                                            'div' => false,
                                            'title' => __('Select Showroom'),
                                            'options' => $showrooms,
                                            'data-live-search' => "true",
                                            'class' => 'form-control select2'
                                        ]
                                    );
                                    ?>
                                    <span style="display:none;color: #dc3545;" id="edit_showroom_error"><?= __('Please select showroom') ?></span>
                                </div>

                                <div id="warehouses-select-edit-div">
                                    <?php
                                    echo $this->Form->control(
                                        'id_deliver_to',
                                        [
                                            'id' => 'warehouses-select-edit',
                                            'type' => 'select',
                                            'empty' => 'Select Warehouse',
                                            'label' => false,
                                            'div' => false,
                                            'title' => __('Select Warehouse'),
                                            'options' => $warehouses,
                                            'data-live-search' => "true",
                                            'class' => 'form-control select2'
                                        ]
                                    );
                                    ?>
                                    <span style="display:none;color: #dc3545;" id="edit_warehouse_error"><?= __('Please select warehouse') ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="required_delivery_date" class="col-sm-2 col-form-label fw-bold"><?= __("Required Delivery Date") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('required_delivery_date', [
                                    'type' => 'date',
                                    'class' => 'form-control',
                                    'id' => 'edit_required_delivery_date',
                                    'placeholder' => __('Required Delivery Date'),
                                    'min' => date('Y-m-d'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="edit_required_delivery_date_error"><?= __('Please choose required delivery date') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="payment_status" class="col-sm-2 col-form-label fw-bold"><?= __("Payment Status") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('payment_status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'edit_payment_status',
                                    'options' => [
                                        'Pending' => 'Pending',
                                        'Paid' => 'Paid'
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="payment_due_date" class="col-sm-2 col-form-label fw-bold"><?= __("Payment Due Date") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('payment_due_date', [
                                    'type' => 'date',
                                    'class' => 'form-control',
                                    'id' => 'edit_payment_due_date',
                                    'placeholder' => __('Payment Due Date'),
                                    'min' => date('Y-m-d'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="edit_payment_due_date_error"><?= __('Please choose payment due date') ?></span>
                            </div>
                            <div class="col-sm-12">
                                <p><?= __('Credit Period:') ?> <?= $supplier->credit_period ?></p>
                            </div>
                        </div>

                        <h6 class="m-b-20" style="color: #004958"><?= __("Add Products") ?></h6>
                        <table id="edit_product_table" class="edit_product_table table-responsive table">
                          <thead>
                            <tr>
                              <td class="fw-bold"><?= __("Product") ?></td>
                              <td class="fw-bold"><?= __("Product Variant") ?></td>
                              <td class="fw-bold"><?= __("Product Attribute") ?></td>
                              <td class="fw-bold"><?= __("SKU") ?></td>
                              <td class="fw-bold"><?= __("Supplier") ?></td>
                              <td class="fw-bold"><?= __("Quantity") ?></td>
                              <td class="fw-bold"><?= __("Image") ?></td>
                            </tr>
                          </thead>  
                          <tbody id="edit_product_table_tbody">
                            <tr>
                              <td>
                                 <select id="edit_product_option" data-live-search="true" class="form-control edit_product_option select2">
                                    <option value="0">Select Product</option>
                                    <?php foreach ($supplier_products_active as $product): ?>
                                        <option data-product-supplier="<?= h($product->supplier->name) ?>" data-product-image="<?= h($product->product->product_image) ?>" data-product-sku="<?= h($product->product->sku) ?>" value="<?= h($product->product->id) ?>"><?= h($product->product->name) ?></option> 
                                    <?php endforeach; ?>
                                  </select><br>
                                <span id="edit_product_option_error" style="color: #dc3545;"></span> 
                              </td>

                              <td>
                                  <?php 
                                        echo $this->Form->control('variant_id', [
                                        'id' => 'edit_po_variant_id',
                                        'type' => 'select',
                                        'label' => false,
                                        'empty' => __('Select Variant'),
                                        'options' => [],
                                        'class' => 'form-control select2'
                                    ]);
                                  ?>

                              </td>

                              <td>
                                <?php 
                                        echo $this->Form->control('attribute_id', [
                                        'id' => 'edit-po-attribute-dropdown',
                                        'type' => 'select',
                                        'label' => false,
                                        'empty' => __('Select Attribute'),
                                        'options' => [],
                                        'class' => 'form-control select2'
                                    ]);
                                  ?>

                              </td>

                              <td>
                                    <span id="product_sku"></span>
                              </td>

                              <td>
                                    <span id="product_supplier"></span>
                              </td>

                              <td>
                                  <input type="number" min="1" class="form-control edit_product_quantity" id="edit_product_quantity">
                                <span id="edit_product_quantity_error" style="color: #dc3545;"></span>
                              </td>

                              <td>
                                  <img id="product_image" style="height: 40px;width: auto;"></img>
                              </td>
                              
                              <td>
                                  <button type="button" id="updateQuest" class="btn admin_btn btn-md updateQuest"><?= __("Add") ?> </button>
                              </td>

                              </td>
                            </tr>
                          </tbody>                 
                        </table>
                        

                        <div class="form-group row">
                            <div class="col-sm-12 edit_button_append">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>
</div>

<div id="Return_request" class="tabcontent" style="display: none;">
    <section id="view-product-product-details" class="section-body">

        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4 style="color: black !important;"><?= __('Return Order Request') ?></h4>
                    <div class="card-header-form">
                        <form>
                            <div class="input-group" style="margin-left:17px;">
                                <input type="text" class="form-control search-control" placeholder="Search" id="customReturnOrderSearchBox">
                                <div class="input-group-btn">
                                    <button class="btn admin_btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <?php if ($canAdd): ?>
                                    <a class="btn view-add-return-order-modal btn-primary admin_btn m-r-15">
                                        <i class="fas fa-plus"></i>
                                        <?= __('Add Return Request') ?>
                                    </a>
                                <?php endif; ?>
                                <button
                                    class="btn admin_btn btn-primary return-order-menu-toggle fw-bold"
                                    type="submit">
                                    <i
                                        class="fas fa-filter"                 
                                    ></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div id="return-order-filter-body-container">
                    <div class="" style="margin-top: 0px !important;margin-right: 0px !important;">
                        <form method="get" accept-charset="utf-8" class="form-inline filter-rating attribute"
                            id="filter-search" action="">
                            <div class="d-flex card-body">
                                <div class="d-flex align-items-center m-l-20">
                                    <label class="m-r-5" for="return_status"><?= __('Return Status:') ?></label>
                                    <select name="return_status" id="filterReturnStatus" class="form-control form-select">
                                        <option value=""><?= __("Filter By Return Status") ?></option>
                                        <option value="Completed"><?= __("Completed") ?>
                                        </option>
                                        <option value="Pending"><?= __("Pending") ?>
                                        </option>
                                    </select>
                                </div>
                                <div class="d-flex align-items-center m-l-20 col-sm-3"
                                >
                                    <label class="m-r-5" for="status" style="width: max-content;"><?= __('Status:') ?></label>
                                    <select name="status" id="filterReturnOrderStatus" class="form-control form-select">
                                        <option value=""><?= __('Filter By Status') ?></option>
                                        <option value="A"><?= __('Active') ?></option>
                                        <option value="I"><?= __('Inactive') ?></option>
                                    </select>
                                </div>
                                <div class="d-flex">
                                    <button type="button" style="margin-right: 0px !important;" class="m-l-20 admin_btn btn return_order_filter">
                                        <i class="fa fa-filter" aria-hidden="true"></i>
                                    </button>
                                    <button type="reset" class="m-l-10 admin_btn btn reset_return_order_filter"><i class="fas fa-redo-alt"></i></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body" style="padding-top: 0px;">
                    <div class="table-responsive">
                        <div id="table-attributes_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                            <table class="table table-striped dataTable no-footer" id="returnOrderTable" role="grid" aria-describedby="table-attributes_info">
                                <thead>
                                    <tr>
                                        <th><?= __('Return Date') ?></th>
                                        <th><?= __('Bill No.') ?></th>
                                        <th><?= __('Supplier Bill No.') ?></th>
                                        <th id="return_status"><?= __('Return Status') ?></th>
                                        <th id="status"><?= __('Status') ?></th>
                                        <th class="actions"><?= __('Actions') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($return_request as $order): ?>
                                    <tr>
                                        <td><?php echo !empty($order->initiated_date) ? $order->initiated_date->format($dateFormat . ' ' . $timeFormat) : 'N/A'; ?></td>
                                        <td><?php echo !empty($order->bill_no) ? h($order->bill_no) : 'N/A'; ?></td>
                                        <td><?php echo !empty($order->supplier_bill_no) ? h($order->supplier_bill_no) : 'N/A'; ?>
                                        </td>
                                        <td>
                                            <?php
                                                $returnStatusMap = [
                                                    'Completed' => ['label' => __('Completed'), 'class' => 'col-green'],
                                                    'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                                                ];

                                                $return_status = $returnStatusMap[$order->return_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $return_status['class'] ?>">
                                                    <?= h($return_status['label']) ?>
                                                </div>
                                        </td>
                                        <td>
                                            <?php
                                                $statusMap = [
                                                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                                                    'I' => ['label' => __('Inactive'), 'class' => 'col-blue'],
                                                    'D' => ['label' => __('Deleted'), 'class' => 'col-red']
                                                ];

                                                $status = $statusMap[$order->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $status['class'] ?>">
                                                    <?= h($status['label']) ?>
                                                </div>
                                        </td>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a onclick="openViewReturnOrderModal(<?= $order->id ?>)" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>    
                                                <a onClick="openEditReturnOrderModal(<?= $order->id ?>)" data-toggle="tooltip" title="Edit"><i
                                                        class="fas fa-pencil-alt m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canDelete): ?>            
                                                <a href="<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'delete', $order->id]) ?>"
                                                    class="delete-btn" data-toggle="tooltip" title="Delete"
                                                    data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                        <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?>    
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            </div>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
    </section>

    <div id="add-return-order-modal" style="margin-top: 50px;" class="modal" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Add Return Order Request") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container">
                    <form id="add_return_order_form" action="<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'add']) ?>" method="post" enctype="multipart/form-data">

                        <div class="form-group row">
                            <label for="initiated_date" class="col-sm-2 col-form-label fw-bold"><?= __("Return Date") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <?php echo $this->Form->control('initiated_date', [
                                    'type' => 'date',
                                    'class' => 'form-control',
                                    'id' => 'return_initiated_date',
                                    'value' => date('Y-m-d'),
                                    'placeholder' => __('Order Date'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="return_initiated_date_error"><?= __('Please choose return date') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Bill No.") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="bill_no" id="return_bill_no">
                                <?php
                                echo $this->Form->control(
                                    'bill_no_select',
                                    [
                                        'id' => 'bill-select',
                                        'type' => 'select',
                                        'empty' => 'Select Bill No',
                                        'label' => false,
                                        'div' => false,
                                        'title' => __('Select Bill No'),
                                        'options' => $purchase_order_bills,
                                        'data-live-search' => "true",
                                        'class' => 'form-control select2'
                                    ]
                                );
                                ?>
                                <span style="display:none;color: #dc3545;" id="bill_select_error"><?= __('Please select bill no.') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Bill No.") ?></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_bill_no" id="return_supplier_bill_no">
                                <?php echo $this->Form->control('supplier_bill_no', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'supplier_bill_no_disable',
                                    'disabled' => true,
                                    'label' => false
                                ]); ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="delivery_address" class="col-sm-2 col-form-label fw-bold"><?= __("Pickup Address") ?> <sup class="text-danger font-11">*</sup></label>
                            
                            <div class="col-sm-5 d-flex main-field">
                                <div class="form-check mr-3">
                                    <input class="form-check-input" style="width: 10px;height: 10px;" onchange="returnToggleSelectBox('<?= __('Showroom') ?>')" type="radio" id="return_yes" name="deliver_to" value="Showroom" checked>
                                    <label class="form-check-label" for="return_yes">
                                        <?= __('Showroom') ?>
                                    </label>
                                </div>
                                <div class="form-check mr-3">
                                    <input class="form-check-input" style="width: 10px;height: 10px;" onchange="returnToggleSelectBox('<?= __('Warehouse') ?>')" type="radio" id="return_no" name="deliver_to" value="Warehouse">
                                    <label class="form-check-label" for="return_no">
                                        <?= __('Warehouse') ?>
                                    </label>
                                </div>
                            </div>                   
                        </div>

                        <div class="form-group row">
                            <label for="" class="col-sm-2 col-form-label fw-bold"></label>
                            <div class="col-sm-5 main-field">
                                <div id="return-showrooms-select-div">
                                    <?php
                                    echo $this->Form->control(
                                        'id_deliver_to',
                                        [
                                            'id' => 'return-showrooms-select',
                                            'type' => 'select',
                                            'empty' => 'Select Showroom',
                                            'label' => false,
                                            'div' => false,
                                            'title' => __('Select Showroom'),
                                            'options' => $showrooms,
                                            'data-live-search' => "true",
                                            'class' => 'form-control select2'
                                        ]
                                    );
                                    ?>
                                    <span style="display:none;color: #dc3545;" id="return_showroom_error"><?= __('Please select showroom') ?></span>
                                </div>
                                <div id="return-warehouses-select-div" style="display: none;">
                                    <?php
                                    echo $this->Form->control(
                                        'id_deliver_to',
                                        [
                                            'id' => 'return-warehouses-select',
                                            'type' => 'select',
                                            'empty' => 'Select Warehouse',
                                            'label' => false,
                                            'div' => false,
                                            'title' => __('Select Warehouse'),
                                            'options' => $warehouses,
                                            'data-live-search' => "true",
                                            'class' => 'form-control select2'
                                        ]
                                    );
                                    ?>
                                    <span style="display:none;color: #dc3545;" id="return_warehouse_error"><?= __('Please select warehouse') ?></span>
                                </div>
                            </div>
                        </div>

                        <h6 class="m-b-20" style="color: #004958"><?= __("Products") ?></h6>
                        <div style="overflow:scroll">
                        <table id="add_product_table" class="add_product_table table-responsive table">
                          <thead>
                            <tr style="background-color: #FDF3E8 !important";>
                              <td class="fw-bold"><?= __("Image") ?></td>
                              <td class="fw-bold"><?= __("Product") ?></td>
                              <td class="fw-bold"><?= __("Product Variant") ?></td>
                              <td class="fw-bold"><?= __("Product Attribute") ?></td>
                              <td class="fw-bold"><?= __("SKU") ?></td>
                              <td class="fw-bold"><?= __("Supplier Price") ?></td>
                              <td class="fw-bold"><?= __("Approved Quantity") ?></td>
                              <td class="fw-bold"><?= __("Return Quantity") ?></td>
                              <td class="fw-bold"><?= __("Upload Image for Defect") ?></td>
                              <td class="fw-bold"><?= __("Reason") ?></td>
                            </tr>
                          </thead>  
                          <tbody id="product_tbody">

                          </tbody>                 
                        </table>
                        </div>

                        <p class="m-b-20"><?= __("Total Price:") ?> <span id="add_total_price"></span></p>

                        <div class="form-group row">
                            <div class="col-sm-12">
                                <button style="background-color: #0d839b !important;color: white !important;float: right;position: relative;right: 30px;" type="submit" class="btn add_return_order_btn admin_btn"><?= __('Save') ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>

    <div id="view-return-order-modal" style="margin-top: 50px;" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("View Return Order Request") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container view_return_order_container">
                    
                </div>
                <!-- <p class="m-b-20">< ?= __("Total Price:") ?> <span id="view_total_price"></span></p> -->
            </div>
        </div>
      </div>  
    </div>

    <div id="edit-return-order-modal" style="margin-top: 50px;" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Edit Return Order Request") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container edit_return_order_container">
                    <form id="edit_return_order_form" method="post" enctype="multipart/form-data">
                        <div class="form-group row">
                            <label for="initiated_date" class="col-sm-2 col-form-label fw-bold"><?= __("Return Date") ?></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <span id="edit_return_initiated_date" style="color: black;"></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Bill No.") ?></label>
                            <div class="col-sm-5 main-field">
                                <span id="edit_return_bill_no" style="color: black;"></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_bill_no" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Bill No.") ?></label>
                            <div class="col-sm-5 main-field">
                                <span id="edit_return_supplier_bill_no" style="color: black;"></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="" class="col-sm-2 col-form-label fw-bold"><?= __("Pickup Address") ?></label>
                            
                            <div class="col-sm-5 d-flex main-field">
                                <span id="edit_return_pickup_address" style="color: black;"></span>
                            </div>                   
                        </div>

                        <div class="form-group row">
                            <label for="" class="col-sm-2 col-form-label fw-bold"><?= __("Return Status") ?></label>
                            
                            <div class="col-sm-5 d-flex main-field">
                                <span id="edit_order_return_status_text" style="display:none;color: black;"></span>
                                <?php echo $this->Form->control('return_status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'edit_order_return_status',
                                    'options' => [
                                        __('Pending') => __('Pending'),
                                        __('Completed') => __('Completed')
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>                   
                        </div>

                        <div class="form-group row">
                            <label for="" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?></label>
                            
                            <div class="col-sm-5 d-flex main-field">
                                <!-- <span id="edit_return_status" style="color: black;"></span> -->
                                <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'edit_return_status',
                                    'options' => [
                                        __('A') => __('Active'),
                                        __('I') => __('Inactive')
                                    ],
                                    'label' => false
                                ]); ?>
                            </div>                   
                        </div>
                        
                        <div class="edit_return_order_product_table">
                            
                        </div>

                        <!-- <p class="m-b-20">< ?= __("Total Price:") ?> <span id="edit_total_price"> -->

                        <div class="form-group row">
                            <div class="col-sm-12">
                                <button style="background-color: #0d839b !important;color: white !important;float: right;position: relative;right: 30px;" type="submit" class="btn edit_return_order_btn admin_btn"><?= __('Save') ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>

</div>

<div id="Supplier_payment" class="tabcontent" style="display: none;">
    <section id="view-product-product-details" class="section-body">
        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
            <div class="card">
                <div class="row ">
                    <div class="col-xl-3 col-lg-6">
                      <div class="card l-bg-style1" style="border-radius: 13px;">
                        <div>
                          <div class="card-content">
                            <h5 class="card-title"><?= __('Credit Period of Supplier :') ?></h5>
                            <span><?= !empty($supplier->credit_period) ? h($supplier->credit_period) : 'N/A'; ?></span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-xl-3 col-lg-6">
                      <div class="card l-bg-style1" style="border-radius: 13px;">
                        <div>
                          <div class="card-content">
                            <h5 class="card-title"><?= __('Total Payments Done :') ?></h5>
                            <span id="totalPaymentsDone"><?= !empty($totalPaymentsDoneFormatted) ? h($totalPaymentsDoneFormatted) : 0.00; ?></span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-xl-3 col-lg-6">
                      <div class="card l-bg-style1" style="border-radius: 13px;">
                        <div>
                          <div class="card-content">
                            <h5 class="card-title"><?= __('Total Payments Pending :') ?></h5>
                            <span id="totalPendingPayments"><?= !empty($totalPendingPaymentsFormatted) ? h($totalPendingPaymentsFormatted) : 0.00; ?></span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-xl-3 col-lg-6">
                      <div class="card l-bg-style1" style="border-radius: 13px;">
                        <div>
                          <div class="card-content">
                            <h5 class="card-title"><?= __('Total Payments Due :') ?></h5>
                            <span id="totalPendingDue"><?= !empty($totalPendingDueFormatted) ? h($totalPendingDueFormatted) : 0.00; ?></span>
                          </div>
                        </div>
                      </div>
                    </div>
                </div>

                <div class="card-header align-items-center">
                    <h4 style="color: black !important;"><?= __('Supplier Payment') ?></h4>
                    <div class="card-header-form">
                        <form>
                            <div class="input-group" style="margin-left:17px;">
                                <input type="text" class="form-control search-control" placeholder="Search" id="customSupplierPaymentSearchBox">
                                <div class="input-group-btn">
                                    <button class="btn admin_btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <?php if ($canAdd): ?>
                                    <a class="btn view-add-supplier-payment-modal btn-primary admin_btn m-r-15">
                                        <i class="fas fa-plus"></i>
                                        <?= __('Add Supplier Payment') ?>
                                    </a>
                                <?php endif; ?>
                                <button
                                    class="btn admin_btn btn-primary supplier-payments-menu-toggle fw-bold"
                                    type="button">
                                    <i
                                        class="fas fa-filter"                 
                                    ></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div id="supplier-payments-filter-body-container" style="display: none;">
                    <div class="" style="margin-top: 0px !important;margin-right: 0px !important;">
                        <form method="get" accept-charset="utf-8" class="form-inline filter-rating attribute"
                            id="filter-search" action="">
                            <div class="d-flex card-body">
                                <div class="d-flex align-items-center m-l-20"
                                >
                                    <label class="m-r-5" for="payment_status" style="width: 170px;"><?= __('Payment Status:') ?></label>
                                    <select name="payment_status" id="filterSupplierPaymentStatus" class="form-control form-select">
                                        <option value=""><?= __("Filter By Payment Status") ?></option>
                                        <option value="Paid"><?= __("Paid") ?>
                                        </option>
                                        <option value="Partially Paid"><?= __("Partially Paid") ?>
                                        </option>
                                        <option value="Pending"><?= __("Pending") ?>
                                        </option>
                                    </select>
                                </div>
                                <div class="d-flex">
                                    <button type="button" style="margin-right: 0px !important;" class="m-l-20 admin_btn btn supplier_payment_filter">
                                        <i class="fa fa-filter" aria-hidden="true"></i>
                                    </button>
                                    <button type="reset" class="m-l-10 admin_btn btn reset_supplier_payment_filter"><i class="fas fa-redo-alt"></i></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body" style="padding-top: 0px;">
                    <div class="table-responsive">
                        <div id="table-attributes_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                            <table class="table table-striped dataTable no-footer" id="supplierPaymentTable" role="grid" aria-describedby="table-attributes_info">
                                <thead>
                                    <tr>
                                        <th><?= __('Supplier') ?></th>
                                        <th><?= __('Bill No.') ?></th>
                                        <th><?= __('Payment Date') ?></th>
                                        <th><?= __('Amount') ?></th>
                                        <th><?= __('Due Date') ?></th>
                                        <th id="supplierPaymentStatus"><?= __('Payment Status') ?></th>
                                        <th class="actions"><?= __('Actions') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($supplier_payment as $payment): ?>
                                    <tr>
                                        <td><?php echo !empty($payment->supplier->name) ? h($payment->supplier->name) : 'N/A'; ?></td>

                                        <td><?php echo !empty($payment->supplier_purchase_order->bill_no) ? h($payment->supplier_purchase_order->bill_no) : 'N/A'; ?></td>

                                        <td><?php echo !empty($payment->payment_date) ? $payment->payment_date->format($dateFormat . ' ' . $timeFormat) : 'N/A'; ?></td>

                                        <td><?php echo !empty($payment->amount) ? h(number_format($payment->amount, 0, '', $thousandSeparator)).' '.$currencySymbol : 'N/A'; ?></td>


                                        <!-- <td>< ?php 

                                            $date = $payment->supplier_purchase_order->payment_due_date->format($dateFormat . ' ' . $timeFormat);

                                                echo !empty($date) ? date('Y-m-d', strtotime($date. ' + '.$supplier->credit_period.' days')) : 'N/A';
                                            ?>
                                        </td> -->

                                        <td>
                                        <?php 
                                            if (!empty($payment->supplier_purchase_order) && !empty($payment->supplier_purchase_order->payment_due_date)) {
                                                $dueDate = $payment->supplier_purchase_order->payment_due_date;

                                                if ($dueDate instanceof \Cake\I18n\FrozenTime) {
                                                    $formattedDate = $dueDate->format($dateFormat . ' ' . $timeFormat);
                                                    $adjustedDate = date('Y-m-d', strtotime($formattedDate . ' + ' . ($supplier->credit_period ?? 0) . ' days'));
                                                    echo $adjustedDate;
                                                } else {
                                                    echo 'N/A';
                                                }
                                            } else {
                                                echo 'N/A';
                                            }
                                        ?>
                                        </td>

                                        <td>
                                            <?php
                                                $paymentStatusMap = [
                                                    __('Paid') => ['label' => __('Paid'), 'class' => 'col-green'],
                                                    __('Partially Paid') => ['label' => __('Partially Paid'), 'class' => 'col-orange'],
                                                    __('Pending') => ['label' => __('Pending'), 'class' => 'col-blue']
                                                ];

                                                $payment_status = $paymentStatusMap[$payment->supplier_purchase_order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                ?>
                                                <div class="badge-outline <?= $payment_status['class'] ?>">
                                                    <?= h($payment_status['label']) ?>
                                                </div>
                                        </td>
                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a onclick="openViewSupplierPaymentModal(<?= $payment->id ?>)" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>    
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            </div>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
    </section>

    <div id="add-supplier-payment-modal" style="margin-top: 50px;" class="modal" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("Add Supplier Payment") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container">
                    <form id="add_supplier_payment_form" action="<?= $this->Url->build(['controller' => 'SupplierPayment', 'action' => 'add']) ?>" method="post" enctype="multipart/form-data">

                        <!-- <div class="form-group row">
                            <label for="bill_no" class="col-sm-2 col-form-label fw-bold">< ?= __("Bill No.") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                < ?php 
                                    echo $this->Form->control(
                                        'supplier_purchase_order_id',
                                        [
                                            'id' => 'payment-bill-select',
                                            'type' => 'select',
                                            'empty' => false, // Disable 'empty' as it's unnecessary with placeholder
                                            'label' => false,
                                            'div' => false,
                                            'options' => $pending_purchase_order_bills,
                                            'data-live-search' => "true",
                                            'class' => 'form-control select2',
                                            'multiple' => 'multiple',
                                            'style' => 'width: 100%;',
                                            'data-placeholder' => __('Select Bill No') // Use placeholder instead of title
                                        ]
                                    );
                                ?>
                                <span style="display:none;color: #dc3545;" id="payment_bill_select_error">< ?= __('Please select bill no.') ?></span>
                            </div>
                        </div> -->

                        <div class="form-group row">
                            <label for="supplier_name" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Name") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>">
                                <p style="color: black"><?= $supplier->name; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="supplier_name" class="col-sm-2 col-form-label fw-bold"><?= __("Collection Showroom") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php
                                echo $this->Form->control(
                                    'showroom_id',
                                    [
                                        'id' => 'payment-showrooms-select',
                                        'type' => 'select',
                                        'empty' => 'Select Showroom',
                                        'label' => false,
                                        'div' => false,
                                        'title' => __('Select Showroom'),
                                        'options' => $showrooms,
                                        'data-live-search' => "true",
                                        'class' => 'form-control select2'
                                    ]
                                );
                                ?>
                                <span style="display:none;color: #dc3545;" id="payment_showroom_error"><?= __('Please select showroom') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="payment_mode" class="col-sm-2 col-form-label fw-bold"><?= __("Mode of Payment") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <select name="payment_mode" id="payment-mode" title="Select Payment Mode" data-live-search="true" class="form-control">
                                    <option value=""><?= __('Select Payment Mode') ?></option>
                                    <option value="cheque"><?= __('Cheque') ?></option>
                                    <option value="cash"><?= __('Cash') ?></option>
                                </select>
                                <span style="display:none;color: #dc3545;" id="payment_mode_error"><?= __('Please select payment mode') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="amount" class="col-sm-2 col-form-label fw-bold"><?= __("Amount") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('amount', [
                                    'type' => 'number',
                                    'class' => 'form-control',
                                    'id' => 'amount',
                                    'placeholder' => __('Amount'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="payment_amount_error"><?= __('Please enter amount') ?></span>
                            </div>
                        </div>

                        <!-- Hidden container to store bill amounts dynamically -->
                        <div id="billAmountsContainer"></div>


                        <div class="form-group row">
                            <label for="payment_date" class="col-sm-2 col-form-label fw-bold"><?= __("Payment Date and Time") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('payment_date', [
                                    'type' => 'datetime',
                                    'class' => 'form-control',
                                    'id' => 'payment_date',
                                    'value' => date('Y-m-d'),
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="payment_date_error"><?= __('Please choose a payment date') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="receipt_number" class="col-sm-2 col-form-label fw-bold"><?= __("Receipt Number") ?> <sup class="text-danger font-11">*</sup></label>
                            <div class="col-sm-5 main-field">
                                <?php echo $this->Form->control('receipt_number', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'receipt_number',
                                    'label' => false
                                ]); ?>
                                <span style="display:none;color: #dc3545;" id="receipt_number_error"><?= __('Please enter a receipt') ?></span>
                            </div>
                        </div>

                        <div class="form-group row">
                                <label for="receipt" class="col-sm-2 col-form-label fw-bold"><?= __("Upload Receipt") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('receipt', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'id' => 'receipt',
                                        'accept' => 'image/*',
                                        'onchange' => "previewReceipt(event)",
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="receipt_error"><?= __('Please upload a receipt.') ?></span>
                                    <div class="receipt-preview m-t-20"></div>
                                </div>
                            </div>

                        <div id="cheque_div" style="display: none;">
                            <div class="form-group row">
                                <label for="cheque_no" class="col-sm-2 col-form-label fw-bold"><?= __("Cheque No") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('cheque_no', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'cheque_no',
                                        'placeholder' => __('Cheque No'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="cheque_no_error"><?= __('Please enter cheque number.') ?></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="cheque_date" class="col-sm-2 col-form-label fw-bold"><?= __("Date of Cheque") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('cheque_date', [
                                        'type' => 'date',
                                        'class' => 'form-control',
                                        'id' => 'cheque_date',
                                        'min' => date('Y-m-d'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="cheque_date_error"><?= __('Please choose cheque date.') ?></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="bank_name" class="col-sm-2 col-form-label fw-bold"><?= __("Bank Name") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('bank_name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'bank_name',
                                        'placeholder' => __('Bank Name'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="bank_name_error"><?= __('Please enter bank name.') ?></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="cheque_copy" class="col-sm-2 col-form-label fw-bold"><?= __("Upload Cheque Copy") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('cheque_copy', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'id' => 'cheque_copy',
                                        'accept' => 'image/*',
                                        'onchange' => "previewChequeCopy(event)",
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="cheque_copy_error"><?= __('Please upload a cheque copy.') ?></span>
                                    <div class="cheque-preview m-t-20"></div>
                                </div>
                            </div>
                        </div>

                        <div id="cash_div" style="display: none;">
                            <div class="form-group row">
                                <label for="payee_name" class="col-sm-2 col-form-label fw-bold"><?= __("Payee Name") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo $this->Form->control('payee_name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'payee_name',
                                        'placeholder' => __('Payee Name'),
                                        'label' => false
                                    ]); ?>
                                    <span style="display:none;color: #dc3545;" id="payee_name_error"><?= __('Please enter payee name.') ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-12">
                                <button style="background-color: #0d839b !important;color: white !important;float: right;position: relative;right: 30px;" type="submit" class="btn add_supplier_payment_btn admin_btn"><?= __('Save') ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>  
    </div>

    <div id="view-supplier-payment-modal" style="margin-top: 50px;" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" style="max-width: 900px !important;" role="document">
        <div class="modal-content m-l-70">
          <div class="modal-header">
            <div class="container">
                <h5 class="modal-title"><?= __("View Supplier Payment") ?></h5>
            </div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="modal-body-content">
                <div class="container view_supplier_payment_container">
                    
                </div>
            </div>
        </div>
      </div>  
    </div>

</div>

<!-- Modal HTML -->
<div id="orderModal" style="margin-top: 100px;" class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" style="max-width: 900px !important;" role="document">
    <div class="modal-content m-l-70">
      <div class="modal-header">
        <div class="container">
            <h5 class="modal-title"><?= __("Review Request") ?></h5>
        </div>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="modal-body-content">
            <div class="container">
            <!-- Boolean Display -->
            <div class="row">
                <div class="col-md-4">
                    <strong><?= __("Bill No:") ?></strong> <span id="verify_bill_no"></span>
                </div>
                <div class="col-md-4">
                    <strong><?= __("Supplier Bill No:") ?></strong> <span id="supp_bill_no"></span>
                </div>
                <div class="col-md-4">
                    <strong><?= __("Delivery Address:") ?></strong> <span id="delivery_address"></span>
                </div>
            </div>

            <!-- Table Display -->
            <div class="row">
                <form id="product_approve_form">
                    <input type="hidden" name="purchase_order_id" id="purchase_order_id"> 
                    <input type="hidden" name="supplier_id" value="<?= $supplier->id ?>"> 
                    <div class="col-md-12" id="product_table">
                    </div>
                </form>
            </div>

        </div>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>

    function openTab(evt, tabName) {
      var i, tabcontent, tablinks;
      tabcontent = document.getElementsByClassName("tabcontent");
      for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
      }
      tablinks = document.getElementsByClassName("tablinks");
      for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active_tab", "");
      }
      document.getElementById(tabName).style.display = "block";
      evt.currentTarget.className += " active_tab";
    }

    var paginationCount = <?= json_encode($paginationCount) ?>;
    //SUPPLIER PRODUCT
    document.addEventListener('DOMContentLoaded', function () {
        const filterButton = document.querySelector('.btn.supplier-product-menu-toggle');
        const filterBodyContainer = document.getElementById('supplier-product-filter-body-container');

        filterButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (filterBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                filterBodyContainer.classList.remove('showing');
                filterBodyContainer.classList.add('hiding');
                filterBodyContainer.addEventListener('animationend', function () {
                    filterBodyContainer.classList.remove('hiding');
                    filterBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                filterBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                filterBodyContainer.classList.add('showing');
            }
        });
    });

    var supplierProductTable = $("#supplierProductTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": [
            { "data": "id" },
            { "data": "supplier_name" },
            { "data": "sku_id" },
            { "data": "product_name" },
            { "data": "product_variant_name" },
            { "data": "supplier_price" },
            { "data": "product_image" },
            { "data": "status" },
            {
                "data": "actions",
                "render": function (data, type, row) {
                    return data;
                }
            }
        ]
    });

    $('#customSupplierProductSearchBox').on('keyup', function () {
        supplierProductTable.search(this.value).draw();
    });

    $('.supplier_product_filter').on('click', function () {

        var status = $("#filterSupplierProductStatus option:selected").val();
        var filterStatusValue = status === 'A' ? 'Active' : status === 'I' ? 'Inactive' : '';

        if (status) {
            var statusColumn = supplierProductTable.column('#supplierProductStatus');
            statusColumn.search(filterStatusValue, true, false, false).draw();
        }
    });

    $('.reset_supplier_product_filter').on('click', function () {

        supplierProductTable.search('').columns().search('').draw();

    });

    $('.download_csv_format').on('click', function () {

        $.ajax({

            url: "<?= $this->Url->build(['controller' => 'SupplierProducts', 'action' => 'exportToCsv']) ?>",
            type: 'GET',
            xhrFields: {
                responseType: 'blob'  // This is important for downloading files
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                var a = document.createElement('a');
                var url = window.URL.createObjectURL(data);
                a.href = url;
                a.download = 'sample_supplier_product_format.csv';
                document.body.append(a);
                a.click();
                window.URL.revokeObjectURL(url);
            },
            error: function(xhr, status, error) {

                swal('<?= __('Failed') ?>', '<?= __('Failed to download supplier products csv format. Please try again.') ?>', 'error');
            }
        });

    });

    $('#upload_supplier_product_form').on('submit', function (e) {
        e.preventDefault();

        var fileInput = document.getElementById('csv_file');
        var filePath = fileInput.value;
        var file = fileInput.files[0];

        if (fileInput.files.length === 0) {
            swal('<?= __('Error') ?>', '<?= __('Please select a CSV file') ?>', 'error');
            return;
        }

        // Validate file extension
        var allowedExtensions = /(\.csv)$/i;
        if (!allowedExtensions.exec(filePath)) {
            swal('<?= __('Error') ?>', '<?= __('Please upload a valid CSV file.') ?>', 'error');
            fileInput.value = '';
            return;
        }

        var reader = new FileReader();
        reader.onload = function (e) {
            var csvData = e.target.result;
            const data_rows = csvData.split('\n').map(row => row.split(','));
            var rows = csvData.split("\n");
            var headerRow = rows[0].split(',');

            const cleanHeaderRow = headerRow.map(header => header.trim());

            // Your validation logic
            const expectedHeaders = ["Product Name", "Product SKU", "Supplier Price"];

            if (JSON.stringify(cleanHeaderRow) !== JSON.stringify(expectedHeaders)) {
                swal('<?= __('Error') ?>', 'Invalid CSV format. Expected columns: ' + expectedHeaders.join(', '), 'error');
                fileInput.value = '';
                return;
            }

            // Validate each row based on Product Name
            let invalidProducts = []; // Array to hold invalid product names
            for (let i = 1; i < data_rows.length; i++) { // Start from 1 to skip header
                const row = data_rows[i].map(cell => cell.trim());
                if (row.length === 3) {
                    const productName = row[0];
                    const supplierPrice = row[2];

                    // Validate Supplier Price is numeric
                    if (isNaN(supplierPrice) || parseFloat(supplierPrice) <= 0) {
                        invalidProducts.push(productName); // Add to invalid list
                    }
                }
            }

            // If there are invalid products, show an error message
            if (invalidProducts.length > 0) {
                swal('<?= __('Error') ?>', 'Invalid Supplier Price for products: ' + invalidProducts.join(', '), 'error');
                fileInput.value = '';
                return; // Stop if invalid
            } 

            var formData = new FormData();
            formData.append('csv_file', fileInput.files[0]);
            formData.append('supplier_id', <?= $supplier->id ?>);

            $('.upload_supplier_product_btn').attr('disabled','disabled');

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierProducts', 'action' => 'uploadSupplierProducts']) ?>",
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function (response) {

                    // if(response.data.status == '<?= __('failed') ?>')
                    // if (response.errors)
                    // {
                    //     $('.upload_supplier_product_btn').removeAttr('disabled');

                    //     var errorList = "<ul>";
                    //     response.errors.forEach(function (error) {
                    //         errorList += "<li>" + error + "</li>";
                    //     });
                    //     errorList += "</ul>";

                    //     const wrapper = document.createElement('div');
                    //     wrapper.innerHTML = errorList;

                    //     swal({
                    //         title: "<?= __("Error") ?>", 
                    //         content: wrapper,
                    //         icon: "error",
                    //         confirmButtonText: "<?= __("OK") ?>", 
                    //         allowOutsideClick: "true" 
                    //     });
                    //     // swal('<?= __('Error') ?>', response.data.message, 'error');
                    // }
                    // else
                    // {
                    //     $('.upload_supplier_product_btn').removeAttr('disabled');

                    //     $('#upload-supplier-product-modal').modal('hide');

                    //     supplierProductTable.clear().rows.add(response.data).draw();

                    //     swal('<?= __('Success') ?>', '<?= __('The CSV file has been uploaded.') ?>', 'success');
                    // }

                    if (response.errors.length > 0) {
                        $('.upload_supplier_product_btn').removeAttr('disabled');

                        var errorList = "<ul>";
                        response.errors.forEach(function (error) {
                            errorList += "<li>" + error + "</li>";
                        });
                        errorList += "</ul>";

                        const wrapper = document.createElement('div');
                        wrapper.innerHTML = errorList;

                        swal({
                            title: "<?= __("Error") ?>",
                            content: wrapper,
                            icon: "error",
                            confirmButtonText: "<?= __("OK") ?>",
                            allowOutsideClick: "true"
                        });
                    }

                    // Always update the table with latest supplier products
                    supplierProductTable.clear().rows.add(response.data).draw();
                    $('.upload_supplier_product_btn').removeAttr('disabled');
                    $('#upload-supplier-product-modal').modal('hide');

                    if (response.errors.length === 0) {
                        swal('<?= __('Success') ?>', '<?= __('The CSV file has been uploaded.') ?>', 'success');
                    }

                },
                error: function (xhr, status, error) {
                    // console.error('Error uploading CSV:', error);
                    $('.upload_supplier_product_btn').removeAttr('disabled');

                    swal('<?= __('Error') ?>', '<?= __('An error occurred while uploading the CSV file') ?>', 'error');
                }
            });
        };

        reader.readAsText(file);    

    });

    $('#resetButton').click(function () {
        // Reset the form
        $('#add_supplier_product_form')[0].reset();

        // Reset Select2 dropdowns
        $('#supplier_product_id').val(null).trigger('change');
        $('#supplier_product_variant_id').val(null).trigger('change');
    });

    $('.view-upload-supplier-product-modal').on('click', function() {

        $('#upload_supplier_product_form')[0].reset();
        $('#upload-supplier-product-modal').modal('show');
    });

    $('.view-add-supplier-product-modal').on('click', function() {

        $('#add_supplier_product_form')[0].reset();
        $('#add-supplier-product-modal').modal('show');
    });

    $('#add_supplier_product_form').on('reset', function(e) {
        e.preventDefault(); // Prevent default reset behavior

        // Reset the form (this resets all native elements)
        this.reset();

        // Reset Select2 programmatically
        $('#product-id').val(null).trigger('change');
        $('#supplier-price').val('');
    });

    $('#supplier_product_id').on('change', function () {
        var productId = $(this).val();
        var variantDropdown = $('#supplier_product_variant_id');

        // Clear previous options
        variantDropdown.html('<option value=""><?= __('Loading...') ?></option>');

        if (productId) {
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Suppliers', 'action' => 'getVariants']) ?>/' + productId,
                type: 'GET',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                dataType: 'json',
                success: function (response) {

                    if (response.variants && Object.keys(response.variants).length > 0)
                    {
                        // Clear the existing options in the variant dropdown
                        variantDropdown.empty();

                        const firstVariantKey = Object.keys(response.variants)[0];
                        const firstSKU = response.variants[firstVariantKey].sku;

                        $.each(response.variants, function(id, variant) {
                            variantDropdown.append(
                                $('<option>', {
                                    value: id,
                                    text: variant.name,
                                    'data-sku': variant.sku // Store the SKU in a data attribute
                                })
                            );
                        });

                    }
                    else
                    {
                        variantDropdown.empty();
                        variantDropdown.append('<option value="">No variants available</option>');
                    }
                },
                error: function () {
                    variantDropdown.html('<option value=""><?= __('Error loading variants') ?></option>');
                }
            });
        } else {
            variantDropdown.html('<option value=""><?= __('Select Variant') ?></option>');
        }
    });

    function saveSupplierProducts()
    {
        var form_data = $('#add_supplier_product_form').serialize();
        var error = 0;


        // Reset previous errors
        $('#supplier-id').removeClass('is-invalid');
        $('#supplier_product_id').removeClass('is-invalid');
        $('#supplier-price').removeClass('is-invalid');

        $('#supplier_error').hide();
        $('#product_error').hide();
        $('#supplier_price_error').hide();

        if($("#supplier-id option:selected").val() == '' || $("#supplier-id option:selected").val() == null)
        {
            error = 1;
            $('#supplier_error').show();
            $('#supplier-id').addClass('is-invalid');
        }

        // if($("#supplier_product_id option:selected").val() == '' || $("#supplier_product_id option:selected").val() == null)
        // {
        //     error = 1;
        //     $('#product_error').show();
        //     $('#supplier_product_id').addClass('is-invalid');
        // }

        if ($("#supplier_product_id").val() === '' || $("#supplier_product_id").val() == null) {
            error = 1;
            $('#product_error').show();

            // Add red border to select2 visible container
            $('#supplier_product_id').next('.select2-container').find('.select2-selection').addClass('is-invalid');
        } else {
            $('#product_error').hide();
            $('#supplier_product_id').next('.select2-container').find('.select2-selection').removeClass('is-invalid');
        }

        if($("#supplier-price").val() == '' || $("#supplier-price").val() == null)
        {
            error = 1;
            $('#supplier_price_error').show();
            $('#supplier-price').addClass('is-invalid');
        }

        if(error == 0)
        {   
            $('#supplier_error').hide();
            $('#product_error').hide();
            $('#supplier_price_error').hide();
            $('.add_supplier_product_btn').attr('disabled','disabled');
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierProducts', 'action' => 'add']) ?>",
                type: 'POST',
                data: form_data,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {

                    if(response.status == '<?= __('exist_error') ?>')
                    {
                        $('.add_supplier_product_btn').removeAttr('disabled');

                        swal('<?= __('Error') ?>', response.message, 'error');
                    }
                    else
                    {
                        $('#add_supplier_product_form')[0].reset();

                        $("#supplier_product_id").val(null).trigger('change');
                        $("#supplier_product_variant_id").val(null).trigger('change');

                        $('.add_supplier_product_btn').removeAttr('disabled');

                        $('#add-supplier-product-modal').modal('hide');

                        supplierProductTable.clear().rows.add(response.data).draw();

                        swal('<?= __('Success') ?>', '<?= __('The supplier products has been saved.') ?>', 'success');
                    }
                },
                error: function(xhr, status, error) {

                    $('.add_supplier_product_btn').removeAttr('disabled');

                    swal('<?= __('Failed') ?>', '<?= __('Failed to save supplier products. Please try again.') ?>', 'error');
                }
            });
        }

    }

    function openEditSupplierProductModal(supplier_product_id)
    {
        $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierProducts', 'action' => 'getSupplierProductById']) ?>"+"/"+supplier_product_id,
                type: 'GET',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {

                    if (response.status === 'success') {

                        $('.edit_supplier_product_footer').html("");

                        $('#edit-supplier-product-modal').modal('show');


                        // $(".edit_supplier_product_id").val(response.supplier_product.id);
                        $(".edit_supplier_id").val(response.supplier_product.supplier_id).change();
                        $("#supplier_product_id_value").val(response.supplier_product.product_id);
                        $(".edit_product_id").val(response.supplier_product.product_id).change();

                        $("#edit_supplier_product_variant_id").val(response.supplier_product.product_variant_id);
                        if (response.supplier_product.product_variant && response.supplier_product.product_variant.variant_name) {
                            $(".supplier_product_variant_id_text").val(response.supplier_product.product_variant.variant_name);
                        } else {
                            $(".supplier_product_variant_id_text").val('N/A');
                        }

                        $(".edit_supplier_price").val(response.supplier_product.supplier_price).change();
                        $(".edit_supplier_product_status").val(response.supplier_product.status).change();
                        var html = `<button style="background-color: #0d839b !important;color: white !important" type="button" onclick="editSupplierProducts(${response.supplier_product.id})" class="btn edit_supplier_product_btn admin_btn"><?= __('Save') ?></button>`;

                        $('.edit_supplier_product_footer').append(html);

                    } else {

                        swal('<?= __('Failed') ?>', '<?= __('Failed to fetch supplier products. Please try again.') ?>', 'error');
                    }
                },
                error: function(xhr, status, error) {

                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch supplier products. Please try again.') ?>', 'error');
                }
            });
    }

    function editSupplierProducts(id)
    {
        var form_data = $('#edit_supplier_product_form').serialize();
        var error = 0;

        if($(".edit_supplier_id option:selected").val() == '' || $(".edit_supplier_id option:selected").val() == null)
        {
            error = 1;
            $('#edit_supplier_error').show();
        }
        if($(".edit_product_id option:selected").val() == '' || $(".edit_product_id option:selected").val() == null)
        {
            error = 1;
            $('#edit_product_error').show();
        }
        if($(".edit_supplier_price").val() == '' || $(".edit_supplier_price").val() == null)
        {
            error = 1;
            $('.edit_supplier_price').addClass('is-invalid');
            $('#edit_supplier_price_error').show();
        }

        if(error == 0)
        {   
            $('#edit_supplier_error').hide();
            $('#edit_product_error').hide();
            $('#edit_supplier_price_error').hide();
            $('.edit_supplier_product_btn').attr('disabled','disabled');
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierProducts', 'action' => 'edit']) ?>"+"/"+id,
                type: 'POST',
                data: form_data,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {

                    if(response.status == '<?= __('exist_error') ?>')
                    {
                        $('.edit_supplier_product_btn').removeAttr('disabled');
                        swal('<?= __('Error') ?>', response.message, 'error');
                    }
                    else
                    {
                        $('.edit_supplier_product_btn').removeAttr('disabled');

                        $('#edit-supplier-product-modal').modal('hide');

                        supplierProductTable.clear().rows.add(response.data).draw();

                        swal('<?= __('Success') ?>', '<?= __('The supplier products has been saved.') ?>', 'success');
                    }
                },
                error: function(xhr, status, error) {

                    $('.edit_supplier_product_btn').removeAttr('disabled');

                    swal('<?= __('Failed') ?>', '<?= __('Failed to save supplier products. Please try again.') ?>', 'error');
                }
            });
        }

    }

    //PURCHASE ORDER REQUEST
    var purchaseOrderTable = $("#purchaseOrderTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": [
            { "data": "order_date" },
            { "data": "bill_no" },
            { "data": "supp_bill_no" },
            { "data": "status" },
            { "data": "payment_status" },
            { "data": "delivery_status" },
            {
                "data": "actions",
                "render": function (data, type, row) {
                    return data;
                }
            }
        ]
    });

    $('#customPurchaseOrderSearchBox').on('keyup', function () {
        purchaseOrderTable.search(this.value).draw();
    });

    // function generateAlphanumeric() {
    //     const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    //     let result = '';
    //     for (let i = 0; i < 8; i++) {
    //         result += characters.charAt(Math.floor(Math.random() * characters.length));
    //     }
    //     return result;
    // }

    $('.view-add-purchase-order-modal').on('click', function() {

        // var sleep = 0;
        $('.ibtnDel').each(function(){
            var likeElement = $(this);
            // setTimeout(function() {
            likeElement.trigger('click');
            // }, sleep);
        });

        $('#add_purchase_order_form')[0].reset();
        // Example of setting the value when loading the form
        // document.getElementById('bill_no').value = generateAlphanumeric();
        $('#add-purchase-order-modal').modal('show');
    });

    function openVerifyModal(orderPurchaseId)
    {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierPurchaseOrders', 'action' => 'order']) ?>"+"/"+orderPurchaseId,
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                if(data.status == 'success')
                {   
                    $('#product_table').html('');
                    $('.modal-footer').html('');

                    $('#verify_bill_no').text(data.order_request.bill_no);
                    $('#supp_bill_no').text(data.order_request.supplier_bill_no);
                    $('#purchase_order_id').val(data.order_request.id);
                    if(data.order_request.deliver_to == '<?= __('Showroom') ?>')
                    {
                        $('#delivery_address').text(data.order_request.showroom.name+' - '+data.order_request.showroom.address);
                    }
                    else if(data.order_request.deliver_to == '<?= __('Warehouse') ?>')
                    {
                        $('#delivery_address').text(data.order_request.warehouse.name+' - '+data.order_request.warehouse.location);
                    }

                    var html = `<table class='table table-bordered m-t-15'>
                                    <thead>
                                        <tr>
                                        <th><?= __('Image') ?></th>
                                        <th><?= __('Product') ?></th>
                                        <th><?= __('Product Variant') ?></th>
                                        <th><?= __('Product Attribute') ?></th>
                                        <th><?= __('SKU') ?></th>
                                        <th><?= __('PO Quantity') ?></th>
                                        <th style='width: 150px;'><?= __('Approve Quantity') ?></th>
                                        <th><?= __('Rejected Reason') ?></th>
                                        </tr>
                                    </thead>
                                <tbody>`;

                    for(var i=0; i < data.order_products.length; i++)
                    {
                        // Construct variants
                        var variantHtml = '';
                        if (data.order_products[i].product.variant_name !== undefined) {

                            variantHtml = data.order_products[i].product.variant_name;

                        } else {
                            variantHtml = 'N/A';
                        }

                        // Construct attributes
                        var attributesHtml = '';
                        if (data.order_products[i].product_attribute_id !== null) {

                            attributesHtml = data.order_products[i].product.attributes.attribute.name + ': ' + data.order_products[i].product.attributes.attribute_value.value;

                        } else {
                            attributesHtml = 'N/A';
                        }

                        html += "<tr class='item-row'>";

                        html += "<td><img src='"+data.order_products[i].product.product_image+"' style='height: 40px;width: auto;'></img></td>";

                        html += "<td>"+data.order_products[i].product.name+"</td>";

                        html += "<td>"+variantHtml+"</td>";
                        
                        html += "<td>"+attributesHtml+"</td>";

                        html += "<td>"+data.order_products[i].sku+"</td>";

                        html += "<td><input type='hidden' class='quantity' value='"+data.order_products[i].quantity+"'>"+data.order_products[i].quantity+"</td>";

                        html += "<td><input type='hidden' name='order_product_id[]' value='"+data.order_products[i].id+"'><input type='number' data-quantity='"+data.order_products[i].quantity+"' class='form-control product_quantity approved_quantity' id='product_quantity' name='quantity[]' value='"+data.order_products[i].approved_quantity+"'><span class='error-message approved_quantity-error' style='color: #dc3545;'></span></td>";

                        // if(data.order_products[i].approved_quantity)
                        // {
                        //     html += "<td><input type='hidden' name='order_product_id[]' value='"+data.order_products[i].id+"'><input type='number' data-quantity='"+data.order_products[i].quantity+"' class='form-control product_quantity approved_quantity' id='product_quantity' name='quantity[]' value='"+data.order_products[i].approved_quantity+"'><span class='error-message approved_quantity-error' style='color: red;'></span></td>";
                        // }
                        // else
                        // {
                        //     html += "<td><input type='hidden' name='order_product_id[]' value='"+data.order_products[i].id+"'><input type='number' class='form-control product_quantity approved_quantity' data-quantity='"+data.order_products[i].quantity+"' id='product_quantity' name='quantity[]' value='"+data.order_products[i].quantity+"'><span class='error-message approved_quantity-error' style='color: red;'></span></td>";
                        // }

                        // if(data.order_products[i].approve_status !== '<?= __('Pending') ?>')
                        // {
                        //     var isChecked = "";
                        //     if(data.order_products[i].approve_status == '<?= __('Approved') ?>')
                        //     {
                        //         isChecked = "<?= __('checked') ?>";
                        //     }

                        //     html += "<td><input type='hidden' class='approve_status_"+data.order_products[i].id+"' data-id='"+data.order_products[i].id+"' value='"+data.order_products[i].approve_status+"' name='approve_status[]'><label class='switch'><input id='toggleSwitch' onchange='getToggleValue(this, "+data.order_products[i].id+")' type='checkbox' "+isChecked+"><span class='slider round'></span></label></td>";
                        // }
                        // else
                        // {
                        //     html += "<td><input type='hidden' class='approve_status_"+data.order_products[i].id+"' data-id='"+data.order_products[i].id+"' value='Rejected' name='approve_status[]'><label class='switch'><input id='toggleSwitch' onchange='getToggleValue(this, "+data.order_products[i].id+")' type='checkbox'><span class='slider round'></span></label></td>";
                        // }

                        if(data.order_products[i].comment == null || data.order_products[i].comment == "")
                        {
                            html += `<td>
                                        <textarea name='comment[]' class='rejected_reason_${data.order_products[i].id} rejected_reason'></textarea>
                                        <span class="rejected_reason_error" style="color: #dc3545;display: none;" id="rejected_reason_error_${data.order_products[i].id}"><?= __('Enter rejected reason') ?></span>
                                        <span class="error-message rejected_reason-error" style="color: #dc3545;"></span>
                                    </td>`;
                        }
                        else
                        {
                            html += "<td><textarea value='"+data.order_products[i].comment+"' name='comment[]' class='rejected_reason'>"+data.order_products[i].comment+"</textarea><span class='error-message rejected_reason-error' style='color: #dc3545;'></span></td>";
                        }

                        html += "</tr>";
                    }
                        
                    html += "</tbody></table>";

                    var footer_html = `
                            <div class="input-group-btn">
                                <button type="button" style="background-color: #0d839b !important;color: white !important" onclick="myFunction()" class="btn product_approve_btn">Save</button>
                            </div>`;

                    $('.modal-footer').append(footer_html);
                    $('#product_table').append(html);
                    $('#orderModal').modal('show');
                }

            },
            error: function() {
                swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
            }
        });
    }

    function getToggleValue(toggle, purchase_order_id) {

        if (toggle.checked) {
            $('.approve_status_'+purchase_order_id).val('Approved');
            $('#rejected_reason_error_'+purchase_order_id).hide();
        } else {
            $('.approve_status_'+purchase_order_id).val('Rejected');
            $('#rejected_reason_error_'+purchase_order_id).show();
        }
    }

    $(document).on("change", ".product_option", function (event) {

        if($('option:selected', this).val() !== 0)
        {
            var productId = $('option:selected', this).val();

            if(productId == 0)
            {
                $(this).closest("tr").find('#product_sku').text('');
                $(this).closest("tr").find('#product_supplier').text('');
                $(this).closest("tr").find('#product_image').attr('src','');
                return false;
            }

            $('#po-attribute-dropdown').empty();
            $('#po-attribute-dropdown').append('<option value="">' + 'Select Attribute' + '</option>');
            $('#po_variant_id').empty();
            $('#po_variant_id').append('<option value="">' + 'Select Variant' + '</option>');
            
            $(this).closest("tr").find('#product_sku').text('');
            var product_sku = $('option:selected', this).attr('data-product-sku');
            var product_supplier = $('option:selected', this).attr('data-product-supplier');
            var product_image = $('option:selected', this).attr('data-product-image');
            $(this).closest("tr").find('#product_supplier').text(product_supplier);
            $(this).closest("tr").find('#product_image').attr('src',product_image);

            if (productId !== 0) {
                
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Suppliers', 'action' => 'getVariants']) ?>/' + productId,
                    type: 'GET',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        
                        if (response.variants && Object.keys(response.variants).length > 0)
                        {
                            var variantDropdown = $('#po_variant_id');

                            // Clear the existing options in the variant dropdown
                            variantDropdown.empty();

                            const firstVariantKey = Object.keys(response.variants)[0];
                            const firstSKU = response.variants[firstVariantKey].sku;

                            $('#po_variant_id').closest("tr").find('#product_sku').text(firstSKU);

                            $.each(response.variants, function(id, variant) {
                                variantDropdown.append(
                                    $('<option>', {
                                        value: id,
                                        text: variant.name,
                                        'data-sku': variant.sku // Store the SKU in a data attribute
                                    })
                                );
                            });

                        }
                        else
                        {
                            $('#po_variant_id').closest("tr").find('#product_sku').text(product_sku);
                            $('#po_variant_id').empty();
                            $('#po_variant_id').append('<option value="">No variants available</option>');
                        }

                        if (response.attributes.length > 0) {

                            $('#po-attribute-dropdown').empty(); // Clear existing options

                            response.attributes.forEach(function(attribute) {
                                $('#po-attribute-dropdown').append(
                                    $('<option>', {
                                        value: attribute.attribute_id,
                                        text: attribute.attribute_name + ': ' + attribute.attribute_value
                                    })
                                );
                            });

                        } else {
                            $('#po-attribute-dropdown').empty();
                            $('#po-attribute-dropdown').append('<option value="">No attributes available</option>');
                        }
                    },
                    error: function() {

                        swal('<?= __('Failed') ?>', '<?= __('Unable to load variants. Please try again.') ?>', 'error');
                    }
                });
            }
        }
        else
        {
            $(this).closest("tr").find('#product_sku').text('');
            $(this).closest("tr").find('#product_supplier').text('');
            $(this).closest("tr").find('#product_image').attr('src','');
        }

    });

    $('#po_variant_id').change(function() {
        var selectedSku = $(this).find('option:selected').data('sku');
        // console.log("Selected SKU: " + selectedSku);
        $(this).closest("tr").find('#product_sku').text(selectedSku);
    });

    $("table.add_product_table").on("click", ".updateQuest", function (event) {

      var error = 0;

      if($(this).closest("tr").find('#product_option').val() == 0)
      {  
          error = 1;
          $(this).closest("tr").find('#product_option_error').html('Select product');
      }
      if($(this).closest("tr").find('#product_quantity').val() == "")
      {
          error = 1;
          $(this).closest("tr").find('#product_quantity_error').html('Enter quantity');
      }
        
      if(error == 0)
      {

        $('#product_option_error').html('');
        $('#product_quantity_error').html('');

        var newRow = $("<tr>");
        var cols = "";
        var pc_options = $(".product_option > option").clone();  
        
    
        cols += '<td><input type="hidden" class="form-control" name="product_id[]" value="'+$(this).closest("tr").find('.product_option').find(':selected').val()+'" />';
        cols += '<select class="form-control product_option ctemp1"/></select></td>';

        cols += '<td><input type="hidden" class="form-control" name="product_variant_id[]" value="'+$(this).closest("tr").find('#po_variant_id').find(':selected').val()+'" />';
        cols += '<input type="text" class="form-control ctemp2" value="'+$(this).closest("tr").find('#po_variant_id').find(':selected').text()+'"/></td>';

        cols += '<td><input type="hidden" class="form-control" name="product_attribute_id[]" value="'+$(this).closest("tr").find('#po-attribute-dropdown').find(':selected').val()+'" />';
        cols += '<input type="text" class="form-control ctemp3" value="'+$(this).closest("tr").find('#po-attribute-dropdown').find(':selected').text()+'"/></td>';

        cols += '<td><input type="hidden" class="form-control" name="sku[]" value="'+$(this).closest("tr").find("#product_sku").text()+'"/>';
        cols += '<input type="text" class="form-control ttemp1" value="'+$(this).closest("tr").find("#product_sku").text()+'"/></td>';

        cols += '<td><input type="text" class="form-control ttemp2" value="'+$(this).closest("tr").find('.product_option').find(':selected').attr('data-product-supplier')+'"/></td>';

        cols += '<td><input type="hidden" class="form-control" name="quantity[]" value="'+$(this).closest("tr").find(".product_quantity").val()+'"/>';
        cols += '<input type="text" class="form-control ttemp4" value="'+$(this).closest("tr").find(".product_quantity").val()+'"/></td>';

        cols += '<td><img style="height: 40px;width: auto;" src="'+$(this).closest("tr").find('.product_option').find(':selected').attr('data-product-image')+'"></img></td>';


        cols += '<td><button type="button" class="ibtnDel btn admin_btn"><?= __("Delete") ?></button></td></tr>';
        newRow.append(cols);
        $("table.add_product_table").append(newRow);
        $(".ctemp1").append(pc_options); 
        $(".ctemp1").prop('disabled', 'true');

        $(".ctemp2").prop('disabled', 'true'); 
        $(".ctemp3").prop('disabled', 'true');

        $(".ttemp1").prop('disabled', 'true');
        $(".ttemp2").prop('disabled', 'true');
        $(".ttemp4").prop('disabled', 'true');
        
        $(".ctemp1 option[value='"+$(this).closest("tr").find(".product_option").find(':selected').val()+"']").attr('selected','selected');
        $(".product_option").removeClass('ctemp1');

        $(".ctemp2 option[value='"+$(this).closest("tr").find("#po_variant_id").find(':selected').val()+"']").attr('selected','selected');
        $("#po_variant_id").removeClass('ctemp2');

        $(".ctemp3 option[value='"+$(this).closest("tr").find("#po-attribute-dropdown").find(':selected').val()+"']").attr('selected','selected');
        $("#po-attribute-dropdown").removeClass('ctemp3');

        $('#po-attribute-dropdown').empty();
        $('#po-attribute-dropdown').append('<option value="">' + 'Select Attribute' + '</option>');
        $('#po_variant_id').empty();
        $('#po_variant_id').append('<option value="">' + 'Select Variant' + '</option>');

        $(this).closest("tr").find(".product_option").val(0).trigger('change');
        $(this).closest("tr").find(".product_quantity").val("");
        $(this).closest("tr").find('#product_sku').text('');
        $(this).closest("tr").find('#product_image').attr('src','');
      }

    });
    $("table.add_product_table").on("click", ".ibtnDel", function (event) {    
         $(this).closest("tr").remove(); 
    });

    // Initialize based on the currently checked radio button
    var selectedValue = $('input[name="deliver_to"]:checked').val();
    toggleSelectBox(selectedValue);

    // Handle radio button change event to toggle select boxes
    $('input[name="deliver_to"]').change(function() {
        var selectedValue = $('input[name="deliver_to"]:checked').val();
        toggleSelectBox(selectedValue);
    });

    // Function to toggle between showroom and warehouse dropdowns
    function toggleSelectBox(value) {        

        if (value === '<?= __('Showroom') ?>') {

            $('#showrooms-select-div').show();
            $('#showrooms-select').show().trigger('change');
            $('#showrooms-select').attr('name','id_deliver_to');

            $('#showroom_error').css('display','none');
            $('#warehouse_error').css('display','none');

            $('#warehouses-select').removeAttr('name');
            // $('#warehouses-select').hide();
            $('#warehouses-select-div').hide();

        } else if (value === '<?= __('Warehouse') ?>') {

            $('#showrooms-select-div').hide();
            // $('#showrooms-select').hide();
            $('#showrooms-select').removeAttr('name');

            $('#showroom_error').css('display','none');
            $('#warehouse_error').css('display','none');

            $('#warehouses-select-div').show();
            $('#warehouses-select').show().trigger('change');
            $('#warehouses-select').attr('name','id_deliver_to');
        }
    }

    function toggleSelectBoxEdit(toggle) {

        // alert(toggle.value);

        if (toggle.value === '<?= __('Showroom') ?>') {

            $('#showrooms-select-edit-div').show();
            $('#showrooms-select-edit').show().trigger('change');
            $('#showrooms-select-edit').attr('name','id_deliver_to');

            $('#edit_showroom_error').css('display','none');
            $('#edit_warehouse_error').css('display','none');

            $('#warehouses-select-edit').removeAttr('name');
            // $('#warehouses-select-edit').hide();
            $('#warehouses-select-edit-div').hide();
        } else if (toggle.value === '<?= __('Warehouse') ?>') {

            $('#showrooms-select-edit-div').hide();
            // $('#showrooms-select-edit').hide();  // Hide Showrooms select box
            $('#showrooms-select-edit').removeAttr('name');

            $('#edit_showroom_error').css('display','none');
            $('#edit_warehouse_error').css('display','none');

            $('#warehouses-select-edit-div').show();
            $('#warehouses-select-edit').show().trigger('change');
            $('#warehouses-select-edit').attr('name','id_deliver_to');
        }
    }

    function savePurchaseOrderRequest()
    {
        var form_data = $('#add_purchase_order_form').serialize();
        var error = 0;

        const tbody = document.querySelector('#add_product_table tbody');
        const numberOfRows = tbody.querySelectorAll('tr').length;
        // console.log(numberOfRows);

        // alert(numberOfRows);

        if($("#order_date").val() == '' || $("#order_date").val() == null)
        {
            error = 1;
            $('#order_date_error').show();
            $('#order_date').addClass('is-invalid');
        }
        if($("#bill_no").val() == '' || $("#bill_no").val() == null)
        {
            error = 1;
            $('#bill_no_error').show();
            $('#bill_no').addClass('is-invalid');
        }
        if($('input[name="deliver_to"]:checked').val() == '<?= __('Showroom') ?>')
        {
            if($("#showrooms-select option:selected").val() == '' || $("#showrooms-select option:selected").val() == null)
            {
                error = 1;
                $('#showroom_error').show();
                $('#showrooms-select').next('.select2-container').find('.select2-selection').addClass('is-invalid');
            }

        }
        else if($('input[name="deliver_to"]:checked').val() == '<?= __('Warehouse') ?>')
        {
            if($("#warehouses-select option:selected").val() == '' || $("#warehouses-select option:selected").val() == null)
            {
                error = 1;
                $('#warehouse_error').show();
                $('#warehouses-select').next('.select2-container').find('.select2-selection').addClass('is-invalid');
            }
        }
        if($("#required_delivery_date").val() == '' || $("#required_delivery_date").val() == null)
        {
            error = 1;
            $('#required_delivery_date_error').show();
            $('#required_delivery_date').addClass('is-invalid');
        }
        if($("#payment_due_date").val() == '' || $("#payment_due_date").val() == null)
        {
            error = 1;
            $('#payment_due_date_error').show();
            $('#payment_due_date').addClass('is-invalid');
        }

        if(numberOfRows <= 1)
        {
            swal('<?= __('Error') ?>', '<?= __('Please add product.') ?>', 'error');
            return false;
        }

        if(error == 0)
        {   
            $('#order_date_error').hide();
            $('#bill_no_error').hide();
            $('#showroom_error').hide();
            $('#warehouse_error').hide();
            $('#required_delivery_date_error').hide();
            $('#payment_due_date_error').hide();
            $('.add_purchase_order_btn').attr('disabled','disabled');
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierPurchaseOrders', 'action' => 'add']) ?>",
                type: 'POST',
                data: form_data,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {


                        // console.log(response);

                        // Get the select box
                        let $select = $('#bill-select');

                        // Clear the existing options
                        $select.empty();

                        // Optionally, add a default "Select" option
                        $select.append('<option value=""><?= __('Select Bill No') ?></option>');

                        // Loop through the object and append each option
                        $.each(response.purchase_order_bills, function(key, value) {
                            $select.append('<option value="' + key + '">' + value + '</option>');
                        });

                        // Get the select box
                        let $selectPayment = $('#payment-bill-select');

                        // Clear the existing options
                        $selectPayment.empty();

                        // Optionally, add a default "Select" option
                        $selectPayment.append('<option value=""><?= __('Select Bill No') ?></option>');

                        // Loop through the object and append each option
                        $.each(response.purchase_order_bills, function(key, value) {
                            $selectPayment.append('<option value="' + key + '">' + value + '</option>');
                        });

                        $('.add_purchase_order_btn').removeAttr('disabled');

                        $('#add-purchase-order-modal').modal('hide');

                        purchaseOrderTable.clear().rows.add(response.data).draw();

                        swal('<?= __('Success') ?>', '<?= __('The purchase order request has been saved.') ?>', 'success');
                    
                },
                error: function(xhr, status, error) {

                    $('.add_purchase_order_btn').removeAttr('disabled');

                    swal('<?= __('Failed') ?>', '<?= __('Failed to save purchase order request. Please try again.') ?>', 'error');
                }
            });
        }

    }

    function myFunction()
    {
        var form_data = $('#product_approve_form').serialize();

        var error = 0;

        $('tr.item-row').each(function(index, row) {
            
            // console.log(index);

            let quantity = parseFloat($(row).find('.quantity').val());
            let approvedQuantity = parseFloat($(row).find('.approved_quantity').val());
            let rejectedReason = $(row).find('.rejected_reason').val();
            let approvedQuantityErrorSpan = $(row).find('.approved_quantity-error');
            let rejectedReasonErrorSpan = $(row).find('.rejected_reason-error');

            // Validate that approved quantity is not greater than the total quantity
            if (approvedQuantity > quantity) {
                
                error = 1;
                approvedQuantityErrorSpan.html('Approved quantity is greater.');
            }

            // Validate rejection reason if approved quantity is less than total quantity
            if (approvedQuantity < quantity && rejectedReason.trim() === '') {
                
                error = 1;
                rejectedReasonErrorSpan.html('Rejection reason is required.');
            }
        });

        // return false;

        // $('input[name="approve_status[]"]').each(function () {

        //     var value = $(this).val();        // Get the checkbox value
        //     var dataId = $(this).data('id');  // Get the data-custom attribute

        //     if(value == '<?= __('Rejected') ?>')
        //     {
        //         if($('.rejected_reason_'+dataId).val() == '' || $('.rejected_reason_'+dataId).val() == null)
        //         {
        //             $('#rejected_reason_error_'+dataId).show();
        //             error = 1;
        //         }
        //     }
        // });

        if(error == 0)
        {
            // $('.rejected_reason_error').hide();
            $('tr.item-row').each(function(index, row) {
                let approvedQuantityErrorSpan = $(row).find('.approved_quantity-error');
                let rejectedReasonErrorSpan = $(row).find('.rejected_reason-error');
                approvedQuantityErrorSpan.html('');
                rejectedReasonErrorSpan.html('');
            });

            $('.product_approve_btn').text('<?= __('Saving...') ?>');
            $('.product_approve_btn').attr('disabled','disabled');

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierPurchaseOrders', 'action' => 'approveProduct']) ?>",
                type: 'POST',
                data: form_data,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {

                    if (response.status === 'success') {

                        // Get the select box
                        let $select = $('#bill-select');

                        // Clear the existing options
                        $select.empty();

                        // Optionally, add a default "Select" option
                        $select.append('<option value=""><?= __('Select Bill No') ?></option>');

                        // Loop through the object and append each option
                        $.each(response.purchase_order_bills, function(key, value) {
                            $select.append('<option value="' + key + '">' + value + '</option>');
                        });

                        // Get the select box
                        let $selectPayment = $('#payment-bill-select');

                        // Clear the existing options
                        $selectPayment.empty();

                        // Optionally, add a default "Select" option
                        $selectPayment.append('<option value=""><?= __('Select Bill No') ?></option>');

                        // Loop through the object and append each option
                        $.each(response.purchase_order_bills, function(key, value) {
                            $selectPayment.append('<option value="' + key + '">' + value + '</option>');
                        });

                        $('.product_approve_btn').text('<?= __('Save') ?>');
                        $('.product_approve_btn').removeAttr('disabled');

                        purchaseOrderTable.clear().rows.add(response.data).draw();

                        $('#orderModal').modal('hide');

                        swal('<?= __('Success') ?>', '<?= __('The purchase order has been approved.') ?>', 'success');
                    } else {

                        $('.product_approve_btn').text('<?= __('Save') ?>');
                        $('.product_approve_btn').removeAttr('disabled');

                        swal('<?= __('Failed') ?>', '<?= __('Failed to fetch products. Please try again.') ?>', 'error');
                    }
                },
                error: function(xhr, status, error) {

                    $('.product_approve_btn').text('<?= __('Save') ?>');
                        $('.product_approve_btn').removeAttr('disabled');

                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch products. Please try again.') ?>', 'error');
                }
            });
        }
    }

    function openViewPurchaseOrderModal(orderPurchaseId)
    {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierPurchaseOrders', 'action' => 'order']) ?>"+"/"+orderPurchaseId,
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                if(data.status == 'success')
                {   
                    $('.view_purchase_order_container').html('');

                    if(data.order_request.status == '<?= __('A') ?>')
                    {
                        var status = '<?= __('Active') ?>';
                    }
                    else if(data.order_request.status == '<?= __('I') ?>')
                    {
                        var status = '<?= __('Inactive') ?>'
                    }
                    else
                    {
                        var status = '<?= __('Pending') ?>'
                    }

                    if(data.order_request.deliver_to) {

                        if(data.order_request.deliver_to == '<?= __('Showroom') ?>')
                        {
                            var delivery = `${data.order_request.showroom.name} - ${data.order_request.showroom.address}`;

                        }
                        else if (data.order_request.deliver_to == '<?= __('Warehouse') ?>') {
                             
                            var delivery = `${data.order_request.warehouse.name} - ${data.order_request.warehouse.location}`;
                        } 
                    }
                    
                    var html = `

                            <div class="form-group row">
                                <label for="category" class="col-sm-3 col-form-label fw-bold"><?= __('Order Date') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${data.order_request.order_date ? data.order_request.order_date : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="sub-category" class="col-sm-3 col-form-label fw-bold"><?= __('Bill No.') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${data.order_request.bill_no ? data.order_request.bill_no : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-size" class="col-sm-3 col-form-label fw-bold"><?= __('Supplier Bill No.') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${data.order_request.supplier_bill_no ? data.order_request.supplier_bill_no : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="start-date" class="col-sm-3 col-form-label fw-bold"><?= __('Status') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${status ? status : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-3 col-form-label fw-bold"><?= __('Payment Status') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${data.order_request.payment_status ? data.order_request.payment_status : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-3 col-form-label fw-bold"><?= __('Delivery Status') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${data.order_request.delivery_status ? data.order_request.delivery_status : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="brand" class="col-sm-3 col-form-label fw-bold"><?= __('Delivery Address') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${delivery ? delivery : '-' }
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-description" class="col-sm-3 col-form-label fw-bold"><?= __('Required Delivery Date') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${data.order_request.required_delivery_date ? data.order_request.required_delivery_date : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-description" class="col-sm-3 col-form-label fw-bold"><?= __('Payment Due Date') ?></label>
                                <div class="col-sm-5">
                                    <p class="ps-5" style="color: black;">${data.order_request.payment_due_date ? data.order_request.payment_due_date : '-' }</p>
                                </div>
                            </div>

                            <h6 class="mt-5 m-b-25" style="color: #004958">
                                Products
                            </h6>
                    `;

                    html += `<table class='table m-t-15' id="purchaseOrderProductTable">
                                    <thead>
                                        <tr id="purchaseOrderProductTableHeader">
                                          <td class="fw-bold"><?= __("Image") ?></td>  
                                          <td class="fw-bold"><?= __("Product") ?></td>
                                          <td class="fw-bold"><?= __("Product Variant") ?></td>
                                          <td class="fw-bold"><?= __("Product Attribute") ?></td>
                                          <td class="fw-bold"><?= __("SKU") ?></td>
                                          <td class="fw-bold"><?= __("Supplier") ?></td>
                                          <td class="fw-bold"><?= __("PO Quantity") ?></td>
                                          <td class="fw-bold"><?= __("Approved Quantity") ?></td>
                                        </tr>
                                    </thead>
                                <tbody>`;

                    for(var i=0; i < data.order_products.length; i++)
                    {

                        if(data.order_products[i].comment == null || data.order_products[i].comment == '')
                        {
                            var comment = "";
                        }
                        else
                        {
                            var comment = data.order_products[i].comment;
                        }

                        // Construct variants
                        var variantHtml = '';
                        if (data.order_products[i].product.variant_name !== undefined) {

                            variantHtml = data.order_products[i].product.variant_name;

                        } else {
                            variantHtml = 'N/A';
                        }

                        // Construct attributes
                        var attributesHtml = '';
                        if (data.order_products[i].product_attribute_id !== null) {

                            attributesHtml = data.order_products[i].product.attributes.attribute.name + ': ' + data.order_products[i].product.attributes.attribute_value.value;

                        } else {
                            attributesHtml = 'N/A';
                        }

                        var supplier_name = '';
                        if (data.order_products[i].product.supplier_products.length > 0) {

                            supplier_name = data.order_products[i].product.supplier_products[0].supplier.name;

                        } else {
                            supplier_name = 'N/A';
                        }

                        html += `<tr>

                                <td><img src="${data.order_products[i].product.product_image}" style='height: 40px;width: auto;'></img></td> 

                                <td>${data.order_products[i].product.name}</td>
                                <td>${variantHtml}</td>
                                <td>${attributesHtml}</td>
                                <td>${data.order_products[i].product.sku}</td>
                                <td>${supplier_name}</td>
                                <td>${data.order_products[i].quantity}</td>
                                <td>${data.order_products[i].approved_quantity}</td>

                                </tr>
                        `;
                    }
                        
                    html += `</tbody></table>`;

                    $('.view_purchase_order_container').append(html);

                    $('#purchaseOrderProductTable').DataTable().destroy();

                    $('#purchaseOrderProductTable').DataTable({
                        // "pageLength": 5, // Number of rows per page
                        "order": [[0, 'asc']], // Default ordering by first column
                        "searching": true,  // Enable search functionality
                        // "paging": true,     // Enable pagination
                        // "info": true       // Show table information
                        // "lengthChange": true // Allow user to change page length
                    });

                    setTimeout(()=>{
                    
                    $('.dataTables_length').css('display', 'none')

                    },100)

                    $('#view-purchase-order-modal').modal('show');
                }

            },
            error: function() {
                swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
            }
        });
    }

    $(document).on("change", ".edit_product_option", function (event) {

        // if($('option:selected', this).val() !== '')
        // {
        //     var product_sku = $('option:selected', this).attr('data-product-sku');
        //     var product_supplier = $('option:selected', this).attr('data-product-supplier');
        //     var product_image = $('option:selected', this).attr('data-product-image');

        //     $(this).closest("tr").find('#product_sku').text(product_sku);
        //     $(this).closest("tr").find('#product_supplier').text(product_supplier);
        //     $(this).closest("tr").find('#product_image').attr('src',product_image);
        // }
        // else
        // {
        //     $(this).closest("tr").find('#product_sku').text('');
        //     $(this).closest("tr").find('#product_supplier').text('');
        //     $(this).closest("tr").find('#product_image').attr('src','');
        // }

        if($('option:selected', this).val() !== 0)
        {
            var productId = $('option:selected', this).val();

            if(productId == 0)
            {
                $(this).closest("tr").find('#product_sku').text('');
                $(this).closest("tr").find('#product_supplier').text('');
                $(this).closest("tr").find('#product_image').attr('src','');
                return false;
            }

            $('#edit-po-attribute-dropdown').empty();
            $('#edit-po-attribute-dropdown').append('<option value="">' + 'Select Attribute' + '</option>');
            $('#edit_po_variant_id').empty();
            $('#edit_po_variant_id').append('<option value="">' + 'Select Variant' + '</option>');
            
            $(this).closest("tr").find('#product_sku').text('');
            var product_sku = $('option:selected', this).attr('data-product-sku');
            var product_supplier = $('option:selected', this).attr('data-product-supplier');
            var product_image = $('option:selected', this).attr('data-product-image');
            $(this).closest("tr").find('#product_supplier').text(product_supplier);
            $(this).closest("tr").find('#product_image').attr('src',product_image);

            if (productId !== 0) {
                
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Suppliers', 'action' => 'getVariants']) ?>/' + productId,
                    type: 'GET',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        
                        if (response.variants && Object.keys(response.variants).length > 0)
                        {
                            var variantDropdown = $('#edit_po_variant_id');

                            // Clear the existing options in the variant dropdown
                            variantDropdown.empty();

                            const firstVariantKey = Object.keys(response.variants)[0];
                            const firstSKU = response.variants[firstVariantKey].sku;

                            $('#edit_po_variant_id').closest("tr").find('#product_sku').text(firstSKU);

                            $.each(response.variants, function(id, variant) {
                                variantDropdown.append(
                                    $('<option>', {
                                        value: id,
                                        text: variant.name,
                                        'data-sku': variant.sku // Store the SKU in a data attribute
                                    })
                                );
                            });

                        }
                        else
                        {
                            $('#edit_po_variant_id').closest("tr").find('#product_sku').text(product_sku);
                            $('#edit_po_variant_id').empty();
                            $('#edit_po_variant_id').append('<option value="">No variants available</option>');
                        }

                        if (response.attributes.length > 0) {

                            $('#edit-po-attribute-dropdown').empty(); // Clear existing options

                            response.attributes.forEach(function(attribute) {
                                $('#edit-po-attribute-dropdown').append(
                                    $('<option>', {
                                        value: attribute.attribute_id,
                                        text: attribute.attribute_name + ': ' + attribute.attribute_value
                                    })
                                );
                            });

                        } else {
                            $('#edit-po-attribute-dropdown').empty();
                            $('#edit-po-attribute-dropdown').append('<option value="">No attributes available</option>');
                        }
                    },
                    error: function() {

                        swal('<?= __('Failed') ?>', '<?= __('Unable to load variants. Please try again.') ?>', 'error');
                    }
                });
            }
        }
        else
        {
            $(this).closest("tr").find('#product_sku').text('');
            $(this).closest("tr").find('#product_supplier').text('');
            $(this).closest("tr").find('#product_image').attr('src','');
        }

    });

    $("table.edit_product_table").on("click", ".updateQuest", function (event) {

      var error = 0;

      if($(this).closest("tr").find('#edit_product_option').val() == 0)
      {  
          error = 1;
          $(this).closest("tr").find('#edit_product_option_error').html('Select product');
      }
      if($(this).closest("tr").find('#edit_product_quantity').val() == "")
      {
          error = 1;
          $(this).closest("tr").find('#edit_product_quantity_error').html('Enter quantity');
      }
        
      if(error == 0)
      {

        $('#edit_product_option_error').html('');
        $('#edit_product_quantity_error').html('');

        var newRow = $("<tr>");
        var cols = "";
        var pc_options = $(".edit_product_option > option").clone();    
        
        
        /*question type*/
        cols += '<td><input type="hidden" class="form-control" name="product_id[]" value="'+$(this).closest("tr").find('.edit_product_option').find(':selected').val()+'" />';
        cols += '<select class="form-control edit_product_option ctemp1"/></select></td>';

        cols += '<td><input type="hidden" class="form-control" name="product_variant_id[]" value="'+$(this).closest("tr").find('#edit_po_variant_id').find(':selected').val()+'" />';
        cols += '<input type="text" class="form-control ctemp2" value="'+$(this).closest("tr").find('#edit_po_variant_id').find(':selected').text()+'"/></td>';

        cols += '<td><input type="hidden" class="form-control" name="product_attribute_id[]" value="'+$(this).closest("tr").find('#edit-po-attribute-dropdown').find(':selected').val()+'" />';
        cols += '<input type="text" class="form-control ctemp3" value="'+$(this).closest("tr").find('#edit-po-attribute-dropdown').find(':selected').text()+'"/></td>';

        cols += '<td><input type="hidden" class="form-control" name="sku[]" value="'+$(this).closest("tr").find("#product_sku").text()+'"/>';
        cols += '<input type="text" class="form-control ttemp1" value="'+$(this).closest("tr").find("#product_sku").text()+'"/></td>';

        cols += '<td><input type="text" class="form-control ttemp2" value="'+$(this).closest("tr").find('.edit_product_option').find(':selected').attr('data-product-supplier')+'"/></td>';

        /*option*/
        cols += '<td><input type="hidden" class="form-control" name="quantity[]" value="'+$(this).closest("tr").find(".edit_product_quantity").val()+'"/>';
        cols += '<input type="text" class="form-control ttemp4" value="'+$(this).closest("tr").find(".edit_product_quantity").val()+'"/></td>';

        /*option*/
        cols += '<td><img style="height: 40px;width: auto;" src="'+$(this).closest("tr").find('.edit_product_option').find(':selected').attr('data-product-image')+'"></img></td>';


        cols += '<td><button type="button" class="ibtnDel btn admin_btn"><?= __("Delete") ?></button></td></tr>';
        newRow.append(cols);
        $("table.edit_product_table").append(newRow);
        $(".ctemp1").append(pc_options); 
        $(".ctemp1").prop('disabled', 'true');

        $(".ctemp2").prop('disabled', 'true'); 
        $(".ctemp3").prop('disabled', 'true');

        $(".ttemp1").prop('disabled', 'true');
        $(".ttemp2").prop('disabled', 'true');
        $(".ttemp4").prop('disabled', 'true');
        
        $(".ctemp1 option[value='"+$(this).closest("tr").find(".edit_product_option").find(':selected').val()+"']").attr('selected','selected');
        $(".edit_product_option").removeClass('ctemp1');

        $(".ctemp2 option[value='"+$(this).closest("tr").find("#edit_po_variant_id").find(':selected').val()+"']").attr('selected','selected');
        $("#edit_po_variant_id").removeClass('ctemp2');

        $(".ctemp3 option[value='"+$(this).closest("tr").find("#edit-po-attribute-dropdown").find(':selected').val()+"']").attr('selected','selected');
        $("#edit-po-attribute-dropdown").removeClass('ctemp3');

        $('#edit-po-attribute-dropdown').empty();
        $('#edit-po-attribute-dropdown').append('<option value="">' + 'Select Attribute' + '</option>');
        $('#edit_po_variant_id').empty();
        $('#edit_po_variant_id').append('<option value="">' + 'Select Variant' + '</option>');


        $(this).closest("tr").find(".edit_product_option").val(0).trigger('change');
        $(this).closest("tr").find(".edit_product_quantity").val("");
        $(this).closest("tr").find('#product_sku').text('');
        $(this).closest("tr").find('#product_image').attr('src','');
      }

    });
    $("table.edit_product_table").on("click", ".ibtnDel", function (event) {    
         $(this).closest("tr").remove(); 
    });

    function openEditPurchaseOrderModal(orderPurchaseId)
    {


        $('#edit_purchase_order_form')[0].reset();

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierPurchaseOrders', 'action' => 'order']) ?>"+"/"+orderPurchaseId,
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                if(data.status == 'success')
                {   
                    $('.ibtnDel').each(function(){
                        var likeElement = $(this);
                        // setTimeout(function() {
                        likeElement.trigger('click');
                        // }, sleep);
                    });

                    var clear_html = "";    
                    $('#edit_product_table_tbody tr:first').after(clear_html);
                    $('.edit_button_append').html("");

                    $("#edit_order_date").val(data.order_request.order_date);
                    $("#edit_bill_no").val(data.order_request.bill_no);
                    $("#edit_supplier_bill_no").val(data.order_request.supplier_bill_no);
                    $("#edit_delivery_status").val(data.order_request.delivery_status).change();
                    $("#edit_payment_status").val(data.order_request.payment_status).change();
                    $("#edit_status").val(data.order_request.status).change();
                    $("#edit_required_delivery_date").val(data.order_request.required_delivery_date);
                    $("#edit_payment_due_date").val(data.order_request.payment_due_date);

                    if(data.order_request.deliver_to == '<?= __('Showroom') ?>')
                    {
                        $('#yes1').prop("checked", true); 

                        // $('#showrooms-select-edit').show();
                        $('#showrooms-select-edit-div').show();
                        $('#showrooms-select-edit').val(data.order_request.id_deliver_to).change();
                        $('#showrooms-select-edit').attr('name','id_deliver_to');

                        $('#warehouses-select-edit').val('').change();
                        $('#warehouses-select-edit').removeAttr('name');
                        // $('#warehouses-select-edit').hide();
                        $('#warehouses-select-edit-div').hide();

                    }
                    else if (data.order_request.deliver_to == '<?= __('Warehouse') ?>') {
                         
                        $('#no1').prop("checked", true); 

                        $('#showrooms-select-edit').val('').change();
                        // $('#showrooms-select-edit').hide();
                        $('#showrooms-select-edit-div').hide();
                        $('#showrooms-select-edit').removeAttr('name');

                        // $('#warehouses-select-edit').show();
                        $('#warehouses-select-edit-div').show();
                        $('#warehouses-select-edit').val(data.order_request.id_deliver_to).change();
                        $('#warehouses-select-edit').attr('name','id_deliver_to');
                    } 

                    var html = "";
                    for(var i=0; i < data.order_products.length; i++)
                    {

                        // Construct variants
                        var variantHtml = '';
                        if (data.order_products[i].product.variant_name !== undefined) {

                            variantHtml = data.order_products[i].product.variant_name;

                        } else {
                            variantHtml = 'N/A';
                        }

                        // Construct attributes
                        var attributesHtml = '';
                        if (data.order_products[i].product_attribute_id !== null) {

                            attributesHtml = data.order_products[i].product.attributes.attribute.name + ': ' + data.order_products[i].product.attributes.attribute_value.value;

                        } else {
                            attributesHtml = 'N/A';
                        }

                        var supplier_name = '';
                        if (data.order_products[i].product.supplier_products.length > 0) {

                            supplier_name = data.order_products[i].product.supplier_products[0].supplier.name;

                        } else {
                            supplier_name = 'N/A';
                        }

                        html += `<tr>

                                <td><input type="hidden" class="form-control" name="product_id[]" value="${data.order_products[i].product.id}">${data.order_products[i].product.name}</td>

                                <td><input type="hidden" class="form-control" name="product_variant_id[]" value="${data.order_products[i].product_variant_id}">${variantHtml}</td>

                                <td><input type="hidden" class="form-control" name="product_attribute_id[]" value="${data.order_products[i].product_attribute_id}">${attributesHtml}</td>

                                <td><input type="hidden" class="form-control" name="sku[]" value="${data.order_products[i].product.sku}">${data.order_products[i].product.sku}</td>

                                <td>${supplier_name}</td>

                                <td><input type="number" min="1" class="form-control product_quantity" id="product_quantity" name="quantity[]" value="${data.order_products[i].quantity}">
                                </td>

                                <td><img src="${data.order_products[i].product.product_image}" style='height: 40px;width: auto;'></img></td> 

                                <td><button type="button" class="ibtnDel btn admin_btn"><?= __('Delete') ?></button></td>
                                </tr>
                        `;
                    }

                    var button_html = `<button style="background-color: #0d839b !important;color: white !important;float: right;position: relative;right: 30px;" onclick="editPurchaseOrderRequest(${data.order_request.id})" type="button" class="btn edit_purchase_order_btn admin_btn"><?= __('Save') ?></button>`;

                    $('.edit_button_append').append(button_html);
                    $('#edit_product_table_tbody tr:last').after(html);
                    $('#edit-purchase-order-modal').modal('show');
                }

            },
            error: function() {
                swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
            }
        });
    }

    function editPurchaseOrderRequest(orderPurchaseId)
    {
        var form_data = $('#edit_purchase_order_form').serialize();
        var error = 0;

        const tbody = document.querySelector('#edit_product_table tbody');
        const numberOfRows = tbody.querySelectorAll('tr').length;

        if($("#edit_order_date").val() == '' || $("#edit_order_date").val() == null)
        {
            error = 1;
            $('#edit_order_date_error').show();
        }
        if($("#edit_bill_no").val() == '' || $("#edit_bill_no").val() == null)
        {
            error = 1;
            $('#edit_bill_no_error').show();
        }
        if($("input:radio.edit_delivery:checked").val() == '<?= __('Showroom') ?>')
        {
            if($("#showrooms-select-edit option:selected").val() == '' || $("#showrooms-select-edit option:selected").val() == null)
            {
                error = 1;
                $('#edit_showroom_error').show();
            }

        }
        else if($("input:radio.edit_delivery:checked").val() == '<?= __('Warehouse') ?>')
        {
            if($("#warehouses-select-edit option:selected").val() == '' || $("#warehouses-select-edit option:selected").val() == null)
            {
                error = 1;
                $('#edit_warehouse_error').show();
            }
        }
        if($("#edit_required_delivery_date").val() == '' || $("#edit_required_delivery_date").val() == null)
        {
            error = 1;
            $('#edit_required_delivery_date_error').show();
        }
        if($("#edit_payment_due_date").val() == '' || $("#edit_payment_due_date").val() == null)
        {
            error = 1;
            $('#edit_payment_due_date_error').show();
        }

        if(numberOfRows <= 1)
        {
            swal('<?= __('Error') ?>', '<?= __('Please add product.') ?>', 'error');
            return false;
        }

        if(error == 0)
        {   
            $('#edit_order_date_error').hide();
            $('#edit_bill_no_error').hide();
            $('#edit_showroom_error').hide();
            $('#edit_warehouse_error').hide();
            $('#edit_required_delivery_date_error').hide();
            $('#edit_payment_due_date_error').hide();
            $('.edit_purchase_order_btn').attr('disabled','disabled');
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierPurchaseOrders', 'action' => 'edit']) ?>"+"/"+orderPurchaseId,
                type: 'POST',
                data: form_data,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {

                    // if (response.status === 'success') {

                        $('.edit_purchase_order_btn').removeAttr('disabled');

                        $('#edit-purchase-order-modal').modal('hide');

                        purchaseOrderTable.clear().rows.add(response.data).draw();

                        swal('<?= __('Success') ?>', '<?= __('The purchase order request has been saved.') ?>', 'success');
                    // } else {

                    //     $('.add_supplier_product_btn').attr('disabled','disabled');

                    //     swal('<?= __('Failed') ?>', '<?= __('Failed to save supplier products. Please try again.') ?>', 'error');
                    // }
                },
                error: function(xhr, status, error) {

                    $('.edit_purchase_order_btn').removeAttr('disabled');

                    swal('<?= __('Failed') ?>', '<?= __('Failed to save purchase order request. Please try again.') ?>', 'error');
                }
            });
        }

    }

    document.addEventListener('DOMContentLoaded', function () {
        const purchaseOrderFilterButton = document.querySelector('.btn.purchase-order-menu-toggle');
        const purchaseOrderFilterBodyContainer = document.getElementById('purchase-order-filter-body-container');

        purchaseOrderFilterButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (purchaseOrderFilterBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                purchaseOrderFilterBodyContainer.classList.remove('showing');
                purchaseOrderFilterBodyContainer.classList.add('hiding');
                purchaseOrderFilterBodyContainer.addEventListener('animationend', function () {
                    purchaseOrderFilterBodyContainer.classList.remove('hiding');
                    purchaseOrderFilterBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                purchaseOrderFilterBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                purchaseOrderFilterBodyContainer.classList.add('showing');
            }
        });
    });

    $('.purchase_order_filter').on('click', function () {

        var payment = $("#filterPaymentStatus option:selected").val();
        var status = $("#filterPurchaseOrderStatus option:selected").val();
        var filterStatusValue = status === 'A' ? 'Active' : status === 'I' ? 'Inactive' : '';

        purchaseOrderTable.search('').columns().search('');

        if (payment) {
            purchaseOrderTable.column(4).search(payment).draw();
        }

        if (status) {
            purchaseOrderTable.column(3).search(filterStatusValue, true, false, false).draw();
        }
    });

    $('.reset_purchase_order_filter').on('click', function () {

        purchaseOrderTable.search('').columns().search('').draw();

    });

    //RETURN ORDER REQUEST

    document.addEventListener('DOMContentLoaded', function () {
        const returnOrderFilterButton = document.querySelector('.btn.return-order-menu-toggle');
        const returnOrderFilterBodyContainer = document.getElementById('return-order-filter-body-container');

        returnOrderFilterButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (returnOrderFilterBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                returnOrderFilterBodyContainer.classList.remove('showing');
                returnOrderFilterBodyContainer.classList.add('hiding');
                returnOrderFilterBodyContainer.addEventListener('animationend', function () {
                    returnOrderFilterBodyContainer.classList.remove('hiding');
                    returnOrderFilterBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                returnOrderFilterBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                returnOrderFilterBodyContainer.classList.add('showing');
            }
        });
    });

    var returnOrderTable = $("#returnOrderTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": [
            { "data": "return_date" },
            { "data": "bill_no" },
            { "data": "supp_bill_no" },
            { "data": "return_status" },
            { "data": "status" },
            {
                "data": "actions",
                "render": function (data, type, row) {
                    return data;
                }
            }
        ]
    });

    $('.view-add-return-order-modal').on('click', function() {

        $('#product_tbody').html("");
        $('#add_return_order_form')[0].reset();
        $('#add_total_price').text(0.0);
        $('#add-return-order-modal').modal('show');
    });

    // Function to toggle between showroom and warehouse dropdowns
    function returnToggleSelectBox(value) {

        if (value === '<?= __('Showroom') ?>') {

            $('#return-showrooms-select-div').show();
            $('#return-showrooms-select').show().trigger('change');
            $('#return-showrooms-select').attr('name','id_deliver_to');

            $('#return_showroom_error').css('display','none');
            $('#return_warehouse_error').css('display','none');

            $('#return-warehouses-select').removeAttr('name');
            // $('#return-warehouses-select').hide();
            $('#return-warehouses-select-div').hide();

        } else if (value === '<?= __('Warehouse') ?>') {
            // $('#return-showrooms-select').hide();
            $('#return-showrooms-select-div').hide();
            $('#return-showrooms-select').removeAttr('name');

            $('#return_showroom_error').css('display','none');
            $('#return_warehouse_error').css('display','none');

            $('#return-warehouses-select-div').show();
            $('#return-warehouses-select').show().trigger('change');
            $('#return-warehouses-select').attr('name','id_deliver_to');
        }
    }

    $('#bill-select').on('change', function () {
            
            var purchase_order_id = $(this).val();

            if(purchase_order_id == 0 || purchase_order_id == '' || purchase_order_id == null)
            {
                return false;
            }

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'getPurchaseOrderById']) ?>"+"/"+purchase_order_id,
                type: 'GET',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(data) {

                    if (data.status === 'success') {

                        $('#return_initiated_date_error').hide();
                        $('#bill_select_error').hide();
                        $('#return_showroom_error').hide();
                        $('#return_warehouse_error').hide();
                    
                        $('#return_bill_no').val(data.order_request.bill_no);

                        $('#supplier_bill_no_disable').val(data.order_request.supplier_bill_no);
                        $('#return_supplier_bill_no').val(data.order_request.supplier_bill_no);

                        if(data.order_request.deliver_to == '<?= __('Showroom') ?>')
                        {
                            returnToggleSelectBox(data.order_request.deliver_to);
                            // $("#return-showrooms-select option[value="+data.order_request.delivery_address+"]").attr('selected', true);
                            $('#return-showrooms-select').val(data.order_request.id_deliver_to).trigger('change'); 
                            $('#return_yes').prop('checked', true);
                        }
                        else if(data.order_request.deliver_to == '<?= __('Warehouse') ?>')
                        {
                            returnToggleSelectBox(data.order_request.deliver_to);
                            // $("#return-warehouses-select option[value="+data.order_request.delivery_address+"]").attr('selected', true);
                            $('#return-warehouses-select').val(data.order_request.id_deliver_to).trigger('change');
                            $('#return_no').prop('checked', true);
                        }

                        $('#product_tbody').html('');
                        var html = "";
                        var total_supplier_price = 0.0;
                        for(var i=0; i < data.order_products.length; i++)
                        {
                            // total_supplier_price += data.order_products[i].product.supplier_products[0].supplier_price;

                            let price = parseFloat(data.order_products[i].product.supplier_products[0].supplier_price) * parseInt(data.order_products[i].approved_quantity);

                            // Only add valid numbers (skip NaN values)
                            if (!isNaN(price)) {
                                total_supplier_price += price;
                            } 

                            // Construct variants
                            var variantHtml = '';
                            if (data.order_products[i].product.variant_name !== undefined) {

                                variantHtml = data.order_products[i].product.variant_name;

                            } else {
                                variantHtml = 'N/A';
                            }

                            // Construct attributes
                            var attributesHtml = '';
                            if (data.order_products[i].product_attribute_id !== null) {

                                attributesHtml = data.order_products[i].product.attributes.attribute.name + ': ' + data.order_products[i].product.attributes.attribute_value.value;

                            } else {
                                attributesHtml = 'N/A';
                            }

                            html += "<tr style='position:relative;top:10px;'>";

                            html += "<td><img src='"+data.order_products[i].product.product_image+"' style='height: 40px;width: auto;'></img></td>";

                            html += "<td><input type='hidden' value='"+data.order_products[i].product_id+"' name='product_id[]'>"+data.order_products[i].product.name+"</td>";

                            html += "<td><input type='hidden' value='"+data.order_products[i].product_variant_id+"' name='product_variant_id[]'>"+variantHtml+"</td>";

                            html += "<td><input type='hidden' value='"+data.order_products[i].product_attribute_id+"' name='product_attribute_id[]'>"+attributesHtml+"</td>";

                            html += "<td><input type='hidden' value='"+data.order_products[i].sku+"' name='sku[]'>"+data.order_products[i].sku+"</td>";

                            html += "<td>"+data.order_products[i].product.supplier_products[0].supplier_price+"</td>";

                            html += `<td><input type='hidden' value='${data.order_products[i].approved_quantity}' name='quantity[]'>${data.order_products[i].approved_quantity}</td>`;

                            html += `<td>
                                    <input type='number' data-quantity="${data.order_products[i].approved_quantity}" data-id="${data.order_products[i].id}"" name='return_quantity[]'>
                                    <span class="return_quantity_error" style="color: #dc3545;display: none;" id="return_quantity_error_${data.order_products[i].id}"></span>

                                    </td>
                            `;

                            html += `<td class="col-sm-6">

                                    <input data-id="${data.order_products[i].id}" style='border:none;' type='file' name='return_product_image[]' class="return-product-image" onchange="handleImagePreview(event)" accept="image/*">

                                    <span class="return_image_error" style="color: #dc3545;display: none;" id="return_image_error_${data.order_products[i].id}"><?= __('choose a image') ?></span>


                                    <div class="return-image-preview" style="margin-top: 10px;"></div>

                                    </td>`;

                            html += `<td>
                                        <textarea data-id="${data.order_products[i].id}" name='return_reason[]'></textarea>
                                        <span class="return_reason_error" style="color: #dc3545;display: none;" id="return_reason_error_${data.order_products[i].id}"><?= __('Enter a reason') ?></span>
                                    </td>`;

                            html += "</tr>";
                        }

                        $('#add_total_price').html(total_supplier_price.toFixed(2));
                        $('#product_tbody').append(html);

                    } else {

                        swal('<?= __('Failed') ?>', '<?= __('Failed to fetch purchase order detail. Please try again.') ?>', 'error');
                    }
                },
                error: function(xhr, status, error) {

                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch purchase order detail. Please try again.') ?>', 'error');
                }
            });
    });

    // Function to handle image preview
    function handleImagePreview(event) {
        const input = event.target; // Get the input element
        const previewContainer = input.closest('td').querySelector('.return-image-preview'); // Find the preview container
        const files = input.files; // Get the selected files
        previewContainer.innerHTML = ''; // Clear any previous preview

        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Validate file type (only allow images)
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                continue; // Skip to the next file
            }

            const reader = new FileReader();

            reader.onload = function (e) {
                const imgContainer = document.createElement('div'); // Create a container for the image and remove button
                const img = document.createElement('img');
                img.src = e.target.result; // Set the source to the image data
                img.style.width = '60px'; // Set width of the image
                img.style.height = '60px'; // Set height of the image
                img.style.marginRight = '10px'; // Space between images
                img.classList.add('preview-thumbnail'); // Optional class for styling

                const removeButton = document.createElement('button'); // Create a remove button
                removeButton.innerHTML = '&times;'; // Use HTML entity for "X"
                removeButton.style.color = 'red'; // Set color for the "X"
                removeButton.style.background = 'none'; // Remove background
                removeButton.style.border = 'none'; // Remove border
                removeButton.style.cursor = 'pointer'; // Change cursor to pointer
                removeButton.style.backgroundColor = 'lightgray'; // Change cursor to pointer
                removeButton.type = 'button'; // Set button type
                removeButton.className = 'remove-button';
                removeButton.onclick = function () {
                    input.value = '';
                    imgContainer.remove(); // Remove the image container
                };

                imgContainer.appendChild(img); // Append the image to the container
                imgContainer.appendChild(removeButton); // Append the remove button to the container
                previewContainer.appendChild(imgContainer); // Append the container to the preview
            };

            reader.readAsDataURL(file); // Read the image file
        }
    }

    $('#add_return_order_form').on('submit', function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var error = 0;

        if($("#return_initiated_date").val() == '' || $("#return_initiated_date").val() == null)
        {
            error = 1;
            $('#return_initiated_date_error').show();
        }
        if($("#bill-select").val() == '' || $("#bill-select").val() == null)
        {
            error = 1;
            $('#bill_select_error').show();
        }
        if($("input:radio#return_yes:checked").val() == '<?= __('Showroom') ?>')
        {
            if($("#return-showrooms-select option:selected").val() == '' || $("#return-showrooms-select option:selected").val() == null)
            {
                error = 1;
                $('#return_showroom_error').show();
            }

        }
        else if($("input:radio#return_no:checked").val() == '<?= __('Warehouse') ?>')
        {
            if($("#return-warehouses-select option:selected").val() == '' || $("#return-warehouses-select option:selected").val() == null)
            {
                error = 1;
                $('#return_warehouse_error').show();
            }
        }

        $('input[name="return_quantity[]"]').each(function () {

            var value = $(this).val();        
            var dataId = $(this).data('id');
            var quantity = $(this).data('quantity');

            if(value == '' || value == null)
            {   
                error = 1;
                $('#return_quantity_error_'+dataId).html('<?= __('Enter return quantity.') ?>');
                $('#return_quantity_error_'+dataId).show();
            }
            if(quantity < value)
            {
                error = 1;
                $('#return_quantity_error_'+dataId).html('<?= __('Return quantity is greater.') ?>');
                $('#return_quantity_error_'+dataId).show();
            }

        });

        $('input[name="return_product_image[]"]').each(function (index, input) {
            
            var file = input.files[0];  
            var dataId = $(this).data('id');

            if (!file) 
            {
                error = 1;
                $('#return_image_error_'+dataId).show();
            }
        });

        $('textarea[name="return_reason[]"]').each(function () {

            var value = $(this).val();        
            var dataId = $(this).data('id');

            if(value == '' || value == null)
            {
                error = 1;
                $('#return_reason_error_'+dataId).show();
            }
        });

        //IMAGE VALIDATION
        // Get all input fields with the same name "return_product_image[]"
        const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
        const maxFileSize = 5 * 1024 * 1024; // 5 MB
        const imageInputs = $('input[name="return_product_image[]"]');
        let isValid = true;
        let errorMessages = [];

        // Loop through each input field
        imageInputs.each(function(index, input) {
            const file = input.files[0]; // Get the single file from the input

            // Check if a file is selected
            if (file) {
                const fileExtension = file.name.split('.').pop().toLowerCase();

                // Validate file extension
                if ($.inArray(fileExtension, allowedExtensions) === -1) {
                    isValid = false;
                    // errorMessages.push('Invalid file extension for: ' + file.name);
                    errorMessages.push(`${file.name} - Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.`);
                }

                // Validate file size
                if (file.size > maxFileSize) {
                    isValid = false;
                    errorMessages.push('File too large (max 5 MB): ' + file.name);
                }

                // Validate file type (optional)
                if (!file.type.startsWith('image/')) {
                    isValid = false;
                    // errorMessages.push('Selected file is not an image: ' + file.name);
                }
            } else {
                // If no file is selected, add an error message
                errorMessages.push('No file selected in input ' + (index + 1));
            }
        });

        // If not valid, show error messages
        if (!isValid) {
            // alert("Errors:\n" + errorMessages.join("\n"));
            

            const wrapper = document.createElement('div');
            wrapper.innerHTML = '<ul>' + errorMessages.map(msg => `<li>${msg}</li>`).join('') + '</ul>';
            swal({
                title: "<?= __("Error") ?>", 
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>", 
                allowOutsideClick: "true" 
            });
            return false;
        }

        if(error == 0)
        {
            $('.return_quantity_error').hide();
            $('.return_image_error').hide();
            $('.return_reason_error').hide();

            $('#return_initiated_date_error').hide();
            $('#bill_select_error').hide();
            $('#return_showroom_error').hide();
            $('#return_warehouse_error').hide();
            $('.add_return_order_btn').attr('disabled','disabled');

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,  // Important: Prevent jQuery from setting contentType
                processData: false,  // Important: Prevent jQuery from processing data
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                            
                            $('.add_return_order_btn').removeAttr('disabled');

                            $('#add-return-order-modal').modal('hide');

                            returnOrderTable.clear().rows.add(response.data).draw();

                            swal('<?= __('Success') ?>', '<?= __('The return order request has been saved.') ?>', 'success');
                },
                error: function(xhr, status, error) {

                    $('.add_return_order_btn').removeAttr('disabled');
                    swal('<?= __('Failed') ?>', '<?= __('Failed to save return order request. Please try again.') ?>', 'error');
                }
            });
        }
    });

    function openViewReturnOrderModal(orderReturnId)
    {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'view']) ?>"+"/"+orderReturnId,
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                if(data.status == 'success')
                {   

                    $('.view_return_order_container').html('');

                    if(data.return_request.status == '<?= __('A') ?>')
                    {
                        var status = '<?= __('Active') ?>';
                    }
                    else
                    {
                        var status = '<?= __('Inactive') ?>'
                    }

                    if(data.return_request.deliver_to) {

                        if(data.return_request.deliver_to == '<?= __('Showroom') ?>')
                        {
                            var delivery = `${data.return_request.showroom.name} - ${data.return_request.showroom.address}`;

                        }
                        else if (data.return_request.deliver_to == '<?= __('Warehouse') ?>') {
                             
                            var delivery = `${data.return_request.warehouse.name} - ${data.return_request.warehouse.location}`;
                        } 
                    }
                    
                    var html = `

                            <div class="form-group row">
                                <label for="category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Return Date') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.return_request.initiated_date ? data.return_request.initiated_date : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="sub-category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Bill No.') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.return_request.bill_no ? data.return_request.bill_no : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-size" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Supplier Bill No.') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.return_request.supplier_bill_no ? data.return_request.supplier_bill_no : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="brand" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Delivery Address') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${delivery ? delivery : '-' }
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Return Status') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.return_request.return_status ? data.return_request.return_status : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Status') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${status ? status : '-' }</p>
                                </div>
                            </div>

                            <h6 class="mt-5 m-b-20 m-l-35" style="color: #004958">
                                Products
                            </h6>
                    `;

                    html += `<table class='table table-bordered m-t-15' id="returnOrderProductTable">
                                    <thead>
                                        <tr>
                                          <td class="fw-bold"><?= __("Image") ?></td>  
                                          <td class="fw-bold"><?= __("Product") ?></td>
                                          <td class="fw-bold"><?= __("Product Variant") ?></td>
                                          <td class="fw-bold"><?= __("Product Attribute") ?></td>
                                          <td class="fw-bold"><?= __("SKU") ?></td>
                                          <td class="fw-bold"><?= __("Supplier Price") ?></td>
                                          <td class="fw-bold"><?= __("Quantity") ?></td>
                                          <td class="fw-bold"><?= __("Return Quantity") ?></td>
                                          <td class="fw-bold"><?= __("Defect Image") ?></td>
                                          <td class="fw-bold"><?= __("Return Reason") ?></td>
                                        </tr>
                                    </thead>
                                <tbody>`;

                    var total_supplier_price = 0.0;
                    for(var i=0; i < data.return_products.length; i++)
                    {
                        let price = parseFloat(data.return_products[i].product.supplier_products[0].supplier_price);

                        // Only add valid numbers (skip NaN values)
                        if (!isNaN(price)) {
                            total_supplier_price += price;
                        }

                        if(data.return_products[i].return_reason == null || data.return_products[i].return_reason == '')
                        {
                            var return_reason = "";
                        }
                        else
                        {
                            var return_reason = data.return_products[i].return_reason;
                        }

                        // Construct variants
                        var variantHtml = '';
                        if (data.return_products[i].product.variant_name !== undefined) {

                            variantHtml = data.return_products[i].product.variant_name;

                        } else {
                            variantHtml = 'N/A';
                        }

                        // Construct attributes
                        var attributesHtml = '';
                        if (data.return_products[i].attribute_id !== null) {

                            attributesHtml = data.return_products[i].product.attributes.attribute.name + ': ' + data.return_products[i].product.attributes.attribute_value.value;

                        } else {
                            attributesHtml = 'N/A';
                        }

                        html += `<tr>

                                <td><img src="${data.return_products[i].product.product_image}" style='height: 40px;width: auto;'></img></td> 

                                <td>${data.return_products[i].product.name}</td>
                                <td>${variantHtml}</td>
                                <td>${attributesHtml}</td>
                                <td>${data.return_products[i].product.sku}</td>
                                <td>${data.return_products[i].product.supplier_products[0].supplier_price}</td>
                                <td>${data.return_products[i].quantity}</td>
                                <td>${data.return_products[i].return_quantity}</td>
                                <td><img src="${data.return_products[i].return_product_image}" style='height: 40px;width: auto;'></img></td>
                                <td>${return_reason}</td>

                                </tr>
                        `;
                    }
                        
                    html += `</tbody></table>`;

                    $('.view_return_order_container').append(html);

                    $('#returnOrderProductTable').DataTable().destroy();

                    $('#returnOrderProductTable').DataTable({
                        // "pageLength": 5, // Number of rows per page
                        "order": [[0, 'asc']], // Default ordering by first column
                        "searching": true,  // Enable search functionality
                        // "paging": true,     // Enable pagination
                        // "info": true       // Show table information
                        // "lengthChange": true // Allow user to change page length
                    });

                    setTimeout(()=>{
                    
                    $('.dataTables_length').css('display', 'none')

                    },100)

                    // $('#view_total_price').html(total_supplier_price.toFixed(2));
                    $('#view-return-order-modal').modal('show');
                }

            },
            error: function() {
                swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
            }
        });
    }

    function openEditReturnOrderModal(orderReturnId)
    {
        $('#edit_return_order_form')[0].reset();

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'view']) ?>"+"/"+orderReturnId,
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                if(data.status == '<?= __('success') ?>')
                {

                    if(data.return_request.return_status == '<?= __('Pending') ?>')
                    {
                        $(".edit_return_order_product_table").html("");

                        if(data.return_request.status == '<?= __('A') ?>')
                        {
                            var status = '<?= __('Active') ?>';
                        }
                        else
                        {
                            var status = '<?= __('Inactive') ?>'
                        }

                        if(data.return_request.deliver_to) {

                            if(data.return_request.deliver_to == '<?= __('Showroom') ?>')
                            {
                                var delivery = `${data.return_request.showroom.name} - ${data.return_request.showroom.address}`;

                            }
                            else if (data.return_request.deliver_to == '<?= __('Warehouse') ?>') {
                                 
                                var delivery = `${data.return_request.warehouse.name} - ${data.return_request.warehouse.location}`;
                            } 
                        }

                        $("#edit_return_initiated_date").text(data.return_request.initiated_date);
                        $("#edit_return_bill_no").text(data.return_request.bill_no);
                        $("#edit_return_supplier_bill_no").text(data.return_request.supplier_bill_no);
                        $("#edit_return_pickup_address").text(delivery);
                        // $("#edit_order_return_status").text(data.return_request.return_status);
                        // $("#edit_return_status").text(status);

                        $('#edit_order_return_status_text').hide();
                        $("#edit_order_return_status").show();
                        $("#edit_order_return_status option[value="+data.return_request.return_status+"]").attr('selected', true);
                        $("#edit_return_status option[value="+data.return_request.status+"]").attr('selected', true);

                        $('#edit_return_order_form').attr('action',"<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'edit']) ?>"+"/"+orderReturnId+"");

                        var html = `
                                    <h6 class="m-b-20" style="color: #004958">Products</h6>
                                    <table class='table table-bordered m-t-15'>
                                        <thead>
                                            <tr>
                                              <td class="fw-bold"><?= __("Image") ?></td>  
                                              <td class="fw-bold"><?= __("Product") ?></td>
                                              <td class="fw-bold"><?= __("Product Variant") ?></td>
                                              <td class="fw-bold"><?= __("Product Attribute") ?></td>
                                              <td class="fw-bold"><?= __("SKU") ?></td>
                                              <td class="fw-bold"><?= __("Supplier Price") ?></td>
                                              <td class="fw-bold"><?= __("Quantity") ?></td>
                                              <td class="fw-bold"><?= __("Return Quantity") ?></td>
                                              <td class="fw-bold"><?= __("Defect Image") ?></td>
                                              <td class="fw-bold"><?= __("Return Reason") ?></td>
                                            </tr>
                                        </thead>
                                    <tbody>`;

                        var total_supplier_price = 0.0;
                        for(var i=0; i < data.return_products.length; i++)
                        {

                            if(data.return_products[i].return_reason == null || data.return_products[i].return_reason == '')
                            {
                                var return_reason = "";
                            }
                            else
                            {
                                var return_reason = data.return_products[i].return_reason;
                            }

                            if(data.return_products[i].return_product_image == null || data.return_products[i].return_product_image == '')
                            {
                                var image_container = '';
                            }
                            else
                            {
                                var image_container = `<div class="image_container_${data.return_products[i].id}">
                                        <img style="height: 40px;width: 40px;" src="${data.return_products[i].return_product_image}" alt="Image Preview" class="preview-img"/>
                                            <button onclick="deleteReturnProductImage(${data.return_products[i].id})" type="button" class="delete-img-btn1">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>`;
                            }

                            let price = parseFloat(data.return_products[i].product.supplier_products[0].supplier_price) * parseInt(data.return_products[i].quantity);

                            // Only add valid numbers (skip NaN values)
                            if (!isNaN(price)) {
                                total_supplier_price += price;
                            }

                            // Construct variants
                            var variantHtml = '';
                            if (data.return_products[i].product.variant_name !== undefined) {

                                variantHtml = data.return_products[i].product.variant_name;

                            } else {
                                variantHtml = 'N/A';
                            }

                            // Construct attributes
                            var attributesHtml = '';
                            if (data.return_products[i].attribute_id !== null) {

                                attributesHtml = data.return_products[i].product.attributes.attribute.name + ': ' + data.return_products[i].product.attributes.attribute_value.value;

                            } else {
                                attributesHtml = 'N/A';
                            }

                            html += `<tr>

                                    <td><input type="hidden" value="${data.return_products[i].id}" name='return_product_id[]'>
                                        <input type="hidden" name="return_order_id[]" value="${data.return_products[i].return_order_id}">
                                        <img src="${data.return_products[i].product.product_image}" style='height: 40px;width: auto;'></img>
                                    </td> 

                                    
                                    <td><input type="hidden" value="${data.return_products[i].product_id}" name='product_id[]'>${data.return_products[i].product.name}</td>

                                    <td><input type="hidden" value="${data.return_products[i].variant_id}" name='variant_id[]'>${variantHtml}</td>

                                    <td><input type="hidden" value="${data.return_products[i].attribute_id}" name='attribute_id[]'>${attributesHtml}</td>


                                    <td><input type="hidden" value="${data.return_products[i].product.sku}" name='sku[]'>${data.return_products[i].product.sku}</td>


                                    <td>${data.return_products[i].product.supplier_products[0].supplier_price}</td>


                                    <td><input type="hidden" value="${data.return_products[i].quantity}" name='quantity[]'>${data.return_products[i].quantity}</td>


                                    <td>
                                        <input type="number" data-id="${data.return_products[i].id}" style="width: max-content;" min="0" name="return_quantity[]" value="${data.return_products[i].return_quantity}">
                                        <span class="edit_return_quantity_error" style="color: #dc3545;display: none;" id="edit_return_quantity_error_${data.return_products[i].id}"></span>
                                    </td>
                                    
                                    <td>
                                        <input data-id="${data.return_products[i].id}" style="border:none;" type="file" name="return_product_image[]" accept="image/*">
                                        <span class="edit_return_image_error" style="color: #dc3545;display: none;" id="edit_return_image_error_${data.return_products[i].id}"><?= __('choose a image') ?></span>
                                        ${image_container}  
                                    </td>

                                    <td><textarea data-id="${data.return_products[i].id}" name="return_reason[]" value="${return_reason}">${return_reason}</textarea>
                                        <span class="edit_return_reason_error" style="color: #dc3545;display: none;" id="edit_return_reason_error_${data.return_products[i].id}"><?= __('Enter a reason') ?></span>
                                    </td>

                                    </tr>
                            `;
                        }
                            
                        html += `</tbody></table>`;

                        // $('#edit_total_price').html(total_supplier_price.toFixed(2));
                        $(".edit_return_order_product_table").append(html);
                        $('#edit-return-order-modal').modal('show');

                    }
                    else if(data.return_request.return_status == '<?= __('Completed') ?>')
                    {

                        $(".edit_return_order_product_table").html("");

                        if(data.return_request.status == '<?= __('A') ?>')
                        {
                            var status = '<?= __('Active') ?>';
                        }
                        else
                        {
                            var status = '<?= __('Inactive') ?>'
                        }

                        if(data.return_request.deliver_to) {

                            if(data.return_request.deliver_to == '<?= __('Showroom') ?>')
                            {
                                var delivery = `${data.return_request.showroom.name} - ${data.return_request.showroom.address}`;

                            }
                            else if (data.return_request.deliver_to == '<?= __('Warehouse') ?>') {
                                 
                                var delivery = `${data.return_request.warehouse.name} - ${data.return_request.warehouse.location}`;
                            } 
                        }

                        $("#edit_return_initiated_date").text(data.return_request.initiated_date);
                        $("#edit_return_bill_no").text(data.return_request.bill_no);
                        $("#edit_return_supplier_bill_no").text(data.return_request.supplier_bill_no);
                        $("#edit_return_pickup_address").text(delivery);
                        $("#edit_order_return_status_text").show();
                        $("#edit_order_return_status_text").text(data.return_request.return_status);
                        $("#edit_order_return_status option[value="+data.return_request.return_status+"]").attr('selected', true);
                        $("#edit_order_return_status").hide();
                        // $("#edit_return_status").text(status);
                        $("#edit_return_status option[value="+data.return_request.status+"]").attr('selected', true);

                        $('#edit_return_order_form').attr('action',"<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'edit']) ?>"+"/"+orderReturnId+"");

                        var html = `
                                    <h6 class="m-b-20" style="color: #004958">Products</h6>
                                    <table class='table table-bordered m-t-15' id="editReturnOrderProductTable">
                                        <thead>
                                            <tr>
                                              <td class="fw-bold"><?= __("Image") ?></td>  
                                              <td class="fw-bold"><?= __("Product") ?></td>
                                              <td class="fw-bold"><?= __("Product Variant") ?></td>
                                              <td class="fw-bold"><?= __("Product Attribute") ?></td>
                                              <td class="fw-bold"><?= __("SKU") ?></td>
                                              <td class="fw-bold"><?= __("Supplier Price") ?></td>
                                              <td class="fw-bold"><?= __("Quantity") ?></td>
                                              <td class="fw-bold"><?= __("Return Quantity") ?></td>
                                              <td class="fw-bold"><?= __("Defect Image") ?></td>
                                              <td class="fw-bold"><?= __("Return Reason") ?></td>
                                            </tr>
                                        </thead>
                                    <tbody>`;

                        var total_supplier_price = 0.0;
                        for(var i=0; i < data.return_products.length; i++)
                        {
                            let price = parseFloat(data.return_products[i].product.supplier_products[0].supplier_price) * parseInt(data.return_products[i].quantity);

                            // Only add valid numbers (skip NaN values)
                            if (!isNaN(price)) {
                                total_supplier_price += price;
                            }

                            if(data.return_products[i].return_reason == null || data.return_products[i].return_reason == '')
                            {
                                var return_reason = "";
                            }
                            else
                            {
                                var return_reason = data.return_products[i].return_reason;
                            }

                            // Construct variants
                            var variantHtml = '';
                            if (data.return_products[i].product.variant_name !== undefined) {

                                variantHtml = data.return_products[i].product.variant_name;

                            } else {
                                variantHtml = 'N/A';
                            }

                            // Construct attributes
                            var attributesHtml = '';
                            if (data.return_products[i].attribute_id !== null) {

                                attributesHtml = data.return_products[i].product.attributes.attribute.name + ': ' + data.return_products[i].product.attributes.attribute_value.value;

                            } else {
                                attributesHtml = 'N/A';
                            }

                            html += `<tr>

                                    <td><img src="${data.return_products[i].product.product_image}" style='height: 40px;width: auto;'></img></td> 

                                    <td>${data.return_products[i].product.name}</td>
                                    <td>${variantHtml}</td>
                                    <td>${attributesHtml}</td>
                                    <td>${data.return_products[i].product.sku}</td>
                                    <td>${data.return_products[i].product.supplier_products[0].supplier_price}</td>
                                    <td>${data.return_products[i].quantity}</td>
                                    <td>${data.return_products[i].return_quantity}</td>
                                    <td><img src="${data.return_products[i].return_product_image}" style='height: 40px;width: auto;'></img></td>
                                    <td>${return_reason}</td>

                                    </tr>
                            `;
                        }
                            
                        html += `</tbody></table>`;

                        // $('#edit_total_price').html(total_supplier_price.toFixed(2));
                        $(".edit_return_order_product_table").append(html);

                        $('#editReturnOrderProductTable').DataTable().destroy();

                        $('#editReturnOrderProductTable').DataTable({
                            // "pageLength": 5, // Number of rows per page
                            "order": [[0, 'asc']], // Default ordering by first column
                            "searching": true,  // Enable search functionality
                            // "paging": true,     // Enable pagination
                            // "info": true       // Show table information
                            // "lengthChange": true // Allow user to change page length
                        });

                        setTimeout(()=>{
                        
                        $('.dataTables_length').css('display', 'none')

                        },100)

                        $('#edit-return-order-modal').modal('show');
                    }    

                }

            },
            error: function() {
                swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
            }
        });
    }

    $('#edit_return_order_form').on('submit', function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var error = 0;

        $('input[name="return_quantity[]"]').each(function () {

            var value = $(this).val();        
            var dataId = $(this).data('id');

            if(value == '' || value == null)
            {
                error = 1;
                $('#edit_return_quantity_error_'+dataId).show();
            }
        });

        $('input[name="return_product_image[]"]').each(function (index, input) {
            
            var file = input.files[0];  
            var dataId = $(this).data('id');

            var imgs = $('.image_container_'+dataId).find('img').attr('src');
            console.log(imgs);

            if (imgs == '' || imgs == null || imgs == undefined) 
            {   
                if(!file)
                {
                    error = 1;
                    $('#edit_return_image_error_'+dataId).show();
                }
            }
        });

        $('textarea[name="return_reason[]"]').each(function () {

            var value = $(this).val();        
            var dataId = $(this).data('id');

            if(value == '' || value == null)
            {
                error = 1;
                $('#edit_return_reason_error_'+dataId).show();
            }
        });

        //IMAGE VALIDATION
        // Get all input fields with the same name "return_product_image[]"
        const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
        const maxFileSize = 5 * 1024 * 1024; // 5 MB
        const imageInputs = $('input[name="return_product_image[]"]');
        let isValid = true;
        let errorMessages = [];

        // Loop through each input field
        imageInputs.each(function(index, input) {
            const file = input.files[0]; // Get the single file from the input

            // Check if a file is selected
            if (file) {
                const fileExtension = file.name.split('.').pop().toLowerCase();

                // Validate file extension
                if ($.inArray(fileExtension, allowedExtensions) === -1) {
                    isValid = false;
                    // errorMessages.push('Invalid file extension for: ' + file.name);
                    errorMessages.push(`${file.name} - Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.`);
                }

                // Validate file size
                if (file.size > maxFileSize) {
                    isValid = false;
                    errorMessages.push('File too large (max 5 MB): ' + file.name);
                }

                // Validate file type (optional)
                if (!file.type.startsWith('image/')) {
                    isValid = false;
                    // errorMessages.push('Selected file is not an image: ' + file.name);
                }
            }
        });

        // If not valid, show error messages
        if (!isValid) {
            // alert("Errors:\n" + errorMessages.join("\n"));
            

            const wrapper = document.createElement('div');
            wrapper.innerHTML = '<ul>' + errorMessages.map(msg => `<li>${msg}</li>`).join('') + '</ul>';
            swal({
                title: "<?= __("Error") ?>", 
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>", 
                allowOutsideClick: "true" 
            });
            return false;
        }

        if(error == 0)
        {

            $('.edit_return_quantity_error').hide();
            $('.edit_return_image_error').hide();
            $('.edit_return_reason_error').hide();

            $('.edit_return_order_btn').attr('disabled','disabled');

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,  // Important: Prevent jQuery from setting contentType
                processData: false,  // Important: Prevent jQuery from processing data
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                            
                            $('.edit_return_order_btn').removeAttr('disabled');

                            $('#edit-return-order-modal').modal('hide');

                            returnOrderTable.clear().rows.add(response.data).draw();

                            swal('<?= __('Success') ?>', '<?= __('The return order request has been saved.') ?>', 'success');
                },
                error: function(xhr, status, error) {

                    $('.edit_return_order_btn').removeAttr('disabled');
                    swal('<?= __('Failed') ?>', '<?= __('Failed to save return order request. Please try again.') ?>', 'error');
                }
            });
        }
    });

    function deleteReturnProductImage(returnProductId)
    {
        var imageId = returnProductId;

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'deleteImage']) ?>",
            type: 'POST',
            dataType: 'json',
            data: {
                image_id: imageId
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                if (response.status === 'success') {

                    $(".image_container_"+returnProductId).remove();

                    swal('Success', response.message, 'success');

                } else {
                    swal('Failed', response.message, 'error');
                }
            },
            error: function(xhr) {
                swal('Failed', 'Failed to delete image. Please try again.', 'error');
            }
        });
    }

    $('#customReturnOrderSearchBox').on('keyup', function () {
        returnOrderTable.search(this.value).draw();
    });

    $('.return_order_filter').on('click', function () {

        var return_status = $("#filterReturnStatus option:selected").val();
        var status = $("#filterReturnOrderStatus option:selected").val();
        var filterStatusValue = status === 'A' ? 'Active' : status === 'I' ? 'Inactive' : '';

        returnOrderTable.search('').columns().search('');

        if (return_status) {
            returnOrderTable.column(3).search(return_status).draw();
        }

        if (status) {
            returnOrderTable.column(4).search(filterStatusValue).draw();
        }
    });

    $('.reset_return_order_filter').on('click', function () {

        returnOrderTable.search('').columns().search('').draw();
    });


    //SUPPLIER PAYMENT
    var supplierPaymentTable = $("#supplierPaymentTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": [
            { "data": "supplier" },
            { "data": "bill_no" },
            { "data": "payment_date" },
            { "data": "amount" },
            { "data": "due_date" },
            { "data": "payment_status" },
            {
                "data": "actions",
                "render": function (data, type, row) {
                    return data;
                }
            }
        ]
    });

    document.addEventListener('DOMContentLoaded', function () {
        const paymentFilterButton = document.querySelector('.btn.supplier-payments-menu-toggle');
        const paymentFilterBodyContainer = document.getElementById('supplier-payments-filter-body-container');

        paymentFilterButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (paymentFilterBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                paymentFilterBodyContainer.classList.remove('showing');
                paymentFilterBodyContainer.classList.add('hiding');
                paymentFilterBodyContainer.addEventListener('animationend', function () {
                    paymentFilterBodyContainer.classList.remove('hiding');
                    paymentFilterBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                paymentFilterBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                paymentFilterBodyContainer.classList.add('showing');
            }
        });
    });

    $('#customSupplierPaymentSearchBox').on('keyup', function () {
        supplierPaymentTable.search(this.value).draw();
    });

    $('.view-add-supplier-payment-modal').on('click', function() {

        $('#add_supplier_payment_form')[0].reset();
        $('#cheque_div').hide();
        $('#cash_div').hide();
        $('#add-supplier-payment-modal').modal('show');
    });

    $('#payment-mode').on('change', function () {

        payment_mode = this.value;
        if(payment_mode == '<?= __('cheque') ?>')
        {
            $('#payee_name_error').hide();
            $('#payment_mode_error').hide();
            $('#cheque_div').show();
            $('#cash_div').hide();

            $('#payment-mode').removeClass('is-invalid');
        }
        else if(payment_mode == '<?= __('cash') ?>')
        {
            $('#cheque_no_error').hide();
            $('#cheque_date_error').hide();
            $('#bank_name_error').hide();
            $('#cheque_copy_error').hide();
            $('#payment_mode_error').hide();

            $('input[name="cheque_copy"]').val('');
            $('#cheque_div').hide();
            $('#cash_div').show();

            $('#payment-mode').removeClass('is-invalid');
        }
        else
        {
            $('#payment_mode_error').show();
            $('#payment-mode').addClass('is-invalid');
            $('input[name="cheque_copy"]').val('');
            $('#cheque_div').hide();
            $('#cash_div').hide();
        }

    });

    $('#payment-bill-select').on('change', function () {
        var purchase_order_ids = $(this).val(); // Get selected bill IDs (array)

        if (!purchase_order_ids || purchase_order_ids.length === 0) {
            $('#amount').val('0.00');
            return false;
        }

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierReturnOrders', 'action' => 'getMultiplePurchaseOrders']) ?>",
            type: 'POST',
            data: { purchase_order_ids: purchase_order_ids },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {
                if (data.status === 'success') {
                    var totalAmount = 0;
                    var billAmounts = {};

                    data.orders.forEach(function(order) {
                        var billAmount = 0;
                        order.order_products.forEach(function(item) {
                            var supplierPrice = parseFloat(item.supplier_price) || 0; // Use the new supplier_price field
                            var approvedQuantity = parseInt(item.approved_quantity) || 0;

                            billAmount += supplierPrice * approvedQuantity;
                        });

                        billAmounts[order.order_request.id] = billAmount.toFixed(2); // Store bill amount
                        totalAmount += billAmount; // Add to total
                    });

                    $('#amount').val(totalAmount.toFixed(2)); // Display total amount

                    // Store bill amounts in hidden fields for submission
                    $('#billAmountsContainer').html('');
                    Object.keys(billAmounts).forEach(function(billId) {
                        $('#billAmountsContainer').append(
                            `<input type="hidden" name="bill_amounts[${billId}]" value="${billAmounts[billId]}">`
                        );
                    });
                }
                else {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch purchase order details. Please try again.') ?>', 'error');
                }
            },
            error: function(xhr, status, error) {
                swal('<?= __('Failed') ?>', '<?= __('Failed to fetch purchase order details. Please try again.') ?>', 'error');
            }
        });
    });

    function previewChequeCopy(event) {
        const input = event.target;
        const previewContainer = document.querySelector('.cheque-preview');
        const file = input.files[0]; // Get the first file

        // Clear any previous content
        previewContainer.innerHTML = '';

        if (file) {

            $('#cheque_copy_error').hide();
            $('#cheque_copy').removeClass('is-invalid');

            const reader = new FileReader();

            reader.onload = function (e) {
                // Create an image element
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.width = '100px'; // Set width of the image
                img.style.height = '100px'; // Set height of the image
                img.style.marginRight = '10px'; // Space between images

                // Create the remove button
                const removeButton = document.createElement('button');
                removeButton.innerHTML = '&times;'; // Use HTML entity for "X"
                removeButton.style.color = 'red'; // Set color for the "X"
                removeButton.style.background = 'none'; // Remove background
                removeButton.style.border = 'none'; // Remove border
                removeButton.style.cursor = 'pointer'; // Change cursor to pointer
                removeButton.style.backgroundColor = 'lightgray'; // Change cursor to pointer
                removeButton.type = 'button'; // Set button type
                removeButton.className = 'cheque-remove-button';
                removeButton.onclick = function () {
                    previewContainer.innerHTML = ''; // Clear preview
                    input.value = ''; // Clear input field
                };

                // Append image and remove button to the preview container
                previewContainer.appendChild(img);
                previewContainer.appendChild(removeButton);
            };

            reader.readAsDataURL(file); // Read the file as a data URL
        }
    }

    function previewReceipt(event) {
        const input = event.target;
        const previewContainer = document.querySelector('.receipt-preview');
        const file = input.files[0]; // Get the first file

        // Clear any previous content
        previewContainer.innerHTML = '';

        if (file) {

            $('#receipt_error').hide();
            $('#receipt').removeClass('is-invalid');

            const reader = new FileReader();

            reader.onload = function (e) {
                // Create an image element
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.width = '100px'; // Set width of the image
                img.style.height = '100px'; // Set height of the image
                img.style.marginRight = '10px'; // Space between images

                // Create the remove button
                const removeButton = document.createElement('button');
                removeButton.innerHTML = '&times;'; // Use HTML entity for "X"
                removeButton.style.color = 'red'; // Set color for the "X"
                removeButton.style.background = 'none'; // Remove background
                removeButton.style.border = 'none'; // Remove border
                removeButton.style.cursor = 'pointer'; // Change cursor to pointer
                removeButton.style.backgroundColor = 'lightgray'; // Change cursor to pointer
                removeButton.type = 'button'; // Set button type
                removeButton.className = 'receipt-remove-button';
                removeButton.onclick = function () {
                    previewContainer.innerHTML = ''; // Clear preview
                    input.value = ''; // Clear input field
                };

                // Append image and remove button to the preview container
                previewContainer.appendChild(img);
                previewContainer.appendChild(removeButton);
            };

            reader.readAsDataURL(file); // Read the file as a data URL
        }
    }

    $('#add_supplier_payment_form').on('submit', function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var error = 0;

        // var selectedBills = $("#payment-bill-select").val(); // Get multiple selected bills
        // if (!selectedBills || selectedBills.length === 0) {
        //     error = 1;
        //     $('#payment_bill_select_error').show();
        //     $('#payment-bill-select').next('.select2-container').find('.select2-selection').addClass('is-invalid');
        // }

        if ($("#payment-showrooms-select").val() == '' || $("#payment-showrooms-select").val() == null) {
            error = 1;
            $('#payment_showroom_error').show();
            $('#payment-showrooms-select').next('.select2-container').find('.select2-selection').addClass('is-invalid');
        }
        if ($("#payment-mode").val() == '' || $("#payment-mode").val() == null) {
            error = 1;
            $('#payment_mode_error').show();
            $('#payment-mode').addClass('is-invalid');
        }
        if ($("#amount").val() == '' || $("#amount").val() == null) {
            error = 1;
            $('#payment_amount_error').show();
            $('#amount').addClass('is-invalid');
        }
        if ($("#payment_date").val() == '' || $("#payment_date").val() == null) {
            error = 1;
            $('#payment_date_error').show();
            $('#payment_date').addClass('is-invalid');
        }

        if ($("#payment-mode").val() == '<?= __('cheque') ?>') {
            if ($("#cheque_no").val() == '' || $("#cheque_no").val() == null) {
                error = 1;
                $('#cheque_no_error').show();
                $('#cheque_no').addClass('is-invalid');
            }
            if ($("#cheque_date").val() == '' || $("#cheque_date").val() == null) {
                error = 1;
                $('#cheque_date_error').show();
                $('#cheque_date').addClass('is-invalid');
            }
            if ($("#bank_name").val() == '' || $("#bank_name").val() == null) {
                error = 1;
                $('#bank_name_error').show();
                $('#bank_name').addClass('is-invalid');
            }
            if ($("#cheque_copy").val() == '' || $("#cheque_copy").val() == null) {
                error = 1;
                $('#cheque_copy_error').show();
                $('#cheque_copy').addClass('is-invalid');
            }
        } else if ($("#payment-mode").val() == '<?= __('cash') ?>') {
            if ($("#payee_name").val() == '' || $("#payee_name").val() == null) {
                error = 1;
                $('#payee_name_error').show();
                $('#payee_name').addClass('is-invalid');
            }
        }

        if ($("#receipt_number").val() == '' || $("#receipt_number").val() == null) {
            error = 1;
            $('#receipt_number_error').show();
            $('#receipt_number').addClass('is-invalid');
        }

        if ($("#receipt").val() == '' || $("#receipt").val() == null) {
            error = 1;
            $('#receipt_error').show();
            $('#receipt').addClass('is-invalid');
        }

        if (error == 0) {
            $('#payment_bill_select_error').hide();
            $('#payment_showroom_error').hide();
            $('#payment_mode_error').hide();
            $('#payment_amount_error').hide();
            $('#payment_date_error').hide();
            $('.add_supplier_payment_btn').attr('disabled', 'disabled');

            // Append multiple bill data
            // selectedBills.forEach((billId, index) => {
            //     formData.append(`bills[${index}]`, billId);
            // });

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,  
                processData: false,  
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status == '<?= __('success') ?>') {
                        $('#totalPaymentsDone').text(response.totalPaymentsDoneFormatted);
                        $('#totalPendingPayments').text(response.totalPendingPaymentsFormatted);
                        $('#totalPendingDue').text(response.totalPendingDueFormatted);

                        // selectedBills.forEach((billId) => {
                        //     $("#payment-bill-select option[value='" + billId + "']").remove();
                        // });

                        $('.add_supplier_payment_btn').removeAttr('disabled');
                        $('#add-supplier-payment-modal').modal('hide');
                        $('.receipt-preview').html('');
                        $('#payment-showrooms-select').val(null).trigger('change');

                        supplierPaymentTable.clear().rows.add(response.data).draw();
                        purchaseOrderTable.clear().rows.add(response.purchase_orders).draw();

                        swal('<?= __('Success') ?>', '<?= __('Supplier payment requests have been saved.') ?>', 'success');
                    } else {
                        $('.add_supplier_payment_btn').removeAttr('disabled');
                        swal('<?= __('Failed') ?>', '<?= __('Failed to save supplier payment. Please try again.') ?>', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    $('.add_supplier_payment_btn').removeAttr('disabled');
                    swal('<?= __('Failed') ?>', '<?= __('Failed to save supplier payment. Please try again.') ?>', 'error');
                }
            });
        }
    });


    function openViewSupplierPaymentModal(supplierPaymentId)
    {
        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'SupplierPayment', 'action' => 'view']) ?>"+"/"+supplierPaymentId,
            method: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(data) {

                if(data.status == 'success')
                {   

                    $('.view_supplier_payment_container').html('');

                    if(data.supplier_payment.payment_mode) {

                        if(data.supplier_payment.payment_mode == '<?= __('cheque') ?>')
                        {
                            var payment_method = `

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Cheque No.') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;">${data.supplier_payment.cheque_no ? data.supplier_payment.cheque_no : '-' }</p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Date of Cheque') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;">${data.supplier_payment.cheque_date ? data.supplier_payment.cheque_date : '-' }</p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Bank Name') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;">${data.supplier_payment.bank_name ? data.supplier_payment.bank_name : '-' }</p>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Cheque Copy') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <img style="width: 200px;height: 100px;" src="${data.supplier_payment.cheque_copy}"></img>
                                    </div>
                                </div>

                                `;

                        }
                        else if (data.supplier_payment.payment_mode == '<?= __('cash') ?>') {
                             
                            var payment_method = `<div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Payee Name') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.payee_name ? data.supplier_payment.payee_name : '-' }</p>
                                </div>
                            </div>`;
                        } 
                    }

                    const receiptHtml = data.supplier_payment.receipt
                      ? `<img style="width: 200px;height: 100px;" src="${data.supplier_payment.receipt}" alt="Receipt Image">`
                      : `<p class="ps-5" style="color: black;">-</p>`;

                    var html = `

                            <div class="form-group row">
                                <label for="category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Bill No') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.supplier_purchase_order.bill_no ? data.supplier_payment.supplier_purchase_order.bill_no : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="sub-category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Supplier Name') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.supplier.name ? data.supplier_payment.supplier.name : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-size" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Showroom') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.showroom.name ? data.supplier_payment.showroom.name : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="brand" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Mode of Payment') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.payment_mode ? data.supplier_payment.payment_mode : '-' }
                                    </p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Amount') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.amount ? data.supplier_payment.amount : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Payment Date') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.payment_date ? data.supplier_payment.payment_date : '-' }</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Payment Status') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.supplier_purchase_order.payment_status ? data.supplier_payment.supplier_purchase_order.payment_status : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Receipt Number') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">${data.supplier_payment.receipt_number ? data.supplier_payment.receipt_number : '-'}</p>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="product-weight" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Receipt') ?></label>
                                <div class="col-sm-5 ps-5">
                                  ${receiptHtml}
                                </div>
                            </div>

                            ${payment_method}

                    `;

                    $('.view_supplier_payment_container').append(html);
                    $('#view-supplier-payment-modal').modal('show');
                }

            },
            error: function() {
                swal('<?= __('Error') ?>', '<?= __('Error fetching data. Please try again.') ?>', 'error');
            }
        });
    }

    $('.supplier_payment_filter').on('click', function () {

        var payment = $("#filterSupplierPaymentStatus option:selected").val();

        supplierPaymentTable.search('').columns().search('');

        if (payment) {
            supplierPaymentTable.column(5).search(payment, true, false, false).draw();
        }

    });

    $('.reset_supplier_payment_filter').on('click', function () {

        supplierPaymentTable.search('').columns().search('').draw();

    });

</script>
<?php $this->end(); ?>