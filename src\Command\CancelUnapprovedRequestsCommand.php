<?php
declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\I18n\FrozenTime;
use Cake\ORM\TableRegistry;
use Cake\Mailer\Mailer;
use Cake\Log\Engine\FileLog;
use Cake\Log\Log;
use Cake\Core\Configure;

class CancelUnapprovedRequestsCommand extends Command
{
    public function execute(Arguments $args, ConsoleIo $io)
    {
        // Setup logging
        Log::setConfig('stock_request_cron', [
            'className' => FileLog::class,
            'path' => LOGS,
            'file' => 'stock_request_cron',
            'levels' => ['notice', 'info', 'debug', 'error'],
            'scopes' => false,
        ]);

        Log::write('info', '⏱️ Stock request auto-cancel cron started.', 'stock_request_cron');

        // Load necessary models
        $stockRequestsTable = TableRegistry::getTableLocator()->get('StockRequests');
        $siteSettingsTable = TableRegistry::getTableLocator()->get('SiteSettings');
        $productStocksTable = TableRegistry::getTableLocator()->get('ProductStocks');

        // Step 2: Get auto cancel hours from settings
        $autoCancelDuration = $siteSettingsTable->find()
            ->select(['stock_request_auto_cancel_duration'])
            ->first();

        $hours = $autoCancelDuration ? (int)$autoCancelDuration->stock_request_auto_cancel_duration : 2;
        $expiredTime = new FrozenTime("-{$hours} hours");

        Log::write('info', "🔎 Auto-cancel duration set to {$hours} hour(s).", 'stock_request_cron');

        // Step 3: Find unapproved requests older than X hours
        $requests = $stockRequestsTable->find()
            ->where([
                'requestor_type' => 'Showroom',
                'request_status' => 'Pending',
                'status' => 'A',
                'created <' => $expiredTime
            ])
            ->contain(['StockRequestItems'])
            ->all();

        $requestCount = $requests->count();
        Log::write('info', "📦 Found {$requestCount} expired stock requests.", 'stock_request_cron');

        if ($requests->isEmpty()) {
            $msg = 'ℹ️ No expired requests found.';
            $io->out($msg);
            Log::write('info', $msg, 'stock_request_cron');
            return;
        }

        // Step 4: Cancel the request & unreserve stock
        foreach ($requests as $request) {
            $requestId = $request->id;
            $request->status = 'D';
            $stockRequestsTable->save($request);
            Log::write('info', "❌ Request ID {$requestId} marked as cancelled.", 'stock_request_cron');

            $sourceLocationId = $request->warehouse_id ?: $request->to_showroomID;
            $locationType = $request->warehouse_id ? 'warehouse_id' : 'showroom_id';

            foreach ($request->stock_request_items as $item) {
                $stockConditions = [
                    'product_id' => $item->product_id,
                    $locationType => $sourceLocationId
                ];

                if (!empty($item->product_variant_id)) {
                    $stockConditions['product_variant_id'] = $item->product_variant_id;
                }
                if (!empty($item->product_attribute_id)) {
                    $stockConditions['product_attribute_id'] = $item->product_attribute_id;
                }

                $productStock = $productStocksTable->find()
                    ->where($stockConditions)
                    ->first();

                if ($productStock) {
                    $originalReserved = $productStock->reserved_stock;
                    $newReserved = max(0, $originalReserved - $item->requested_quantity);
                    $productStock->reserved_stock = $newReserved;
                    $productStocksTable->save($productStock);

                    Log::write(
                        'info',
                        "🔄 Updated reserved stock for Product ID {$item->product_id} (Variant: {$item->product_variant_id}) ".
                        "from {$originalReserved} to {$newReserved} at {$locationType} {$sourceLocationId}.",
                        'stock_request_cron'
                    );
                } else {
                    Log::write(
                        'warning',
                        "⚠️ No matching stock found for Product ID {$item->product_id} at {$locationType} {$sourceLocationId}.",
                        'stock_request_cron'
                    );
                }
            }
        }

        $finalMsg = '✅ Expired requests cancelled and stock unreserved.';
        $io->out($finalMsg);
        Log::write('info', $finalMsg, 'stock_request_cron');
    }



}
