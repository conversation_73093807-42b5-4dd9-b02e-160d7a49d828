<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class WarehouseStockReturnController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Suppliers;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $Roles;
    protected $SupplierProducts;
    protected $StockMovements;
    protected $StockMovementItems;
    protected $StockReturns;
    protected $StockReturnItems;
    protected $ProductStocks;
    protected $Users;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->StockMovementItems = $this->fetchTable('StockMovementItems');
        $this->Roles = $this->fetchTable('Roles');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->StockReturns = $this->fetchTable('StockReturns');
        $this->StockReturnItems = $this->fetchTable('StockReturnItems');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Users = $this->fetchTable('Users');
    }
    
    public function index()
    {
        // Get the currently authenticated user
        $requested_user = $this->Authentication->getIdentity();

        $warehouse_id = null;

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'warehouse manager') {
                $warehouse = $this->Warehouses->find()
                    ->select(['id'])
                    ->where(['manager_id' => $requested_user->id])
                    ->first();
                $warehouse_id = $warehouse ? $warehouse->id : null;
            } elseif (strtolower($role->name) === 'warehouse assistant') {
                $warehouse = $this->Warehouses->find()
                    ->select(['id'])
                    ->where(['assistant_id' => $requested_user->id])
                    ->first();
                $warehouse_id = $warehouse ? $warehouse->id : null;
            }
        }

        $query = $this->StockReturns->find()
            ->where([
                'StockReturns.return_from' => 'Warehouse'
            ])
            ->leftJoinWith('StockRequests.SupplierPurchaseOrders')
            ->leftJoin(
                ['Suppliers' => 'suppliers'],
                ['StockReturns.return_id_to = Suppliers.id']
            )
            ->leftJoin(
                ['Warehouses' => 'warehouses'],
                ['StockReturns.return_id_from = Warehouses.id']
            )
            ->select([
                'StockReturns.id',
                'StockReturns.return_id_from',
                'StockReturns.return_id_to',
                'StockReturns.return_date',
                'StockReturns.created',
                'SupplierPurchaseOrders.bill_no',
                'Suppliers.name',
                'Warehouses.name',
            ])
            ->order(['StockReturns.created' => 'DESC']);

        // Apply warehouse_id filter if available
        if (!empty($warehouse_id)) {
            $query->where(['StockReturns.return_id_from' => $warehouse_id]);
        }

        $stock_returns = $query->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('currencySymbol', 'decimalSeparator', 'thousandSeparator', 'stock_returns', 'dateFormat', 'timeFormat'));

    }

    // public function add()
    // {
    //     $this->request->allowMethod(['post']); // Ensure only POST requests are allowed
    //     $this->autoRender = false; // Prevent template rendering

    //     $return_request = $this->StockReturns->newEmptyEntity();
    //     $requested_user = $this->Authentication->getIdentity();
    //     $data = $this->request->getData();

    //     // Fix return_date format
    //     if (!empty($data['return_date'])) {
    //         try {
    //             $data['return_date'] = (new \DateTime($data['return_date']))->format('Y-m-d H:i:s');
    //         } catch (\Exception $e) {
    //             return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode(['error' => 'Invalid return date format.']));
    //         }
    //     }

    //     // Fetch Stock Movement ID
    //     $stockMovement = $this->StockMovements->find()
    //         ->select(['id'])
    //         ->where(['StockMovements.referenceID' => $data['stock_request_id']])
    //         ->first();

    //     if (!$stockMovement) {
    //         return $this->response->withType('application/json')
    //             ->withStringBody(json_encode(['error' => 'Stock Movement not found']));
    //     }

    //     // Correct the stock_movement_id key
    //     $data['stock_movement_id'] = $stockMovement->id;
    //     $data['return_request_by'] = $requested_user->id;
    //     $data['return_from'] = 'Warehouse';
    //     $data['return_id_from'] = $data['warehouse_id'];
    //     $data['return_to'] = 'Supplier';
    //     $data['return_id_to'] = $data['supplier_id'];
    //     $data['verify_status'] = 'Approved';
        
    //     $return_request = $this->StockReturns->patchEntity($return_request, $data);

    //     if ($this->StockReturns->save($return_request)) {

    //         $this->saveStockReturnItems($return_request);

    //         // Fetch return orders for response
    //         $query_return_request = $this->StockReturns->find()
    //             ->where([
    //                 'StockReturns.return_from' => 'Warehouse',
    //                 'StockReturns.return_id_from' => $data['warehouse_id']
    //             ])
    //             ->leftJoinWith('StockRequests.SupplierPurchaseOrders') // Join SupplierPurchaseOrders via StockRequests
    //             ->leftJoin(
    //                 ['Suppliers' => 'suppliers'], 
    //                 ['StockReturns.return_id_to = Suppliers.id'] // Join Suppliers based on return_id_to
    //             )
    //             ->select([
    //                 'StockReturns.id',
    //                 'StockReturns.return_id_to',
    //                 'StockReturns.return_date',
    //                 'StockReturns.created',
    //                 'SupplierPurchaseOrders.bill_no',
    //                 'Suppliers.name', // Fetch supplier name
    //             ])
    //             ->order(['StockReturns.created' => 'DESC'])
    //             ->toArray();

    //         $dateFormat = Configure::read('Settings.DATE_FORMAT');
    //         $timeFormat = Configure::read('Settings.TIME_FORMAT');

    //         $return_orders = [];
    //         foreach ($query_return_request as $order) {
                
    //             $billNo = $order->_matchingData['SupplierPurchaseOrders']->bill_no ?? 'N/A';
    //             $supplierName = $order['Suppliers']['name'] ?? 'N/A';

    //             $return_orders[] = [
    //                 'return_date' => $order->return_date->format($dateFormat),
    //                 'bill_no' => h($billNo),
    //                 'supplier_name' => h($supplierName),
    //                 'actions' => '
    //                     <a onClick="openViewReturnOrderModal(' . $order->id . ')" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'
    //             ];
    //         }

    //         return $this->response->withType('application/json')
    //             ->withStringBody(json_encode(['data' => $return_orders]));
    //     }

    //     return $this->response->withType('application/json')
    //         ->withStringBody(json_encode(['error' => 'The return order request could not be saved. Please, try again.']));
    // }

    public function add()
    {
        $returnRequest = $this->StockReturns->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            $requestedUser = $this->Authentication->getIdentity();

            // Format the return_date
            if (!empty($data['return_date'])) {
                try {
                    $data['return_date'] = (new \DateTime($data['return_date']))->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    $this->Flash->error(__('Invalid return date format.'));
                    return $this->redirect($this->referer());
                }
            }

            // Fetch Stock Movement
            $stockMovement = $this->StockMovements->find()
                ->select(['id'])
                ->where(['StockMovements.referenceID' => $data['stock_request_id']])
                ->first();

            if (!$stockMovement) {
                $this->Flash->error(__('Stock Movement not found.'));
                return $this->redirect($this->referer());
            }

            // Set additional return data
            $data['stock_movement_id'] = $stockMovement->id;
            $data['return_request_by'] = $requestedUser->id;
            $data['return_from'] = 'Warehouse';
            $data['return_id_from'] = $data['warehouse_id'];
            $data['return_to'] = 'Supplier';
            $data['return_id_to'] = $data['supplier_id'];
            $data['verify_status'] = 'Approved';

            // Patch entity
            $returnRequest = $this->StockReturns->patchEntity($returnRequest, $data);

            if ($this->StockReturns->save($returnRequest)) {
                // Save return items
                $this->saveStockReturnItems($returnRequest);

                $this->Flash->success(__('The return order request has been saved.'));

                return $this->redirect(['action' => 'index']); // redirect to appropriate index/list page
            }

            $this->Flash->error(__('The return order request could not be saved. Please, try again.'));
        }

        // $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
        //     ->where([
        //         'SupplierPurchaseOrders.delivery_status' => 'Delivered',
        //         // 'SupplierPurchaseOrders.id_deliver_to' => 5,
        //         'SupplierPurchaseOrders.status' => 'A'
        //     ])
        //     ->all();

        // $purchase_order_bills = [];

        // // Loop through categories
        // foreach ($supplierPurchaseOrders as $purchaseOrder) {
        //     $purchase_order_bills[$purchaseOrder->id] = $purchaseOrder->bill_no;
        // }

        // Get the logged-in user
        $requested_user = $this->Authentication->getIdentity();

        // Fetch warehouses based on user role
        $userRole = null;
        $warehouses = [];
        $warehouse = null;
        $warehouseId = null;

        if (!empty($requested_user)) {
            
            $user = $this->Users->get($requested_user->id, [
                'contain' => ['Roles'], // Assuming the Users table is associated with Roles
            ]);

            $userRole = strtolower($user->role->name);

            if ($userRole === 'warehouse manager') {
                // Fetch only the warehouse managed by this user
                $warehouse = $this->Warehouses->find()
                    ->where(['manager_id' => $requested_user->id, 'status' => 'A'])
                    ->first();

                if ($warehouse) {
                    $warehouseId = $warehouse->id;
                }

            } else {
                // Fetch all active warehouses for other roles
                $warehouses = $this->Warehouses->find()
                    ->where(['status' => 'A'])
                    ->order(['name' => 'ASC'])
                    ->toArray();
            }
        }

        // Fetch purchase order bills for selected warehouse
        $purchase_order_bills = [];
        if (!empty($warehouseId)) {
            $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
                ->where([
                    'SupplierPurchaseOrders.delivery_status' => 'Delivered',
                    'SupplierPurchaseOrders.status' => 'A',
                    'SupplierPurchaseOrders.id_deliver_to' => $warehouseId
                ])
                ->all();

            foreach ($supplierPurchaseOrders as $purchaseOrder) {
                $purchase_order_bills[$purchaseOrder->id] = $purchaseOrder->bill_no;
            }
        }

        $this->set(compact('purchase_order_bills', 'warehouses', 'warehouse', 'userRole'));
    }

    public function getBillsByWarehouse()
    {
        $this->request->allowMethod(['ajax']);
        $warehouseId = $this->request->getQuery('warehouse_id');

        $purchase_order_bills = [];

        $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
            ->where([
                'SupplierPurchaseOrders.delivery_status' => 'Delivered',
                'SupplierPurchaseOrders.status' => 'A',
                'SupplierPurchaseOrders.id_deliver_to' => $warehouseId
            ])
            ->all();

        foreach ($supplierPurchaseOrders as $purchaseOrder) {
            $purchase_order_bills[$purchaseOrder->id] = $purchaseOrder->bill_no;
        }

        $response = [
            'status' => 'success',
            'purchase_order_bills' => $purchase_order_bills,
        ];

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    protected function saveStockReturnItems($return_request)
    {
        if (!empty($return_request->product_id) && is_array($return_request->product_id)) {
            $stock_return_id = $return_request->id;
            $product_id = $return_request->product_id;
            $product_variant_id = $return_request->product_variant_id;
            $product_attribute_id = $return_request->product_attribute_id;
            $product_quantity = $return_request->quantity;
            $product_return_quantity = $return_request->return_quantity;
            $return_product_image = $return_request->return_product_image;
            $return_reason = $return_request->return_reason;
            $supplier_purchase_order_id = $return_request->purchase_order_id;
            $warehouse_id = $return_request->warehouse_id;

            // echo '<pre>';print_r($product_return_quantity);die;

            // Delete existing stock return records
            $this->StockReturnItems->deleteAll(['stock_return_id' => $stock_return_id]);

            // Clean return product images: remove empty files
            // $filteredImages = [];
            // if (!empty($return_product_image) && is_array($return_product_image)) {
            //     foreach ($return_product_image as $uploadedFile) {
            //         if ($uploadedFile instanceof \Laminas\Diactoros\UploadedFile && $uploadedFile->getError() === UPLOAD_ERR_OK) {
            //             $filteredImages[] = $uploadedFile;
            //         }
            //     }
            // }
            // $return_product_image = $filteredImages;

            for ($i = 0; $i < sizeof($product_id); $i++) {

                $return_images = [];

                if (!empty($return_product_image[$product_id[$i]])) { // Ensure images exist for the specific product
                    foreach ($return_product_image[$product_id[$i]] as $defect_image) { 
                        if ($defect_image instanceof \Laminas\Diactoros\UploadedFile && $defect_image->getError() === UPLOAD_ERR_OK) {
                            $fileName = trim($defect_image->getClientFilename());

                            if (!empty($fileName)) {
                                $imageTmpName = $defect_image->getStream()->getMetadata('uri');
                                $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                                $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                                $filePath = Configure::read('Constants.ORDER_RETURN_DEFECT_IMAGE');
                                $folderPath = $uploadFolder . $filePath;
                                $targetdir = WWW_ROOT . $folderPath;
                                $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                                $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                                // Upload the image using the Media component
                                $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);

                                if ($uploadResult !== 'Success') {
                                    $this->Flash->error(__('Image could not be uploaded. Please, try again.'));
                                    return $this->redirect(['action' => 'add']);
                                } else {
                                    $return_images[] = $folderPath . $imageFile; // Store uploaded image path
                                }
                            }
                        }
                    }
                }

                // If only one image was uploaded, store it as a single string; otherwise, store multiple images as a CSV string
                $return_image = !empty($return_images) ? implode(',', $return_images) : null;

                // Prepare data for insertion
                $stockReturnData = [
                    'stock_return_id' => $stock_return_id,
                    'product_id' => $product_id[$i],
                    'quantity' => $product_quantity[$i],
                    'return_quantity' => $product_return_quantity[$i],
                    'return_product_image' => $return_image,
                    'return_reason' => $return_reason[$i],
                ];

                // Only add product_variant_id if available
                // Ensure product_variant_id is an integer or null
                if (!empty($product_variant_id[$i]) && $product_variant_id[$i] !== 'null') {
                    $stockReturnData['product_variant_id'] = is_numeric($product_variant_id[$i]) ? (int) $product_variant_id[$i] : null;
                }

                // Ensure product_attribute_id is an integer or null
                if (!empty($product_attribute_id[$i]) && $product_attribute_id[$i] !== 'null') {
                    $stockReturnData['product_attribute_id'] = is_numeric($product_attribute_id[$i]) ? (int) $product_attribute_id[$i] : null;
                }

                // Save stock return item
                $stockReturnItem = $this->StockReturnItems->newEntity($stockReturnData);
                if (!$this->StockReturnItems->save($stockReturnItem)) {
                    return false;
                }

                $supplierOrderItemQuery = $this->SupplierPurchaseOrdersItems->find()
                    ->where([
                        'supplier_purchase_order_id' => $supplier_purchase_order_id,
                        'product_id' => $product_id[$i],
                    ]);

                if (!empty($product_variant_id[$i]) && $product_variant_id[$i] !== 'null') {
                    $supplierOrderItemQuery->where(['product_variant_id' => (int)$product_variant_id[$i]]);
                } elseif (!empty($product_attribute_id[$i]) && $product_attribute_id[$i] !== 'null') {
                    $supplierOrderItemQuery->where(['product_attribute_id' => (int)$product_attribute_id[$i]]);
                }

                $supplierOrderItem = $supplierOrderItemQuery->first();

                if ($supplierOrderItem) {
                    $supplierOrderItem->approved_quantity -= $product_return_quantity[$i];
                    if ($supplierOrderItem->approved_quantity < 0) {
                        $supplierOrderItem->approved_quantity = 0;
                    }
                    $this->SupplierPurchaseOrdersItems->save($supplierOrderItem);
                }

                $productStockQuery = $this->ProductStocks->find()
                    ->where([
                        'warehouse_id' => $warehouse_id,
                        'product_id' => $product_id[$i],
                    ]);

                if (!empty($product_variant_id[$i]) && $product_variant_id[$i] !== 'null') {
                    $productStockQuery->where(['product_variant_id' => (int)$product_variant_id[$i]]);
                } elseif (!empty($product_attribute_id[$i]) && $product_attribute_id[$i] !== 'null') {
                    $productStockQuery->where(['product_attribute_id' => (int)$product_attribute_id[$i]]);
                }

                $productStock = $productStockQuery->first();

                if ($productStock) {
                    $productStock->quantity -= $product_return_quantity[$i];
                    if ($productStock->quantity < 0) {
                        $productStock->quantity = 0;
                    }
                    $this->ProductStocks->save($productStock);
                }
            }
        }

        return true;
    }

    public function view($id = null)
    {
        $return_request = $this->StockReturns->find()
            ->where(['StockReturns.id' => $id])
            ->leftJoinWith('StockRequests.SupplierPurchaseOrders') // Join SupplierPurchaseOrders via StockRequests
            ->leftJoin(
                ['Suppliers' => 'suppliers'], 
                ['StockReturns.return_id_to = Suppliers.id'] // Join Suppliers based on return_id_to
            )
            ->leftJoin(
                ['Users' => 'users'],
                ['StockReturns.return_request_by = Users.id'] // Join Users to get requestor details
            )
            ->select([
                'StockReturns.id',
                'StockReturns.return_id_to',
                'StockReturns.return_date',
                'StockReturns.created',
                'SupplierPurchaseOrders.bill_no',
                'Suppliers.name', // Supplier name
                'Users.first_name',
                'Users.last_name'  // Requestor name
            ])
            ->first();

        // echo '<pre>';print_r($return_request);die;

        if (!$return_request) {
            $this->Flash->error(__('Return request not found.'));
            return $this->redirect(['action' => 'index']);
        }

        $return_products = $this->StockReturnItems->find()
            ->where(['StockReturnItems.stock_return_id' => $id])
            ->contain([
                'Products' => [
                    'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
                    'SupplierProducts' => [
                        'fields' => ['SupplierProducts.product_id', 'SupplierProducts.supplier_price'],
                        'Suppliers' => [
                            'fields' => ['Suppliers.id', 'Suppliers.name']
                        ]
                    ],
                    'ProductVariants' => [
                        'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
                        'conditions' => ['ProductVariants.id IS NOT NULL']
                    ]
                ]
            ])
            ->toArray();

        // echo '<pre>';print_r($return_products);die;

        // Currency Format Settings
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '.';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? ',';

        foreach ($return_products as &$product) {
            // Format supplier price
            if (!empty($product->product->supplier_products)) {
                foreach ($product->product->supplier_products as &$supplierProduct) {
                    if (!empty($supplierProduct->supplier_price)) {
                        $supplierProduct->supplier_price = number_format(
                            (float)$supplierProduct->supplier_price,
                            0,
                            '',
                            $thousandSeparator
                        ) . ' ' . $currencySymbol;
                    }
                }
                unset($supplierProduct); // Unset reference
            }

            // Handle Return Images
            if (!empty($product->return_product_image)) {
                $imageUrls = explode(',', $product->return_product_image);

                $imageUrls = array_map(function ($image) {
                    return $this->Media->getCloudFrontURL(trim($image));
                }, $imageUrls);

                $product->return_product_image = implode(',', $imageUrls);
            }

            // Handle Variants
            $variantDetails = null;
            if ($product->product_variant_id && !empty($product->product->product_variants)) {
                foreach ($product->product->product_variants as $variant) {
                    if ($variant->id == $product->product_variant_id) {
                        $variantDetails = [
                            'variant_name' => $variant->variant_name,
                            'sku' => $variant->sku
                        ];
                        break;
                    }
                }
            }

            if ($variantDetails) {
                $product->product->variant_name = $variantDetails['variant_name'];
                $product->product->sku = $variantDetails['sku'];
            }

            // Handle Attributes
            if ($product->product_attribute_id) {
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $product->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                $product->product->attributes = $attributes ?: [];
            } else {
                $product->product->attributes = [];
            }
        }
        unset($product); // Unset reference

        // Now set data for view
        $this->set(compact('return_request', 'return_products'));
    }


    // public function view($id = null)
    // {
    //     $return_request = $this->StockReturns->find()
    //         ->where(['StockReturns.id' => $id])
    //         ->leftJoinWith('StockRequests.SupplierPurchaseOrders') // Join SupplierPurchaseOrders via StockRequests
    //         ->leftJoin(
    //             ['Suppliers' => 'suppliers'], 
    //             ['StockReturns.return_id_to = Suppliers.id'] // Join Suppliers based on return_id_to
    //         )
    //         ->leftJoin(
    //             ['Users' => 'users'],
    //             ['StockReturns.return_request_by = Users.id'] // Join Users to get requestor details
    //         )
    //         ->select([
    //             'StockReturns.id',
    //             'StockReturns.return_id_to',
    //             'StockReturns.return_date',
    //             'StockReturns.created',
    //             'SupplierPurchaseOrders.bill_no',
    //             'Suppliers.name', // Fetch supplier name
    //             'Users.first_name',
    //             'Users.last_name' // Fetch return requestor name
    //         ])
    //         ->first();

    //     $return_products = $this->StockReturnItems->find()
    //         ->where(['StockReturnItems.stock_return_id' => $id])
    //         ->contain([
    //             'Products' => [
    //                 'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
    //                 'SupplierProducts' => [
    //                     'fields' => ['SupplierProducts.product_id', 'SupplierProducts.supplier_price'],
    //                     'Suppliers' => [
    //                         'fields' => ['Suppliers.id', 'Suppliers.name']
    //                     ]
    //                 ],
    //                 'ProductVariants' => [
    //                     'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
    //                     'conditions' => ['ProductVariants.id IS NOT NULL']
    //                 ]
    //             ]
    //         ])->toArray();

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
    //     $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
    //     $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';

    //     foreach ($return_products as $product) {
    //         // Format supplier price
    //         if (!empty($product->product->supplier_products)) {
    //             foreach ($product->product->supplier_products as $supplierProduct) {
    //                 if (!empty($supplierProduct->supplier_price)) {
    //                     $supplierProduct->supplier_price = number_format(
    //                         (float)$supplierProduct->supplier_price, 
    //                         2, 
    //                         $decimalSeparator, 
    //                         $thousandSeparator
    //                     ) . ' ' . $currencySymbol;
    //                 }
    //             }
    //         }

    //         // if ($product->return_product_image) {
    //         //     $product->return_product_image = $this->Media->getCloudFrontURL($product->return_product_image);
    //         // }

    //         if (!empty($product->return_product_image)) {
    //             $imageUrls = explode(',', $product->return_product_image); // Convert string to array

    //             // Apply CloudFront URL transformation to each image
    //             $imageUrls = array_map(function ($image) {
    //                 return $this->Media->getCloudFrontURL(trim($image));
    //             }, $imageUrls);

    //             // Convert back to comma-separated string
    //             $product->return_product_image = implode(',', $imageUrls);
    //         }


    //         $variantDetails = null;

    //         // Fetch matching variant if variant_id exists
    //         if ($product->product_variant_id) {
    //             $filtered_variants = array_filter($product->product->product_variants, function ($variant) use ($product) {
    //                 return $variant->id == $product->product_variant_id;
    //             });

    //             if (!empty($filtered_variants)) {
    //                 $variant = reset($filtered_variants);
    //                 $variantDetails = [
    //                     'variant_name' => $variant->variant_name,
    //                     'sku' => $variant->sku
    //                 ];
    //             }
    //         }

    //         if ($variantDetails) {
    //             $product->product->variant_name = $variantDetails['variant_name'];
    //             $product->product->sku = $variantDetails['sku'];
    //         }

    //         // Fetch product attributes
    //         if ($product->product_attribute_id) {
    //             $attributes = $this->ProductAttributes->find()
    //                 ->where(['ProductAttributes.id' => $product->product_attribute_id])
    //                 ->contain([
    //                     'Attributes' => ['fields' => ['Attributes.name']],
    //                     'AttributeValues' => ['fields' => ['AttributeValues.value']]
    //                 ])
    //                 ->first();

    //             $product->product->attributes = $attributes ?: [];
    //         } else {
    //             $product->product->attributes = [];
    //         }
    //     }

    //     $response = [
    //         'status' => 'success',
    //         'return_request' => $return_request,
    //         'return_products' => $return_products
    //     ];

    //     return $this->response
    //         ->withType('application/json')
    //         ->withStringBody(json_encode($response));
    // }

    public function getSupplierPurchaseOrderById($id = null)
    {
        $order_request = $this->SupplierPurchaseOrders->get($id, contain: ['Showrooms', 'Warehouses']);

        // Get supplier_id from the SupplierPurchaseOrders
        $purchaseOrder = $this->SupplierPurchaseOrders->get($id, [
            'fields' => ['supplier_id']
        ]);

        $supplier_id = $purchaseOrder->supplier_id;

        // Fetch purchase order products and join related tables
        $order_products = $this->SupplierPurchaseOrdersItems->find()
            ->where([
                'SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id,
                'SupplierPurchaseOrdersItems.approved_quantity >' => 0
            ])
            ->contain([
                'Products' => function ($q) use ($supplier_id) {
                    return $q->select(['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'])
                        ->contain([
                            'SupplierProducts' => function ($q) use ($supplier_id) {
                                return $q->select(['SupplierProducts.product_id', 'SupplierProducts.supplier_price'])
                                    ->where(['SupplierProducts.supplier_id' => $supplier_id])
                                    ->contain([
                                        'Suppliers' => function ($q) {
                                            return $q->select(['Suppliers.id', 'Suppliers.name']);
                                        }
                                    ]);
                            },
                            'ProductVariants' => function ($q) {
                                return $q->select(['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'])
                                    ->where(['ProductVariants.id IS NOT NULL']); // Fetch only if variant exists
                            }
                        ]);
                }
            ])
            ->toArray();

        foreach ($order_products as $product) {

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }

            $variantDetails = null;

            // Check if a variant_id is present
            if ($product->product_variant_id) {
                // Fetch only variants that match the variant_id in SupplierPurchaseOrdersItems
                $filtered_variants = array_filter($product->product->product_variants, function($variant) use ($product) {
                    return $variant->id == $product->product_variant_id;
                });

                // If a matching variant is found, get its details
                if (!empty($filtered_variants)) {
                    $variant = reset($filtered_variants); // Get the first matching variant
                    $variantDetails = [
                        'variant_name' => $variant->variant_name,
                        'sku' => $variant->sku
                    ];
                }
            }

            // Assign SKU priority: Variant SKU > Product SKU
            $product->product->sku = $variantDetails['sku'] ?? $product->product->sku;

            if ($variantDetails) {
                $product->product->variant_name = $variantDetails['variant_name'];
            }

            if ($product->product_attribute_id) {
                // Assuming you have a ProductAttributes model to fetch the attributes
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $product->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                $product->product->attributes = $attributes ?: [];
            } else {
                $product->product->attributes = [];
            }
        }

        $response = [
            'status' => 'success',
            'order_request' => $order_request,
            'order_products' => $order_products
        ];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    
}
