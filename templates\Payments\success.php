<style>
.feature {
    height: 450px;
}
    .success-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
    }

    .success-message {
        font-size: 16px;
        color: #666;
        margin-bottom: 20px;
    }

    .btn {
        display: inline-block;
        background-color: #0d839b;
        color: #fff;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        text-decoration: none;
        cursor: pointer;
        transition: background 0.3s;
    }

    .btn:hover {
        background-color: #f77f00;
    }

    .order-details {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }

    .order-details h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
    }

    .order-details p {
        margin: 8px 0;
        color: #555;
    }
</style>
<div class="my-carousel-container">
    <div class="feature">
        <div class="">
            <div class="success-title">Order Successful!</div>
            <p class="success-message">Thank you for your purchase. Your order has been successfully placed.</p>

            <?php if (isset($orderInfo)): ?>
                <div class="order-details">
                    <h3>Order Details</h3>
                    <p><strong>Order ID:</strong> <?= h($orderInfo->order_number) ?></p>
                    <p><strong>Total Amount:</strong> <?= number_format($total, 0, '.', ' ') ?> <?= h($currency) ?></p>
                    <?php if ($couponCode): ?>
                        <p><strong>Coupon Used:</strong> <?= h($couponCode) ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?= $this->Html->link('Go to Homepage', ['controller' => 'Website', 'action' => 'home', 'prefix' => false], ['class' => 'btn']) ?>
            <?= $this->Html->link('View Orders', ['controller' => 'Account', 'action' => 'order', 'prefix' => false], ['class' => 'btn', 'style' => 'margin-left: 10px;']) ?>
        </div>
    </div>
</div>

<!-- Google Analytics 4 Purchase Tracking -->
<script>
window.dataLayer = window.dataLayer || [];
window.dataLayer.push({
  event: 'purchase',
  ecommerce: {
    transaction_id: '<?= h($orderId) ?>',
    affiliation: '<?= h($affiliation) ?>',
    value: <?= number_format($total, 2, '.', '') ?>,
    currency: '<?= h($currency) ?>',
    tax: <?= number_format($tax, 2, '.', '') ?>,
    shipping: <?= number_format($shipping, 2, '.', '') ?>,
    <?php if($couponCode): ?>coupon: '<?= h($couponCode) ?>',<?php endif; ?>
    items: [
      <?php foreach($orderItems as $index => $item):
         $itemPrice = number_format($item['price'], 2, '.', '');
      ?>
      {
        item_id: '<?= h($item['id']) ?>',
        item_name: '<?= h($item['name']) ?>',
        price: <?= $itemPrice ?>,
        item_category: '<?= h($item['category']) ?>',
        item_brand: '<?= h($item['brand']) ?>',
        item_variant: '<?= h($item['variant']) ?>',
        quantity: <?= (int)$item['quantity'] ?>
        <?php if(!empty($item['coupon'])): ?>, item_coupon: '<?= h($item['coupon']) ?>'<?php endif; ?>
      }<?= ($index < count($orderItems) - 1) ? ',' : '' ?>
      <?php endforeach; ?>
    ]
  }
});

// Optional: Send purchase event to console for debugging
console.log('Purchase event sent to Google Analytics:', {
  transaction_id: '<?= h($orderId) ?>',
  value: <?= number_format($total, 2, '.', '') ?>,
  currency: '<?= h($currency) ?>',
  items_count: <?= count($orderItems) ?>
});
</script>
