<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #toRemovePadding {
        margin-bottom: 0px !important;
        padding-bottom: 0px !important;
    }

    #remove-border {
        border: none;
        background-color: transparent;
    }

    .modal-backdrop {
        background-color : transparent !important;
        position: relative !important;
    }

    label {
    width: 115px !important;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div
        class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item"><a
                    href="<?= $this->Url->build(['controller' => 'ReturnsRefunds', 'action' => 'index']) ?>"><?= __('Return and Refunds') ?></a>
            </li>
            <li class="breadcrumb-item active"><?= __('View') ?></li>
        </ul>
        <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
            <small class="p-10 fw-bold"><?= __('BACK') ?></small>
            <span class="rotate me-2">⤣</span>
        </button>
    </div>

    <h5 class="m-l-10 p-t-10 p-b-10" style="color: black;"><?= __('View Return Request') ?></h5>

    <div class="section-body">
        <div class="container-fluid">
            <div class="card-body">
                <div class="container-fluid">
                    <form>
                        <div class="form-group row">
                            <label for="showroom" class="col-sm-2 col-form-label fw-bold"><?= __('Request ID') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn->id ? $orderReturn->id : '-' ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="showroom" class="col-sm-2 col-form-label fw-bold"><?= __('Customer Details') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;">
                                        
                                    <?php
                                        $user = $orderReturn['order']['customer']['user'] ?? null;

                                        $name = ($user && !empty($user['first_name']) && !empty($user['last_name'])) 
                                            ? h($user['first_name'] . ' ' . $user['last_name']) 
                                            : 'N/A';

                                        $email = ($user && !empty($user['email'])) ? h($user['email']) : 'N/A';
                                        $phone = ($user && !empty($user['mobile_no'])) ? h($user['mobile_no']) : 'N/A';
                                    ?>
                                    <strong><?= __('Name') ?>:</strong> <?= h($name) ?><br>
                                    <strong><?= __('Email') ?>:</strong> <?= h($email) ?><br>
                                    <strong><?= __('Phone') ?>:</strong> <?= h($phone) ?>

                                </p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="showroom" class="col-sm-2 col-form-label fw-bold"><?= __('Order ID') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn['order'] ? $orderReturn['order']->id : '-' ?></p>
                            </div>
                        </div>
            
                        <div class="form-group row">
                            <label for="requestedDate" class="col-sm-2 col-form-label fw-bold"><?= __('Order Date') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn['order'] ? $orderReturn['order']->order_date->format('Y-m-d') : '-' ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="requestedDate" class="col-sm-2 col-form-label fw-bold"><?= __('Pickup Charge') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn->pickup_charge ? $orderReturn->pickup_charge : '-' ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="requestedDate" class="col-sm-2 col-form-label fw-bold"><?= __('Pickup Required') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn->pickup_required ? $orderReturn->pickup_required : '-' ?></p>
                            </div>
                        </div>

                        <?php if (!empty($orderReturn->pickup_required) && $orderReturn->pickup_required === 'Yes'): ?>
                            <div class="form-group row">
                                <label for="requestedDate" class="col-sm-2 col-form-label fw-bold"><?= __('Return To') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <p class="ps-5" style="color: black;">
                                        <?= !empty($orderReturn->return_to_name) ? h($orderReturn->return_to_name) : '-' ?>
                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>


                        <div class="form-group row">
                            <label for="requestedBy" class="col-sm-2 col-form-label fw-bold"><?= __('Requested By') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn->user ? $orderReturn->user->first_name.' '.$orderReturn->user->last_name : '-' ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="requestedBy" class="col-sm-2 col-form-label fw-bold"><?= __('Requested Date') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn->requested_at ? $orderReturn->requested_at->format('Y-m-d') : '-' ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="deliveryDate" class="col-sm-2 col-form-label fw-bold"><?= __('Completion Date') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn->processed_at ? $orderReturn->processed_at->format('Y-m-d') : '-' ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __('Request Status') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $orderReturn->status ? $orderReturn->status : '-' ?></p>
                            </div>
                        </div>
            
                        <?php if ($orderReturn->status === 'Approved') 
                                {
                                    if (!empty($orderReturn->verified_by)) { ?>

                                        <div class="form-group row">
                                            <label for="approvedDate" class="col-sm-2 col-form-label fw-bold"><?= __('Approved Date') ?></label>
                                            <div class="col-sm-5 ps-5">
                                                <p class="ps-5" style="color: black;"><?= $orderReturn->verified_time ? $orderReturn->verified_time->format('Y-m-d') : '-' ?></p>
                                            </div>
                                        </div>
                            
                                        <div class="form-group row">
                                            <label for="approvedBy" class="col-sm-2 col-form-label fw-bold"><?= __('Approved By') ?></label>
                                            <div class="col-sm-5 ps-5">
                                                <p class="ps-5" style="color: black;"><?= $orderReturn->verified_by_user ? $orderReturn->verified_by_user->first_name.' '.$orderReturn->verified_by_user->last_name : '-' ?></p>
                                            </div>
                                        </div>
                        <?php } } elseif($orderReturn->status === 'Rejected') {

                                if (!empty($orderReturn->verified_by)) { ?>
                            
                            <div class="form-group row">
                                    <label for="approvedDate" class="col-sm-2 col-form-label fw-bold"><?= __('Rejected Date') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;"><?= $orderReturn->verified_time ? $orderReturn->verified_time->format('Y-m-d') : '-' ?></p>
                                    </div>
                                </div>
                    
                                <div class="form-group row">
                                    <label for="approvedBy" class="col-sm-2 col-form-label fw-bold"><?= __('Rejected By') ?></label>
                                    <div class="col-sm-5 ps-5">
                                        <p class="ps-5" style="color: black;"><?= $orderReturn->verified_by_user ? $orderReturn->verified_by_user->first_name.' '.$orderReturn->verified_by_user->last_name : '-' ?></p>
                                    </div>
                                </div>
                            <?php } } ?>  

                            <div class="form-group row">
                                <label for="internal-notes" class="col-sm-2 col-form-label fw-bold"><?= __('Internal Comments / Notes') ?></label>
                                <div class="col-sm-5 ps-5">
                                    <textarea id="internal-notes" class="form-control" rows="4" placeholder="<?= __('Add internal comments or notes...') ?>"><?= h($orderReturn->note) ?></textarea>
                                    <button id="save-note-btn" class="btn btn-primary mt-2"><?= __('Save Note') ?></button>
                                </div>
                            </div>

                    </form>
                </div>
            </div>

            <div class="card-body pt-0">
                <div class="card" id="toRemovePadding">
                    <div class="card-header p-0">
                        <h4 style="display: none;"></h4>
                        <div class="card-header-form">
                            <form>
                                <div class="input-group">
                                    <input
                                        type="text"
                                        class="form-control search-control"
                                        placeholder="Search By product Name"
                                        id="customSearchBox"
                                    />
                                    <div class="input-group-btn">
                                        <button class="btn">
                                            <i
                                                class="fas fa-search"
                                            ></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="table-responsive" tabindex="1">
                            <table id="stockRequestTable" class="table dataTable table-hover table-xl mb-0 mt-4" id="stockTable">
                                <thead>
                                    <tr>
                                        <th><?= __('Product Name') ?></th>
                                        <th><?= __('Product Variant') ?></th>
                                        <th><?= __('Product Attribute') ?></th>
                                        <th><?= __('SKU') ?></th>
                                        <th><?= __('Quantity') ?></th>
                                        <th><?= __('Uploaded Image(s)') ?></th>
                                        <th><?= __('Reason') ?></th>
                                    </tr>
                                </thead>
                                <tbody id="table_datalist">
                                    <?php if (!empty($orderReturn->order_item)): ?>
                                    <tr>
                                        <td><?= h($orderReturn->order_item->product->name ?? 'N/A') ?></td>
                                        <td><?= h($orderReturn->order_item->product_variant->variant_name ?? 'N/A') ?></td>
                                        <td>
                                            <?php
                                                $attribute = $orderReturn->order_item->attributes ?? null;
                                                if ($attribute) {
                                                    echo h($attribute['attribute_name'] . ': ' . $attribute['attribute_value']);
                                                } else {
                                                    echo __('N/A');
                                                }
                                            ?>
                                        </td>
                                        <td><?php
                                            if (!empty($orderReturn->order_item->product_variant_id)) {
                                                echo h($orderReturn->order_item->product_variant->sku ?? 'N/A');
                                            } else {
                                                echo h($orderReturn->order_item->product->sku ?? 'N/A');
                                            }
                                        ?></td>
                                        <td><?= h($orderReturn->return_quantity ?? 'N/A') ?></td>
                                        <!-- <td>
                                            < ?php
                                                if (!empty($orderReturn->return_product_image)) {
                                                    $images = explode(',', $orderReturn->return_product_image);
                                                    foreach ($images as $img) {
                                                        echo $this->Html->image($img, ['style' => 'width: 80px; height: 80px; margin: 5px;']);
                                                    }
                                                } else {
                                                    echo '-';
                                                }
                                            ?>
                                        </td> -->
                                        <td>
                                            <?php if (!empty($orderReturn->return_product_image)): ?>
                                                <button type="button" class="btn btn-sm" onclick="showReturnImagesModal(<?= $orderReturn->id ?>)">
                                                    <?= __('View Images') ?>
                                                </button>

                                                <!-- Hidden images data for JS -->
                                                <div id="images-data-<?= $orderReturn->id ?>" style="display: none;">
                                                    <?php
                                                        $images = explode(',', $orderReturn->return_product_image);
                                                        foreach ($images as $img) {
                                                            echo $this->Html->image($img, ['style' => 'width: 80px; height: 80px; margin: 5px;', 'class' => 'return-image']);
                                                        }
                                                    ?>
                                                </div>
                                            <?php else: ?>
                                                <?= __('N/A') ?>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= h($orderReturn->order_return_category->name ?? 'N/A') ?></td>
                                    </tr>
                                <?php endif; ?>
                                </tbody>
                            </table>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="modal fade" id="returnImagesModal" tabindex="-1" aria-labelledby="returnImagesModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="returnImagesModalLabel"><?= __('Return Product Images') ?></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?= __('Close') ?>"></button>
      </div>
      <div class="modal-body text-center" id="returnImagesContainer">
        <!-- Images will be injected here by JS -->
      </div>
    </div>
  </div>
</div>


<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    function showReturnImagesModal(id) {
        const imageContainer = document.getElementById('returnImagesContainer');
        const imageHtml = document.getElementById(`images-data-${id}`)?.innerHTML;

        if (imageHtml) {
            imageContainer.innerHTML = imageHtml;
            const modal = new bootstrap.Modal(document.getElementById('returnImagesModal'));
            modal.show();
        } else {
            imageContainer.innerHTML = '<p><?= __("No images found.") ?></p>';
        }
    }


    document.getElementById('save-note-btn').addEventListener('click', function (e) {

        e.preventDefault();
        const notes = document.getElementById('internal-notes').value.trim();
        const orderReturnId = <?= json_encode($orderReturn->id) ?>;

        fetch('<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'saveNote']) ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            body: JSON.stringify({ id: orderReturnId, note: notes })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                swal('<?= __('Success') ?>', '<?= __('Note saved successfully.') ?>', 'success');
            } else {
                swal('<?= __('Failed') ?>', '<?= __('Failed to save note. Please try again.') ?>', 'error');
            }
        })
        .catch(() => {
            swal('<?= __('Failed') ?>', '<?= __('Failed to save note. Please try again.') ?>', 'error');
        });
    });

    var paginationCount = <?= json_encode($paginationCount) ?>;

    var columns = [
        { "data": "product_name" },
        { "data": "product_variant" },
        { "data": "product_attribute" },
        { "data": "sku" },
        { "data": "return_quantity" },
        { "data": "uploaded_images" },
        { "data": "reason" },
    ];

    var table = $("#stockRequestTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": columns
    });

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });
</script>
<?php $this->end(); ?>