<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class WarehouseStockOutgoingController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Showrooms;
    protected $Drivers;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrderItems;
    protected $Roles;
    protected $Users;
    protected $StockMovements;
    protected $StockMovementItems;
    protected $SupplierProducts;
    protected $ProductStocks;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Drivers = $this->fetchTable('Drivers');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->StockMovementItems = $this->fetchTable('StockMovementItems');
        $this->Roles = $this->fetchTable('Roles');
        $this->Users = $this->fetchTable('Users');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
    }
    
    public function index()
    {
        // Get the currently authenticated user
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            // Check if the user's role is "Warehouse Manager"
            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'warehouse assistant') {

                // Get the warehouse_id for the manager
                if(strtolower($role->name) === 'warehouse manager')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['manager_id' => $requested_user->id])
                        ->first();
                }
                else if(strtolower($role->name) === 'warehouse assistant')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['assistant_id' => $requested_user->id])
                        ->first();
                }

                if (!empty($warehouse)) {
                    $warehouseId = $warehouse->id;

                    // Update the StockMovements query to filter by warehouse_id
                    $stock_movements = $this->StockMovements->find()
                        ->select([
                            'StockMovements.id',
                            'StockMovements.movement_type',
                            'StockMovements.movement_date',
                            'StockMovements.verify_status',
                            'StockMovements.created',
                            'Warehouses.name',
                            'Showrooms.name',
                            'total_items' => $this->StockMovements->StockMovementItems->find()
                                ->func()
                                ->sum('StockMovementItems.quantity'),
                            'total_value' => $this->StockMovements->StockMovementItems->find()
                                ->func()
                                ->sum(
                                    'CASE 
                                        WHEN SupplierProducts.product_id IS NOT NULL 
                                            THEN COALESCE(SupplierProducts.supplier_price, 0) * StockMovementItems.quantity 
                                        ELSE 0
                                     END'
                                )
                        ])
                        ->contain(['Warehouses']) // For warehouse name
                        ->leftJoin(
                            ['StockMovementItems' => 'stock_movement_items'],
                            'StockMovementItems.stock_movement_id = StockMovements.id'
                        )
                        ->leftJoin(
                            ['StockRequests' => 'stock_requests'],
                            'StockRequests.id = StockMovements.referenceId'
                        )
                        ->leftJoin(
                            ['SupplierProducts' => 'supplier_products'],
                            'SupplierProducts.product_id = StockMovementItems.product_id 
                             AND SupplierProducts.supplier_id = StockRequests.supplier_id'
                        )
                        ->leftJoin(
                            ['Showrooms' => 'showrooms'],
                            'Showrooms.id = StockRequests.showroom_id'
                        )
                        ->where([
                            'StockMovements.movement_type' => 'Outgoing',
                            'StockMovements.warehouse_id' => $warehouseId // Filter by warehouse_id
                        ])
                        ->group([
                            'StockMovements.id',
                            'StockMovements.movement_type',
                            'StockMovements.movement_date',
                            'StockMovements.created',
                            'Warehouses.name',
                            'Showrooms.name'
                        ])
                        ->order(['StockMovements.id' => 'DESC'])
                        ->toArray();
                } else {
                    $stock_movements = []; // No warehouse assigned to this manager
                }
            } else {
                // Handle cases for non-Warehouse Manager roles
                $stock_movements = $this->StockMovements->find()
                    ->select([
                        'StockMovements.id',
                        'StockMovements.movement_type',
                        'StockMovements.movement_date',
                        'StockMovements.verify_status',
                        'StockMovements.created',
                        'Warehouses.name',
                        'Showrooms.name',
                        'total_items' => $this->StockMovements->StockMovementItems->find()
                            ->func()
                            ->sum('StockMovementItems.quantity'),
                        'total_value' => $this->StockMovements->StockMovementItems->find()
                            ->func()
                            ->sum(
                                'CASE 
                                    WHEN SupplierProducts.product_id IS NOT NULL 
                                        THEN COALESCE(SupplierProducts.supplier_price, 0) * StockMovementItems.quantity 
                                    ELSE 0
                                 END'
                            )
                    ])
                    ->contain(['Warehouses']) // For warehouse name
                    ->leftJoin(
                        ['StockMovementItems' => 'stock_movement_items'],
                        'StockMovementItems.stock_movement_id = StockMovements.id'
                    )
                    ->leftJoin(
                        ['StockRequests' => 'stock_requests'],
                        'StockRequests.id = StockMovements.referenceId'
                    )
                    ->leftJoin(
                        ['SupplierProducts' => 'supplier_products'],
                        'SupplierProducts.product_id = StockMovementItems.product_id 
                         AND SupplierProducts.supplier_id = StockRequests.supplier_id'
                    )
                    ->leftJoin(
                        ['Showrooms' => 'showrooms'],
                        'Showrooms.id = StockRequests.showroom_id'
                    )
                    ->where([
                        'StockMovements.movement_type' => 'Outgoing',
                        'StockMovements.warehouse_id IS NOT' => null
                    ])
                    ->group([
                        'StockMovements.id',
                        'StockMovements.movement_type',
                        'StockMovements.movement_date',
                        'StockMovements.created',
                        'Warehouses.name',
                        'Showrooms.name'
                    ])
                    ->order(['StockMovements.id' => 'DESC'])
                    ->toArray();
            }
        } else {
            $stock_movements = []; // Handle unauthenticated users
        }



        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';    
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';        

        $this->set(compact('stock_movements', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }

    public function add()
    {

        if ($this->request->is('post')) {

            $this->loadComponent('Stock'); // Load the Stock component

            $data = $this->request->getData();

            $requested_user = $this->Authentication->getIdentity();

            if (!empty($requested_user)) {
                $role = $this->Roles->get($requested_user->role_id);
            }

            $stockRequestId = $data['stock_request_id'];
            $driverId = $data['driver_id'];

            if ($stockRequestId == 'no_request') 
            {
                // Fetch the authenticated user details
                $user_detail = $this->Authentication->getIdentity();

                // Prepare the stock request data
                $stock_request_data = [
                    'requested_by' => $user_detail->id,
                    'requestor_type' => 'Showroom',
                    'warehouse_id' => $data['warehouse_id'],
                    'showroom_id' => $data['showroom_id'],
                    'manager_review_status' => 'Pending',
                    'supervisor_verify_status' => 'Approved',
                    'supervisor_verified_time' => date('Y-m-d H:i:s'),
                    'verified_by' => $user_detail->id,
                    'request_status' => 'Approved',
                    'request_date' => date('Y-m-d H:i:s'),
                    'product_id' => $data['product_id'], 
                    'product_variant_id' => $data['product_variant_id'], 
                    'product_attribute_id' => $data['product_attribute_id'], 
                    'sku' => $data['sku'], 
                    'quantity' => $data['quantity']
                ];

                // Create a new stock request entity
                $stock_request = $this->StockRequests->newEmptyEntity();
                
                // Patch the data into the entity
                $stock_request = $this->StockRequests->patchEntity($stock_request, $stock_request_data);

                // Save the stock request
                if ($this->StockRequests->save($stock_request)) {

                    // Once saved, fetch the ID of the new stock request
                    $stockRequestId = $stock_request->id;

                    // Save stock request items (ensure this method is properly defined)
                    $this->saveStockRequestItems($stock_request);

                    $stockRequest = $this->StockRequests->find()
                        ->where(['StockRequests.id' => $stockRequestId])
                        ->contain([
                            'StockRequestItems' => function ($q) {
                                return $q->where(['StockRequestItems.status' => 'Approved']);
                            }
                        ])
                        ->first();

                    if (!$stockRequest) {
                        $this->Flash->error(__('The stock request not found.'));
                    }

                    // Prepare parameters for the Stock component
                    $warehouse_id = $stockRequest->warehouse_id;
                    $movement_date = date('Y-m-d');
                    $reference_type = 'stock_request';
                    $referenceID = $stockRequestId;

                    // Prepare stock items
                    $stock_items = [];
                    foreach ($stockRequest->stock_request_items as $item) {
                        $stock_items[] = [
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'product_attribute_id' => $item->product_attribute_id,
                            'quantity' => $item->fulfilled_quantity ?? $item->fulfilled_quantity
                        ];
                    }

                    // Call the Stock component's method
                    // $result = $this->Stock->addWarehouseOutStock($warehouse_id, $movement_date, $reference_type, $referenceID, $stock_items, $image = null, $driverId);

                    if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                        $result = $this->Stock->addWarehouseOutStock($warehouse_id, $movement_date, $reference_type, $referenceID, $stock_items, $image = null, $driverId);
                    } elseif (strtolower($role->name) === 'warehouse assistant') {

                        // Call a modified version that only inserts into StockMovements and StockMovementItems
                        $result = $this->Stock->addWarehouseOutStockWithoutUpdatingProductStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image = null, $stock_items, $driverId);

                        if($result)
                        {
                            $this->sendOutgoingStockApprovalEmail($stockRequest);
                        }
                    }

                    // Return the result as JSON
                    if ($result) {

                        if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                            $this->StockMovements->updateAll(
                                ['verify_status' => 'Approved', 
                                 'verified_by' => $this->Authentication->getIdentity()->id, 
                                 'verify_time' => date('Y-m-d H:i:s')], 
                                ['referenceID' => $stockRequestId, 'reference_type' => 'stock_request']
                            );
                        }

                        if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                            // Send dispatch email
                            $this->sendStockDispatchEmail($stockRequest);
                        }

                        $this->Flash->success(__('The stocks have been saved.'));

                        return $this->redirect(['action' => 'index']);
                    
                    } else {

                        $this->Flash->error(__('Failed to process stock request. Please, try again.'));
                    
                    }

                }
            }
            else
            {
                // Step 1: Delete all existing stock request items for the given stock_request_id
                $this->StockRequests->StockRequestItems->deleteAll(['stock_request_id' => $stockRequestId]);


                // Step 2: Insert new stock request items based on the input array
                $productIds = $data['product_id'];
                $productVariantIds = $data['product_variant_id'];
                $productAttributeIds = $data['product_attribute_id'];
                $skus = $data['sku'];
                $quantities = $data['quantity'];


                $newItems = [];
                foreach ($productIds as $index => $productId) {

                    // Ensure valid integers or set to null
                    $productVariantId = isset($productVariantIds[$index]) && is_numeric($productVariantIds[$index]) ? $productVariantIds[$index] : null;
                    $productAttributeId = isset($productAttributeIds[$index]) && is_numeric($productAttributeIds[$index]) ? $productAttributeIds[$index] : null;

                    $newItems[] = [
                        'stock_request_id' => $stockRequestId,
                        'product_id' => $productId,
                        'product_variant_id' => $productVariantId,
                        'product_attribute_id' => $productAttributeId,
                        'requested_quantity' => $quantities[$index] ?? 0,
                        'fulfilled_quantity' => $quantities[$index] ?? 0,
                        'status' => 'Approved'
                    ];
                }

                // Save new items to the database
                $stockRequestItems = $this->StockRequests->StockRequestItems->newEntities($newItems);

                $this->StockRequests->StockRequestItems->saveMany($stockRequestItems);

                // Fetch stock request details
                $stockRequest = $this->StockRequests->find()
                    ->where(['StockRequests.id' => $stockRequestId])
                    ->contain([
                        'StockRequestItems' => function ($q) {
                            return $q->where(['StockRequestItems.status' => 'Approved']);
                        }
                    ])
                    ->first();

                if (!$stockRequest) {
                    $this->Flash->error(__('The stock request not found.'));
                }

                // Prepare parameters for the Stock component
                $warehouse_id = $stockRequest->warehouse_id;
                $movement_date = date('Y-m-d');
                $reference_type = 'stock_request';
                $referenceID = $stockRequestId;

                // Prepare stock items
                $stock_items = [];
                foreach ($stockRequest->stock_request_items as $item) {
                    $stock_items[] = [
                        'product_id' => $item->product_id,
                        'product_variant_id' => $item->product_variant_id,
                        'product_attribute_id' => $item->product_attribute_id,
                        'quantity' => $item->fulfilled_quantity ?? $item->fulfilled_quantity
                    ];
                }

                // Call the Stock component's method
                // $result = $this->Stock->addWarehouseOutStock($warehouse_id, $movement_date, $reference_type, $referenceID, $stock_items, $image = null, $driverId);

                if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                    $result = $this->Stock->addWarehouseOutStock($warehouse_id, $movement_date, $reference_type, $referenceID, $stock_items, $image = null, $driverId);
                } elseif (strtolower($role->name) === 'warehouse assistant') {

                    // Call a modified version that only inserts into StockMovements and StockMovementItems
                    $result = $this->Stock->addWarehouseOutStockWithoutUpdatingProductStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image = null, $stock_items, $driverId);

                    if($result)
                    {
                        $this->sendOutgoingStockApprovalEmail($stockRequest);
                    }
                }

                // Return the result as JSON
                if ($result) {

                    if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                        $this->StockMovements->updateAll(
                            ['verify_status' => 'Approved', 
                             'verified_by' => $this->Authentication->getIdentity()->id, 
                             'verify_time' => date('Y-m-d H:i:s')], 
                            ['referenceID' => $stockRequestId, 'reference_type' => 'stock_request']
                        );
                    }

                    if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                        // Send dispatch email
                        $this->sendStockDispatchEmail($stockRequest);
                    }

                    $this->Flash->success(__('The stocks have been saved.'));

                    return $this->redirect(['action' => 'index']);
                
                } else {

                    $this->Flash->error(__('Failed to process stock request. Please, try again.'));
                
                }
            }
        }

        $showrooms = $this->Showrooms->find()
            ->where(['Showrooms.status IN' => ['A', 'I']])
            ->order(['Showrooms.name' => 'ASC'])->toArray();

        // Query the Drivers table and join with Users to get driver names
        $query = $this->Drivers->find()
            ->select(['Drivers.id', 'Users.first_name', 'Users.last_name'])
            ->where(['Users.status' => 'A', 'Users.user_type' => 'Driver'])
            ->contain(['Users']);

        $driversList = $query->toArray();

        $drivers = [];
        foreach ($driversList as $driver) {
            $drivers[$driver->id] = $driver->user->first_name . ' ' . $driver->user->last_name;
        }

        $products = $this->Products->find()
            ->where(['Products.status' => 'A'])
            ->order(['Products.name' => 'ASC'])
            ->toArray();

        // Get the logged-in user
        $requested_user = $this->Authentication->getIdentity();

        // Fetch warehouses based on user role
        $userRole = null;
        $warehouses = [];
        $warehouse = null;

        if (!empty($requested_user)) {
            
            $user = $this->Users->get($requested_user->id, [
                'contain' => ['Roles'], // Assuming the Users table is associated with Roles
            ]);

            $userRole = strtolower($user->role->name);

            if ($userRole === 'warehouse manager') {
                // Fetch only the warehouse managed by this user
                $warehouse = $this->Warehouses->find()
                    ->where(['manager_id' => $requested_user->id, 'status' => 'A'])
                    ->first();
            }
            if ($userRole === 'warehouse assistant') {
                // Fetch only the warehouse managed by this user
                $warehouse = $this->Warehouses->find()
                    ->where(['assistant_id' => $requested_user->id, 'status' => 'A'])
                    ->first();
            } else {
                // Fetch all active warehouses for other roles
                $warehouses = $this->Warehouses->find()
                    ->where(['status' => 'A'])
                    ->order(['name' => 'ASC'])
                    ->toArray();
            }
        }

        $this->set(compact('warehouses', 'showrooms', 'drivers' , 'products', 'requested_user', 'warehouse', 'userRole')); 

    }

    private function sendOutgoingStockApprovalEmail($stockRequest)
    {
        $toEmails = [];

        // Fetch Warehouse Manager
        $warehouse = $this->Warehouses->get($stockRequest->warehouse_id, [
                'contain' => ['Managers']
            ]);

        $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
        $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

        $toEmails = array_filter([$warehouseManagerEmail]); // Remove null values

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for incoming stock approval request ID: " . $stockRequest->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stockRequest->created ? $stockRequest->created->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'stock_request_id' => $stockRequest->id,
            'warehouse_name' => $warehouse->name,
            'greeting' => "Dear {$warehouseName},",
            'message' => "An outgoing stock has been added by the warehouse assistant and requires your approval.",
            'request_date' => $formattedDate,
        ];

        $subject = "Outgoing Stock Approval Required - #{$stockRequest->id}";

        // Use the global email sending function
        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'outgoing_stock_approval', // Email template name
            $emailData
        );
    }

    private function sendStockDispatchEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Dispatching Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);
        
        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Dispatch
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Dispatch
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail; // Send to warehouse manager
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for dispatched stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Dispatched',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'dispatch_date' => date('d-m-Y')
        ];

        $subject = "Stock Dispatch #{$stock_request->id} - Completed";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_dispatch_notification',
            $emailData
        );
    }

    protected function saveStockRequestItems($stock_request)
    {
        if (!empty($stock_request->product_id) && is_array($stock_request->product_id)) {

            $stock_request_id = $stock_request->id;
            $product_id = $stock_request->product_id;
            $product_variant_id = $stock_request->product_variant_id;
            $product_attribute_id = $stock_request->product_attribute_id;
            $requested_quantity = $stock_request->quantity;

            $this->StockRequestItems->deleteAll(['stock_request_id' => $stock_request_id]);

            for($i=0; $i < sizeof($product_id); $i++)
            {
                $mapping = $this->StockRequestItems->find()
                    ->where(['stock_request_id' => $stock_request_id, 'product_id' => $product_id[$i], 'requested_quantity' => $requested_quantity[$i]])
                    ->first();

                if ($mapping) {
                    $mapping->product_id = $product_id[$i];
                } else {
                    $mapping = $this->StockRequestItems->newEntity([
                        'stock_request_id' => $stock_request_id,
                        'product_id' => $product_id[$i],
                        'product_variant_id' => $product_variant_id[$i],
                        'product_attribute_id' => $product_attribute_id[$i],
                        'requested_quantity' => $requested_quantity[$i],
                        'supervisor_approved_quantity' => $requested_quantity[$i],
                        'fulfilled_quantity' => $requested_quantity[$i],
                        'status' => 'Approved'
                    ]);
                }

                if (!$this->StockRequestItems->save($mapping)) {
                    return false;
                }
            }
        }

        return true;
    }

    public function getRequestIds()
    {
        $this->request->allowMethod(['ajax']); // Ensure the action is accessible only via AJAX

        $warehouseId = $this->request->getQuery('warehouse_id');
        $showroomId = $this->request->getQuery('showroom_id');

        if (!$warehouseId || !$showroomId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Both warehouse and showroom must be selected.'),
            ]));
        }

        // Query to fetch request IDs with specific conditions
        $requests = $this->StockRequests->find()
            ->select(['id'])
            ->where([
                'StockRequests.requestor_type' => 'Showroom',
                'StockRequests.request_status' => 'Approved',
                'StockRequests.warehouse_id' => $warehouseId,
                'StockRequests.showroom_id' => $showroomId,
                'StockRequests.status' => 'A',
            ])
            ->all();

        // Extract the request IDs into an array
        $requestIds = array_map(function($request) {
            return $request->id;
        }, $requests->toArray());

        // Query to check which of the request IDs exist in stock_movements.referenceID
        // $existingReferences = $this->StockMovements->find()
        //     ->select(['referenceID'])
        //     ->where([
        //         'StockMovements.referenceID IN' => $requestIds
        //     ])
        //     ->all();

        // // Extract reference IDs into an array
        // $existingReferenceIds = array_map(function($movement) {
        //     return $movement->referenceID;
        // }, $existingReferences->toArray());

        $existingReferenceIds = [];

        if (!empty($requestIds)) {
            // Query to check which of the request IDs exist in stock_movements.referenceID
            $existingReferences = $this->StockMovements->find()
                ->select(['referenceID'])
                ->where([
                    'StockMovements.referenceID IN' => $requestIds
                ])
                ->all();

            // Extract reference IDs into an array
            $existingReferenceIds = array_map(function($movement) {
                return $movement->referenceID;
            }, $existingReferences->toArray());
        }

        // Filter out the request IDs that are already in stock_movements.referenceID
        $filteredRequestIds = array_diff($requestIds, $existingReferenceIds);

        // Format the filtered request IDs as an array of objects with "id" keys
        $formattedRequestIds = array_map(function($id) {
            return ['id' => $id];
        }, $filteredRequestIds);

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => true,
            'data' => array_values($formattedRequestIds), // Ensure indexed array format
        ]));
    }

    public function getStockRequestItems()
    {
        $this->request->allowMethod(['get']);

        $stockRequestId = $this->request->getQuery('stock_request_id');

        if ($stockRequestId) {
            $stockRequestItems = $this->StockRequests->StockRequestItems->find()
                ->select([
                    'StockRequestItems.id',
                    'StockRequestItems.fulfilled_quantity',
                    'StockRequestItems.product_id',
                    'StockRequestItems.product_variant_id',
                    'StockRequestItems.product_attribute_id',
                    'StockRequestItems.stock_request_id',
                    'StockRequestItems.supervisor_approved_quantity',
                    'Products.name',
                    'Products.purchase_price', 
                    'Products.sku', 
                    'Products.supplier_id',  // Assuming Products has a supplier_id field
                    'ProductVariants.id', 
                    'ProductVariants.variant_name', 
                    'ProductVariants.purchase_price', 
                    'ProductVariants.sku', 
                    'Suppliers.name'  // Accessing supplier's name via Products
                ])
                ->leftJoinWith('Products')
                ->leftJoinWith('Products.Suppliers')  // Access Suppliers through Products
                ->leftJoinWith('ProductVariants')
                ->where([
                    'StockRequestItems.stock_request_id' => $stockRequestId,
                    'StockRequestItems.status' => 'Approved'
                ])
                ->toArray();  

            // Prepare the data for the response
            $response = [];
            foreach ($stockRequestItems as $item) {

                $item->attributes = [];

                if ($item->product_attribute_id) {
                    // Fetch attributes related to the product
                    $attributes = $this->ProductAttributes->find()
                        ->where(['ProductAttributes.id' => $item->product_attribute_id])
                        ->contain([
                            'Attributes' => [
                                'fields' => ['Attributes.name']
                            ],
                            'AttributeValues' => [
                                'fields' => ['AttributeValues.value']
                            ]
                        ])
                        ->first();

                    if ($attributes) {
                        // Add attribute details to the item if found
                        $item->attributes = [
                            'attribute_name' => $attributes->attribute->name ?? '',
                            'attribute_value' => $attributes->attribute_value->value ?? ''
                        ];
                    }
                }

                $itemId = $item->id;
                $product_name = $item->_matchingData['Products']->name;
                $product_variant = $item->_matchingData['ProductVariants']->id ? $item->_matchingData['ProductVariants']->variant_name : 'N/A';
                $product_attribute = $item->attributes ? $item->attributes['attribute_name'] . ':' . $item->attributes['attribute_value'] : 'N/A';
                if ($item['product_variant_id'])
                {
                    $sku = $item->_matchingData['ProductVariants']->sku;
                }
                else
                {
                    $sku = $item->_matchingData['Products']->sku;
                }
                $supplier_name = $item->_matchingData['Suppliers']->name;
                $fulfilled_quantity = $item->fulfilled_quantity;

                $response[] = [
                    'id' => $itemId,
                    'product_name' => $product_name,
                    'product_id' => $item['product_id'],
                    'product_variant' => $product_variant,
                    'product_variant_id' => $item['product_variant_id'],
                    'product_attribute' => $product_attribute,
                    'product_attribute_id' => $item['product_attribute_id'],
                    'sku' => $sku,
                    'fulfilled_quantity' => $fulfilled_quantity,
                ];
            }

            // Return the response as JSON
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'data' => $response]));
        } else {
            // If no stock_request_id is passed, return an error response
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => false, 'message' => 'Invalid request']));
        }
    }

    public function view($id = null)
    {
        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.verify_status',
                'StockMovements.created',
                'Warehouses.name',
                'Showrooms.name', // Fetching related showroom name
                'DriverUser.first_name', // Fetch driver's first name from Users
                'DriverUser.last_name' // Fetch driver's last name from Users
            ])
            ->contain([
                'Warehouses',
            ])
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                ['StockRequests.id = StockMovements.referenceId']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['Showrooms.id = StockRequests.showroom_id']
            )
            ->leftJoin(
                ['Drivers' => 'drivers'], // Join for Drivers table
                ['Drivers.id = StockMovements.driver_id']
            )
            ->leftJoin(
                ['DriverUser' => 'users'], // Join for Users table
                ['DriverUser.id = Drivers.user_id']
            )
            ->where(['StockMovements.id' => $id])
            ->first();   

        // Fetch StockRequestItems for the StockRequest with id = 7
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockMovementItems.stock_movement_id' => $id])
            ->toArray();

        foreach ($StockMovementItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('StockMovement', 'StockMovementItems', 'currencySymbol', 'decimalSeparator', 'thousandSeparator')); 
    }

    public function getVariants($productId)
    {
        $this->request->allowMethod(['get']);

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku']) // Fetch ID, name, and SKU of the variants
            ->where(['ProductVariants.product_id' => $productId]) // Filter by product_id
            ->andWhere(['ProductVariants.status' => 'A'])
            ->toArray();

        // Fetch product attributes and their values
        $productAttributes = $this->ProductAttributes->find()
            ->contain(['Attributes', 'AttributeValues'])
            ->where(['ProductAttributes.product_id' => $productId])
            ->andWhere(['ProductAttributes.status' => 'A'])
            ->toArray();

        // Prepare the response
        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $variantData = [];
        foreach ($variants as $variant) {
            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
            ];
        }

        foreach ($productAttributes as $productAttribute) {
            $response['attributes'][] = [
                'attribute_id' => $productAttribute->id,
                'attribute_name' => $productAttribute->attribute->name,
                'attribute_value' => $productAttribute->attribute_value->value,
            ];
        }

        $this->set([
                    'response' => $response,
                    '_serialize' => ['response'],
                ]);

        // Return JSON response
        return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
    }

    public function approve($id = null)
    {
        $requested_user = $this->Authentication->getIdentity();

        if ($this->request->is(['patch', 'post', 'put'])) {

            try {

                $user_detail = $this->Authentication->getIdentity();

                if(!empty($user_detail))
                {
                    $role = $this->Roles->find()
                            ->where(['id' => $user_detail->role_id])
                            ->first();

                    if (strtolower($role->name) == 'warehouse manager' || strtolower($role->name) === 'admin') {

                        $data = $this->request->getData();

                        $verified_by = $user_detail->id;
                        $verify_status = 'Approved';
                        $verify_time = date('Y-m-d H:i:s');

                        $product_id = $this->request->getData('product_id');
                        $product_variant_id = $this->request->getData('product_variant_id');
                        $product_attribute_id = $this->request->getData('product_attribute_id');
                        $requested_quantity = $this->request->getData('quantity');
                        $accepted_quantity = $this->request->getData('accepted_quantity');
                        $statuses = $this->request->getData('status');

                        // Fetch and update the stock request data
                        $stock_movement_data = $this->StockMovements->get($id);

                        $stock_movement_data->verify_status = $verify_status;
                        $stock_movement_data->verify_time = $verify_time;
                        $stock_movement_data->verified_by = $verified_by;

                        if ($this->StockMovements->save($stock_movement_data)) {

                            $stockRequestId = $stock_movement_data->referenceID;

                            // Fetch stock request details
                            $stockRequest = $this->StockRequests->find()
                                ->where(['StockRequests.id' => $stockRequestId])
                                ->contain([
                                    'StockRequestItems' => function ($q) {
                                        return $q->where(['StockRequestItems.status' => 'Approved']);
                                    }
                                ])
                                ->first();

                            if (!$stockRequest) {
                                $this->Flash->error(__('The stock request not found.'));
                            }

                            $warehouse_id = $stockRequest->warehouse_id;

                            foreach ($stockRequest->stock_request_items as $item) {
                                $product_id = $item->product_id;
                                $product_variant_id = !empty($item->product_variant_id) && $item->product_variant_id !== 'null' ? $item->product_variant_id : null;
                                $product_attribute_id = !empty($item->product_attribute_id) && $item->product_attribute_id !== 'null' ? $item->product_attribute_id : null;
                                $quantity = $item->fulfilled_quantity ?? 0;

                                // Conditions to check stock availability
                                $conditions = [
                                    'warehouse_id' => $warehouse_id,
                                    'product_id' => $product_id
                                ];

                                if ($product_variant_id) {
                                    $conditions['product_variant_id'] = $product_variant_id;
                                }
                                if ($product_attribute_id) {
                                    $conditions['product_attribute_id'] = $product_attribute_id;
                                }

                                // Check stock availability
                                $stockExist = $this->ProductStocks->find()
                                    ->where($conditions)
                                    ->first();

                                if ($stockExist) {
                                    // Deduct quantity if stock is available
                                    $productStockID = $stockExist->id;
                                    $existQuantity = $stockExist->quantity;
                                    $decreaseQuantity = max(0, $existQuantity - $quantity); // Ensure non-negative

                                    $reduceData = [
                                        'quantity' => $decreaseQuantity,
                                        'reduce_reserved_stock' => $quantity // Deduct from reserved stock
                                    ];

                                    $this->ProductStocks->updateProductStock($productStockID, $reduceData);
                                } else {
                                    // Add new stock if it doesn't exist
                                    $this->ProductStocks->addProductStock('Warehouse', $warehouse_id, [
                                        'product_id' => $product_id,
                                        'product_variant_id' => $product_variant_id,
                                        'product_attribute_id' => $product_attribute_id,
                                        'quantity' => $quantity
                                    ]);
                                }
                            }

                            // Send Dispatch Notification
                            $this->sendStockDispatchEmail($stockRequest);
                                
                            // Successfully updated all items and modifications
                            $this->Flash->success(__('The outgoing stocks have been approved successfully.'));
                        } else {
                            $this->Flash->error(__('Failed to approve the outgoing stock.'));
                        }

                        return $this->redirect(['action' => 'index']);
                    }
                }
                else
                {
                    $this->Flash->success(__('Failed to approve the stock request.'));

                    return $this->redirect(['action' => 'index']);
                }

            } catch (\Exception $e) {
                $response['message'] = $e->getMessage();
            }

        }

        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.verify_status',
                'StockMovements.created',
                'Warehouses.name',
                'Showrooms.name', // Fetching related showroom name
                'DriverUser.first_name', // Fetch driver's first name from Users
                'DriverUser.last_name' // Fetch driver's last name from Users
            ])
            ->contain([
                'Warehouses',
            ])
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                ['StockRequests.id = StockMovements.referenceId']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['Showrooms.id = StockRequests.showroom_id']
            )
            ->leftJoin(
                ['Drivers' => 'drivers'], // Join for Drivers table
                ['Drivers.id = StockMovements.driver_id']
            )
            ->leftJoin(
                ['DriverUser' => 'users'], // Join for Users table
                ['DriverUser.id = Drivers.user_id']
            )
            ->where(['StockMovements.id' => $id])
            ->first();   

        // Fetch StockRequestItems for the StockRequest with id = 7
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_id',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockMovementItems.stock_movement_id' => $id])
            ->toArray();

        foreach ($StockMovementItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $this->set(compact('StockMovement', 'StockMovementItems'));

    }

    
}
