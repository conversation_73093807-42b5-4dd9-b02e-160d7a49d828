<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
    }
    .pending_actions {
    min-height: max-content;
}

</style>
<?php $this->end(); ?>

<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
        </ul>
    </div>

    <?php if (!empty($noActiveShowroomsMessage)): ?>
        <div class="alert alert-warning">
            <?= h($noActiveShowroomsMessage); ?>
        </div>
    <?php endif; ?>

    <div class="row mt-5 pe-3 date_picker">
        <div class="col-sm-5">
            <a href="<?= $this->Url->build(['controller' => 'CashDesk', 'action' => 'index']) ?>" class="btn w-100">
                <i class="fas fa-cash-register"></i> <?= __('Go to Cash Desk') ?>
            </a>
        </div>
        <div class="col-sm-7 text-end">
            <div class="row align-items-center mb-2">
                <div class="col-md-7">

                </div>
                <div class="col-sm-5">
                    <div class="float-end">
                        <div class="col-sm-5">
                        </div>
                        <div class="col-sm-13">
                            <select id="date-period" class="form-select" onchange="handleChange(this)">
                                <option value="current_month"><?= __('Current Month') ?></option>
                                <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                                <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                                <option value="current_year"><?= __('Current Year') ?></option>
                                <option value="4"><?= __('Custom') ?></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row align-items-center mb-2 d-none" id="datesappear">
                <form id="dateRangeForm" class="d-flex p-0">
                    <div class="col-sm-2">
                        <label for="from-date" class="col-form-label fw-400 m-r-10"><?= __('From Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="from-date" name="from-date" />
                    </div>
                    <div class="col-sm-2">
                        <label for="to-date" class="col-form-label fw-400 m-r-10"><?= __('To Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="to-date" name="to-date" />
                    </div>
                    <div class="col-sm-2">
                        <button class="btn" type="submit"><?= __('Submit') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="section-body mt-3">
        <div class="container-fluid">
            <div class="row ">
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style1 dashboard_box">
                        <div class="card-statistic-3 bg1">
                            <div class="card-icon card-icon-large"><i class="fa fa-award"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Number of Orders') ?></h4>
                                <span id="totalOrders"><?php echo !empty($totalOrders) ? h($totalOrders).' No.s' : '0 No.s'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="orderProgressBar" role="progressbar" data-width="<?= h($percentageOrders) ?>%"
                                        aria-valuenow="<?= h($percentageOrders) ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalSalesAmount"><?php echo !empty($totalSalesAmount) ? h(number_format((float)$totalSalesAmount, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) : h(number_format((float)0, 0, '', $thousandSeparator)). ' ' . h($currencySymbol); ?></span></span>
                                    <span class="text-nowrap" style="opacity:0" id="userText"><?= __('New users since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style2 dashboard_box">
                        <div class="card-statistic-3 bg2">
                            <div class="card-icon card-icon-large"><i class="fa fa-briefcase"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Users') ?></h4>
                                <span id="totalUsers"><?php echo !empty($totalUsers) ? h($totalUsers) : '0'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="userProgressBar" role="progressbar" data-width="<?= $newUsersPercentage ?>%"
                                        aria-valuenow="<?= $newUsersPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewUsers"><?php echo !empty($newUsers) ? h($newUsers) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="userText"><?= __('New users since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style3 dashboard_box">
                        <div class="card-statistic-3 bg3">
                            <div class="card-icon card-icon-large"><i class="fa fa-globe"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Products') ?></h4>
                                <span id="totalProducts"><?php echo !empty($totalActiveProducts) ? h($totalActiveProducts) : '0'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="productProgressBar" role="progressbar" data-width="<?= $newProductsPercentage ?>%"
                                        aria-valuenow="<?= $newProductsPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewProducts"><?php echo !empty($newProducts) ? h($newProducts) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="productText"><?= __('New products since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style4 dashboard_box">
                        <div class="card-statistic-3 bg4">
                            <div class="card-icon card-icon-large"><i class="fa fa-money-bill-alt"></i>
                            </div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Showrooms') ?></h4>
                                <span id="totalShowrooms"><?php echo !empty($totalShowrooms) ? h($totalShowrooms) : '0'; ?> <?= __('Showrooms') ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="showroomProgressBar" role="progressbar" data-width="<?= $newShowroomsPercentage ?>%"
                                        aria-valuenow="<?= $newShowroomsPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewShowrooms"><?php echo !empty($newShowrooms) ? h($newShowrooms) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="showroomText"><?= __('New showrooms since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Order Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart3"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Revenues Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart1"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-lg-3 col-xl-3">
                    <div class="row">
                    <div class="col-12">
                            <div class="pending_actions mb-3">
                                <div class="card">
                                    <!-- Supplier Pending Bills -->
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h4 class="mb-0"><?= __('Supplier Pending Bills') ?></h4>
                                        <a class="text-decoration-none" data-bs-toggle="collapse" href="#supplierPendingBills" role="button" aria-expanded="false" aria-controls="supplierPendingBills">
                                            <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
                                        </a>
                                    </div>
                                    <div id="supplierPendingBills" class="collapse">
                                        <div class="content">
                                            <ul>
                                                <?php foreach ($supplier_payment_pendings as $supplier): ?>
                                                    <li><b><?= h($supplier['bill_number']) ?> (<?= h(number_format((float)$supplier['pending_amount'], 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?>) - </b><?= __('Pending') ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Stock Pendings -->
                                    <div class="card-header mt-3 d-flex justify-content-between align-items-center">
                                        <h4 class="mb-0 mt-0"><?= __('Stock Pendings') ?></h4>
                                        <a class="text-decoration-none" data-bs-toggle="collapse" href="#stockPendings" role="button" aria-expanded="false" aria-controls="stockPendings">
                                            <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
                                        </a>
                                    </div>
                                    <div id="stockPendings" class="collapse">
                                        <div class="content">
                                            <ul>
                                                <li><b><?= __('Stock Return') ?> - </b><?= __('Approval Pending') ?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="pending_actions">
                                <div class="card">
                                    <!-- Card Header with Toggle Icon -->
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h4 class="mb-0"><?= __('Refunds Pending and Returns Pending') ?></h4>
                                        <a class="text-decoration-none" data-bs-toggle="collapse" href="#refundsReturnsPending" role="button" aria-expanded="false" aria-controls="refundsReturnsPending">
                                            <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
                                        </a>
                                    </div>

                                    <!-- Collapsible Content -->
                                    <div id="refundsReturnsPending" class="collapse">
                                        <div class="content">
                                            <ul>
                                                <li><b><?= __('Refunds Pending') ?> - </b><?= __('100') ?></li>
                                                <li><b><?= __('Returns Pending') ?> - </b><?= __('200') ?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="col-12 col-lg-9 col-xl-9">
    <div class="card supplier_payments">
        <!-- Card Header with Toggle Icon -->
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0 mt-0"><?= __('Supplier Payments') ?></h4>
            <a class="text-decoration-none" data-bs-toggle="collapse" href="#supplierPayments" role="button" aria-expanded="false" aria-controls="supplierPayments">
                <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
            </a>
        </div>

        <!-- Collapsible Content -->
        <div id="supplierPayments" class="collapse">
            <div class="card-body">
                <div class="media-list position-relative">
                    <div class="table-responsive" tabindex="1" style="height: 300px; overflow-y: scroll; outline: none; touch-action: none;">
                        <table class="table table-hover table-xl mb-0">
                            <thead>
                                <tr>
                                    <th><?= __('Supplier Name') ?></th>
                                    <th><?= __('Amount') ?></th>
                                    <th><?= __('Status') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($supplier_payments as $payment): ?>
                                <tr>
                                    <td class="text-truncate"><?php echo !empty($payment->supplier->name) ? h($payment->supplier->name) : 'N/A'; ?></td>
                                    <td class="text-truncate">
                                        <?= !empty($payment->amount) ? h(number_format((float)$payment->amount, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) : 'N/A'; ?>
                                    </td>
                                    <td class="text-truncate">
                                        <?php
                                        $paymentStatusMap = [
                                            __('Paid') => ['label' => __('Paid'), 'class' => 'col-green'],
                                            __('Partially Paid') => ['label' => __('Partially Paid'), 'class' => 'col-orange'],
                                            __('Pending') => ['label' => __('Pending'), 'class' => 'col-red']
                                        ];

                                        $payment_status = $paymentStatusMap[$payment->supplier_purchase_order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <div class="badge-outline <?= $payment_status['class'] ?>">
                                        <?= h($payment_status['label']) ?>
                                    </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

            </div>

            <div class="row">
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Top 3 Showrooms') ?></h4>
                        </div>
                        <div class="card-footer pt-3 d-flex justify-content-center">
                            <div class="budget-price justify-content-end">
                                <div class="budget-price-square" data-width="30"></div>
                                <div class="budget-price-label"><?= __('Revenues') ?></div>
                            </div>
                        </div>
                        <div class="card-body top_products" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border"
                                style="position: relative; max-height: 320px;" [perfectScrollbar]>
                                <?php foreach ($topShowrooms as $showroom): ?>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/showroom.jpg') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= h($showroom->showroom->name) ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: <?= $showroom->sales_percentage ?>%;"></div>
                                                <div class="budget-price-label"><?= h(number_format((float)$showroom->total_sales, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Top 5 Product Categories') ?></h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border"
                                style="position: relative; max-height: 320px;" [perfectScrollbar]>
                                <?php foreach ($topCategories as $category): ?>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= h($category['category_icon']) ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= h($category['category_name']) ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: <?= $category['percentage'] ?>%;"></div>
                                                <div class="budget-price-label"><?= h(number_format((float)$category['total_sales'], 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/page/chart-apexcharts.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>
    function handleChange(answer) {
        
        if (answer.value == 4) {
            document.getElementById('datesappear').classList.remove('d-none');
        } else 
        {

            document.getElementById('datesappear').classList.add('d-none');

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterSupervisorDashboardCard']); ?>',
                type: 'GET',
                data: {
                    dateRange: answer.value
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    
                    if(answer.value == '<?= __('current_month') ?>')
                    {
                        var userText = '<?= __('New users since this month') ?>';
                        var productText = '<?= __('New products since this month') ?>';
                        var showroomText = '<?= __('New showrooms since this month') ?>';
                    }
                    else if(answer.value == '<?= __('last_3_months') ?>')
                    {
                        var userText = '<?= __('Users since 3 months') ?>';
                        var productText = '<?= __('Products since 3 months') ?>';
                        var showroomText = '<?= __('Showrooms since 3 months') ?>';
                    }
                    else if(answer.value == '<?= __('last_6_months') ?>')
                    {
                        var userText = '<?= __('Users since 6 months') ?>';
                        var productText = '<?= __('Products since 6 months') ?>';
                        var showroomText = '<?= __('Showrooms since 6 months') ?>';
                    }
                    else if(answer.value == '<?= __('current_year') ?>')
                    {
                        var userText = '<?= __('Users in current year') ?>';
                        var productText = '<?= __('Products in current year') ?>';
                        var showroomText = '<?= __('Showrooms in current year') ?>';
                    }

                    $('#totalOrders').text(response.totalOrders + ' No.s');
                    $('#totalSalesAmount').text(response.totalSalesAmount);
                    $('#orderProgressBar').css('width', response.percentageOrders + '%').attr('data-width', response.percentageOrders + '%').attr('aria-valuenow', response.percentageOrders);

                    $('#totalUsers').text(response.totalUsers);
                    $('#totalNewUsers').text(response.newUsers);
                    $('#userProgressBar').css('width', response.newUsersPercentage + '%').attr('data-width', response.newUsersPercentage + '%').attr('aria-valuenow', response.newUsersPercentage);
                    $('#userText').text(userText);

                    $('#totalProducts').text(response.totalActiveProducts);
                    $('#totalNewProducts').text(response.newProducts);
                    $('#productProgressBar').css('width', response.newProductsPercentage + '%').attr('data-width', response.newProductsPercentage + '%').attr('aria-valuenow', response.newProductsPercentage);
                    $('#productText').text(productText);

                    $('#totalShowrooms').text(response.totalShowrooms);
                    $('#totalNewShowrooms').text(response.newShowrooms);
                    $('#showroomProgressBar').css('width', response.newShowroomsPercentage + '%').attr('data-width', response.newShowroomsPercentage + '%').attr('aria-valuenow', response.newShowroomsPercentage);
                    $('#showroomText').text(showroomText);
                },
                error: function() {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
                }
            });
        }
    }

    $('#dateRangeForm').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission

        // Get values from the input fields
        const fromDate = $('#from-date').val();
        const toDate = $('#to-date').val();

        // Validate date inputs
        if (!fromDate || !toDate) {
            swal('<?= __('Failed') ?>', '<?= __('Both dates are required.') ?>', 'error');
            return false;
        }

        // Check if the from date is earlier than or equal to the to date
        if (new Date(fromDate) > new Date(toDate)) {
            swal('<?= __('Failed') ?>', '<?= __('The "From Date" must be earlier than or equal to the "To Date".') ?>', 'error');
            return false;
        }

        // Send AJAX request
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterSupervisorDashboardCardByDate']); ?>',
            method: 'GET',
            data: { fromDate: fromDate, toDate: toDate },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                
                $('#totalOrders').text(response.totalOrders + ' No.s');
                $('#totalSalesAmount').text(response.totalSalesAmount);
                $('#orderProgressBar').css('width', response.percentageOrders + '%').attr('data-width', response.percentageOrders + '%').attr('aria-valuenow', response.percentageOrders);

                $('#totalUsers').text(response.totalUsers);
                $('#totalNewUsers').text(response.newUsers);
                $('#userProgressBar').css('width', response.newUsersPercentage + '%').attr('data-width', response.newUsersPercentage + '%').attr('aria-valuenow', response.newUsersPercentage);
                $('#userText').text(`Users for the period`);

                $('#totalProducts').text(response.totalActiveProducts);
                $('#totalNewProducts').text(response.newProducts);
                $('#productProgressBar').css('width', response.newProductsPercentage + '%').attr('data-width', response.newProductsPercentage + '%').attr('aria-valuenow', response.newProductsPercentage);
                $('#productText').text(`Products for the period`);

                $('#totalShowrooms').text(response.totalShowrooms);
                $('#totalNewShowrooms').text(response.newShowrooms);
                $('#showroomProgressBar').css('width', response.newShowroomsPercentage + '%').attr('data-width', response.newShowroomsPercentage + '%').attr('aria-valuenow', response.newShowroomsPercentage);
                $('#showroomText').text(`Showrooms for the period`);

            },
            error: function(xhr) {
                swal('<?= __('Failed') ?>', 'An error occurred: ' + xhr.responseText, 'error');
            }
        });
    });

$(function () {
    chart1();
    chart3();
});

function chart3() {
    var options = {
        chart: {
            height: 350,
            type: "line",
            shadow: {
                enabled: true,
                color: "#000",
                top: 18,
                left: 7,
                blur: 10,
                opacity: 1,
            },
            toolbar: {
                show: false,
            },
        },
        // colors: ["#77B6EA", "#0d839b", "#f77f00"],
        dataLabels: {
            enabled: true,
        },
        stroke: {
            curve: "smooth",
        },
        series: [
            <?php foreach ($orderTrendsgraphData as $data): ?>
            {
                name: "<?= $data['name'] ?>",
                data: <?= json_encode($data['data']) ?>
            },
            <?php endforeach; ?>
        ],
        title: {
            text: ".",
            align: "left",
        },
        grid: {
            borderColor: "#e7e7e7",
            row: {
                colors: ["#f3f3f3", "transparent"], // takes an array which will be repeated on columns
                opacity: 0.5,
            },
        },
        markers: {
            size: 6,
        },
        xaxis: {
            categories: <?= json_encode($monthLabels) ?>,
            title: {
                text: "Months",
            },
            labels: {
                style: {
                    colors: "#8e8da4",
                },
            },
        },
        yaxis: {
            title: {
                text: "Total Orders",
            },
            labels: {
                style: {
                    color: "#8e8da4",
                },
            },
            min: 1,
            max: 30,
        },
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
            offsetY: -25,
            offsetX: -5,
        },
    };

    var chart = new ApexCharts(document.querySelector("#chart3"), options);

    chart.render();
}

function chart1() {
    var options = {
        chart: {
            height: 350,
            type: "bar",
        },
        plotOptions: {
            bar: {
                horizontal: false,
                endingShape: "rounded",
                columnWidth: "50%",
            },
        },
        dataLabels: {
            enabled: false,
        },
        stroke: {
            show: true,
            width: 2,
            colors: ["transparent"],
        },
        series: [
            <?php foreach ($revenueFormattedData as $data): ?>
            {
                name: "<?= $data['showroom'] ?>",
                data: <?= json_encode($data['revenue']) ?>
            },
            <?php endforeach; ?>
        ],
        // colors: ["#77B6EA", "#0d839b", "#f77f00"],
        xaxis: {
            categories: <?= json_encode($revenueMonths) ?>,
            title: {
                text: "Months",
            },
            labels: {
                style: {
                    colors: "#8e8da4",
                },
            },
        },
        yaxis: {
            title: {
                text: "Showrooms",
            },
            labels: {
                style: {
                    color: "#8e8da4",
                },
            },
        },

        fill: {
            opacity: 1,
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return "$ " + val + " thousands";
                },
            },
        },
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
        },
    };

    var chart = new ApexCharts(document.querySelector("#chart1"), options);

    chart.render();
}    
</script>
<?php $this->end(); ?>