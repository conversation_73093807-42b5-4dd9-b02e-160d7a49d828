<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;
use Cake\I18n\FrozenDate;

/**
 * Showrooms Controller
 *
 * @property \App\Model\Table\ShowroomsTable $Showrooms
 */
class SuppliersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $SupplierProducts;
    protected $Showrooms;
    protected $Warehouses;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $SupplierReturnOrders;
    protected $SupplierReturnOrdersItems;
    protected $SupplierPayment;
    protected $ProductVariants;
    protected $ProductAttributes;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->SupplierReturnOrders = $this->fetchTable('SupplierReturnOrders');
        $this->SupplierReturnOrdersItems = $this->fetchTable('SupplierReturnOrdersItems');
        $this->SupplierPayment = $this->fetchTable('SupplierPayment');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
    }
    
    public function index()
    {
        $suppliers = $this->Suppliers->find()
            ->where(['Suppliers.status IN' => ['A', 'I']])
            ->order(['Suppliers.name' => 'ASC'])->toArray();    

        $this->set(compact('suppliers'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $supplier = $this->Suppliers->get($id, contain: []);

        //FETCH SUPPLIER PRODUCTS

        $supplier_products = $this->SupplierProducts->find()
            ->where([
                'SupplierProducts.supplier_id' => $id,
                'SupplierProducts.status IN' => ['A', 'I']
            ])
            ->contain([
                'Suppliers',
                'Products' => function ($q) {
                    return $q->contain([
                        'ProductImages' => function ($q2) {
                            return $q2->where([
                                'ProductImages.status' => 'A',
                                'ProductImages.image_default' => 1
                            ])->select(['id', 'product_id', 'image']);
                        }
                    ]);
                },
                'ProductVariants' => function ($q) {
                    return $q->select(['id', 'variant_name', 'sku']);
                }
            ])
            ->order(['Suppliers.name' => 'ASC'])
            ->toArray();

        foreach ($supplier_products as $product) {
            
            if (!empty($product->product->product_images) && !empty($product->product->product_images[0]->image)) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_images[0]->image);
            } else {
                $product->product->product_image = null;
            }

            if (!empty($product->product_variant_id) && !empty($product->product_variant->sku)) {
                $product->product->sku = $product->product_variant->sku;
            }
        }

        //FETCH PURCHASE ORDER REQUEST
        $order_request = $this->SupplierPurchaseOrders->find()
            ->where(['SupplierPurchaseOrders.supplier_id' => $id])
            ->where(['SupplierPurchaseOrders.status IN' => ['A', 'P', 'I']])
            ->order(['SupplierPurchaseOrders.created' => 'DESC'])->toArray();    

        //FETCH RETURN ORDER REQUEST
        $return_request = $this->SupplierReturnOrders->find()
            ->where(['SupplierReturnOrders.supplier_id' => $id])
            ->where(['SupplierReturnOrders.status IN' => ['A', 'I']])
            ->order(['SupplierReturnOrders.created' => 'DESC'])->toArray();  

        //FETCH SUPPLIER PAYMENTS
        $supplier_payment = $this->SupplierPayment->find()
            ->where(['SupplierPayment.supplier_id' => $id])
            ->contain([
                'Suppliers','SupplierPurchaseOrders'
                ])
            ->order(['SupplierPayment.created' => 'DESC'])->toArray();      

        $suppliers = $this->fetchTable('Suppliers')
                        ->find('list')
                        ->where(['status' => 'A'])
                        ->all();

        $products = $this->fetchTable('Products')
                        ->find('list')
                        ->where(['status' => 'A'])
                        ->all();

        //FETCH FOR PURCHASE ORDER REQUEST FORM
        $showrooms = $this->Showrooms->getShowrooms();

        $warehouses = $this->Warehouses->getWarehouses();

        $supplier_products_active = $this->SupplierProducts->find()
            ->where(['SupplierProducts.supplier_id' => $id])
            ->where(['SupplierProducts.status IN' => 'A'])
            ->contain(['Products', 'Suppliers'])->toArray();           

        foreach ($supplier_products_active as $product) {

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }
        }

        //FETCH FOR RETURN ORDER REQUEST
        $purchase_order_bills = $this->SupplierPurchaseOrders->getPurchaseOrderBills($id);

        //FETCH FOR RETURN ORDER REQUEST UNPAID BILLS
        $pending_purchase_order_bills = $this->SupplierPurchaseOrders->getPendingPurchaseOrderBills($id);                         

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $query = $this->SupplierPayment->find()
                    ->select([
                        'total_amount' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
                    ])
                    ->where(['SupplierPayment.supplier_id' => $id])
                    ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
                        return $q->where(['SupplierPurchaseOrders.payment_status IN' => ['Partially Paid', 'Paid']]);
                    })
                    ->first();
        
        // Get the total amount from the result, set to 0 if no result
        $totalPaymentsDone = $query ? $query->total_amount : 0.00;

        // Format the total payments done to two decimal places
        // $totalPaymentsDoneFormatted = $totalPaymentsDone ? number_format($totalPaymentsDone, 2) : 0.00;  

        $totalPaymentsDoneFormatted = $totalPaymentsDone ? number_format((float)$totalPaymentsDone, 0, '', $thousandSeparator).' '.$currencySymbol : 0.00;          

        /**  TOTAL PENDING AMOUNT **/
        $pendingBills = $this->SupplierPurchaseOrders->find()
            ->contain([
                'SupplierPurchaseOrdersItems' => function ($q) {
                    return $q->contain(['Products'])
                         ->select([
                            'SupplierPurchaseOrdersItems.product_id',
                            'SupplierPurchaseOrdersItems.approved_quantity',
                            'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
                        ]);
                }
            ])
            ->where([
                'SupplierPurchaseOrders.payment_status IN' => ['Partially Paid', 'Pending'],
                'SupplierPurchaseOrders.status' => 'A',
                'SupplierPurchaseOrders.supplier_id' => $id
            ])
            ->all();

        $totalPendingPayments = 0; 

        foreach ($pendingBills as $purchaseOrder) {
            $orderTotal = 0;

            foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {
                $conditions = [
                    'supplier_id' => $purchaseOrder->supplier_id,
                    'product_id' => $product->product_id,
                    'status' => 'A'
                ];

                if (!empty($product->product_variant_id)) {
                    $conditions['product_variant_id'] = $product->product_variant_id;
                }

                $supplierPrice = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where($conditions)
                    ->first();

                if ($supplierPrice) {
                    $orderTotal += $supplierPrice->supplier_price * $product->approved_quantity;
                }
            }

            // Fetch total paid for this order
            $alreadyPaid = $this->SupplierPayment->find()
                ->where(['supplier_purchase_order_id' => $purchaseOrder->id])
                ->select(['sum' => 'SUM(amount)'])
                ->first()->sum ?? 0;

            $pendingAmount = $orderTotal - $alreadyPaid;
            if ($pendingAmount > 0) {
                $totalPendingPayments += $pendingAmount;
            }
        }

        // foreach ($pendingBills as $purchaseOrder) {

        //     // print_r($purchaseOrder);die;

        //     foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {

        //         // print_r($purchaseOrder->supplier_id.'----'.$product->product_id).'\n';

        //         // Fetch supplier price
        //         $supplierPrice = $this->SupplierProducts->find()
        //             ->select(['supplier_price'])
        //             ->where([
        //                 'supplier_id' => $purchaseOrder->supplier_id,
        //                 'product_id' => $product->product_id
        //             ])
        //             ->first();

        //         // Calculate total pending amount for each product
        //         if ($supplierPrice) {

        //             // echo '<pre>';print_r($purchaseOrder->id.'---'.$supplierPrice->supplier_price * $product->approved_quantity);

        //             $totalPendingPayments += $supplierPrice->supplier_price * $product->approved_quantity;
        //         }
        //     }
        // }

        // Output the total pending payments
        // $totalPendingPaymentsFormatted = number_format($totalPendingPayments, 2); 

        $totalPendingPaymentsFormatted = number_format((float)$totalPendingPayments, 0, '', $thousandSeparator).' '.$currencySymbol;

        // Fetch pending purchase orders
        $pendingOrders = $this->SupplierPurchaseOrders->find()
            ->where([
                'payment_status IN' => ['Partially Paid', 'Pending'],
                'status' => 'A',
                'supplier_id' => $id
            ])
            ->all();

        $totalPendingDue = 0;
        $today = new FrozenDate();
        // Loop through each pending order
        foreach ($pendingOrders as $order) {
            if (!empty($order->payment_due_date)) {
                $currentDueDate = new FrozenDate($order->payment_due_date);
            } else {
                $currentDueDate = null;
            }

            if ($currentDueDate) {
                $newDueDate = $currentDueDate->addDays($supplier->credit_period);
            } else {
                $newDueDate = null;
            }

            if ($today > $newDueDate) {
                $orderTotal = 0;

                // Fetch the approved products for the current order
                $approvedProducts = $this->SupplierPurchaseOrdersItems->find()
                    ->where(['supplier_purchase_order_id' => $order->id])
                    ->all();

                // Calculate total for approved quantities
                foreach ($approvedProducts as $product) {
                    
                    $conditions = [
                        'supplier_id' => $order->supplier_id,
                        'product_id' => $product->product_id,
                        'status' => 'A'
                    ];

                    // Include product_variant_id if available
                    if (!empty($product->product_variant_id)) {
                        $conditions['product_variant_id'] = $product->product_variant_id;
                    }

                    $supplierProduct = $this->SupplierProducts->find()
                        ->where($conditions)
                        ->first();

                    if ($supplierProduct) {
                        $orderTotal += $supplierProduct->supplier_price * $product->approved_quantity;
                    }
                }

                // Fetch total paid amount for this order
                $alreadyPaid = $this->SupplierPayment->find()
                    ->where(['supplier_purchase_order_id' => $order->id])
                    ->select(['sum' => 'SUM(amount)'])
                    ->first()->sum ?? 0;

                $pendingDue = $orderTotal - $alreadyPaid;

                if ($pendingDue > 0) {
                    $totalPendingDue += $pendingDue;
                }
            }
        }


        // foreach ($pendingOrders as $order) {
        //     // Credit 5 days to payment_due_date
        //     // $currentDueDate = new FrozenDate($order->payment_due_date);
        //     // $newDueDate = $currentDueDate->addDays($supplier->credit_period);

        //     if (!empty($order->payment_due_date)) {
        //         $currentDueDate = new FrozenDate($order->payment_due_date);
        //     } else {
        //         $currentDueDate = null; // Or handle accordingly
        //     }

        //     if ($currentDueDate) {
        //         $newDueDate = $currentDueDate->addDays($supplier->credit_period);
        //     } else {
        //         $newDueDate = null; // Handle accordingly
        //     }

        //     // echo "<pre>";print_r($order['bill_no'].'  '.$currentDueDate. "   " .$newDueDate.'   '.$today);

        //     if ($today > $newDueDate) {

        //         // echo "<pre>";print_r($order['bill_no'].'  '.$currentDueDate. "   " .$newDueDate.'   '.$today);

        //         // Fetch the approved products for the current order
        //         $approvedProducts = $this->SupplierPurchaseOrdersItems->find()
        //             ->where(['supplier_purchase_order_id' => $order->id])
        //             ->all();

        //         // Calculate total due for this order
        //         foreach ($approvedProducts as $product) {
        //             // Fetch the supplier price for the product
        //             $supplierProduct = $this->SupplierProducts->find()
        //                 ->where(['supplier_id' => $order->supplier_id, 'product_id' => $product->product_id])
        //                 ->first();

        //             if ($supplierProduct) {
        //                 // Calculate the pending due for this product
        //                 $pendingDue = $supplierProduct->supplier_price * $product->approved_quantity;
        //                 $totalPendingDue += $pendingDue;
        //             }
        //         }
        //     }
        // }

        // Display the total pending due
        // $totalPendingDueFormatted = number_format($totalPendingDue, 2);
        $totalPendingDueFormatted = number_format((float)$totalPendingDue, 0, '', $thousandSeparator).' '.$currencySymbol;

        $this->set(compact('supplier', 'supplier_products', 'order_request', 'return_request', 'suppliers', 'products', 'showrooms', 'warehouses', 'supplier_products_active', 'purchase_order_bills', 'pending_purchase_order_bills', 'supplier_payment', 'dateFormat', 'timeFormat', 'totalPaymentsDoneFormatted' , 'totalPendingPaymentsFormatted', 'totalPendingDueFormatted', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $supplier = $this->Suppliers->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $countryCode = '+225';
            $phoneNumber = $data['phone_number'];

            if (strpos($phoneNumber, $countryCode) !== 0) {
                $phoneNumber = $countryCode.' '.$phoneNumber;
            }

            $data['phone_number'] = $phoneNumber;

            $supplier = $this->Suppliers->patchEntity($supplier, $data);

            if ($this->Suppliers->save($supplier)) {
                $this->Flash->success(__('The supplier has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The supplier could not be saved. Please, try again.'));
        }
        
        $this->set(compact('supplier'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $supplier = $this->Suppliers->get($id, contain: []);
        // Trim the Ivory Coast country code from the contact number
        if (isset($supplier->phone_number)) {
            $supplier->phone_number = preg_replace('/^\+225 /', '', $supplier->phone_number);
        }
        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $countryCode = '+225';
            $phoneNumber = $data['phone_number'];

            if (strpos($phoneNumber, $countryCode) !== 0) {
                $phoneNumber = $countryCode.' '.$phoneNumber;
            }

            $data['phone_number'] = $phoneNumber;

            $supplier = $this->Suppliers->patchEntity($supplier, $data);
            if ($this->Suppliers->save($supplier)) {
                $this->Flash->success(__('The supplier has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The supplier could not be saved. Please, try again.'));
        }

        $this->set(compact('supplier'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The supplier could not be deleted. Please, try again.'];

        try {
            $record = $this->Suppliers->get($id);
            $record->status = 'D';

            if ($this->Suppliers->save($record)) {
                $response = ['success' => true, 'message' => 'The supplier has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function getVariants($productId)
    {
        $this->request->allowMethod(['get']);

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku']) // Fetch ID, name, and SKU of the variants
            ->where(['ProductVariants.product_id' => $productId]) // Filter by product_id
            ->andWhere(['ProductVariants.status' => 'A'])
            ->toArray();

        // Fetch product attributes and their values
        $productAttributes = $this->ProductAttributes->find()
            ->contain(['Attributes', 'AttributeValues'])
            ->where(['ProductAttributes.product_id' => $productId])
            ->andWhere(['ProductAttributes.status' => 'A'])
            ->toArray();

        // echo "<pre>";print_r($variants);die;

        // Prepare the response
        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $variantData = [];
        foreach ($variants as $variant) {
            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
            ];
        }

        foreach ($productAttributes as $productAttribute) {
            $response['attributes'][] = [
                'attribute_id' => $productAttribute->id,
                'attribute_name' => $productAttribute->attribute->name,
                'attribute_value' => $productAttribute->attribute_value->value,
            ];
        }

        $this->set([
                    'response' => $response,
                    '_serialize' => ['response'],
                ]);

        // Return JSON response
        return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));

        // $this->set([
        //             'variants' => $variants,
        //             '_serialize' => ['variants'],
        //         ]);

        // return $this->response->withType('application/json')
        //         ->withStringBody(json_encode(['variants' => $variants]));
    }
}
