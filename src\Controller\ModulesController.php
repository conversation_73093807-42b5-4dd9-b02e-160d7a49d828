<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Routing\Router;

/**
 * Modules Controller
 *
 * @property \App\Model\Table\ModulesTable $Modules
 */
class ModulesController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'login', 'logout']);
    }

    public function index()
    {
        $statusFilter = $this->request->getQuery('status');

        $query = $this->Modules->find('all', [
            'contain' => ['ParentModules'], // Load parent module data
        ]);

        if (!empty($statusFilter)) {
            $query->where(['Roles.status' => $statusFilter]);
        }

        $modules = $query->toArray();
        $this->set(compact('modules', 'statusFilter'));
    }

    /**
     * View method
     *
     * @param string|null $id Module id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $module = $this->Modules->get($id, contain: ['Permissions']);
        $this->set(compact('module'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $module = $this->Modules->newEmptyEntity();
        $parentModules = $this->Modules->find('list', [
            'keyField' => 'id', 
            'valueField' => 'display_name',
            'conditions' => ['parent_id IS' => null],
        ])->toArray();
        
        if ($this->request->is('post')) {
            $module = $this->Modules->patchEntity($module, $this->request->getData());
            if ($this->Modules->save($module)) {
                $this->Flash->success(__('The module has been saved.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('Unable to add the module. Please, try again.'));
        }
        
        $this->set(compact('module', 'parentModules'));
    }

    public function edit($id = null)
    {
        $module = $this->Modules->get($id, [
            'contain' => [],
        ]);

        $parentModules = $this->Modules->find('list', [
            'keyField' => 'id',
            'valueField' => 'display_name',
            'conditions' => ['id !=' => $id],
        ])->toArray();
        
        if ($this->request->is(['patch', 'post', 'put'])) {
            // echo "<pre>"; print_r($this->request->getData()); die;
            $module = $this->Modules->patchEntity($module, $this->request->getData());
            if ($this->Modules->save($module)) {
                $this->Flash->success(__('The module has been updated.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('Unable to update the module. Please, try again.'));
        }
        
        $this->set(compact('module', 'parentModules'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Module id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $module = $this->Modules->get($id);
        if ($this->Modules->delete($module)) {
            $this->Flash->success(__('The module has been deleted.'));
        } else {
            $this->Flash->error(__('The module could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
