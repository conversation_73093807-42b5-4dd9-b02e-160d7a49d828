<?php

namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\ORM\TableRegistry;
use Cake\Mailer\Mailer;
use Cake\Core\Configure;
use Cake\Utility\Text;
use Cake\Event\Event;
use Cake\Utility\Security;
use Cake\Auth\DefaultPasswordHasher;
//use QRcode;
use Cake\Http\Session;
use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Twilio\Rest\Client;
use SendGrid\Mail\Mail;
use Cake\View\View;
use Cake\Routing\Router;

//require_once(ROOT .DS. "vendor" . DS  . 'qrcode' . DS . 'qrlib.php');
// Testing

class WaveComponent extends Component
{
    protected $PaymentMethods;
    protected $PaymentMethodSettings;
    protected $WaveCheckoutSessions;
    protected $api_key;
    
    public function initialize($config): void
    {
        parent::initialize($config);

        $this->PaymentMethods = TableRegistry::getTableLocator()->get('PaymentMethods');
        $this->PaymentMethodSettings = TableRegistry::getTableLocator()->get('PaymentMethodSettings');
        $this->WaveCheckoutSessions = TableRegistry::getTableLocator()->get('WaveCheckoutSessions');
        // Fetch settings specific to Wave
        $settings_data = $this->fetchSettings('Wave');

        // Store settings in the component
        foreach ($settings_data as $setting) {
            $settings[$setting->attribute] = $setting->value;
        }
        $this->api_key = $settings['api_key'] ?? null;
    }

    protected function fetchSettings(string $paymentName)
    {
        // Fetch the payment method by name
        $paymentMethod = $this->PaymentMethods->find()
            ->select(['id'])
            ->where(['name' => $paymentName])
            ->first();

        if (!$paymentMethod) {
            throw new \RuntimeException("Payment method '{$paymentName}' not found.");
        }

        // Fetch settings for the payment method
        $settings = $this->PaymentMethodSettings->find()
            ->select(['attribute', 'value'])
            ->where(['payment_method_id' => $paymentMethod->id])
            ->all();

        return $settings;
    }

    public function createCheckoutSession($clientReferenceId, $amount) {

    	$apiKey = $this->api_key ?? null;
        $url = "https://api.wave.com/v1/checkout/sessions";
        //$clientReferenceId = uniqid('', true); // Generates a unique identifier

        // Generate the success URL using Router::url()
        $successUrl = Configure::read('Settings.SITE_URL')."Payments/wavePaymentSuccess?client_reference=$clientReferenceId";
		//$successUrl = Router::url(['controller' => 'Payments', 'action' => 'success'], true); // true ensures it's a full URL

		// Generate the error URL using Router::url()
		$errorUrl = Configure::read('Settings.SITE_URL')."Payments/wavePaymentError?client_reference=$clientReferenceId";
		//$errorUrl = Router::url(['controller' => 'Payments', 'action' => 'error'], true); // true ensures it's a full URL

		$data = [
		    'amount' => $amount,
		    'currency' => 'XOF',
		    'client_reference' => $clientReferenceId,
		    'error_url' => $errorUrl,
		    'success_url' => $successUrl
		];

		//print_r($data); exit;

		$options = [
		    CURLOPT_URL => $url,
		    CURLOPT_RETURNTRANSFER => true,
		    //CURLOPT_ENCODING => "",
			//CURLOPT_MAXREDIRS => 10,
			//CURLOPT_TIMEOUT => 0,
			//CURLOPT_FOLLOWLOCATION => true,
			//CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			//CURLOPT_CUSTOMREQUEST => "POST",
		    CURLOPT_POST => true,
		    CURLOPT_HTTPHEADER => [
		        "Authorization: Bearer $apiKey",
		        "Content-Type: application/json"
		    ],
		    CURLOPT_POSTFIELDS => json_encode($data)
		];

		$curl = curl_init();
		curl_setopt_array($curl, $options);
		$response = curl_exec($curl);
		$err = curl_error($curl);

		$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
		curl_close($curl);

		if ($err) {
		  return "cURL Error #:" . $err;
		} else {
		    # You can now decode the response and use the checkout session. Happy coding ;)
		    $checkout_session = json_decode($response, true);

		    if($httpCode == 200) {

		    	/* if(isset($checkout_session['wave_launch_url'])) {

			    	$sessionEntity = $this->WaveCheckoutSessions->newEntity([
		                'client_referenceID' => $clientReferenceId,
		                'checkout_sessionID' => $checkout_session['id'],
		                'wave_launch_url' => $checkout_session['wave_launch_url']
		            ]);
		            $this->WaveCheckoutSessions->save($sessionEntity);
			    }*/
			    $res_result = ['status'=>'success', 'httpcode'=>$httpCode, 'data'=>$checkout_session];

		    } else {
		    	$res_result = ['status' => 'error', 'httpcode'=>$httpCode, 'message'=>$checkout_session['message'], 'error_code'=>$checkout_session['code']];
		    }

		    return $res_result;

		    //"wave_launch_url": "https://pay.wave.com/c/cos-18qq25rgr100a",
		    # You can redirect the user by using the 'wave_launch_url' field.
		    /*$wave_launch_url = $checkout_session["wave_launch_url"]
		    header('Location: $wave_launch_url');
		    exit;*/
		}
	}

	public function retrieveCheckout($checkoutId) {

    	$apiKey = $this->api_key ?? null;
        $url = "https://api.wave.com/v1/checkout/sessions/$checkoutId";

		$options = [
		    CURLOPT_URL => $url,
		    //CURLOPT_TIMEOUT => 5,
		    CURLOPT_RETURNTRANSFER => true,
		    CURLOPT_HTTPHEADER => [
		        "Authorization: Bearer $apiKey",
		        "Content-Type: application/json"
		    ]
		];

		$curl = curl_init();
		curl_setopt_array($curl, $options);
		$response = curl_exec($curl);
		$err = curl_error($curl);

		$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
		curl_close($curl);

		if ($err) {
		  return "cURL Error #:" . $err;
		} else {
		    # You can now decode the response and use the checkout session. Happy coding ;)
		    $checkout_session = json_decode($response, true);
		    //echo $httpCode; exit;
		    //return json_encode($checkout_session);
		    return $checkout_session;
		}
	}

}

?>
