<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;

/**
 * Banners Controller
 *
 * @property \App\Model\Table\BannersTable $Banners
 */
class BannersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }

    public function index()
    {
        $query = $this->Banners->find()->where(['status !=' => 'D'])->applyOptions(['order' => ['title' => 'ASC']]);
        $banners = $query->all();
        foreach ($banners as $banner) {
            if (!empty($banner->web_banner)) {
                $banner->web_media = $this->Media->getCloudFrontURL($banner->web_banner);
            }

            if (!empty($banner->mob_banner)) {
                $banner->mob_media = $this->Media->getCloudFrontURL($banner->mob_banner);
            }
        }
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('banners', 'dateFormat', 'timeFormat', 'status', 'statusMap'));
    }

    /**
     * View method
     *
     * @param string|null $id Banner id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $banner = $this->Banners->get($id, contain: []);
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $bannertype = Configure::read('Constants.BANNER_TYPE');
        $bannerloc = Configure::read('Constants.BANNER_LOCATION');
        $taregtMob = Configure::read('Constants.TARGET_MOB');
        $web_media = $this->Media->getCloudFrontURL($banner->web_banner);
        $mob_media = $this->Media->getCloudFrontURL($banner->mobile_banner);
        $bannerviewVal = $banner->display_in_web == 1 && $banner->display_in_mobile == 1
            ? 'Both'
            : ($banner->display_in_web == 1
                ? 'Web'
                : ($banner->display_in_mobile == 1
                    ? 'Mobile'
                    : '')
            );

        $this->set(compact('banner', 'dateFormat', 'timeFormat', 'status', 'statusMap', 'bannertype', 'bannerloc', 'web_media', 'mob_media', 'dateFormat', 'bannerviewVal','taregtMob'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $banner = $this->Banners->newEmptyEntity();
        $bannertype = Configure::read('Constants.BANNER_TYPE');
        $bannerloc = Configure::read('Constants.BANNER_LOCATION');
        $taregtMob = Configure::read('Constants.TARGET_MOB');
        $this->set([
            'webImageSize' => Configure::read('Constants.BANNER_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.BANNER_MOB_IMAGE_SIZE'),
            'webVideoSize' => Configure::read('Constants.BANNER_WEB_VIDEO_SIZE'),
            'mobVideoSize' => Configure::read('Constants.BANNER_MOB_VIDEO_SIZE'),
            'webImageTypedisp' => Configure::read('Constants.BANNER_WEB_IMAGE_TYPE_DISP'),
            'webImageType' => Configure::read('Constants.BANNER_WEB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.BANNER_MOB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.BANNER_MOB_IMAGE_JS_TYPE'),
            'webVideoType' => Configure::read('Constants.BANNER_WEB_VIDEO_JS_TYPE'),
            'webVideoTypedisp' => Configure::read('Constants.BANNER_WEB_VIDEO_TYPE_DISP'),
            'mobVideoType' => Configure::read('Constants.BANNER_MOB_VIDEO_JS_TYPE'),
            'mobVideoTypedisp' => Configure::read('Constants.BANNER_MOB_VIDEO_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $bannerview = Configure::read('Constants.BANNER_VIEW');
        $this->set(compact('banner', 'bannertype', 'bannerloc', 'bannerview','taregtMob'));
        if ($this->request->is('post')) {
            $bannerData = $this->request->getData();
            if ($bannerData['banner_type'] === 'Image') {

                $allowedFormats = Configure::read('Constants.BANNER_WEB_IMAGE_TYPE');
                $maxWebMediaSize = Configure::read('Constants.BANNER_WEB_IMAGE_SIZE') * 1024 * 1024;
                $maxMobileMediaSize = Configure::read('Constants.BANNER_MOB_IMAGE_SIZE') * 1024 * 1024;
            } else {

                $allowedFormats = Configure::read('Constants.BANNER_WEB_VIDEO_TYPE');
                $maxWebMediaSize = Configure::read('Constants.BANNER_WEB_VIDEO_SIZE') * 1024 * 1024;
                $maxMobileMediaSize = Configure::read('Constants.BANNER_MOB_VIDEO_SIZE') * 1024 * 1024;
            }

            if (!empty($bannerData['web_media_file']) && $bannerData['web_media_file']->getError() === UPLOAD_ERR_OK) {
                $web_media = $bannerData['web_media_file'];
                $webMediaName = trim($web_media->getClientFilename());
                $webMediaSize = $web_media->getSize();
                $webMediaExt = strtolower(pathinfo($webMediaName, PATHINFO_EXTENSION));

                if (!in_array($webMediaExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webMediaSize > $maxWebMediaSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of '.$maxWebMediaSize.' MB.'));
                }

                if ($bannerData['banner_type'] === 'Image') {
                    list($width, $height) = getimagesize($web_media->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.BANNER_WEB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.BANNER_WEB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.BANNER_WEB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.BANNER_WEB_IMAGE_MAX_HEIGHT');
                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Web image dimensions must be between ' . $width . 'x' . $minHeight . ' and ' . $height . 'x' . $maxHeight . ' pixels.'));
                    }
                }

                if (!empty($webMediaName)) {
                    $webMeidaTmpName = $web_media->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.BANNER_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webMediaFile = pathinfo($webMediaName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webMediaExt;
                    $uploadResult = $this->Media->upload($webMeidaTmpName, $targetdir, $webMediaFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerData['web_banner'] = $uploadFolder . $webMediaFile;
                    }
                }
            }

            if (!empty($bannerData['mobile_media_file']) && $bannerData['mobile_media_file']->getError() === UPLOAD_ERR_OK) {
                $mob_media = $bannerData['mobile_media_file'];
                $mobMediaName = trim($mob_media->getClientFilename());
                $mobMediaSize = $mob_media->getSize();
                $mobMediaExt = strtolower(pathinfo($mobMediaName, PATHINFO_EXTENSION));

                if (!in_array($mobMediaExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobMediaSize > $maxMobileMediaSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of '.$maxMobileMediaSize.' MB.'));
                }

                if ($bannerData['banner_type'] === 'Image') {
                    list($width, $height) = getimagesize($mob_media->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.BANNER_MOB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.BANNER_MOB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.BANNER_MOB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.BANNER_MOB_IMAGE_MAX_HEIGHT');

                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                    }
                }

                if (!empty($mobMediaName)) {
                    $mobMediaTmpName = $mob_media->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.BANNER_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobMediaFile = pathinfo($mobMediaName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobMediaExt;
                    $uploadResult = $this->Media->upload($mobMediaTmpName, $targetdir, $mobMediaFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerData['mobile_banner'] = $uploadFolder . $mobMediaFile;
                    }
                }
            }
            $bannerData['display_in_web'] = $bannerData['banner_view'] == 'Web' || $bannerData['banner_view'] == 'Both' ? 1 : 0;
            $bannerData['display_in_mobile'] = $bannerData['banner_view'] == 'Mobile' || $bannerData['banner_view'] == 'Both' ? 1 : 0;
            $bannerData['target_mob'] = empty($bannerData['target_mob']) ? null : $bannerData['target_mob'];
            $banner = $this->Banners->patchEntity($banner, $bannerData);
            if ($this->Banners->save($banner)) {
                $this->Flash->success(__('The banner has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The banner could not be saved. Please, try again.'));
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Banner id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $banner = $this->Banners->get($id, contain: []);
        $this->set([
            'webImageSize' => Configure::read('Constants.BANNER_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.BANNER_MOB_IMAGE_SIZE'),
            'webVideoSize' => Configure::read('Constants.BANNER_WEB_VIDEO_SIZE'),
            'mobVideoSize' => Configure::read('Constants.BANNER_MOB_VIDEO_SIZE'),
            'webImageType' => Configure::read('Constants.BANNER_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.BANNER_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.BANNER_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.BANNER_MOB_IMAGE_TYPE_DISP'),
            'webVideoType' => Configure::read('Constants.BANNER_WEB_VIDEO_JS_TYPE'),
            'webVideoTypedisp' => Configure::read('Constants.BANNER_WEB_VIDEO_TYPE_DISP'),
            'mobVideoType' => Configure::read('Constants.BANNER_MOB_VIDEO_JS_TYPE'),
            'mobVideoTypedisp' => Configure::read('Constants.BANNER_MOB_VIDEO_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $bannertype = Configure::read('Constants.BANNER_TYPE');
        $bannerloc = Configure::read('Constants.BANNER_LOCATION');
        $web_media = $this->Media->getCloudFrontURL($banner->web_banner);
        $mob_media = $this->Media->getCloudFrontURL($banner->mobile_banner);
        $bannerview = Configure::read('Constants.BANNER_VIEW');
        $bannerviewVal = $banner->display_in_web == 1 && $banner->display_in_mobile == 1
            ? 'Both'
            : ($banner->display_in_web == 1
                ? 'Web'
                : ($banner->display_in_mobile == 1
                    ? 'Mobile'
                    : '')
            );
        $status = Configure::read('Constants.STATUS');
        $taregtMob = Configure::read('Constants.TARGET_MOB');
        $this->set(compact('banner', 'bannertype', 'bannerloc', 'web_media', 'mob_media', 'bannerview', 'bannerviewVal', 'status','taregtMob'));
        if ($this->request->is(['patch', 'post', 'put'])) {
            $bannerData = $this->request->getData();
            if ($bannerData['banner_type'] === 'Image') {

                $allowedFormats = Configure::read('Constants.BANNER_WEB_IMAGE_TYPE');
                $maxWebMediaSize = Configure::read('Constants.BANNER_WEB_IMAGE_SIZE') * 1024 * 1024;
                $maxMobileMediaSize = Configure::read('Constants.BANNER_MOB_IMAGE_SIZE') * 1024 * 1024;
            } else {

                $allowedFormats = Configure::read('Constants.BANNER_WEB_VIDEO_TYPE');
                $maxWebMediaSize = Configure::read('Constants.BANNER_WEB_VIDEO_SIZE') * 1024 * 1024;
                $maxMobileMediaSize = Configure::read('Constants.BANNER_MOB_VIDEO_SIZE') * 1024 * 1024;
            }

            if (!empty($bannerData['web_media_file']) && $bannerData['web_media_file']->getError() === UPLOAD_ERR_OK) {
                $web_media = $bannerData['web_media_file'];
                $webMediaName = trim($web_media->getClientFilename());
                $webMediaSize = $web_media->getSize();
                $webMediaExt = strtolower(pathinfo($webMediaName, PATHINFO_EXTENSION));

                if (!in_array($webMediaExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webMediaSize > $maxWebMediaSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebMediaSize . ' MB.'));
                }

                if ($bannerData['banner_type'] === 'Image') {
                    list($width, $height) = getimagesize($web_media->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.BANNER_WEB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.BANNER_WEB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.BANNER_WEB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.BANNER_WEB_IMAGE_MAX_HEIGHT');
                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Web image dimensions must be between ' . $width . 'x' . $minHeight . ' and ' . $height . 'x' . $maxHeight . ' pixels.'));
                    }
                }

                if (!empty($webMediaName)) {
                    $webMeidaTmpName = $web_media->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.BANNER_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webMediaFile = pathinfo($webMediaName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webMediaExt;
                    $uploadResult = $this->Media->upload($webMeidaTmpName, $targetdir, $webMediaFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerData['web_banner'] = $uploadFolder . $webMediaFile;
                    }
                }
            }

            if (!empty($bannerData['mobile_media_file']) && $bannerData['mobile_media_file']->getError() === UPLOAD_ERR_OK) {
                $mob_media = $bannerData['mobile_media_file'];
                $mobMediaName = trim($mob_media->getClientFilename());
                $mobMediaSize = $mob_media->getSize();
                $mobMediaExt = strtolower(pathinfo($mobMediaName, PATHINFO_EXTENSION));

                if (!in_array($mobMediaExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobMediaSize > $maxMobileMediaSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileMediaSize . ' MB.'));
                }

                if ($bannerData['banner_type'] === 'Image') {
                    list($width, $height) = getimagesize($mob_media->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.BANNER_MOB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.BANNER_MOB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.BANNER_MOB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.BANNER_MOB_IMAGE_MAX_HEIGHT');

                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                    }
                }

                if (!empty($mobMediaName)) {
                    $mobMediaTmpName = $mob_media->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.BANNER_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobMediaFile = pathinfo($mobMediaName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobMediaExt;
                    $uploadResult = $this->Media->upload($mobMediaTmpName, $targetdir, $mobMediaFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerData['mobile_banner'] = $uploadFolder . $mobMediaFile;
                    }
                }
            }
            $bannerData['display_in_web'] = $bannerData['banner_view'] == 'Web' || $bannerData['banner_view'] == 'Both' ? 1 : 0;
            $bannerData['display_in_mobile'] = $bannerData['banner_view'] == 'Mobile' || $bannerData['banner_view'] == 'Both' ? 1 : 0;

            $banner = $this->Banners->patchEntity($banner, $bannerData);
            if ($this->Banners->save($banner)) {
                $this->Flash->success(__('The banner has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The banner could not be saved. Please, try again.'));
        }
    }

    /**
     * Delete method
     *
     * @param string|null $id Banner id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $banner = $this->Banners->get($id);
        $response = ['success' => false, 'message' => 'The banner could not be deleted. Please, try again.'];
        if ($banner) {
            if ($this->Banners->delete($banner)) {
                $response = ['success' => true, 'message' => 'The banner has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The banner could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The banner does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $bannerId = $this->request->getData('image_id');

        if (!$imageType || !$bannerId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $banner = $this->Banners->get($bannerId);

        if (!$banner) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Banner not found']));
        }

        $imageField = $imageType === 'web' ? 'web_banner' : 'mobile_banner';
        $existingImagePath = $banner->{$imageField};

        if ($existingImagePath) {
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');

            $filePath = WWW_ROOT . $uploadFolder . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $banner->{$imageField} = null;
            if ($this->Banners->save($banner)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update banner']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }
}
