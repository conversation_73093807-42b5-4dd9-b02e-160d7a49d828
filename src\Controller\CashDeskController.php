<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class CashDeskController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $OrderReturns;
    protected $Suppliers;
    protected $Showrooms;
    protected $Users;
    protected $Roles;
    protected $SupplierPayment;
    protected $Orders;
    protected $CashHandovers;
    protected $CashDeskClosures;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->Users = $this->fetchTable('Users');
        $this->Roles = $this->fetchTable('Roles');
        $this->SupplierPayment = $this->fetchTable('SupplierPayment');
        $this->Orders = $this->fetchTable('Orders');
        $this->CashHandovers = $this->fetchTable('CashHandovers');
        $this->CashDeskClosures = $this->fetchTable('CashDeskClosures');
    }

    public function index()
    {
        $requested_user = $this->Authentication->getIdentity();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');

        $suppliers = $this->Suppliers->find()
            ->select(['Suppliers.id', 'Suppliers.name'])
            ->where(['Suppliers.status IN' => ['A', 'I']])
            ->order(['Suppliers.name' => 'ASC'])
            ->toArray();

        $showrooms = [];
        $conditions = [];

        if ($requested_user) {
            $role = $this->Roles->get($requested_user->role_id);

            // Role-based showroom access
            if (strtolower($role->name) === 'showroom supervisor') {
                $showrooms = $this->Showrooms->find()
                    ->where([
                        'Showrooms.showroom_supervisor' => $requested_user->id,
                        'Showrooms.status' => 'A'
                    ])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->all() // Fetch results
                    ->extract('id') // Extract IDs
                    ->toList(); // Convert to an array


                if (!empty($supervisorShowrooms)) {
                    $conditions['SupplierPayment.showroom_id IN'] = $supervisorShowrooms;
                }

            } elseif (strtolower($role->name) === 'showroom manager') {
                $showrooms = $this->Showrooms->find()
                    ->where([
                        'Showrooms.showroom_manager' => $requested_user->id,
                        'Showrooms.status' => 'A'
                    ])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    
                    $managerShowroomId = $managerShowroom->id;

                    $conditions['SupplierPayment.showroom_id'] = $managerShowroomId;
                }

            } else {
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();
            }
        }

        $supplier_payment = $this->SupplierPayment->find()
            ->where($conditions)
            ->contain(['Showrooms', 'Suppliers', 'SupplierPurchaseOrders'])
            ->order(['SupplierPayment.created' => 'DESC'])
            ->toArray();

        // echo '<pre>';print_r($supplier_payment);die;

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('suppliers', 'showrooms', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'requested_user', 'role', 'supplier_payment', 'dateFormat', 'timeFormat'));


    }

    // public function cashDesk()
    // {
    //     $this->request->allowMethod(['post']);
    //     $this->autoRender = false;

    //     $result = ['status' => 'error', 'message' => __('Invalid request')];

    //     try {
    //         $identity = $this->request->getAttribute('identity');
    //         if (!$identity) {
    //             throw new \Exception(__('User not authenticated.'));
    //         }

    //         $roleId = $identity->get('role_id');
    //         $userId = $identity->get('id');
    //         $role = $this->Users->Roles->get($roleId);
    //         $roleName = $role->name;

    //         $showroom_id = $this->request->getData('showroom_id');
    //         if (empty($showroom_id)) {
    //             throw new \Exception(__('Showroom ID is required.'));
    //         }

    //         // Roles to consider for sales
    //         $roles = $this->Roles->find()
    //             ->where([
    //                 'name IN' => ['Showroom Manager', 'Sales Person'],
    //                 'status' => 'A'
    //             ])
    //             ->all()
    //             ->toArray();
    //         $all_roles = array_column($roles, 'id');

    //         $cash_handovers = [];
    //         $sales_data = [];
    //         $today_sales = 0;
    //         $total_paid = 0;
    //         $available_cash = 0;

    //         if ($roleName === 'Showroom Manager') {
    //             // $sales_data = $this->Orders->salesPersonOrder($showroom_id);
    //             $manager_sales = $this->Orders->getManagerSales($showroom_id, $userId);

    //             foreach ($sales_data as $sale) {
    //                 $today_sales += $sale['total_amount'];
    //             }

    //             $available_cash = $manager_sales + $today_sales;

    //         } elseif ($roleName === 'Showroom Supervisor') {
    //             $cash_handovers = $this->CashHandovers->cashHandoverDetail($userId, $showroom_id);

    //             foreach ($cash_handovers as $handover) {
    //                 $available_cash += $handover['amount'];
    //             }
    //         }

    //         $available_cash = number_format($available_cash ?? 0, 2, '.', '');

    //         $supplier_payment_data = $this->SupplierPayment->getSupplierPayments($showroom_id);

    //         foreach ($supplier_payment_data as $payment) {
    //             $total_paid += $payment['amount'];
    //         }

    //         $total_paid = number_format($total_paid ?? 0, 2, '.', '');

    //         $cash_desk_close_amount = number_format(($available_cash - $total_paid), 2, '.', '');

    //         $cash_desk_close = $this->CashDeskClosures->find()
    //             ->select(['closing_time'])
    //             ->order(['closing_time' => 'DESC'])
    //             ->first();

    //         $last_cash_desk_close = $cash_desk_close ? $cash_desk_close->closing_time->format('Y-m-d H:i') : __('N/A');

    //         $result = [
    //             'status' => 'success',
    //             'data' => [
    //                 'cash_handovers' => $cash_handovers,
    //                 'sales_data' => $sales_data,
    //                 // 'supplier_payment_data' => $supplier_payment_data,
    //                 'today_sales' => $today_sales,
    //                 'total_paid' => $total_paid,
    //                 'available_cash' => $available_cash,
    //                 'cash_desk_close_amount' => $cash_desk_close_amount,
    //                 'last_cash_desk_close' => $last_cash_desk_close
    //             ]
    //         ];

    //     } catch (\Exception $e) {
    //         $this->log('CashDesk Error: ' . $e->getMessage(), 'error');
    //         $result = [
    //             'status' => 'error',
    //             'message' => $e->getMessage()
    //         ];
    //     }

    //     return $this->response->withType('application/json')->withStringBody(json_encode($result));
    // }

    public function cashDesk()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $result = ['status' => 'error', 'message' => __('Invalid request')];

        try {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                throw new \Exception(__('User not authenticated.'));
            }

            $roleId = $identity->get('role_id');
            $userId = $identity->get('id');
            $role = $this->Users->Roles->get($roleId);
            $roleName = $role->name;

            $showroom_id = $this->request->getData('showroom_id');
            if (empty($showroom_id)) {
                throw new \Exception(__('Showroom ID is required.'));
            }

            // Currency Format
            $currencyConfig = Configure::read('Settings.Currency.format');
            $currencySymbol = $currencyConfig['currency_symbol'] ?? 'FCFA';
            $decimalSeparator = $currencyConfig['decimal_separator'] ?? '.';
            $thousandSeparator = $currencyConfig['thousand_separator'] ?? ',';

            // Roles to consider
            $roles = $this->Roles->find()
                ->where(['name IN' => ['Showroom Manager', 'Sales Person'], 'status' => 'A'])
                ->all()
                ->toArray();
            $all_roles = array_column($roles, 'id');

            $cash_handovers = [];
            $sales_data = [];
            $today_sales = 0;
            $total_paid = 0;
            $available_cash = 0;

            if ($roleName === 'Showroom Manager') {
                $manager_sales = $this->Orders->getManagerSales($showroom_id, $userId);

                // This line is commented: $sales_data = $this->Orders->salesPersonOrder($showroom_id);
                // No sales_data loop used here; just $manager_sales considered
                $available_cash = $manager_sales;

            } elseif ($roleName === 'Showroom Supervisor') {
                $cash_handovers = $this->CashHandovers->cashHandoverDetail($userId, $showroom_id);

                foreach ($cash_handovers as $handover) {
                    $available_cash += $handover['amount'];
                }
            }

            $supplier_payment_data = $this->SupplierPayment->getSupplierPayments($showroom_id);
            foreach ($supplier_payment_data as $payment) {
                $total_paid += $payment['amount'];
            }

            $cash_desk_close_amount = $available_cash - $total_paid;

            $cash_desk_close = $this->CashDeskClosures->find()
                ->select(['closing_time'])
                ->order(['closing_time' => 'DESC'])
                ->first();

            $last_cash_desk_close = $cash_desk_close ? $cash_desk_close->closing_time->format('Y-m-d H:i') : __('N/A');

            // Format values
            $formattedAvailableCash = number_format((float)$available_cash, 0, '', $thousandSeparator);
            $formattedTotalPaid = number_format((float)$total_paid, 0, '', $thousandSeparator);
            $formattedCashDeskCloseAmount = number_format((float)$cash_desk_close_amount, 0, '', $thousandSeparator);

            $result = [
                'status' => 'success',
                'data' => [
                    'cash_handovers' => $cash_handovers,
                    'sales_data' => $sales_data,
                    'today_sales' => number_format((float)$today_sales, 0, '', $thousandSeparator),
                    'total_paid' => $formattedTotalPaid . ' ' . $currencySymbol,
                    'available_cash' => $formattedAvailableCash . ' ' . $currencySymbol,
                    'cash_desk_close_amount' => $formattedCashDeskCloseAmount . ' ' . $currencySymbol,
                    'last_cash_desk_close' => $last_cash_desk_close
                ]
            ];

        } catch (\Exception $e) {
            $this->log('CashDesk Error: ' . $e->getMessage(), 'error');
            $result = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($result));
    }


    public function cashDeskClose()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        try {
            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                throw new \Exception(__('User not authenticated.'));
            }

            $roleId = $identity->get('role_id');
            $userId = $identity->get('id');
            $role = $this->Users->Roles->get($roleId);
            $roleName = $role->name;

            $data = $this->request->getData();
            $data['closed_by'] = $userId;

            if (empty($data['showroom_id'])) {
                throw new \Exception(__('Showroom ID is required.'));
            }

            $showroom = $this->Showrooms->showroomDetailById($data['showroom_id']);
            if (!$showroom) {
                throw new \Exception(__('Showroom not found.'));
            }

            if ($roleName == 'Showroom Manager') {
                $cash_desk_close = $this->CashDeskClosures->cashDeskClose($data);

                if (!empty($data['total_cash'])) {
                    $cash_handover = $this->CashHandovers->newEmptyEntity();
                    $cash_handover['showroom_manager'] = $userId;
                    $cash_handover['handed_to_supervisor'] = $showroom['showroom_supervisor'];
                    $cash_handover['amount'] = $data['total_cash'];
                    $cash_handover['handover_date'] = date('Y-m-d H:i:s');
                    $cash_handover['showroom_id'] = $data['showroom_id'];
                    // $cash_handover['remarks'] = $data['remarks'];
                    $this->CashHandovers->save($cash_handover);
                }

            } elseif ($roleName == 'Showroom Supervisor') {
                $cash_desk_close = $this->CashDeskClosures->cashDeskClose($data);
            }

            if (!empty($cash_desk_close)) {
                $result = ['status' => 'success', 'message' => 'Cash desk closed'];
                return $this->response
                    ->withType('application/json')
                    ->withStringBody(json_encode($result))
                    ->withStatus(200);
            } else {
                throw new \Exception(__('Unable to close, Please try again.'));
            }

        } catch (\Exception $e) {
            $this->log("CashDeskClose Error: " . $e->getMessage(), 'error');
            $result = ['status' => 'error', 'message' => $e->getMessage()];
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($result))
                ->withStatus(400);
        }
    }


}
