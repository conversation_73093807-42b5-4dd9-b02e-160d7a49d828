<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;
use Cake\Routing\Router;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class SupplierPurchaseOrdersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Showrooms;
    protected $Warehouses;
    protected $SupplierProducts;
    protected $SupplierPurchaseOrdersItems;
    protected $ProductAttributes;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
    }
    
    public function index()
    {
        $order_request = $this->SupplierPurchaseOrders->find()
            ->where(['SupplierPurchaseOrders.status IN' => ['A', 'I']])
            ->order(['SupplierPurchaseOrders.created' => 'DESC'])->toArray();  

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
            
        $this->set(compact('order_request', 'dateFormat', 'timeFormat'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $order_request = $this->SupplierPurchaseOrders->get($id, contain: [
            'Showrooms', 'Warehouses'
        ]);

        $order_products = $this->SupplierPurchaseOrdersItems->find()
                    ->where(['SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id])
                    ->contain([
                        'Products' => [
                            'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
                            'SupplierProducts' => [
                                'fields' => ['SupplierProducts.product_id'],
                                'Suppliers' => [ 
                                    'fields' => ['Suppliers.id', 'Suppliers.name']
                                ]
                            ]
                        ]
                    ])->toArray();

        foreach ($order_products as $product) {

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }
        }

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('order_request' , 'order_products', 'dateFormat', 'timeFormat'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */

    public function add()
    {
        $order_request = $this->SupplierPurchaseOrders->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $supplierId = $data['supplier_id'];

            $order_request = $this->SupplierPurchaseOrders->patchEntity($order_request, $data);

            if ($this->SupplierPurchaseOrders->save($order_request)) {
                $this->saveSupplierPurchaseOrdersItems($order_request);
                
                $query_purchase_order = $this->SupplierPurchaseOrders->find()
                                        ->where(['SupplierPurchaseOrders.supplier_id' => $supplierId])    
                                        ->where(['SupplierPurchaseOrders.status IN' => ['A', 'P', 'I']])
                                        ->order(['SupplierPurchaseOrders.created' => 'DESC'])->toArray();  

                $dateFormat = Configure::read('Settings.DATE_FORMAT');
                $timeFormat = Configure::read('Settings.TIME_FORMAT');
                $purchase_orders = [];
                $i = 1;
                foreach ($query_purchase_order as $order) {

                    $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                    'P' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $status = $statusMap[$order->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

                    $paymentStatusMap = [
                        'Paid' => ['label' => __('Paid'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $payment_status = $paymentStatusMap[$order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                    $deliveredStatusMap = [
                        'Delivered' => ['label' => __('Delivered'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $delivery_status = $deliveredStatusMap[$order->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];


                    $purchase_orders[] = [
                        'order_date' => $order->order_date->format($dateFormat . ' ' . $timeFormat),
                        'bill_no' => h($order->bill_no),
                        'supp_bill_no' => h($order->supplier_bill_no),
                        'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                        'payment_status' => '<div class="badge-outline ' . h($payment_status['class']) . '">' . h($payment_status['label']) . '</div>',
                        'delivery_status' => '<div class="badge-outline ' . h($delivery_status['class']) . '">' . h($delivery_status['label']) . '</div>',
                        'actions' => '
                            <a onClick="openViewPurchaseOrderModal(' . $order->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'
                            // '<a onClick="openEditPurchaseOrderModal('.$order->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                            // '<a href="' . Router::url(['controller' => 'SupplierPurchaseOrders', 'action' => 'delete', $order->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'.
                            // '<a data-toggle="tooltip" title="Verify" onclick="openVerifyModal('.$order->id.')" data-id="'.$order->id.'"><i class="fa fa-check" aria-hidden="true"></i></a>'
                    ];

                }

                $purchase_order_bills = $this->SupplierPurchaseOrders->getPurchaseOrderBills($supplierId);

                $this->set([
                    'purchase_orders' => $purchase_orders,
                    '_serialize' => ['purchase_orders'],
                ]);

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['data' => $purchase_orders, 'purchase_order_bills' => $purchase_order_bills]));

            }
            $this->Flash->error(__('The order request could not be saved. Please, try again.'));
        }
    }

    protected function saveSupplierPurchaseOrdersItems($order_request)
    {
        if (!empty($order_request->product_id) && is_array($order_request->product_id)) {

            $purchase_order_id = $order_request->id;
            $product_id = $order_request->product_id;
            $variant_id = $order_request->product_variant_id;
            $attribute_id = $order_request->product_attribute_id;
            $product_sku = $order_request->sku;
            $product_quantity = $order_request->quantity;

            $this->SupplierPurchaseOrdersItems->deleteAll(['supplier_purchase_order_id' => $purchase_order_id]);

            for($i=0; $i < sizeof($product_id); $i++)
            {
                $mapping = $this->SupplierPurchaseOrdersItems->find()
                    ->where(['supplier_purchase_order_id' => $purchase_order_id, 'product_id' => $product_id[$i], 'sku' => $product_sku[$i], 'quantity' => $product_quantity[$i]])
                    ->first();

                if ($mapping) {
                    $mapping->product_id = $product_id[$i];
                } else {
                    $mapping = $this->SupplierPurchaseOrdersItems->newEntity([
                        'supplier_purchase_order_id' => $purchase_order_id,
                        'product_id' => $product_id[$i],
                        'product_variant_id' => $variant_id[$i],
                        'product_attribute_id' => $attribute_id[$i],
                        'sku' => $product_sku[$i],
                        'quantity' => $product_quantity[$i]
                    ]);
                }

                if (!$this->SupplierPurchaseOrdersItems->save($mapping)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Edit method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $order_request = $this->SupplierPurchaseOrders->get($id, contain: []);

        $order_products = $this->SupplierPurchaseOrdersItems->find()
                    ->where(['SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id])
                    ->contain([
                        'Products' => [
                            'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
                            'SupplierProducts' => [
                                'fields' => ['SupplierProducts.product_id'],
                                'Suppliers' => [ 
                                    'fields' => ['Suppliers.id', 'Suppliers.name']
                                ]
                            ]
                        ]
                    ])->toArray();

        foreach ($order_products as $product) {

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }
        }                    

        if ($this->request->is(['patch', 'post', 'put'])) {
            
            $data = $this->request->getData();
            $supplierId = $data['supplier_id'];

            $order_request = $this->SupplierPurchaseOrders->patchEntity($order_request, $this->request->getData());

            if ($this->SupplierPurchaseOrders->save($order_request)) {
                $this->saveSupplierPurchaseOrdersItems($order_request);
                
                $query_purchase_order = $this->SupplierPurchaseOrders->find()
                                        ->where(['SupplierPurchaseOrders.supplier_id' => $supplierId])
                                        ->where(['SupplierPurchaseOrders.status IN' => ['A', 'P', 'I']])
                                        ->order(['SupplierPurchaseOrders.created' => 'DESC'])->toArray();  

                $dateFormat = Configure::read('Settings.DATE_FORMAT');
                $timeFormat = Configure::read('Settings.TIME_FORMAT');
                $purchase_orders = [];
                $i = 1;
                foreach ($query_purchase_order as $order) {

                    $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'col-green'],
                    'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
                    'P' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $status = $statusMap[$order->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

                    $paymentStatusMap = [
                        'Paid' => ['label' => __('Paid'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $payment_status = $paymentStatusMap[$order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                    $deliveredStatusMap = [
                        'Delivered' => ['label' => __('Delivered'), 'class' => 'col-green'],
                        'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
                    ];

                    $delivery_status = $deliveredStatusMap[$order->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];


                    $purchase_orders[] = [
                        'order_date' => $order->order_date->format($dateFormat . ' ' . $timeFormat),
                        'bill_no' => h($order->bill_no),
                        'supp_bill_no' => h($order->supplier_bill_no),
                        'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                        'payment_status' => '<div class="badge-outline ' . h($payment_status['class']) . '">' . h($payment_status['label']) . '</div>',
                        'delivery_status' => '<div class="badge-outline ' . h($delivery_status['class']) . '">' . h($delivery_status['label']) . '</div>',
                        'actions' => '
                            <a onClick="openViewPurchaseOrderModal(' . $order->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'
                            // '<a onClick="openEditPurchaseOrderModal('.$order->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                            // '<a href="' . Router::url(['controller' => 'SupplierPurchaseOrders', 'action' => 'delete', $order->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'.
                            // '<a data-toggle="tooltip" title="Verify" onclick="openVerifyModal('.$order->id.')" data-id="'.$order->id.'"><i class="fa fa-check" aria-hidden="true"></i></a>'
                    ];

                }    

                $this->set([
                    'purchase_orders' => $purchase_orders,
                    '_serialize' => ['purchase_orders'],
                ]);

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['data' => $purchase_orders]));

            }
            $this->Flash->error(__('The order request could not be saved. Please, try again.'));
        }

        $showrooms = $this->Showrooms->getShowrooms();

        $warehouses = $this->Warehouses->getWarehouses();

        $supplier_products = $this->SupplierProducts->find()
            ->where(['SupplierProducts.status IN' => 'A'])
            ->contain(['Products', 'Suppliers'])->toArray();           

        foreach ($supplier_products as $product) {

            if ($product->product->product_image) {
                $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
            }
        }

        $this->set(compact('order_request', 'showrooms', 'warehouses', 'supplier_products', 'order_products'));
    }

    public function order($id = null)
    {

        $order_request = $this->SupplierPurchaseOrders->get($id, contain: ['Showrooms', 'Warehouses']);

        // $order_products = $this->SupplierPurchaseOrdersItems->find()
        //     ->where(['SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id])
        //     ->contain([
        //         'Products' => [
        //             'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
        //             'SupplierProducts' => [
        //                 'fields' => ['SupplierProducts.product_id'],
        //                 'Suppliers' => [ 
        //                     'fields' => ['Suppliers.id', 'Suppliers.name']
        //                 ]
        //             ],
        //             'ProductVariants' => [
        //                 'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
        //                 // Ensure that ProductVariants are fetched only if variant_id exists
        //                 'conditions' => ['ProductVariants.id IS NOT NULL']
        //             ]
        //         ]
        //     ])
        //     ->toArray();

        // foreach ($order_products as $product) {

        //     if ($product->product->product_image) {
        //         $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_image);
        //     }

        //     $variantDetails = null;

        //     // Check if a variant_id is present
        //     if ($product->product_variant_id) {
        //         // Fetch only variants that match the variant_id in SupplierPurchaseOrdersItems
        //         $filtered_variants = array_filter($product->product->product_variants, function($variant) use ($product) {
        //             return $variant->id == $product->product_variant_id;
        //         });

        //         // If a matching variant is found, get its details
        //         if (!empty($filtered_variants)) {
        //             $variant = reset($filtered_variants); // Get the first matching variant
        //             $variantDetails = [
        //                 'variant_name' => $variant->variant_name,
        //                 'sku' => $variant->sku
        //             ];
        //         }
        //     }

        //     // Assign variant details back to the product
        //     if ($variantDetails) {
        //         $product->product->variant_name = $variantDetails['variant_name'];
        //         $product->product->sku = $variantDetails['sku'];
        //     }
            
        //     if ($product->product_attribute_id) 
        //     {
        //         // Assuming you have a ProductAttributes model to fetch the attributes
        //         $attributes = $this->ProductAttributes->find()
        //             ->where(['ProductAttributes.id' => $product->product_attribute_id])
        //             ->contain([
        //                 'Attributes' => [
        //                     'fields' => ['Attributes.name']
        //                 ],
        //                 'AttributeValues' => [
        //                     'fields' => ['AttributeValues.value']
        //                 ]
        //             ])
        //             ->first();

        //         if ($attributes) {
        //             $product->product->attributes = $attributes; // Add attributes to the product
        //         } else {
        //             $product->product->attributes = []; // Set to empty if no attributes found
        //         }
        //     } else {
        //         $product->product->attributes = []; // No attributes if attribute_id is null
        //     }
        // }

        $order_products = $this->SupplierPurchaseOrdersItems->find()
            ->where(['SupplierPurchaseOrdersItems.supplier_purchase_order_id' => $id])
            ->contain([
                'Products' => function ($q) {
                    return $q->select(['Products.id', 'Products.name', 'Products.sku'])
                        ->contain([
                            'ProductImages' => function ($q2) {
                                return $q2->where([
                                    'ProductImages.status' => 'A',
                                    'ProductImages.image_default' => 1
                                ])->select(['ProductImages.id', 'ProductImages.product_id', 'ProductImages.image']);
                            },
                            'SupplierProducts' => [
                                'Suppliers' => [
                                    'fields' => ['Suppliers.id', 'Suppliers.name']
                                ]
                            ],
                            'ProductVariants' => [
                                'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
                                'conditions' => ['ProductVariants.id IS NOT NULL']
                            ]
                        ]);
                }
            ])
            ->toArray();

            foreach ($order_products as $product) {

                if (!empty($product->product->product_images) && !empty($product->product->product_images[0]->image)) {
                    $product->product->product_image = $this->Media->getCloudFrontURL($product->product->product_images[0]->image);
                } else {
                    $product->product->product_image = null;
                }

                $variantDetails = null;
                if ($product->product_variant_id) {
                    $filtered_variants = array_filter($product->product->product_variants, function ($variant) use ($product) {
                        return $variant->id == $product->product_variant_id;
                    });

                    if (!empty($filtered_variants)) {
                        $variant = reset($filtered_variants);
                        $variantDetails = [
                            'variant_name' => $variant->variant_name,
                            'sku' => $variant->sku
                        ];
                    }
                }

                if ($variantDetails) {
                    $product->product->variant_name = $variantDetails['variant_name'];
                    $product->product->sku = $variantDetails['sku'];
                }

                if ($product->product_attribute_id) {
                    $attributes = $this->ProductAttributes->find()
                        ->where(['ProductAttributes.id' => $product->product_attribute_id])
                        ->contain([
                            'Attributes' => ['fields' => ['Attributes.name']],
                            'AttributeValues' => ['fields' => ['AttributeValues.value']]
                        ])
                        ->first();

                    $product->product->attributes = $attributes ?: [];
                } else {
                    $product->product->attributes = [];
                }
            }

        $response = [
            'status' => 'success',
            'order_request' => $order_request,
            'order_products' => $order_products
        ];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The order request could not be deleted. Please, try again.'];

        try {
            $record = $this->SupplierPurchaseOrders->get($id);
            $record->status = 'D';

            if ($this->SupplierPurchaseOrders->save($record)) {
                $response = ['success' => true, 'message' => 'The order request has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function approveProduct()
    {
        $this->request->allowMethod(['post']);

        $response = ['success' => false, 'message' => 'The products could not be approved. Please, try again.'];

        try {

            
            $id = $this->request->getData('order_product_id');
            $quantity = $this->request->getData('quantity');
            // $approve_status = $this->request->getData('approve_status');
            $comment = $this->request->getData('comment');

            $purchase_order_id = $this->request->getData('purchase_order_id');
            $supplierId = $this->request->getData('supplier_id');

            $record1 = $this->SupplierPurchaseOrders->get($purchase_order_id);
            $record1->status = __('A');

            $this->SupplierPurchaseOrders->save($record1);
            
            for($i=0; $i < sizeof($id); $i++)
            {
                $record = $this->SupplierPurchaseOrdersItems->get($id[$i]);
                $record->approved_quantity = $quantity[$i];
                // $record->approve_status = $approve_status[$i];
                $record->comment = $comment[$i];

                if ($this->SupplierPurchaseOrdersItems->save($record)) {
                    $response = ['status' => 'success'];
                }

            }

        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }


        $query_purchase_order = $this->SupplierPurchaseOrders->find()
                                ->where(['SupplierPurchaseOrders.supplier_id' => $supplierId])
                                ->where(['SupplierPurchaseOrders.status IN' => ['A', 'P', 'I']])
                                ->order(['SupplierPurchaseOrders.created' => 'DESC'])->toArray();  

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $purchase_orders = [];
        $i = 1;
        foreach ($query_purchase_order as $order) {

            $statusMap = [
            'A' => ['label' => __('Active'), 'class' => 'col-green'],
            'I' => ['label' => __('Inactive'), 'class' => 'col-red'],
            'P' => ['label' => __('Pending'), 'class' => 'col-blue']
            ];

            $status = $statusMap[$order->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];

            $paymentStatusMap = [
                'Paid' => ['label' => __('Paid'), 'class' => 'col-green'],
                'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
            ];

            $payment_status = $paymentStatusMap[$order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

            $deliveredStatusMap = [
                'Delivered' => ['label' => __('Delivered'), 'class' => 'col-green'],
                'Pending' => ['label' => __('Pending'), 'class' => 'col-blue']
            ];

            $delivery_status = $deliveredStatusMap[$order->delivery_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];


            $purchase_orders[] = [
                'order_date' => $order->order_date->format($dateFormat . ' ' . $timeFormat),
                'bill_no' => h($order->bill_no),
                'supp_bill_no' => h($order->supplier_bill_no),
                'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                'payment_status' => '<div class="badge-outline ' . h($payment_status['class']) . '">' . h($payment_status['label']) . '</div>',
                'delivery_status' => '<div class="badge-outline ' . h($delivery_status['class']) . '">' . h($delivery_status['label']) . '</div>',
                'actions' => '
                    <a onClick="openViewPurchaseOrderModal(' . $order->id . ')" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>'.
                    '<a onClick="openEditPurchaseOrderModal('.$order->id.')" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                    '<a href="' . Router::url(['controller' => 'SupplierPurchaseOrders', 'action' => 'delete', $order->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'.
                    '<a data-toggle="tooltip" title="Verify" onclick="openVerifyModal('.$order->id.')" data-id="'.$order->id.'"><i class="fa fa-check" aria-hidden="true"></i></a>'
            ];

        }    

        $purchase_order_bills = $this->SupplierPurchaseOrders->getPurchaseOrderBills($supplierId);

        $this->set([
            'purchase_orders' => $purchase_orders,
            '_serialize' => ['purchase_orders'],
        ]);

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => __('success'), 'data' => $purchase_orders, 'purchase_order_bills' => $purchase_order_bills]));

        // return $this->response->withType('application/json')
        //     ->withStringBody(json_encode(['status' => 'success', 'data' => $purchase_orders]));

    }

    public function testEmail()
    {
        $to = trim('<EMAIL>');
        $from = Configure::read('Settings.ADMIN_EMAIL');
        $subject = "Supplier Purchase Order";
        $template = "supplier_purchase_order";
        // $viewVars = array('name' => 'zaid', 'text' => 'email');
        // echo print_r($viewVars);die;    
        $send_email = $this->Global->send_email($to, $from, $subject, $template);

        // print_r($send_email);die;

        if ($send_email) {
            $this->Flash->success('Please check your email.');
        } else {
            $this->Flash->error('Unable to send the email. Please try again.');
        }

        return $this->redirect(['action' => 'index']);
    }
}
