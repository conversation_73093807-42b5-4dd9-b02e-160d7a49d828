<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Suppliers Model
 *
 * @property \App\Model\Table\SupplierGroupsTable&\Cake\ORM\Association\BelongsTo $SupplierGroups
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\HasMany $Products
 * @property \App\Model\Table\SupplierCreditNotesTable&\Cake\ORM\Association\HasMany $SupplierCreditNotes
 * @property \App\Model\Table\SupplierPaymentsTable&\Cake\ORM\Association\HasMany $SupplierPayments
 * @property \App\Model\Table\SupplierPurchaseOrdersTable&\Cake\ORM\Association\HasMany $SupplierPurchaseOrders
 * @property \App\Model\Table\SupplierShowroomsTable&\Cake\ORM\Association\HasMany $SupplierShowrooms
 * @property \App\Model\Table\SupplierStocksTable&\Cake\ORM\Association\HasMany $SupplierStocks
 *
 * @method \App\Model\Entity\Supplier newEmptyEntity()
 * @method \App\Model\Entity\Supplier newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Supplier> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Supplier get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Supplier findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Supplier patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Supplier> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Supplier|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Supplier saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class SupplierPaymentTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('supplier_payments');
        $this->setDisplayField('payment_mode');
        $this->setPrimaryKey('id');

        $this->belongsTo('SupplierPurchaseOrders', [
            'foreignKey' => 'supplier_purchase_order_id',
            'joinType' => 'INNER'
        ]);

        $this->belongsTo('Suppliers', [
            'foreignKey' => 'supplier_id',
        ]);

        $this->belongsTo('Showrooms', [
            'foreignKey' => 'showroom_id',
        ]);

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {

        $validator
            ->nonNegativeInteger('supplier_id')
            ->notEmptyString('supplier_id'); 

        $validator
            ->nonNegativeInteger('supplier_purchase_order_id')
            ->notEmptyString('supplier_purchase_order_id');

        $validator
            ->nonNegativeInteger('showroom_id')
            ->notEmptyString('showroom_id');        
 

        return $validator;
    }

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['supplier_purchase_order_id'], 'SupplierPurchaseOrders'), ['errorField' => 'supplier_purchase_order_id']);
        $rules->add($rules->existsIn(['supplier_id'], 'Suppliers'), ['errorField' => 'supplier_id']);
        $rules->add($rules->existsIn(['showroom_id'], 'Showrooms'), ['errorField' => 'showroom_id']);

        return $rules;
    }

    //S
    public function getSupplierPayments($showroom_id) {
         
        $today_date = date('Y-m-d');
        return $this->find()
            ->select(['SupplierPayment.id', 'Suppliers.name', 'SupplierPayment.amount', 'SupplierPurchaseOrders.payment_status', 'SupplierPayment.payment_date', 'SupplierPayment.supplier_id', 'SupplierPayment.supplier_purchase_order_id', 'SupplierPayment.showroom_id', 'SupplierPayment.payment_mode', 'SupplierPayment.cheque_no', 'SupplierPayment.cheque_date', 'SupplierPayment.bank_name', 'SupplierPayment.cheque_copy', 'SupplierPayment.payee_name', 'SupplierPayment.receipt_number', 'SupplierPayment.receipt'])
            ->contain([
                'SupplierPurchaseOrders',
                'Suppliers',
                'Showrooms'
            ])
            ->where(['SupplierPayment.showroom_id' => $showroom_id, 'DATE(SupplierPayment.payment_date)' => $today_date])
            ->order(['SupplierPayment.payment_date' => 'DESC'])
            ->all();
    }
}
