<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;

/**
 * ProductImages Controller
 *
 * @property \App\Model\Table\ProductImagesTable $ProductImages
 */
class ProductImagesController extends AppController
{

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->ProductImages->find()
            ->contain(['Products']);
        $productImages = $this->paginate($query);

        $this->set(compact('productImages'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Image id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $productImage = $this->ProductImages->get($id, contain: ['Products']);
        $this->set(compact('productImage'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    // public function add()
    // {
    //     $productImage = $this->ProductImages->newEmptyEntity();
    //     if ($this->request->is('post')) {
    //         $productImage = $this->ProductImages->patchEntity($productImage, $this->request->getData());
    //         if ($this->ProductImages->save($productImage)) {
    //             $this->Flash->success(__('The product image has been saved.'));

    //             return $this->redirect(['action' => 'index']);
    //         }
    //         $this->Flash->error(__('The product image could not be saved. Please, try again.'));
    //     }
    //     $products = $this->ProductImages->Products->find('list', limit: 200)->all();
    //     $this->set(compact('productImage', 'products'));
    // }

    public function addMedia()
    {
        $this->request->allowMethod(['post']);
        $productImageData = $this->request->getData();
        $allowedImageFormats = Configure::read('Constants.PRODUCT_IMAGE_TYPE');
        $maxImageSize = Configure::read('Constants.PRODUCT_IMAGE_SIZE') * 1024 * 1024;
        $minWidth = Configure::read('Constants.PRODUCT_IMAGE_MIN_WIDTH');
        $maxWidth = Configure::read('Constants.PRODUCT_IMAGE_MAX_WIDTH');
        $minHeight = Configure::read('Constants.PRODUCT_IMAGE_MIN_HEIGHT');
        $maxHeight = Configure::read('Constants.PRODUCT_IMAGE_MAX_HEIGHT');

        $allowedVideoFormats = Configure::read('Constants.PRODUCT_VIDEO_TYPE');
        $maxVideoSize = Configure::read('Constants.PRODUCT_VIDEO_SIZE') * 1024 * 1024;

        $successfulUploads = [];
        $errors = [];

        // Handle Images
        if (!empty($productImageData['images']) && $productImageData['media_type'] === 'Image') {
            $images = $productImageData['images'];

            foreach ($images as $image) {
                if ($image->getError() === UPLOAD_ERR_OK) {
                    $imageName = trim($image->getClientFilename());
                    $imageSize = $image->getSize();
                    $imageExt = strtolower(pathinfo($imageName, PATHINFO_EXTENSION));

                    if (!in_array($imageExt, $allowedImageFormats)) {
                        $errors[] = __('Invalid file type for product image: {0}. Only the following formats are allowed: {1}', [$imageName, implode(', ', $allowedImageFormats)]);
                        continue;
                    }

                    if ($imageSize > $maxImageSize) {
                        $errors[] = __('Product image size exceeds the maximum allowed size of {0} MB for image: {1}.', [($maxImageSize / (1024 * 1024)), $imageName]);
                        continue;
                    }

                    list($width, $height) = getimagesize($image->getStream()->getMetadata('uri'));

                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        $errors[] = __('Product image dimensions must be between {0}x{1} and {2}x{3} pixels for image: {4}.', [$minWidth, $minHeight, $maxWidth, $maxHeight, $imageName]);
                        continue;
                    }

                    if (!empty($imageName)) {
                        $imageTmpName = $image->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.PRODUCT_IMAGE_PATH');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $imageFile = pathinfo($imageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $imageExt;

                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $uploadFolder);
                        if ($uploadResult === 'Success') {
                            $successfulUploads[] = [
                                'product_id' => $productImageData['product_id'],
                                'image' => $folderPath . $imageFile,
                                'image_default' => 0,
                                'status' => 'A',
                                'media_type' => 'Image'
                            ];
                        } else {
                            $errors[] = __('There was an issue uploading the product image: {0}.', [$imageName]);
                        }
                    }
                } else {
                    $errors[] = __('Error uploading image: {0}.', [$image->getError()]);
                }
            }
        }

        // Handle Videos
        if (!empty($productImageData['videos']) && $productImageData['media_type'] === 'Video') {
            $videos = $productImageData['videos'];

            foreach ($videos as $video) {
                if ($video->getError() === UPLOAD_ERR_OK) {
                    $videoName = trim($video->getClientFilename());
                    $videoSize = $video->getSize();
                    $videoExt = strtolower(pathinfo($videoName, PATHINFO_EXTENSION));

                    if (!in_array($videoExt, $allowedVideoFormats)) {
                        $errors[] = __('Invalid file type for product video: {0}. Only the following formats are allowed: {1}', [$videoName, implode(', ', $allowedVideoFormats)]);
                        continue;
                    }

                    if ($videoSize > $maxVideoSize) {
                        $errors[] = __('Product video size exceeds the maximum allowed size of {0} MB for video: {1}.', [($maxVideoSize / (1024 * 1024)), $videoName]);
                        continue;
                    }

                    // list($width, $height) = getimagesize($video->getStream()->getMetadata('uri'));

                    // if ($width < $minVideoWidth || $width > $maxVideoWidth || $height < $minVideoHeight || $height > $maxVideoHeight) {
                    //     $errors[] = __('Product video dimensions must be between {0}x{1} and {2}x{3} pixels for video: {4}.', [$minVideoWidth, $minVideoHeight, $maxVideoWidth, $maxVideoHeight, $videoName]);
                    //     continue;
                    // }

                    if (!empty($videoName)) {
                        $videoTmpName = $video->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.PRODUCT_VIDEO_PATH');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $videoFile = pathinfo($videoName, PATHINFO_FILENAME) . '_' . $rand . '.' . $videoExt;

                        $uploadResult = $this->Media->upload($videoTmpName, $targetdir, $videoFile, $uploadFolder);
                        if ($uploadResult === 'Success') {
                            $successfulUploads[] = [
                                'product_id' => $productImageData['product_id'],
                                'video' => $folderPath . $videoFile,
                                'status' => 'A',
                                'media_type' => 'Video'
                            ];
                        } else {
                            $errors[] = __('There was an issue uploading the product video: {0}.', [$videoName]);
                        }
                    }
                } else {
                    $errors[] = __('Error uploading video: {0}.', [$video->getError()]);
                }
            }
        }


        foreach ($successfulUploads as $data) {
            $productImage = $this->ProductImages->newEntity($data);
            if (!$this->ProductImages->save($productImage)) {
                $errors[] = __('Failed to save image/video data for: {0}.', [$data['media_type'] === 'Image' ? $data['image'] : $data['video']]);
            }
        }

        if (!empty($errors)) {
            return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'errors' => $errors]));
        }

        return $this->response->withType('application/json')->withStringBody(json_encode(['success' => true, 'message' => __('The product media has been saved.')]));
    }





    /**
     * Edit method
     *
     * @param string|null $id Product Image id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $productImage = $this->ProductImages->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $productImage = $this->ProductImages->patchEntity($productImage, $this->request->getData());
            if ($this->ProductImages->save($productImage)) {
                $this->Flash->success(__('The product image has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product image could not be saved. Please, try again.'));
        }
        $products = $this->ProductImages->Products->find('list', limit: 200)->all();
        $this->set(compact('productImage', 'products'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Product Image id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function deleteImage($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $productImage = $this->ProductImages->get($id);
        $response = ['success' => false, 'message' => 'The product image could not be deleted. Please, try again.'];
        if ($productImage) {
            if ($this->ProductImages->delete($productImage)) {
                $response = ['success' => true, 'message' => 'The product image has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The product image could not be deleted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product image does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function makeDefault($id = null)
    {
        $this->request->allowMethod(['post']);
        $productImage = $this->ProductImages->get($id);
        $response = ['success' => false, 'message' => 'The product image could not be made default. Please, try again.'];
        if ($productImage) {
            if ($this->ProductImages->makeDefault($productImage)) {
                $response = ['success' => true, 'message' => 'The product image has been makred default.'];
            } else {
                $response = ['success' => false, 'message' => 'The product image could not be made default. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product image does not exist.'];
        }


        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    public function getImages()
    {
        $this->request->allowMethod(['get']);
        $productId = $this->request->getQuery('product_id');

        $productImages = $this->ProductImages->find('all', [
            'conditions' => ['product_id' => $productId, 'status' => 'A', 'media_type' => 'Image'],
            'order' => ['image_default' => 'DESC']
        ]);

        if ($productImages->count() == 0) {
            return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'No images found for this product.']));
        }

        $images = [];
        foreach ($productImages as $image) {
            $fileName = basename($image->image);
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            $nameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

            $shortName = strlen($nameWithoutExtension) > 14 ? substr($nameWithoutExtension, 0, 11) : $nameWithoutExtension;
            $shortName .= '.' . $extension;

            $images[] = [
                'id' => $image->id,
                'src' => $this->Media->getCloudFrontURL($image->image),
                'default' => $image->image_default,
                'shortName' => $shortName
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode(['success' => true, 'images' => $images]));
    }


    public function getVideos()
    {
        $this->request->allowMethod(['get']);
        $productId = $this->request->getQuery('product_id');

        $productVideos = $this->ProductImages->find('all', [
            'conditions' => [
                'product_id' => $productId,
                'status' => 'A',
                'media_type' => 'Video'
            ],
            'order' => ['id' => 'DESC'] // Adjust order as needed
        ]);

        if ($productVideos->count() == 0) {
            return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'No videos found for this product.']));
        }

        $videos = [];
        foreach ($productVideos as $video) {
            $fileName = basename($video->video);
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            $nameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

            $shortName = strlen($nameWithoutExtension) > 14 ? substr($nameWithoutExtension, 0, 11) : $nameWithoutExtension;
            $shortName .= '.' . $extension;

            $videos[] = [
                'id' => $video->id,
                'src' => $this->Media->getCloudFrontURL($video->video),
                'shortName' => $shortName
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode(['success' => true, 'videos' => $videos]));
    }
}
