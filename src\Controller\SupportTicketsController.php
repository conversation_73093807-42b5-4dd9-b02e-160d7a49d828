<?php
declare(strict_types=1);

namespace App\Controller;

use App\Enum\ExpenseStatus;
use App\Model\Entity\Expense;
use Cake\Core\Configure;
use Cake\I18n\FrozenTime;
use Cake\ORM\TableRegistry;

/**
 * Expenses Controller
 *
 * @property \App\Model\Table\SupportTicketsTable $SupportTickets
 */
class SupportTicketsController extends AppController
{    
    protected $SupportCategories, $Customers, $Users, $SupportTicketImages, $SupportTicketUpdates, $Roles;
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('Authentication.Authentication');
        $this->SupportCategories = $this->fetchTable('SupportCategories');
        $this->Customers = $this->fetchTable('Customers');
        $this->Users = $this->fetchTable('Users');
        $this->SupportTicketImages = $this->fetchTable('SupportTicketImages');
        $this->SupportTicketUpdates = $this->fetchTable('SupportTicketUpdates'); 
        $this->Roles = $this->fetchTable('Roles'); 
    }

    public function index()
    {
        $title = 'Support Tickets';
        $query = $this->SupportTickets->find('all')
            ->contain(['SupportCategories']);
        $supportTickets = $this->paginate($query);

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('supportTickets', 'dateFormat', 'timeFormat', 'title'));
    }

    public function search()
    {
        $this->autoRender = false;
        $this->response = $this->response->withType('application/json');

        $query = $this->request->getQuery('term'); // Get the search term

            $customers = $this->Customers->find()
                ->contain(['Users']) // Join users table
                ->select(['Customers.id', 'Users.email', 'Users.mobile_no', 'Users.first_name', 'Users.last_name'])
                ->where([
                    'Users.status' => 'A', // Select only active users
                    'OR' => [
                        'Users.email LIKE' => "%$query%",
                        'Users.mobile_no LIKE' => "%$query%"
                    ]
                ])
                ->limit(100)
                ->toArray();

            $results = [];
        foreach ($customers as $customer) {
            $email = $customer->user->email ?? '';
            $mobile = $customer->user->country_code. ' ' . $customer->user->mobile_no ?? '';
            $fullname = $customer->user->first_name ?? '' . $customer->user->last_name ?? '';

            $text = trim("Email: $email | Mobile: $mobile", '| ');

            $results[] = [
                'id' => $customer->id,
                'email' => $email,
                'mobile' => $mobile,
                'text' => $text,
                'fullname' => $fullname
            ];
        }

        return $this->response->withStringBody(json_encode($results));

    }
    public function supportTicketAdd()
    {
        $title = 'Support Tickets Add';

        $allCategory = $this->SupportCategories->getAllActiveSupportCategories()->all();

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }
            $data = $this->request->getData();
            $data['created_by'] = $userId;
            $data['updated_by'] = $userId;
            $data['support_category_id'] = $data['category'];
            $timestamp = FrozenTime::now()->format('YmdHis'); // Current date and time in YYYYMMDDHHMMSS format
            $randomNumber = mt_rand(1000, 9999); // Random 4-digit number
            $data['ticketID'] = "$timestamp-$randomNumber";

            $addTicket = $this->SupportTickets->addTicketData($data);
           
            if ($addTicket) {
              
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_id', $addTicket) : [];
         
                $this->Flash->websiteSuccess(__('Ticket created successfully.'));
                return $this->redirect(['controller' => 'SupportTickets', 'action' => 'supportTicketList']);
            } else {
                $this->Flash->websiteSuccess(__('Unable to create ticket. Please try again.'));
            }
        }
        $this->set(compact( 'allCategory','dateFormat', 'timeFormat', 'title'));
    }

    public function reply()
    {
        $identity = $this->Authentication->getIdentity();
        $ticketId = $this->request->getData('id');
      
        if (!$identity) {
            return $this->redirect(['controller' => 'Website', 'action' => 'login'])->withFlash(__('Please login to access this page.'), ['element' => 'error']);
        }

        $users = $this->Users->find()
        ->contain([
            'Customers' => function ($q) {
                return $q->select(['id']); // Select only the Customer.id field
            }
        ])
        ->select(['Users.id']) // Select the necessary fields from Users
        ->where(['Users.status' => 'A'])
        ->where(['Users.id IS' => $identity->id])
        ->first();


        if ($this->request->is(['post', 'put'])) {
           
            $data = $this->request->getData();
         
            $support_ticket_id = $data['id'];           
            $data['updated_by'] = $this->Authentication->getIdentity()->id;                
           
            $addUpdate = $this->SupportTicketUpdates->addUpdate($support_ticket_id, $data); 
         
            if ($addUpdate) {
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_update_id', $addUpdate) : [];
         
                //  $this->Flash->websiteSuccess(__('Your reply has been submitted.'));
                $this->redirect('/support-tickets/view/' . $support_ticket_id);


              
            }
          //  $this->Flash->websiteSuccess(__('Unable to submit your reply. Please try again.'));
        }
        $this->Flash->websiteSuccess(__('Your reply has been submitted.'));
     
        $this->redirect('/support-tickets/view/' . $support_ticket_id);


    }

    public function supportTicketList()
    {
        $title = 'Support Tickets List';
        $search_str = "";
        $limit = 20;
        $page = (int) $this->request->getQuery('page', 1);
        $filter_ticket_status = "";
        $filter_support_category = "";
        $priority = ""; // <-- Add this to avoid undefined variable error

        if ($this->request->getQuery('priority')) {
            $priority = $this->request->getQuery('priority');
        }

        if ($this->request->getQuery('status')) {
            $filter_ticket_status = $this->request->getQuery('status');
        }

        if ($this->request->getQuery('category')) {
            $filter_support_category = $this->request->getQuery('category');
        }

        if ($this->request->getQuery('search')) {
            $search_str = $this->request->getQuery('search');
        }

        // Call the function to get tickets
        $supportTickets = $this->SupportTickets->listSupportTicketsData(
            $filter_ticket_status,
            $filter_support_category,
            $search_str,
            $page,
            $limit,
            $priority
        );
        
        // Ensure settings exist
        $dateFormat = Configure::read('Settings.DATE_FORMAT') ?? 'Y-m-d';
        $timeFormat = Configure::read('Settings.TIME_FORMAT') ?? 'H:i:s';

        $allCategory = $this->SupportCategories->getAllActiveSupportCategories()->all();
        
        $this->set(compact('allCategory', 'supportTickets', 'dateFormat', 'timeFormat', 'title'));
    }


    public function supportTicketDashboard()
    {
        $title = 'Support Tickets Dashboard';
        $data = $this->request->getQuery();
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $statuses = ['All' => null, 'Open' => 'Open', 'In Progress' => 'In Progress', 'Resolved' => 'Resolved'];

        $counts = [];
        foreach ($statuses as $key => $status) {
            $query = $this->SupportTickets->find();
            if ($status !== null) {
                $query->where(['status' => $status]);
            }
            $counts[$key] = $query->count();
        }

        $after_sales_service = $this->SupportTickets->find()
            ->where(['support_category_id' => 3])
            ->count();

        $allSupportCategoriesWithStats = $this->SupportCategories->allSupportCategoriesWithStats();
        // Format closure time from hours to "Xh Ym"
        foreach ($allSupportCategoriesWithStats as &$category) {
            if (!is_null($category['avg_closure_time'])) {
                $hours = floor($category['avg_closure_time']); // Extract hours
                $minutes = round(($category['avg_closure_time'] - $hours) * 60); // Convert fraction to minutes
                $category['avg_closure_time'] = ($hours ? "{$hours}h " : '') . ($minutes ? "{$minutes}m" : '');
            } else {
                $category['avg_closure_time'] = "N/A"; // Handle cases with no closure time
            }
        }

        $requested_user = $this->Authentication->getIdentity();
        $role_name = '';

        if ($requested_user && !empty($requested_user->role_id)) {
            $role = $this->Roles->find()
                ->select(['name'])
                ->where(['id' => $requested_user->role_id])
                ->first();

            if ($role) {
                $role_name = strtolower($role->name);
            }
        }

        // Roles that should not see the Dashboard breadcrumb
        $restrictedRoles = [
            'call center agent',
            'call center supervisor',
            'after sales call center agent'
        ];

        $hideDashboardBreadcrumb = in_array($role_name, $restrictedRoles);

        $this->set(compact('after_sales_service','allSupportCategoriesWithStats','counts', 'dateFormat', 'timeFormat', 'title', 'hideDashboardBreadcrumb'));
    }

    public function view($id = null)
    {
        $user = $this->Authentication->getIdentity();
        $customers = $this->Users->find()
            ->contain(['Customers' => function ($q) {
            return $q->select(['id']); // Select only the Customer.id field
            }])
            ->where(['Users.status' => 'A'])
            ->all();
       
        $title = 'View Support Ticket';
        $supportTicket = $this->SupportTickets->webViewTicketDetail($id);  
      
        // dd($customers);

        // $supportTicket = $this->SupportTickets->get($id, [
        //     'contain' => [
        //         'SupportCategories',
        //         'CreatedByUsers' => ['fields' => ['id', 'first_name', 'last_name', 'email']],
        //         'SupportTicketImages',
        //         'SupportTicketUpdates' => [
        //             'CreatedByUsers' => ['fields' => ['id', 'first_name', 'last_name', 'email']],
        //             'sort' => ['SupportTicketUpdates.created' => 'ASC']
        //         ]
        //     ]
        // ]);

        // // Get current user for message alignment in view
        // $currentUser = $this->request->getAttribute('identity');
        
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->viewBuilder()->setTemplatePath('SupportTickets');

        $this->set(compact('supportTicket', 'dateFormat', 'timeFormat', 'title','customers'));

        $this->render('support_ticket_view');

    }
    // update status code
    public function updateStatus()
    {
        if ($this->request->is(['post', 'put'])) {
            $data = $this->request->getData();
            $supportTicket = $this->SupportTickets->get($data['id']);
            $supportTicket->status = $data['status'];

            if (!empty($data['customer_id'])) {
                $supportTicket->customer_id = $data['customer_id'];
            }

            if ($this->SupportTickets->save($supportTicket)) {
            $this->Flash->success(__('Status updated successfully.'));
            } else {
            $this->Flash->error(__('Unable to update status. Please try again.'));
            }
        }

        return $this->redirect($this->referer());
    }

    /***  Private functions start  ****/
    private function supportImageUpload($flag, $id)
        {
            $files =  $this->request->getData('images');
            $uploadedImages = [];
            $i = 0;
            foreach ($files as $file) {
                if ($file->getError() === UPLOAD_ERR_OK) {
                    $fileName = trim($file->getClientFilename());
                    if (!empty($fileName)) {
                        $imageTmpName = $file->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Settings.SUPPORT_DESK');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                        } else {
                            $supportImage = $this->SupportTicketImages->newEmptyEntity();
                            if($flag == 'ticket_update_id'){
                                $supportImage->support_ticket_update_id = $id;
                            }else{
                                $supportImage->support_ticket_id = $id;
                            }
                            $supportImage->image = $folderPath . $imageFile;
                            $i++;
                            if ($this->SupportTicketImages->save($supportImage)) {
                                $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                            } else {
                                $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                            }
                        }
                    }
                }
            }
            return $uploadedImages;
        }
    

    /****  Private functions end   ***/
}
