<?php
declare(strict_types=1);

namespace App\Enum;

enum ExpensePaymentStatus: string
{
    case UNPAID = 'Unpaid';
    case PAID = 'Paid';

    /**
     * Returns the label for the enum value.
     *
     * This method maps the enum value to a human-readable label.
     * It can be used to display a label in the UI, such as a dropdown or list.
     *
     * @return string The label associated with the enum value.
     */
    public function label(): string
    {
        return match ($this) {
            self::UNPAID => 'Unpaid',
            self::PAID => 'Paid',
        };
    }

    /**
     * Returns the CSS badge class for the enum value.
     *
     * This method maps the enum value to a CSS class that can be used to
     * style the status in the UI (e.g., a "success" or "danger" badge).
     *
     * @return string The badge class associated with the enum value.
     */
    public function getBadge(): string
    {
        return match ($this) {
            self::UNPAID => 'badge-danger',
            self::PAID => 'badge-success',
        };
    }

    /**
     * Returns an associative array of enum labels for use in a dropdown (for UI rendering).
     *
     * This method returns an associative array where keys are the enum values
     * (e.g., 'Unpaid', 'Paid') and values are human-readable labels (e.g., 'Unpaid', 'Paid').
     * This is useful for populating a dropdown list or select field in a form.
     *
     * @return array<string, string> An associative array of enum values and labels.
     */
    public static function toDropdownArray(): array
    {
        return array_map(
            fn ($status) => $status->label(),
            array_combine(
                array_map(fn ($status) => $status->value, self::cases()),
                self::cases() // enum cases
            )
        );
    }

    /**
     * Returns an array of enum values for use in validation (for the `inList` rule).
     *
     * This method returns an array where the values are the actual enum values
     * (e.g., 'Unpaid', 'Paid'). This is useful for validation rules like `inList`.
     *
     * @return array<string> An array of enum values.
     */
    public static function toDropdownValues(): array
    {
        return array_keys(self::toDropdownArray());
    }
}
