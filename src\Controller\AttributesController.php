<?php

declare(strict_types=1);

namespace App\Controller;

/**
 * Attributes Controller
 *
 * @property \App\Model\Table\AttributesTable $Attributes
 */
class AttributesController extends AppController
{

    protected $AttributeValues;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->AttributeValues = $this->fetchTable('AttributeValues');
    }

    public function index()
    {

        $statusFilter = $this->request->getQuery('status');

        $query = $this->Attributes->find();

        if (!empty($statusFilter)) {
            $query->where(['Attributes.status' => $statusFilter]);
        }

        $attributes = $this->paginate($query);

        $this->set(compact('attributes', 'statusFilter'));
    }

    /**
     * View method
     *
     * @param string|null $id Attribute id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $attribute = $this->Attributes->get($id, contain: ['AttributeValues', 'CategoryAttributes', 'ProductAttributes']);
        $this->set(compact('attribute'));
    }

    public function add()
    {
        $attribute = $this->Attributes->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if ($this->Attributes->saveWithValues($data)) {
                $this->Flash->success(__('The attribute has been saved.'));
                return $this->redirect(['action' => 'index']);
            } else {
                $this->Flash->error(__('The attribute could not be saved. Please, try again.'));
            }
        }

        // If the request is not POST, or save failed, render the form
        $this->set(compact('attribute'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Attribute id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $attribute = $this->Attributes->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $attribute = $this->Attributes->patchEntity($attribute, $this->request->getData());
            if ($this->Attributes->save($attribute)) {
                $this->Flash->success(__('The attribute has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The attribute could not be saved. Please, try again.'));
        }
        $this->set(compact('attribute'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Attribute id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $attribute = $this->Attributes->get($id);
        if ($this->Attributes->delete($attribute)) {
            $this->Flash->success(__('The attribute has been deleted.'));
        } else {
            $this->Flash->error(__('The attribute could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function autocomplete()
    {

        if ($this->request->is('ajax')) {
            // Example: Get query parameter
            $query = $this->request->getQuery('query');

            // Fetch attributes matching the query
            $results = $this->Attributes->find('all')
                ->where(['name LIKE' => "%$query%"])
                ->limit(10) // Limit results for better performance
                ->toArray();

            // Prepare response data
            $data = [];
            foreach ($results as $result) {
                $data[] = [
                    'id' => $result->id,
                    'name' => $result->name,
                ];
            }

            $this->set([
                'customers' => $data,
                '_serialize' => ['data'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $data]));
        }

        return null;
    }

    public function getValues($id)
    {
        $this->request->allowMethod(['ajax']);

        $attributeValues = $this->Attributes->AttributeValues->find()
            ->where(['attribute_id' => $id, 'status' => 'A'])
            ->all()
            ->toArray();

        $values = array_map(function ($value) {
            return $value->value;
        }, $attributeValues);

        $this->set([
            'customers' => $values,
            '_serialize' => ['values'],
        ]);

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([$values]));
    }

    public function getValuesforAttr($id)
    {
        $this->request->allowMethod(['ajax']);

        $attributeValues = $this->Attributes->AttributeValues->find()
            ->select(['id', 'value'])
            ->where(['attribute_id' => $id, 'status' => 'A'])
            ->all()
            ->toArray();

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($attributeValues)); 
    }
}
