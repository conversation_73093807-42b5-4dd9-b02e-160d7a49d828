<?php $this->start('meta'); ?>
<meta name="title" content="<?= h($product_detail['meta_title']) ?>">
<meta name="description" content="<?= h($product_detail['meta_description']) ?>">
<meta name="keywords" content="<?= h($product_detail['meta_keyword']) ?>">

<?php $this->end(); ?>
<style>

span#flash_sale-label {
    background: #f9dab9;
    padding: 10px 10px;
    margin-top: 10px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 17px;
}
.counter-btn {
    background-color: #ddd;
    border: 1px solid #ccc;
    cursor: pointer;
    font-size: 14px !important;
    margin: 0px;
    height: 45px;
    border-radius: 70px;
    width: 45px;
}
.counter-value{
    width: 40px;
}
.counter {
    display: inline-flex;
    width: max-content;
    align-items: center;
    background-color: white;
    border-radius: 22px;
    border: 1px solid rgba(128, 128, 128, 0.411);
    margin-top: 30px;
    margin-bottom: 30px;
    justify-content: center;
}
.counter-btn {
    background-color: #ddd;
    border: 1px solid #ccc;
    cursor: pointer;
    font-size: 18px;
    margin: 0px;
    height: 45px;
    border-radius: 70px;
    width: 45px;
}</style>
<?php $this->start('add_css'); ?>

<style>
        /* Auto-scroll controls styling */
        .auto-scroll-controls {
            position: absolute;
            bottom: -30px;
            right: 0;
            z-index: 10;
        }

        #toggle-auto-scroll {
            background: rgba(255, 165, 0, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        #toggle-auto-scroll:hover {
            background: rgba(255, 165, 0, 1);
        }

        /* Hide scrollbar but keep functionality */
        .p-v-p-item-description-image-slider-container {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .ax-stars {
            display: flex;
            flex-direction: row-reverse;
            justify-content: left;
            font-size: 30px;
            cursor: pointer;
        }
        .ax-stars input {
            display: none;
        }
        .ax-stars label {
            color: gray;
            transition: color 0.2s;
        }
        .ax-stars input:checked ~ label,
        .ax-stars label:hover,
        .ax-stars label:hover ~ label {
            color: #37cd37;
        }

        .customer.add-review-section {
            max-width: 500px;
            text-align: left;
        }

        .rating-container {
            position: relative;
            margin-bottom: 20px;
        }

        .rating-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
        }

        .error-message {
            color: #ff0000;
            font-size: 14px;
            margin-top: 5px;
            display: block;
        }

        #rating-error {
            margin-top: 8px;
            margin-bottom: 5px;
        }

        .rating-selected .ax-stars {
            border-color: #37cd37;
        }

        /* Add a note about the rating requirement */
        .ax-stars::after {
            content: '(Click on a star to rate)';
            font-size: 12px;
            color: #666;
            display: block;
            margin-top: 8px;
        }

        .rating-selected::after {
            display: none;
        }

        /* Textarea error styling */
        #reviewText.error {
            border: 1px solid #ff0000;
        }

        /* Make the review error message more prominent */
        #review-error {
            color: #ff0000;
            margin-top: 5px;
            margin-bottom: 10px;
        }

        /* Style the textarea */
        #reviewText {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            min-height: 100px;
            font-family: inherit;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        #reviewText:focus {
            outline: none;
            border-color: #37cd37;
            box-shadow: 0 0 5px rgba(55, 205, 55, 0.3);
        }

        /* Style for the textarea container and label */
        .textarea-container {
            margin-bottom: 15px;
        }

        .textarea-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
        }

        .required {
            color: #ff0000;
            margin-left: 3px;
        }

    .color-gb.g {
        background: #d9d9d9 ;
    }
    .rating-stars .star, .star-rating .star {
        color: rgb(55, 205, 55);
        font-size: 26px;
        margin-right: 2px;
    }

    .rating-stars .star:before, .star-rating .star:before {
        content: '\2605';
    }

    .rating-stars .star.half:after, .star-rating .star.half:after {
        content: '\2605';
        color: rgb(55, 205, 55);
        position: absolute;
        margin-left: -17px;
        width: 9px;
        overflow: hidden;
    }

    .rating-stars .star.empty, .star-rating .star.empty {
        color: #CBCBCB; /* Gray for empty stars */
    }

    /* Improved review card styling */
    .my-carousel-inner-s {
        margin-bottom: 20px;
    }

    .my-card-s {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 15px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .my-card-s:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .product-view-page-card-date {
        color: #888;
        font-size: 12px;
        display: block;
        margin-bottom: 10px;
    }

    .cards-inner-text {
        display: flex;
        flex-direction: column;
    }

    .dp-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 15px;
        float: left;
    }

    .dp-name {
        font-weight: 600;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .comment {
        margin-top: 15px;
        color: #333;
        font-style: italic;
        line-height: 1.5;
        clear: both;
    }

    .clone-other-footer-text {
        margin: -205px 0px 0px 0px;
    }

    .for-r-margin {
        margin-right: -120px;
    }

    .c-custom-card-name {
        margin-left:12px
    }

    #parentReview {
        height: 530px;
        /* overflow-y: scroll;
        overflow-x: hidden; */
        position: relative;
        padding: 10px;
    }
    #parentReview::-webkit-scrollbar {
        width: 8px;
    }
    #parentReview::-webkit-scrollbar-track {
        border-radius: 50px !important;
    }
    #parentReview::-webkit-scrollbar-thumb {
        border-radius: 10px;
        -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
        background-color: #aaa;
    }

    /* Review Modal Styles */
    .review-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
    }

    .review-modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        width: 500px;
        max-width: 90%;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        position: relative;
    }

    .review-modal-close {
        color: #aaa;
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .review-modal-close:hover,
    .review-modal-close:focus {
        color: black;
        text-decoration: none;
    }

    .review-modal-title {
        margin-top: 0;
        color: #333;
        font-size: 20px;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    /* Loading indicator for infinite scroll */
    .review-loading {
        text-align: center;
        padding: 15px;
        display: none;
    }

    .review-loading img {
        width: 30px;
        height: 30px;
    }
    .p-v-p-item-description-image-description-price-offer {
        margin-left: 12px;
    }
    .custom-carousel-inner .custom-card{
        padding-bottom: 20px;
    }
    .p-v-p-item-description-share {
        cursor: pointer;
    }
    .p-v-p-item-description-share-list{
        position: absolute;
        background-color: #fff;
        top: 35px;
        right: 0;
        margin: 0;
        padding-left: 0;
        list-style-type: none;
        box-shadow: -1px 3px 15px -10px #000;
        border: 1px solid #ccc;
        padding: 15px;
        border-radius: 5px;
        display: none;
    }
    .p-v-p-item-description-share-list li{
        margin-bottom: 10px;
    }
    /* .p-v-p-item-description-container {
        justify-content: flex-start !important;
    } */
    .p-v-p-item-description-container .p-v-p-item-description {
        width: 1201px;
        padding-left: 30px;
    }
    .p-v-p-item-description-image-description-title-container {
        width: 288px;
    }
    .p-v-p-item-description-add-to-wishlist-share-container {
        left: 20px;
    }
    .dropdown .no-of-days-dropdown{
        font-size: 18px;
        width: 250px;
    }
    .p-v-p-item-description-Total-price {
        font-size: 15px;
        font-weight: 500;
    }
    .cards-inner-text p.comment{
        font-size: 18px;
    }
    .prod-view-page-my-parent-carousel-s .buy-now-button{
        margin-left: 100px;
        margin-top: 30px;
    }
    .productCategoryListing-home-span {
        font-size: 18px;
    }
    .custom-parent-carousel .custom-carousel{
        height: 481px;
    }
    .custom-parent-carousel .custom-carousel .custom-carousel-inner .card-t-name{
        height: 48px;
    }
    .p-v-p-item-description-image-description-model-label.ka-text-black{
        color:#000;
    }
</style>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productViewPage.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/product-view-responsive-fix.css') ?>">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"/>
<script src="<?= $this->Url->webroot('js/productViewPage.js') ?>"></script>
<script>
// Auto-scrolling functionality for product images
document.addEventListener('DOMContentLoaded', function() {
    // Create a global function to reinitialize auto-scrolling after variant changes
    window.reinitializeAutoScroll = function() {
        initProductImageAutoScroll();
    };

    // Initialize auto-scrolling on page load
    initProductImageAutoScroll();
});

// Function to initialize or reinitialize the auto-scrolling functionality
function initProductImageAutoScroll() {
    const imageSlider = document.getElementById('product-image-slider');
    const toggleButton = document.getElementById('toggle-auto-scroll');
    const toggleIcon = document.getElementById('auto-scroll-icon');
    const leftArrow = document.querySelector('.arrow-left');
    const rightArrow = document.querySelector('.arrow-right');
    const mainImage = document.getElementById('main-image');
    const mainVideo = document.getElementById('main-video');

    if (!imageSlider || !toggleButton || !toggleIcon) return;

    // Clear any existing intervals to prevent duplicates
    if (window.autoScrollInterval) clearInterval(window.autoScrollInterval);
    if (window.autoChangeInterval) clearInterval(window.autoChangeInterval);

    let isAutoScrolling = true;
    let scrollAmount = 0;
    const scrollStep = 1; // Small step for smooth scrolling
    const scrollInterval = 30; // Milliseconds between each scroll step

    // Get all thumbnail elements (these may have changed after variant selection)
    const thumbnailElements = imageSlider.querySelectorAll('.p-v-p-item-description-image-slider-image');
    let currentThumbnailIndex = 0;
    const changeInterval = 3000; // Change main display every 3 seconds

    // Function to update the main display based on the current thumbnail
    function updateMainDisplay(thumbnail) {
        if (!thumbnail) return;

        const type = thumbnail.dataset.type;

        if (type === 'image') {
            mainImage.src = thumbnail.src;
            mainImage.style.display = 'block';
            mainVideo.style.display = 'none';
            if (mainVideo.played) {
                mainVideo.pause();
            }
        } else if (type === 'video') {
            mainVideo.src = thumbnail.src;
            mainVideo.style.display = 'block';
            mainImage.style.display = 'none';
            mainVideo.play();
        }
    }

    // Function to start auto-changing the main display
    function startAutoChange() {
        if (window.autoChangeInterval) clearInterval(window.autoChangeInterval);

        window.autoChangeInterval = setInterval(function() {
            // Check if thumbnails exist (they might have been replaced)
            const updatedThumbnails = imageSlider.querySelectorAll('.p-v-p-item-description-image-slider-image');
            if (updatedThumbnails.length === 0) return;

            currentThumbnailIndex = (currentThumbnailIndex + 1) % updatedThumbnails.length;
            updateMainDisplay(updatedThumbnails[currentThumbnailIndex]);

            // Scroll to make the current thumbnail visible
            const currentThumbnail = updatedThumbnails[currentThumbnailIndex];
            const thumbnailRect = currentThumbnail.getBoundingClientRect();
            const sliderRect = imageSlider.getBoundingClientRect();

            if (thumbnailRect.left < sliderRect.left || thumbnailRect.right > sliderRect.right) {
                scrollAmount = currentThumbnail.offsetLeft - 50; // 50px offset for better visibility
                imageSlider.scrollLeft = scrollAmount;
            }
        }, changeInterval);
    }

    // Function to stop auto-changing
    function stopAutoChange() {
        if (window.autoChangeInterval) clearInterval(window.autoChangeInterval);
    }

    // Function to start auto-scrolling
    function startAutoScroll() {
        if (window.autoScrollInterval) clearInterval(window.autoScrollInterval);

        window.autoScrollInterval = setInterval(function() {
            // If we've reached the end, reset to beginning
            if (scrollAmount >= imageSlider.scrollWidth - imageSlider.clientWidth) {
                scrollAmount = 0;
                imageSlider.scrollLeft = 0;
            } else {
                scrollAmount += scrollStep;
                imageSlider.scrollLeft = scrollAmount;
            }
        }, scrollInterval);

        isAutoScrolling = true;
        toggleIcon.className = 'fa fa-pause';

        // Also start auto-changing the main display
        startAutoChange();
    }

    // Function to stop auto-scrolling
    function stopAutoScroll() {
        if (window.autoScrollInterval) clearInterval(window.autoScrollInterval);
        isAutoScrolling = false;
        toggleIcon.className = 'fa fa-play';

        // Also stop auto-changing the main display
        stopAutoChange();
    }

    // Remove any existing event listeners to prevent duplicates
    const newToggleButton = toggleButton.cloneNode(true);
    toggleButton.parentNode.replaceChild(newToggleButton, toggleButton);

    // Toggle auto-scrolling on button click
    newToggleButton.addEventListener('click', function() {
        if (isAutoScrolling) {
            stopAutoScroll();
        } else {
            startAutoScroll();
        }
    });

    // Pause scrolling when user interacts with the slider
    imageSlider.addEventListener('mouseenter', function() {
        stopAutoScroll();
    });

    // Resume scrolling when user leaves the slider (if it was previously scrolling)
    imageSlider.addEventListener('mouseleave', function() {
        startAutoScroll();
    });

    // Stop auto-scrolling when user clicks on the arrows
    if (leftArrow) {
        leftArrow.addEventListener('click', function() {
            stopAutoScroll();
            // Store current scroll position
            scrollAmount = imageSlider.scrollLeft;
        });
    }

    if (rightArrow) {
        rightArrow.addEventListener('click', function() {
            stopAutoScroll();
            // Store current scroll position
            scrollAmount = imageSlider.scrollLeft;
        });
    }

    // Handle clicks on the thumbnail images
    thumbnailElements.forEach(function(thumbnail, index) {
        thumbnail.addEventListener('click', function() {
            stopAutoScroll();
            currentThumbnailIndex = index;
            updateMainDisplay(thumbnail);
        });
    });

    // Initialize auto-scrolling and auto-changing
    startAutoScroll();
}
</script>
<?php $this->end(); ?>

<div class="productCategoryListingC" id="productCategoryListingC">
    <div class="productCategoryListing" id="productCategoryListing">
        <img src="<?= $this->Url->webroot('img/icons8-home-100.png') ?>" class="productCategoryListing-home-icn"/>

        <a href="/"> <span class="productCategoryListing-home-span">Home</span> </a>
        <span class="productCategoryListing-home-span">></span>

        <?php foreach ($product_detail->product_categories as $val): ?>
            <a href="/product-list/<?= $val['url_key'] ?>"> <span
                    class="productCategoryListing-home-span"><?= $val['category_name'] ?></span> </a>
            <span class="productCategoryListing-home-span">></span>
        <?php endforeach; ?>
        <span class="productCategoryListing-home-span Electronic-devices"><?= $product_detail->name ?></span>

    </div>
</div>

<div class="p-v-p-item-description-container">
    <div class="p-v-p-item-description">
        <div class="p-v-p-item-internal-description">

            <div class="p-v-p-item-description-image-container">
                <img id="main-image" src="<?= $product_detail->product_image ?>" class="p-v-p-item-description-image"/>
                <video id="main-video" class="p-v-p-item-description-video"
                       style="display: none; width: 525px;height: 546px;" controls></video>
            </div>
            <div class="p-v-p-item-description-image-slider-wrapper">
                <button class="arrow arrow-left">🠔</button>
                <div class="p-v-p-item-description-image-slider-container" id="product-image-slider">

                        <?php foreach ($product_detail->product_images as $val): ?>
                            <?php if ($val->media_type == 'Image'): ?>
                                <div class="img-container">
                                    <img src="<?= $val->image ?>" class="p-v-p-item-description-image-slider-image"
                                    data-type="image"/>
                                </div>
                            <?php elseif ($val->media_type == 'Video'): ?>
                                <video src="<?= $val->video ?>" class="p-v-p-item-description-image-slider-image"
                                    data-type="video"></video>
                            <?php endif; ?>
                        <?php endforeach; ?>

                </div>
                <button class="arrow arrow-right">🠖</button>
                <div class="auto-scroll-controls">
                    <button id="toggle-auto-scroll" title="Pause/Resume auto-scroll">
                        <i class="fa fa-pause" id="auto-scroll-icon"></i>
                    </button>
                </div>
            </div>

        </div>

        <div class="p-v-p-item-description-image-description">
            <div class="p-v-p-item-description-image-description-title-addToWishlist-container">
                <div class="p-v-p-item-description-image-description-title-container">

                    <span class="p-v-p-item-description-image-description-title"
                          id="product_title"><?= $product_detail->name ?>
                    </span>

                    <?php
                    $currentDateTime = new DateTime();
                    $promotionEndDate = isset($product_detail['promotion_end_date']) ? new DateTime($product_detail['promotion_end_date']) : null;
                    $endDate = new DateTime(date('Y-m-d') . ' 23:59:59');

                    if ($promotionEndDate > $currentDateTime) {
                        $remainingTime = $endDate->getTimestamp() - $currentDateTime->getTimestamp();
                        $hours = floor($remainingTime / 3600);
                        $minutes = floor(($remainingTime % 3600) / 60);
                        $seconds = $remainingTime % 60;
                    ?>
                        <span class="flash-sale-label" id="flash_sale-label">
                            Flash Sale Ends In
                            <span class="flash-sale" id="flash_sale">
                                <?= sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds) ?>
                            </span>
                        </span>

                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                const countdownElement = document.getElementById('flash_sale');
                                let remainingTime = <?= $remainingTime ?>;

                                function updateCountdown() {
                                    if (remainingTime <= 0) {
                                        countdownElement.textContent = 'Deal has ended';
                                        return;
                                    }

                                    remainingTime--;
                                    const hours = Math.floor(remainingTime / 3600);
                                    const minutes = Math.floor((remainingTime % 3600) / 60);
                                    const seconds = remainingTime % 60;

                                    countdownElement.textContent = `${hours.toString().padStart(2, '0')}H:${minutes.toString().padStart(2, '0')}M:${seconds.toString().padStart(2, '0')}S`;
                                    setTimeout(updateCountdown, 1000);
                                }

                                updateCountdown();
                            });
                        </script>
                    <?php
                    } else {
                    ?>
                        <span class="flash-sale" id="flash_sale">
                            <?= $product_detail['promotion_end_date'] ?>
                        </span>
                    <?php
                    }
                    ?>

                    <div class="star-rating">
                        <?php echo $this->Rating->renderStars($product_detail->rating); ?>

                        <span
                            class="p-v-p-item-description-image-description-star-and-ratings">(<?= $product_detail->rating ?> Ratings & <?= $product_detail->total_review ?> Reviews)</span>
                        </span>
                    </div>

                    <span
                        class="p-v-p-item-description-image-description-price"><span
                            class="ax-promo-price"><?= $this->Price->setPriceFormat($product_detail->promotion_price) ?></span>
                    <span
                        class="p-v-p-item-description-image-description-price-discount"><span
                            class="ax-sale-price"><?= $this->Price->setPriceFormat($product_detail->sales_price) ?></span></span>
                    <span
                        class="p-v-p-item-description-image-description-price-offer"><span
                            class="ax-sale-off"><?= $product_detail->discount ?></span>% 0ff</span>
                     </span>
                    <span class="p-v-p-item-description-image-description-model-label"><?= __('Reference: '); ?>
                     <span
                         class="p-v-p-item-description-image-description-model-input ax-sku"><?= $supplierRenference ?? "" ?></span>
                    </span>
                    <span class="p-v-p-item-description-image-description-model-label"><?= __('Brand: '); ?>
                    <span
                        class="p-v-p-item-description-image-description-model-input"><?= $product_detail['Brands']['name'] ?></span>
                    </span>
                    <span class="p-v-p-item-description-image-description-model-label"><?= __('Category: '); ?>
                      <span
                          class="p-v-p-item-description-image-description-model-input"><?= end($product_detail->product_categories)['category_name'] ?></span>
                     </span>

                    <span class="p-v-p-item-description-image-description-model-label"><?= __('Seller Info: '); ?>
                     <span
                         class="p-v-p-item-description-image-description-model-input"><?= $supplierRenference ?? "" ?></span>
                     </span>

                    <?php
                    $isFirstAttributeGroup = true; // Track if this is the first attribute group
                    foreach ($uniqueAttributes as $attributeName => $values): ?>
                        <span class="p-v-p-item-description-image-description-model-label ka-text-black"><?php echo htmlspecialchars($attributeName); ?>
        <div class="p-v-p-item-description-image-description-model-input" style="margin-top:20px; padding-left:0px">
            <?php
            $suffixes = ['e', 'w', 'g']; // Define the suffix sequence
            $counter = 0; // Initialize counter

            foreach ($values as $value):
                // Ensure the expected structure with 'id' and 'value'
                $attributeId = $value['id'];
                $attributeValue = $value['value'];

                // Calculate the current suffix based on the counter
                $currentSuffix = $suffixes[$counter % count($suffixes)];

                // Add 'active' class to the first attribute of the first group
                $activeClass = ($isFirstAttributeGroup && $counter === 0) ? ' active' : '';
                ?>
                <span class="color-gb <?php echo $currentSuffix . $activeClass; ?>" data-id="<?php echo $attributeId; ?>">
                    <?php echo htmlspecialchars($attributeValue); ?>
                </span>
                <?php
                $counter++; // Increment counter for the next iteration
            endforeach;
            $isFirstAttributeGroup = false; // Set to false after first group
            ?>
        </div>
    </span>
                    <?php endforeach; ?>

                    <?php if (!empty($product_detail->product_variants)): ?>
                        <?php
                        $suffixes = ['e', 'w', 'p']; // Define the suffix sequence
                        $counter = 0; // Initialize counter
                        ?>
                    <?php endif; ?>

                    <script>
                        document.addEventListener("DOMContentLoaded", function () {
                            let selectedAttributes = {}; // Store only one attribute selection at a time

                            // Auto-select the first attribute on page load
                            function selectFirstAttribute() {
                                const firstAttributeGroup = document.querySelector('.p-v-p-item-description-image-description-model-input');
                                if (firstAttributeGroup) {
                                    const firstAttribute = firstAttributeGroup.querySelector('.color-gb.active');
                                    if (firstAttribute) {
                                        // Get attribute ID and set it to the add-to-cart button
                                        let attributeId = firstAttribute.getAttribute('data-id');
                                        if (attributeId) {
                                            const addToCartButton = document.querySelector('.add-to-cart-button');
                                            if (addToCartButton) {
                                                addToCartButton.setAttribute('data-attributes', attributeId);
                                            }

                                            // Get group name for selectedAttributes
                                            let groupName = firstAttributeGroup.previousElementSibling?.textContent.trim();
                                            if (groupName && attributeId) {
                                                selectedAttributes[groupName] = attributeId;
                                            }

                                            // Check stock status for the first attribute
                                            let variantId = null;
                                            const variantSelect = document.getElementById('product-variant');
                                            if (variantSelect && variantSelect.value) {
                                                variantId = variantSelect.value;
                                            }

                                            checkProductStockStatus(<?= $product_detail->id ?>, variantId, attributeId);
                                        }
                                    }
                                }
                            }

                            // Call the function to select first attribute after a short delay to ensure DOM is ready
                            setTimeout(selectFirstAttribute, 100);

                            document.querySelectorAll('.p-v-p-item-description-image-description-model-input').forEach(group => {
                                group.addEventListener('click', (event) => {

                                    let span = event.target.closest('.color-gb');
                                    if (!span) return; // Ignore clicks outside the target spans

                                    // Deselect all previous selections from all groups
                                    document.querySelectorAll('.color-gb.active').forEach(el => el.classList.remove('active'));
                                    selectedAttributes = {}; // Clear previous attribute

                                    // Add active class to the newly selected item
                                    span.classList.add('active');

                                    // Get attribute ID and group name
                                    let attributeId = span.getAttribute('data-id');
                                    let groupName = group.previousElementSibling?.textContent.trim(); // Optional chaining for safety

                                    document.querySelector('.add-to-cart-button').setAttribute('data-attributes', attributeId);

                                    // Make AJAX call to check stock status
                                    // Get the current variant ID if any
                                    let variantId = null;
                                    const variantSelect = document.getElementById('product-variant');
                                    if (variantSelect && variantSelect.value) {
                                        variantId = variantSelect.value;
                                    }

                                    checkProductStockStatus(<?= $product_detail->id ?>, variantId, attributeId);

                                    if (!attributeId || !groupName) return;

                                    // Set the new selected attribute
                                    selectedAttributes[groupName] = attributeId;



                                });
                            });

                            // Function to check product stock status via AJAX
                            function checkProductStockStatus(productId, variantId, attributeId) {
                                $.ajax({
                                    url: "<?= $this->Url->build(['controller' => 'Website', 'action' => 'checkStockStatus']) ?>",
                                    type: 'POST',
                                    data: {
                                        product_id: productId,
                                        variant_id: variantId,
                                        attribute_id: attributeId
                                    },
                                    headers: {
                                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                                    },
                                    dataType: 'json',
                                    success: function(response) {
                                        if (response.status === 'success') {
                                            console.log(2)
                                            // Update the stock status display
                                            if (response.stock_status === 'In Stock') {
                                                $(".add-to-cart-button").removeClass('disabled');
                                                $(".add-to-cart-button").addClass('add-to-cart');
                                                $(".ax-stock-status").html("<span class='text-success'>In stock</span>");
                                                $(".buy-now-button").removeClass('disabled');
                                            } else {
                                                $(".add-to-cart-button").addClass('disabled');
                                                $(".add-to-cart-button").removeClass('add-to-cart');
                                                $(".ax-stock-status").html("<span class='text-danger'>Out of Stock</span>");
                                                $(".buy-now-button").addClass('disabled');
                                            }
                                        }
                                    },
                                    error: function() {
                                        console.error('Error checking stock status');
                                    }
                                });
                            }
                        });
                    </script>

                    <br />
                    <div class="dropdown">

                        <label for="options">Select Variant</label>
                        <select id="product-variant" class="no-of-days-dropdown" name="options">
                        <?php if (isset($product_detail->product_variants) && empty($product_detail->product_variants)): ?>
                           <option  data-product_title="<?= $product_detail->name ?>"
                                    data-sales_price="<?= $product_detail->sales_price ?>"
                                    data-promotion_price="<?= $product_detail->promotion_price ?>"
                                    data-sku="<?= $product_detail->sku ?>"
                                    data-quantity="<?= $product_detail->quantity ?>"
                                    data-status="<?= $product_detail->status ?>"
                                    value=""><?= $product_detail->name ?></option>
                            <?php else: ?>
                            <?php foreach ($product_detail->product_variants as $k => $val): ?>
                                <option data-product_title="<?= $val->variant_name ?>"
                                        data-sales_price="<?= $val->sales_price ?>"
                                        data-promotion_price="<?= $val->promotion_price ?>"
                                        data-sku="<?= $val->sku ?>"
                                        data-quantity="<?= $val->quantity ?>"
                                        data-status="<?= $val->status ?>"
                                        data-variant-id="<?= $val->id ?>"
                                        value="<?= $val->id ?>"><?= $val->variant_name ?></option>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>

                <div class="p-v-p-item-description-add-to-wishlist-share-container">

                    <div class="p-v-p-item-description-add-to-wishlist-share">

                        <?php if ($product_detail->whishlist): ?>
                            <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                 data-product-id="<?= $product_detail->id ?>"><span
                                    class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                        src="/assets/heart-background.png" class="wishlist"> </span>REMOVE FROM WISHLIST
                            </div>
                        <?php else: ?>
                            <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                 data-product-id="<?= $product_detail->id ?>"><span
                                    class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                        src="/assets/heart-nobackgrounddark.png" class="wishlist"> </span>ADD TO WISHLIST
                            </div>
                        <?php endif; ?>

                        <span class="p-v-p-item-description-share"> <i class="fa fa-share-alt" aria-hidden="true"></i> Share</span>
                        <?php
                        use Cake\Routing\Router;
                        $currentUrl = Router::url(null, true);
                        ?>
                        <ul class="p-v-p-item-description-share-list">
                            <li>
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($currentUrl) ?>" target="_blank" rel="noopener">
                                    <i class="fab fa-facebook-square"></i> <?= __("Facebook") ?>
                                </a>
                            </li>
                            <li>
                                <a href="https://twitter.com/intent/tweet?url=<?= urlencode($currentUrl) ?>&text=<?= urlencode($product_detail->name) ?>" target="_blank" rel="noopener">
                                    <i class="fab fa-twitter-square"></i> <?= __("Twitter") ?>
                                </a>
                            </li>
                            <li>
                                <a href="https://www.linkedin.com/shareArticle?mini=true&url=<?= urlencode($currentUrl) ?>&title=<?= urlencode($product_detail->name) ?>" target="_blank" rel="noopener">
                                    <i class="fab fa-linkedin"></i> <?= __("LinkedIn") ?>
                                </a>
                            </li>
                            <li>
                                <a href="https://wa.me/?text=<?= urlencode($product_detail->name . ' ' . $currentUrl) ?>" target="_blank" rel="noopener">
                                    <i class="fab fa-whatsapp-square"></i> <?= __("WhatsApp") ?>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="p-v-p-item-description-total-price-container">
                        <div class="p-v-p-item-description-Total-price"><?= __('Total Price') ?></div>
                        <div class="p-v-p-item-description-Total-price-amount">
                            <span id="Totalprice"><?= $this->Price->setPriceFormat($product_detail->promotion_price) ?></span> FCFA
                            <input type="hidden" id="TotalpriceValue" value="<?= $product_detail->promotion_price ?>">
                        </div>
                        <div class="p-v-p-item-description-Total-price-availablity"><?= __('Availablity :') ?> <span
                                class="p-v-p-item-description-Total-price-availablity-instock ax-stock-status"><?= $product_detail->availability_status ?></span>
                        </div>
                        <div class="counter">
                            <button class="counter-btn" id="decrease"><i class="fa fa-minus" aria-hidden="true"></i>
                            </button>
                            <div class="counter-value cart-qty" id="value">1</div>
                            <button class="counter-btn" id="increase"><i class="fa fa-plus" aria-hidden="true"></i>
                            </button>
                        </div>
                        <hr/>
                        <div>
                            <button class="add-to-cart-button add-to-cart" data-product-id="<?= $product_detail->id ?>">
                            <i class="fa fa-shopping-cart"></i> <?= __('ADD TO CART') ?> </button>
                        </div>
                        <div>
                            <button class="buy-now-button" id="buy-now-button" onclick="buyNow()" data-product-id="<?= $product_detail->id ?>"><?= __('BUY NOW') ?></button>
                        </div>
                        <div class="Guarantee">100% Guarantee Safe CheckOut</div>

                        <?php if ($product_detail->avl_on_credit == 1): ?>
                        <div>
                            <button class="add-to-cart-button add-to-cart" data-product-id="<?= $product_detail->id ?>" onclick="redirectToCreditPayment(<?= $product_detail->id ?>)">
                                <?= __('PAY WITH CREDIT') ?> <i class="fa fa-university"></i>
                            </button>
                        </div>

                        <div>
                            <button class="return-no" data-product-id="<?= $product_detail->id ?>">
                                🔁 <?= __('Return Is Not Allowed.') ?>
                            </button>
                        </div>
                        <?php endif; ?>

                    </div>

                </div>


            </div>

        </div>

    </div>

</div>



</div>


<p class="Recently-reviewed" id="p-v-page-prod-details"><?= __('Product Details') ?></p>
<div id="prod-view-page-my-parent-carousel-s-container">
    <div class="prod-view-page-my-parent-carousel-s" id="prod-view-page-my-parent-carousel-s">
        <div class="DESC-SPEC">
            <div id="description-tab" class="tab-button active"><?= __('DESCRIPTION') ?></div>
        </div>

        <div id="description-content" class="tab-content">
            <?= $product_detail->description ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const descriptionTab = document.getElementById('description-tab');
        const descriptionContent = document.getElementById('description-content');

        function showContent() {
            descriptionContent.style.display = 'block';
            descriptionTab.classList.add('active');
        }

        descriptionTab.addEventListener('click', function () {
            showContent();
        });

        // Initially display the DESCRIPTION content
        showContent();
    });
</script>
<?php if(!empty($similar_products) && isset($similar_products) && is_array($similar_products)): ?>
<p class="Recently-reviewed" id="similar-prod"><?= __('Similar Products') ?></p>

<div class="custom-carousel-controls" id="p-v-similar-prod-arrows">
    <button class="carousel-button left-btn" onclick="">🡠</button>
    <button class="carousel-button right-btn" onclick="">🡢</button>
</div>

<div style="display:flex; align-items:center;justify-content:center;">
    <div class="cont-p-v-p-r-r">
        <div class="p-v-p-r-r clone-p-v-p-r-r">


            <?php foreach ($similar_products as $val): ?>
                <div class="custom-card-recent clone-custom-card-recent for-r-margin" id="custom-card-recent">
                    <img src="<?= $val->product_image ?>" class="snd-crsl-img"/>
                    <?php // echo $this->Rating->renderStars($product_detail->rating); ?>
                    <div class="custom-card-star">

                        <?php echo $this->Rating->renderStars($val->rating); ?>

                        <span class="custom-card-rating"><?php echo $val->rating; ?></span>

                    </div>
                    <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val->id]) ?>">
                        <div class="custom-card-name c-custom-card-name"><?= $val->name ?></div>
                    </a>
                    <div class="custom-card-subname"><?= $val->reference_name ?></div>
                    <div>
                        <div class="custom-card-price"><?= $this->Price->setPriceFormat($val->promotion_price) ?></div>
                    </div>
                </div>


                <?php endforeach; ?>


        </div>
    </div>
</div>
<?php endif; ?>

<div class="p-v-advertisement-container">
            <div class="p-v-advertisement">
                <?php foreach ($banner_ads as $val): ?>
                    <div class="p-v-advertisement-img-container">
                        <img class="p-v-advertisement-img" src="<?= $val['web_image'] ?>"/>
                    </div>
                <?php endforeach; ?>
            </div>
</div>

<div class="p-v-p-Recently-reviewed-sort-by-container">
    <div class="p-v-p-Recently-reviewed-sort-by">
        <div class="reviews-header">
            <div class="reviews-title">
                <p class="Recently-reviewed" id="Recently-reviewed"><?= __('Ratings and Reviews') ?></p>
                <div class="reviews-summary">
                    <div class="reviews-average">
                        <span class="average-rating"><?= $product_detail->rating ?></span>
                        <div class="rating-stars">
                            <?php echo $this->Rating->renderStars($product_detail->rating); ?>
                        </div>
                        <span class="total-reviews">(<?= $product_detail->total_review ?> <?= __('Reviews') ?>)</span>
                    </div>
                </div>
            </div>
            <div class="reviews-sort">
                <label for="sort-by"><?= __('Sort by:') ?></label>
                <select id="sort-by" name="sort-by" onchange="updateURLWithSort(this.value)">
                    <option value="newest"><?= __('Newest Review') ?></option>
                    <option value="oldest"><?= __('Oldest Review') ?></option>
                    <option value="highest-rated"><?= __('Highest Rated') ?></option>
                    <option value="lowest-rated"><?= __('Lowest Rated') ?></option>
                </select>
            </div>
        </div>
    </div>
</div>

<style>
    .reviews-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .reviews-title {
        display: flex;
        flex-direction: column;
    }

    .reviews-summary {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }

    .reviews-average {
        display: flex;
        align-items: center;
    }

    .average-rating {
        font-size: 24px;
        font-weight: bold;
        margin-right: 10px;
    }

    .total-reviews {
        margin-left: 10px;
        color: #666;
    }

    .reviews-sort {
        display: flex;
        align-items: center;
    }

    .reviews-sort label {
        margin-right: 10px;
        font-weight: 500;
        font-size: 18px;
        font-family: "Arial";
    }

    #sort-by {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        cursor: pointer;
    }
    .ka-prod-carousel-container .prod-view-page-my-parent-carousel-s{
        height: 500px;
    }

    /* Review info message styling */
    .review-info-message {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 12px 15px;
        color: #6c757d;
        font-size: 14px;
        margin: 10px 0;
        text-align: center;
    }

    .login-to-review {
        display: inline-block;
        text-align: center;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .reviews-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .reviews-sort {
            margin-top: 15px;
        }
    }
</style>

<div id="prod-view-page-my-parent-carousel-s-container cloneprod-view-page-my-parent-carousel-s-container"
     style="display:flex; align-items:Center;justify-content:center;" class="ka-prod-carousel-container">
    <div class="prod-view-page-my-parent-carousel-s" style="overflow-y: auto;">
        <div id='parentReview'>
            <?php if (!empty($rating_reviews['data']['items'])): ?>
                <?php foreach ($rating_reviews['data']['items'] as $val): ?>
                    <div class="my-carousel-inner-s">
                        <div class="my-card-s" id="p-v-p-my-card-s">
                            <span class="product-view-page-card-date"><?= date("M d, Y", strtotime($val->created)) ?></span>
                            <div class="cards-inner-text" id="cards-inner-text">
                                <img src="<?= !empty($val->Customers['profile_photo']) ? $val->Customers['profile_photo'] : '/assets/profile-icon.png' ?>" class="dp-img">
                                <p class="dp-name">
                                    <?= $val->Users['first_name'] ?>
                                    <span class="star-rating">
                            <span class="rating">
                            <?php echo $this->Rating->renderStars($val->rating); ?>
                            </span>
                        </span>
                                </p>
                                <p class="comment">“<?= $val->comment ?>”</p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
        </div>
    <div>




            <div class="review-loading">
                <img src="<?= $this->Url->webroot('img/loader.svg') ?>" alt="Loading..." />
            </div>
            <?php else: ?>
                <div style="text-align: center"><?= __('No Record Found!') ?></div>
            <?php endif; ?>

            <div class="customer add-review-section">
                <?php if (isset($userId) && $userId): ?>
                    <?php if ($myReview != null && isset($myReview['userReview']) && $myReview['userReview'] == true): ?>
                        <button id="openReviewModal" class="add-to-cart-button"><?= __('Edit Your Review') ?></button>
                    <?php elseif ($myReview != null && isset($myReview['canAddReview']) && $myReview['canAddReview'] == true): ?>
                        <button id="openReviewModal" class="add-to-cart-button"><?= __('Write a Review') ?></button>
                    <?php else: ?>
                        <!-- <div class="review-info-message"><?= __('') ?></div> -->
                    <?php endif; ?>
                <?php else: ?>
                    <a href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'login']) ?>" class="add-to-cart-button login-to-review"><?= __('Login to Review') ?></a>
                <?php endif; ?>
            </div>

            <!-- Review Modal -->
            <div id="reviewModal" class="review-modal">
                <div class="review-modal-content">
                    <span class="review-modal-close">&times;</span>

                    <?php if (isset($userId) && $userId): ?>

                        <?php if ($myReview != null && isset($myReview['userReview']) && $myReview['userReview'] == true): ?>
                            <!-- Edit Review Form -->
                            <h3 class="review-modal-title"><?= __('Edit Your Review') ?></h3>
                            <form id="reviewForm2">
                                <div class="rating-container">
                                    <label><?= __('Your Rating') ?> <span class="required">*</span></label>
                                    <div class="ax-stars">
                                        <input type="radio" id="edit-star5" name="rating" value="5" <?= $myReview->rating == 5 ? 'checked' : '' ?>><label for="edit-star5">★</label>
                                        <input type="radio" id="edit-star4" name="rating" value="4" <?= $myReview->rating == 4 ? 'checked' : '' ?>><label for="edit-star4">★</label>
                                        <input type="radio" id="edit-star3" name="rating" value="3" <?= $myReview->rating == 3 ? 'checked' : '' ?>><label for="edit-star3">★</label>
                                        <input type="radio" id="edit-star2" name="rating" value="2" <?= $myReview->rating == 2 ? 'checked' : '' ?>><label for="edit-star2">★</label>
                                        <input type="radio" id="edit-star1" name="rating" value="1" <?= $myReview->rating == 1 ? 'checked' : '' ?>><label for="edit-star1">★</label>
                                    </div>
                                    <div id="edit-rating-error" class="error-message"></div>
                                </div>

                                <div class="textarea-container">
                                    <label for="editReviewText"><?= __('Your Review') ?> <span class="required">*</span></label>
                                    <textarea name="review" id="editReviewText" placeholder="<?= __('Edit your review here...') ?>" ><?= $myReview->comment ?></textarea>
                                    <div id="edit-review-error" class="error-message"></div>
                                </div>
                                <button type="submit" class="add-review-button add-to-cart-button"><?= __('Save Changes') ?></button>
                            </form>
                        <?php elseif ($myReview != null && isset($myReview['canAddReview']) && $myReview['canAddReview'] == true): ?>
                            <!-- Add New Review Form -->
                            <h3 class="review-modal-title"><?= __('Write a Review') ?></h3>
                            <form id="reviewForm">
                                <div class="rating-container">
                                    <label><?= __('Your Rating') ?> <span class="required">*</span></label>
                                    <div class="ax-stars">
                                        <input type="radio" id="add-star5" name="rating" value="5"><label for="add-star5">★</label>
                                        <input type="radio" id="add-star4" name="rating" value="4"><label for="add-star4">★</label>
                                        <input type="radio" id="add-star3" name="rating" value="3"><label for="add-star3">★</label>
                                        <input type="radio" id="add-star2" name="rating" value="2"><label for="add-star2">★</label>
                                        <input type="radio" id="add-star1" name="rating" value="1"><label for="add-star1">★</label>
                                    </div>
                                    <div id="add-rating-error" class="error-message"></div>
                                </div>

                                <div class="textarea-container">
                                    <label for="addReviewText"><?= __('Your Review') ?> <span class="required">*</span></label>
                                    <textarea name="review" id="addReviewText" placeholder="<?= __('Add your review here...') ?>" ></textarea>
                                    <div id="add-review-error" class="error-message"></div>
                                </div>
                                <button type="submit" class="add-review-button add-to-cart-button"><?= __('Submit Review') ?></button>
                            </form>
                        <?php elseif ($myReview != null && isset($myReview['canAddReview']) && $myReview['canAddReview'] == false): ?>
                            <!-- User already submitted a review but can't see it for some reason -->
                            <h3 class="review-modal-title"><?= __('Review Status') ?></h3>
                            <!-- <div class="review-info-message">

                            </div> -->
                        <?php endif; ?>
                    <?php else: ?>
                        <!-- Not logged in -->
                        <h3 class="review-modal-title"><?= __('Login Required') ?></h3>
                        <div class="review-info-message">
                            <?= __('Please log in to write a review.') ?>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'login']) ?>" class="add-to-cart-button">
                                <?= __('Login Now') ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <script>
                // Modal functionality
                const reviewModal = document.getElementById('reviewModal');
                const openReviewModalBtn = document.getElementById('openReviewModal');
                const closeReviewModalBtn = document.querySelector('.review-modal-close');

                if (openReviewModalBtn) {
                    openReviewModalBtn.addEventListener('click', function() {
                        reviewModal.style.display = 'block';
                    });
                }

                if (closeReviewModalBtn) {
                    closeReviewModalBtn.addEventListener('click', function() {
                        reviewModal.style.display = 'none';
                    });
                }

                // Close modal when clicking outside of it
                window.addEventListener('click', function(event) {
                    if (event.target === reviewModal) {
                        reviewModal.style.display = 'none';
                    }
                });

                // Form submission for adding a new review
                const reviewForm = document.getElementById('reviewForm');
                if (reviewForm) {
                    reviewForm.addEventListener('submit', function(e) {
                        e.preventDefault();

                        // Clear previous error messages
                        document.getElementById('add-rating-error').textContent = '';
                        document.getElementById('add-review-error').textContent = '';

                        let rating = reviewForm.querySelector('input[name="rating"]:checked');
                        let review = document.getElementById('addReviewText').value;
                        let hasError = false;

                        // Validate rating
                        if (!rating) {
                            document.getElementById('add-rating-error').textContent = '<?= __('Please select a rating') ?>';
                            hasError = true;
                        }

                        // Validate review text
                        if (!review.trim()) {
                            document.getElementById('add-review-error').textContent = '<?= __('Please enter your review') ?>';
                            document.getElementById('addReviewText').classList.add('error');
                            hasError = true;
                        }

                        if (hasError) {
                            return;
                        }

                        fetch('<?= $this->Url->build(['controller' => 'Website', 'action' => 'addReview', $product_detail->id]) ?>', {
                            method: 'POST',
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-Token": "<?= $this->request->getAttribute('csrfToken') ?>"
                            },
                            body: JSON.stringify({
                                rating: rating.value,
                                review: review
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status == 'success') {
                                toastr.success(data.message, '', {
                                    timeOut: 3000,
                                    progressBar: true,
                                    onHidden: function () {
                                        location.reload();
                                    }
                                });
                            } else {
                                toastr.warning(data.message, '', {
                                    timeOut: 3000,
                                    progressBar: true,
                                    onHidden: function () {
                                        location.reload();
                                    }
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            toastr.error('An error occurred while submitting the review.', '', {
                                timeOut: 3000,
                                progressBar: true
                            });
                        });
                    });

                    // Clear error message when a rating is selected
                    reviewForm.querySelectorAll('input[name="rating"]').forEach(radio => {
                        radio.addEventListener('change', function() {
                            document.getElementById('add-rating-error').textContent = '';
                            // Add a visual indication that the rating is selected
                            reviewForm.querySelector('.rating-container').classList.add('rating-selected');
                        });
                    });

                    // Clear error message when review text is entered
                    document.getElementById('addReviewText').addEventListener('input', function() {
                        if (this.value.trim()) {
                            document.getElementById('add-review-error').textContent = '';
                            this.classList.remove('error');
                        }
                    });
                }

                // Form submission for editing a review
                const reviewForm2 = document.getElementById('reviewForm2');
                if (reviewForm2) {
                    reviewForm2.addEventListener('submit', function(e) {
                        e.preventDefault();

                        // Clear previous error messages
                        document.getElementById('edit-rating-error').textContent = '';
                        document.getElementById('edit-review-error').textContent = '';

                        let rating = reviewForm2.querySelector('input[name="rating"]:checked');
                        let review = document.getElementById('editReviewText').value;
                        let hasError = false;

                        // Validate rating
                        if (!rating) {
                            document.getElementById('edit-rating-error').textContent = '<?= __('Please select a rating') ?>';
                            hasError = true;
                        }

                        // Validate review text
                        if (!review.trim()) {
                            document.getElementById('edit-review-error').textContent = '<?= __('Please enter your review') ?>';
                            document.getElementById('editReviewText').classList.add('error');
                            hasError = true;
                        }

                        if (hasError) {
                            return;
                        }

                        <?php if (isset($myReview) && isset($myReview->id)): ?>
                        fetch('<?= $this->Url->build(['controller' => 'Website', 'action' => 'updateReview', $myReview->id]) ?>', {
                            method: 'POST',
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-Token": "<?= $this->request->getAttribute('csrfToken') ?>"
                            },
                            body: JSON.stringify({
                                rating: rating.value,
                                review: review,
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.status === true) {
                                toastr.success(data.message, '', {
                                    timeOut: 3000,
                                    progressBar: true,
                                    onHidden: function () {
                                        location.reload();
                                    }
                                });
                            } else {
                                toastr.warning(data.message || 'An error occurred', '', {
                                    timeOut: 3000,
                                    progressBar: true
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            toastr.error('An error occurred while updating the review.', '', {
                                timeOut: 3000,
                                progressBar: true
                            });
                        });
                        <?php endif; ?>
                    });

                    // Clear error message when a rating is selected
                    reviewForm2.querySelectorAll('input[name="rating"]').forEach(radio => {
                        radio.addEventListener('change', function() {
                            document.getElementById('edit-rating-error').textContent = '';
                            // Add a visual indication that the rating is selected
                            reviewForm2.querySelector('.rating-container').classList.add('rating-selected');
                        });
                    });

                    // Clear error message when review text is entered
                    document.getElementById('editReviewText').addEventListener('input', function() {
                        if (this.value.trim()) {
                            document.getElementById('edit-review-error').textContent = '';
                            this.classList.remove('error');
                        }
                    });
                }
            </script>
            </div>
        </div>

    </div>
</div>

<?php if (isset($userId) && $this->RecentViewProduct->isHaveRecords($userId)): ?>
    <p class="Recently-reviewed"><?= __('Recently Viewed') ?></p>

    <div class="custom-carousel-container">
        <div class="custom-carousel-controls">
            <button class="carousel-button left-btnn" onclick="">🡠</button>
            <button class="carousel-button right-btnn" onclick="">🡢</button>
        </div>
        <div class="custom-parent-carousel">
            <div class="custom-carousel" id="custom-carousel">
                <div class="custom-carousel-inner">
                    <?= $this->RecentViewProduct->renderSlider($userId); ?>
                </div>
            </div>
        </div>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
    const carouselInner = document.querySelector(".custom-carousel-inner");
    const leftButton = document.querySelector(".left-btnn");
    const rightButton = document.querySelector(".right-btnn");

    let scrollAmount = 0;
    const scrollStep = 200; // Adjust based on slide width

    rightButton.addEventListener("click", function () {
        scrollAmount -= scrollStep;
        carouselInner.style.transform = `translateX(${scrollAmount}px)`;
    });

    leftButton.addEventListener("click", function () {
        scrollAmount += scrollStep;
        carouselInner.style.transform = `translateX(${scrollAmount}px)`;
    });
});

        </script>
    </div>
<?php endif; ?>

<script>

    document.addEventListener('DOMContentLoaded', function () {
        const cityDropdown = document.getElementById('getShowRoom');
        const showroomDropdown = document.getElementById('showRoom');

        // Function to fetch showrooms by city
        const getShowRoom = (cityId) => {
            const apiUrl = `${searchAjaxUrl}api/v1.0/listShowroomByCity/${cityId}.json`;

            fetch(apiUrl, {
                method: 'GET',
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Clear existing options
                    showroomDropdown.innerHTML = '<option value="1">Select Showroom</option>';

                    // Populate showroom options
                    data.result.data.forEach(showroom => {
                        const option = document.createElement('option');
                        option.value = showroom.id; // Assuming each showroom has a unique ID
                        option.textContent = showroom.name; // Assuming each showroom has a 'name' property
                        showroomDropdown.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error calling API:', error);
                });
        };

        // Event listener for city dropdown change
        cityDropdown.addEventListener('change', function () {
            const selectedCityId = this.value;
            if (selectedCityId) {
                getShowRoom(selectedCityId);
            }
        });
    });

</script>
<script>

    function updateURLWithSort(sortBy) {
        // Get the current URL
        let currentURL = new URL(window.location.href);

        // Update or add the `sort-by` parameter in the URL
        currentURL.searchParams.set('sort-by', sortBy);

        // Redirect to the updated URL
        window.location.href = currentURL.toString();
    }

    document.addEventListener('DOMContentLoaded', function () {
        const sortBy = new URLSearchParams(window.location.search).get('sort-by') || 'newest';
        const selectElement = document.getElementById('sort-by');
        selectElement.value = sortBy; // Set the selected value
    });


    document.addEventListener('DOMContentLoaded', function () {
        const container = document.getElementById('parentReview');
        const loadingIndicator = document.querySelector('.review-loading');
        if (!container || !loadingIndicator) return; // Exit if elements are not found

        let page = 2;
        let isLoading = false;
        let hasMoreItems = true;

        // Function to load more reviews
        function loadMoreReviews() {
            if (isLoading || !hasMoreItems) return;

            isLoading = true;
            loadingIndicator.style.display = 'block';

            let sortBy = new URLSearchParams(window.location.search).get('sort-by') || 'newest';
            const apiUrl = `${searchAjaxUrl}website/review/<?= $product ?>/${sortBy}/${page}`;

            // Fetch API data
            fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Network response was not ok, status: ${response.status}`);
                }
                return response.json(); // Parse JSON response
            })
            .then(data => {
                const items = data.data.items;
                if (items.length === 0) {
                    hasMoreItems = false;
                    loadingIndicator.style.display = 'none';
                    return;
                }

                // Generate and append reviews dynamically
                let reviewHtml = '';
                items.forEach(item => {
                    // Create star rating HTML based on the rating value
                    let starsHtml = '';
                    const rating = parseFloat(item.rating) || 0;
                    for (let i = 1; i <= 5; i++) {
                        if (i <= rating) {
                            starsHtml += '<span class="star">★</span>';
                        } else if (i - 0.5 <= rating) {
                            starsHtml += '<span class="star half">★</span>';
                        } else {
                            starsHtml += '<span class="star empty">★</span>';
                        }
                    }

                    reviewHtml += `
                        <div class="my-carousel-inner-s">
                            <div class="my-card-s" id="p-v-p-my-card-s">
                                <span class="product-view-page-card-date">${new Date(item.created).toLocaleDateString()}</span>
                                <div class="cards-inner-text" id="cards-inner-text">
                                    <img src="${item.Customers?.profile_photo || '<?= $this->Url->webroot('assets/profile-icon.png') ?>'}"
                                        class="dp-img" alt="Profile Photo">
                                    <p class="dp-name">
                                        ${item.Users?.first_name || 'Guest'} ${item.Users?.last_name || ''}
                                        <span class="star-rating">
                                            ${starsHtml}
                                        </span>
                                    </p>
                                    <p class="comment">"${item.comment || 'No comment available'}"</p>
                                </div>
                            </div>
                        </div>`;
                });

                // Append new reviews to the container
                container.insertAdjacentHTML('beforeend', reviewHtml);

                // Increment page number for next API call
                page += 1;
                isLoading = false;
                loadingIndicator.style.display = 'none';
            })
            .catch(error => {
                console.error('Fetch error:', error);
                isLoading = false;
                loadingIndicator.style.display = 'none';
            });
        }

        // Intersection Observer to detect when user scrolls to bottom of reviews
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !isLoading) {
                    loadMoreReviews();
                }
            });
        }, {
            root: container,
            rootMargin: '0px 0px 100px 0px', // Load more when within 100px of the bottom
            threshold: 0.1
        });

        // Observe the loading indicator
        observer.observe(loadingIndicator);

        // Also check scroll position manually for better compatibility
        container.addEventListener('scroll', function() {
            const scrollPosition = container.scrollTop + container.clientHeight;
            const scrollHeight = container.scrollHeight;

            // Load more when user scrolls to 90% of the container
            if (scrollPosition >= scrollHeight * 0.9 && !isLoading && hasMoreItems) {
                loadMoreReviews();
            }
        });
    });

    document.addEventListener("DOMContentLoaded", () => {
        const cards = document.querySelectorAll(".custom-card-recent");
        const leftButton = document.querySelector(".left-btn");
        const rightButton = document.querySelector(".right-btn");

        let currentIndex = 0; // Tracks the currently visible card
        const totalCards = cards.length;
        const cardWidth = cards[0].offsetWidth + 16; // Card width including margin

        // Function to update the position of the cards
        function updateCarousel() {
            cards.forEach((card, index) => {
                const offset = (index - currentIndex) * 50; // Set position relative to currentIndex
                card.style.transform = `translateX(${offset}%)`;
                card.style.transition = "transform 0.5s ease-in-out"; // Smooth transition
            });
        }

        // Move to the previous card
        leftButton.addEventListener("click", () => {
            currentIndex = (currentIndex === 0) ? totalCards - 1 : currentIndex - 1; // Wrap to the last card
            updateCarousel();
        });

        // Move to the next card
        rightButton.addEventListener("click", () => {
            currentIndex = (currentIndex === totalCards - 1) ? 0 : currentIndex + 1; // Wrap to the first card
            updateCarousel();
        });

        updateCarousel();
    });

</script>
<script>
    function redirectToCreditPayment(productId) {
        let variantId = document.getElementById("product-variant").value; // Get selected variant ID

        let url = "<?= $this->Url->build(['controller' => 'Website', 'action' => 'creditPayment']) ?>/" + productId;

        if (variantId) {
            url += "?variant=" + variantId;
        }

        window.location.href = url; // Redirect to the generated URL
    }
</script>

<script>

    document.addEventListener('DOMContentLoaded', function () {
        // Set up quantity change handlers
        const increaseButton = document.getElementById('increase');
        const decreaseButton = document.getElementById('decrease');
        const valueDisplay = document.getElementById('value');

        if (increaseButton && decreaseButton && valueDisplay) {
            increaseButton.addEventListener('click', function() {
                let qty = parseInt(valueDisplay.textContent);
                qty++;
                valueDisplay.textContent = qty;
                updateTotalPriceDisplay(qty);
            });

            decreaseButton.addEventListener('click', function() {
                let qty = parseInt(valueDisplay.textContent);
                if (qty > 1) {
                    qty--;
                    valueDisplay.textContent = qty;
                    updateTotalPriceDisplay(qty);
                } else {
                    alert('Quantity cannot be less than 1');
                }
            });
        }

        // Function to update total price display and hidden value
        function updateTotalPriceDisplay(qty) {
            // Get the unit price from the variant or default product
            let unitPrice = 0;
            const variantSelect = document.getElementById('product-variant');
            if (variantSelect && variantSelect.value) {
                const selectedOption = variantSelect.options[variantSelect.selectedIndex];
                unitPrice = parseFloat(selectedOption.dataset.promotion_price);
            } else {
                unitPrice = <?= $product_detail->promotion_price ?>;
            }

            // Calculate total price
            const totalPrice = qty * unitPrice;

            // Update display and hidden value
            $("#Totalprice").html(formatAmount(totalPrice.toFixed(2)));
            $("#TotalpriceValue").val(totalPrice.toFixed(2));
        }

        // read url q is empty or not
        const urlParams = new URLSearchParams(window.location.search);
        const q = urlParams.get('q');

        if (q) {
            $(document).ready(function () {
            const variantOption = $(`#product-variant option`).filter(function() {
                return $(this).data("product_title") === q || $(this).text().trim() === q;
            });

            if (variantOption.length) {
                variantOption.prop("selected", true).trigger("change");
            } else {
                $("#product-variant").prop("selectedIndex", 0).trigger("change");
            }
            });
        } else {
            $(document).ready(function () {
            $("#product-variant").prop("selectedIndex", 0).trigger("change");
            });
        }


        // Store the original product images for reverting when no variant is selected
        const originalProductImages = [];
        document.querySelectorAll('.p-v-p-item-description-image-slider-container img, .p-v-p-item-description-image-slider-container video').forEach(item => {
            originalProductImages.push({
                src: item.src,
                type: item.dataset.type
            });
        });

        // Store the original main image
        const originalMainImage = document.getElementById('main-image').src;

        // Product variant data from PHP
        const productVariants = <?= json_encode($product_detail->product_variants) ?>;

        function formatAmount(amount){
            return amount.toString().split('.')[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
        }

        $("#product-variant").on("change", function () {
            var selectedOption = $(this).find(":selected"); // Get selected option
            var variantId = $(this).val(); // Get selected variant ID

            var qty = parseFloat(selectedOption.data("quantity")); // Get quantity
            var product_title = selectedOption.data("product_title");
            var sales_price = parseFloat(selectedOption.data("sales_price"));
            var promotion_price = parseFloat(selectedOption.data("promotion_price"));
            var sku = selectedOption.data("reference_name");
            var status = selectedOption.data("status");
            var discount = sales_price > 0 ? ((sales_price - promotion_price) * 100 / sales_price) : 0;

            // Update product details
            $("#Totalprice").html(formatAmount(promotion_price));
            $("#TotalpriceValue").val(promotion_price); // Update hidden value field
            $(".ax-promo-price").html(formatAmount(promotion_price)+' FCFA');
            $(".ax-sale-price").html(formatAmount(sales_price)+' FCFA');
            $(".ax-sale-off").html(formatAmount(discount));
            $(".ax-sku").html(sku);
            $("#product_title").html(product_title);

            // Reset quantity to 1 and update total price
            const valueDisplay = document.getElementById('value');
            if (valueDisplay) {
                valueDisplay.textContent = '1';

                // Calculate total price based on quantity and promotion price
                const qty = parseInt(valueDisplay.textContent);
                const totalPrice = qty * promotion_price;
                $("#Totalprice").html(formatAmount(totalPrice.toFixed(2)));
                $("#TotalpriceValue").val(totalPrice.toFixed(2)); // Update hidden value field
            }

            // Get the selected attribute ID if any
            let attributeId = null;
            const activeAttribute = document.querySelector('.color-gb.active');
            if (activeAttribute) {
                attributeId = activeAttribute.getAttribute('data-id');
            }

            // If no attribute is selected, auto-select the first one
            if (!attributeId) {
                const firstAttributeGroup = document.querySelector('.p-v-p-item-description-image-description-model-input');
                if (firstAttributeGroup) {
                    const firstAttribute = firstAttributeGroup.querySelector('.color-gb');
                    if (firstAttribute) {
                        // Remove any existing active classes
                        document.querySelectorAll('.color-gb.active').forEach(el => el.classList.remove('active'));

                        // Add active class to the first attribute
                        firstAttribute.classList.add('active');

                        // Get attribute ID
                        attributeId = firstAttribute.getAttribute('data-id');
                        if (attributeId) {
                            document.querySelector('.add-to-cart-button').setAttribute('data-attributes', attributeId);
                        }
                    }
                }
            }

            // Check if we should use the AJAX call or the direct status check
            if (attributeId) {
                // If an attribute is selected, use AJAX to get the accurate stock status
                checkProductStockStatus(<?= $product_detail->id ?>, variantId, attributeId);
            } else {
                // Update stock status based on variant data
                if ("<?= addslashes($product_detail->availability_status) ?>" === 'Out of Stock') {
                    $(".add-to-cart-button").addClass('disabled');
                    $(".add-to-cart-button").removeClass('add-to-cart');
                    $(".ax-stock-status").html("<span class='text-danger'>Out of Stock</span>");
                    $(".buy-now-button").addClass('disabled');
                } else {
                    $(".ax-stock-status").html("<span class='text-success'>In stock</span>");
                    $(".add-to-cart-button").removeClass('disabled');
                    $(".add-to-cart-button").addClass('add-to-cart');
                    $(".buy-now-button").removeClass('disabled');
                }
            }



            // Update product images based on variant selection
            const sliderContainer = document.querySelector('.p-v-p-item-description-image-slider-container');

            if (variantId) {
                // Find the selected variant
                const selectedVariant = productVariants.find(variant => variant.id == variantId);

                if (selectedVariant && selectedVariant.product_variant_images && selectedVariant.product_variant_images.length > 0) {
                    // Clear existing images
                    sliderContainer.innerHTML = '';

                    // Add variant images to the slider
                    selectedVariant.product_variant_images.forEach(image => {
                        if (image.media_type === 'Image') {
                            const imgElement = document.createElement('img');
                            imgElement.src = image.image;
                            imgElement.className = 'p-v-p-item-description-image-slider-image';
                            imgElement.dataset.type = 'image';
                            sliderContainer.appendChild(imgElement);

                            // Update main image with the first variant image
                            if (selectedVariant.product_variant_images.indexOf(image) === 0) {
                                document.getElementById('main-image').src = image.image;
                                document.getElementById('main-image').style.display = 'block';
                                document.getElementById('main-video').style.display = 'none';
                            }
                        } else if (image.media_type === 'Video') {
                            const videoElement = document.createElement('video');
                            videoElement.src = image.video;
                            videoElement.className = 'p-v-p-item-description-image-slider-image';
                            videoElement.dataset.type = 'video';
                            sliderContainer.appendChild(videoElement);
                        }
                    });

                    // Reinitialize the slider functionality
                    initializeImageSlider();

                    // Reinitialize auto-scrolling if it exists
                    if (typeof reinitializeAutoScroll === 'function') {
                        reinitializeAutoScroll();
                    }
                }
            } else {
                // Revert to original product images if no variant is selected
                sliderContainer.innerHTML = '';

                originalProductImages.forEach(item => {
                    if (item.type === 'image') {
                        const imgElement = document.createElement('img');
                        imgElement.src = item.src;
                        imgElement.className = 'p-v-p-item-description-image-slider-image';
                        imgElement.dataset.type = 'image';
                        sliderContainer.appendChild(imgElement);
                    } else if (item.type === 'video') {
                        const videoElement = document.createElement('video');
                        videoElement.src = item.src;
                        videoElement.className = 'p-v-p-item-description-image-slider-image';
                        videoElement.dataset.type = 'video';
                        sliderContainer.appendChild(videoElement);
                    }
                });

                // Revert main image
                document.getElementById('main-image').src = originalMainImage;
                document.getElementById('main-image').style.display = 'block';
                document.getElementById('main-video').style.display = 'none';

                // Reinitialize the slider functionality
                initializeImageSlider();

                // Reinitialize auto-scrolling if it exists
                if (typeof reinitializeAutoScroll === 'function') {
                    reinitializeAutoScroll();
                }
            }

            // Update the add to cart button's product ID
            // if (variantId) {
            //     document.querySelector('.add-to-cart').setAttribute('data-product-id', variantId);
            // } else {
            //     document.querySelector('.add-to-cart').setAttribute('data-product-id', '<?= $product_detail->id ?>');
            // }
        });

        // Function to check product stock status via AJAX
        function checkProductStockStatus(productId, variantId, attributeId) {
            console.log('Checking stock status for:', {
                product_id: productId,
                variant_id: variantId,
                attribute_id: attributeId
            });

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Website', 'action' => 'checkStockStatus']) ?>",
                type: 'POST',
                data: {
                    product_id: productId,
                    variant_id: variantId,
                    attribute_id: attributeId
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                dataType: 'json',
                beforeSend: function() {
                    console.log('Sending AJAX request to:', "<?= $this->Url->build(['controller' => 'Website', 'action' => 'checkStockStatus']) ?>");
                },
                success: function(response) {
                    console.log('Stock status response:', response);
                    if (response.status === 'success') {
                    console.log(1);
                        // Update the stock status display
                        if (response.stock_status === 'In Stock') {
                            $(".ax-stock-status").html("<span class='text-success'>In stock</span>");
                            $(".add-to-cart-button").removeClass('disabled');
                            $(".add-to-cart-button").addClass('add-to-cart');
                            $(".buy-now-button").removeClass('disabled');

                        } else {
                            $(".add-to-cart-button").addClass('disabled');
                            $(".add-to-cart-button").removeClass('add-to-cart');
                            $(".ax-stock-status").html("<span class='text-danger'>Out of Stock</span>");
                            $(".buy-now-button").addClass('disabled');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error checking stock status:', error);
                    console.log('Response status:', status);
                    console.log('Response text:', xhr.responseText);
                    console.log('XHR object:', xhr);
                }
            });
        }

        // Function to initialize image slider functionality
        function initializeImageSlider() {
            // Add click event to thumbnail images
            document.querySelectorAll('.p-v-p-item-description-image-slider-image').forEach(thumbnail => {
                thumbnail.addEventListener('click', function() {
                    const mainImage = document.getElementById('main-image');
                    const mainVideo = document.getElementById('main-video');

                    if (this.dataset.type === 'image') {
                        mainImage.src = this.src;
                        mainImage.style.display = 'block';
                        mainVideo.style.display = 'none';
                    } else if (this.dataset.type === 'video') {
                        mainVideo.src = this.src;
                        mainVideo.style.display = 'block';
                        mainImage.style.display = 'none';
                    }
                });
            });
        }

        // Initialize the slider on page load
        initializeImageSlider();
    });
</script>

<?php $this->start('add_js'); ?>
<script>
$(document).ready(function () {
    // Handle radio button change events
    document.querySelectorAll('.ax-stars input').forEach(star => {
        star.addEventListener('change', () => {
            alert(`You rated: ${star.value} stars`);
        });
    });

    // Also handle clicks on the star labels for better user experience
    document.querySelectorAll('.ax-stars label').forEach(label => {
        label.addEventListener('click', () => {
            // Find the associated input
            const inputId = label.getAttribute('for');
            const input = document.getElementById(inputId);
            if (input) {
                input.checked = true;
                // Trigger the change event manually
                const event = new Event('change');
                input.dispatchEvent(event);
            }
        });
    });

    /* Hide and show share */
    $(".p-v-p-item-description-share").click(function(){
        $(".p-v-p-item-description-share-list").slideToggle('slow');
    });
});
</script>


<!-- SEO Tracking : Inject Data Layer in Product View Page -->
<script>
window.dataLayer = window.dataLayer || [];
dataLayer.push({
    'event': 'view_item',
    'ecommerce': {
        'items': [{
            'item_name': '<?= h($product_detail->name) ?>',
            'item_id': '<?= h($product_detail->id) ?>',
            'price': <?= h($product_detail->promotion_price) ?>,
            'item_category': '<?= h($productCategoryName) ?>',
            'quantity': 1
        }]
    }
});
</script>

<!-- JSON-LD Schema : BreadcrumbList -->
<?php
$breadcrumbItems = [];

// 1. Home item
$breadcrumbItems[] = [
    "@type" => "ListItem",
    "position" => 1,
    "name" => "Home",
    "item" => $this->Url->build('/', ['fullBase' => true])
];

// 2. Category items (from product_categories)
$position = 2;
foreach ($product_detail->product_categories as $val) {
    $breadcrumbItems[] = [
        "@type" => "ListItem",
        "position" => $position,
        "name" => h($val['category_name']),
        "item" => $this->Url->build('/product-list/' . $val['url_key'], ['fullBase' => true])
    ];
    $position++;
}

// 3. Product name
$breadcrumbItems[] = [
    "@type" => "ListItem",
    "position" => $position,
    "name" => h($product_detail->name),
    "item" => $this->Url->build(null, ['fullBase' => true]) // current product URL
];

// Full JSON-LD
$breadcrumbJson = [
    "@context" => "https://schema.org",
    "@type" => "BreadcrumbList",
    "itemListElement" => $breadcrumbItems
];
?>

<script type="application/ld+json">
<?= json_encode($breadcrumbJson, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>
</script>


<!-- JSON-LD Schema : Product -->
<script type="application/ld+json">
{
  "@context": "https://schema.org/",
  "@type": "Product",
  "name": "<?= h($product_detail->name) ?>",
  "image": [<?= json_encode($productImages) ?>],
  "description": <?= json_encode(strip_tags($product_detail->description)) ?>,
  "sku": "<?= $product_detail->sku ?>",
  "brand": {
    "@type": "Brand",
    "name": "<?= $product_detail['Brands']['name'] ?>"
  },
  "review": {
    "@type": "Review",
    "reviewRating": {
      "@type": "Rating",
      "ratingValue": "<?= $product_detail->rating ?>",
      "bestRating": "<?= $product_detail->max_rating['max_rating'] ?>"
    },
    "author": {
      "@type": "Person",
      "name": "<?= $product_detail->max_rating['full_name'] ?>"
    }
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "<?= $product_detail->rating ?>",
    "reviewCount": "<?= $product_detail->total_review ?>"
  },
  "offers": {
    "@type": "Offer",
    "url": "<?= $siteUrl .'product/'. $product_detail->url_key ?>",
    "priceCurrency": "<?= $currency ?>",
    "price": "<?= $product_detail->promotion_price ?>",
    "priceValidUntil": "<?= date('Y-m-d') ?>",
    "itemCondition": "https://schema.org/NewCondition",
    "availability": "https://schema.org/<?= $product_detail->availability_status ?>"
  }
}
</script>

<!-- Buy Now Function -->
<script>
function buyNow() {
    // Get product ID
    const productId = <?= $product_detail->id ?>;

    // Get selected variant ID if available
    const variantSelect = document.getElementById('product-variant');
    let variantId = '';

    if (variantSelect && variantSelect.value) {
        variantId = variantSelect.value;
    }

    // Get quantity
    const quantity = document.getElementById('value').textContent;

    // Get actual price from hidden field
    const actualPrice = document.getElementById('TotalpriceValue').value;

    // Get selected attribute if available
    const selectedAttribute = document.querySelector('.add-to-cart').getAttribute('data-attributes');

    // Check stock status before proceeding
    // Instead of trying to get a return value, we'll check the current state of the stock status element
    const stockStatusElement = document.querySelector('.ax-stock-status');
    if (stockStatusElement && stockStatusElement.textContent.includes('Out of Stock')) {
        return false;
    }

    // Make sure stock status is updated before proceeding
  //  checkProductStockStatus(<?php echo $product_detail->id; ?>, variantId, selectedAttribute);
    // Show loading indicator
    document.querySelector('.buy-now-button').innerHTML = '<i class="fa fa-spinner fa-spin"></i> <?= __('Processing...') ?>';
    document.querySelector('.buy-now-button').disabled = true;

    // First, clear the cart by making an API call to delete all cart items
    $.ajax({
        url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'clearCart']) ?>",
        type: 'POST',
        headers: {
            'X-CSRF-Token': "<?= $this->request->getAttribute('csrfToken') ?>"
        },
        success: function(clearResponse) {
            // After clearing the cart, add the new product
            addToCartAndCheckout(productId, variantId, quantity, selectedAttribute);
        },
        error: function(xhr, status, error) {
            // If clearing cart fails, try to proceed with adding to cart anyway
            console.error('Error clearing cart:', error);
            addToCartAndCheckout(productId, variantId, quantity, selectedAttribute);
        }
    });
}

function addToCartAndCheckout(productId, variantId, quantity, selectedAttribute) {
    // Prepare data for AJAX request
    const data = {
        product_id: productId,
        quantity: quantity
    };

    if (variantId) {
        data.variant_id = variantId;
    }

    if (selectedAttribute) {
        data.product_attribute_id = selectedAttribute;
    }

    // Call the addToCart function with the product data
    $.ajax({
        url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'addToCart']) ?>",
        type: 'POST',
        data: data,
        headers: {
            'X-CSRF-Token': "<?= $this->request->getAttribute('csrfToken') ?>"
        },
        success: function(response) {
            if (response.status === 'success' || response.status === 200) {
                // Redirect to checkout page
                window.location.href = "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkout']) ?>";
            } else {
                // Show error message
                toastr.error(response.message || 'Error adding product to cart', '', {
                    timeOut: 3000,
                    progressBar: true
                });
                document.querySelector('.buy-now-button').innerHTML = '<?= __('BUY NOW') ?>';
                document.querySelector('.buy-now-button').disabled = false;
            }
        },
        error: function(xhr, status, error) {
            // Show error message
            alert('An error occurred. Please try again.');
            document.querySelector('.buy-now-button').innerHTML = '<?= __('BUY NOW') ?>';
            document.querySelector('.buy-now-button').disabled = false;
        }
    });
}
</script>

<?php $this->end(); ?>