<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Showroom $showroom
 * @var string[]|\Cake\Collection\CollectionInterface $cities
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/intlTelInput/css/intlTelInput.min.css') ?>">
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/intlTelInput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"></script>
<style type="text/css">
    .is-invalid {
        border: 1px solid red !important;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 ********* 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    select.is-invalid {
        border-color: #dc3545 !important;
    }

    .error {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 4px;
    }
    .iti--allow-dropdown
    {
        width: 100%;
    }
    #map {

        height: 400px;
        width: 100%;
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Showrooms', 'action' => 'index']) ?>"><?= __('Showrooms') ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __('Edit') ?></li>
    </ul>
    <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
        <small class="p-10 fw-bold"><?= __('BACK') ?></small>
        <span class="rotate me-2">⤣</span>
    </button>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20"><?= __('Edit Showroom') ?></h6>
            <?php echo $this->Form->create($showroom, ['id' => 'edit', 'novalidate' => true, 'type' => 'file']); ?>
            <?php if (!empty($showroom->getErrors())): ?>
                <div class="validation-errors">
                    <?php foreach ($showroom->getErrors() as $field => $errors): ?>
                        <div class="field-errors">
                            <strong><?php echo h(ucwords($field)); ?>:</strong>
                            <ul>
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo h($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label"><?= __('Showroom Name') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name',
                        'placeholder' => 'Showroom Name',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="supervisor" class="col-sm-2 col-form-label"><?= __('Supervisor') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('showroom_supervisor', 
                    ['id' => 'supervisor_model',
                        'type' => 'select',
                        'empty' => 'Select Supervisor',
                        'label' => false,
                        'div' => false,
                        'title' => 'Select Location',
                        'options' => array_reduce($supervisors, function($options, $supervisor) 
                        {
                            $options[$supervisor->id] = $supervisor->first_name.' '.$supervisor->last_name;
                            return $options;
                        }, []),
                        'data-live-search' => "true",
                        'class' => 'form-control select2'
                    ]);
                    ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="city_id" class="col-sm-2 col-form-label"><?= __('Location') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('city_id', 
                    ['id' => 'city_id_model',
                        'type' => 'select',
                        'empty' => 'Select Location',
                        'label' => false,
                        'div' => false,
                        'title' => 'Select Location',
                        'options' => $cities,
                        'data-live-search' => "true",
                        'class' => 'form-control select2'
                    ]);
                    ?>
                </div>
            </div>


            <div class="form-group row">
                <label for="municipality_id" class="col-sm-2 col-form-label"><?= __('Municipality') ?></label>
                <div class="col-sm-5 main-field">
                    <select data-live-search="true" id="municipality" name="municipality_id" class="form-control select2">
                        <option value=""><?= __('Select Municipality') ?></option>
                        <?php if (!empty($municipalities)): ?>
                            <?php foreach ($municipalities as $id => $name): ?>
                                <option value="<?= h($id) ?>" <?= ($showroom->municipality_id == $id) ? 'selected' : '' ?>>
                                    <?= h($name) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
            </div>

            <div class="form-group row">
                <label for="address" class="col-sm-2 col-form-label"><?= __('Address') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <input type="hidden" id="latitude" name="latitude" value="<?= h($showroom->latitude ?? '') ?>">
                    <input type="hidden" id="longitude" name="longitude" value="<?= h($showroom->longitude ?? '') ?>">
                    <?php echo $this->Form->control('address', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'address',
                        'placeholder' => 'Address',
                        'label' => false,
                        'readonly' => true
                    ]); ?>
                    <div id="map" class="mt-2"></div>
                </div>
            </div>

            <div class="form-group row">
                <label for="area_sq_mts" class="col-sm-2 col-form-label"><?= __('Area Sq mts') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('area_sq_mts', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'area_sq_mts',
                        'placeholder' => 'Area Sq mts',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="image" class="col-sm-2 col-form-label fw-bold"><?= __("Image(s)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('image[]', ['type' => 'file', 'class' => 'form-control', 'label' => false, 'multiple' => 'multiple', 'accept' => 'image/*' , 'id' => 'imageInput']);

                    echo $this->Form->hidden('deleted_images', [
                        'id' => 'deletedImagesInput',
                        'value' => ''
                    ]); ?>
                    <span><?= $file_acceptance_msg ?></span>
                    <div id="previeContainer">
                        <ul id="imagePreviewContainer">
                        </ul>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="email" class="col-sm-2 col-form-label"><?= __('Email or Emails') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('email', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'email',
                        'placeholder' => 'Enter email addresses, separated by commas',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="contact_number" class="col-sm-2 col-form-label"><?= __('Phone') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <input id="countryCountryCode" type="hidden" value="<?= $showroom->contact_country_code ?>" name="contact_country_code">
                    <input id="contact_number" type="tel" value="<?= $showroom->contact_number ?>" name="contact_number" required>
                </div>
            </div>

            <div class="form-group row">
                <label for="Showroom_timing" class="col-sm-2 col-form-label"><?= __('Showroom Timings') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('showroom_timing', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'Showroom_timing',
                        'placeholder' => 'Eg: “7 am to 9pm, Monday to Saturday”',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="showroom_manager" class="col-sm-2 col-form-label"><?= __('Showroom Manager') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('showroom_manager', [
                        'id' => 'showroom_manager_model',
                        'type' => 'select',
                        'empty' => 'Select Showroom Manager',
                        'label' => false,
                        'div' => false,
                        'title' => 'Select Location',
                        'options' => $showroom_managers,
                        'value' => $assigned_manager_id, // Preselect assigned manager
                        'data-live-search' => "true",
                        'class' => 'form-control select2'
                    ]);
                    ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="min_product_quantity" class="col-sm-2 col-form-label"><?= __('Minimum Product Quantity') ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('min_product_quantity', [
                        'type' => 'number',
                        'min' => '1',
                        'class' => 'form-control',
                        'id' => 'min_product_quantity',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label"><?= __('Status') ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'status',
                        'options' => [
                            'A' => 'Active',
                            'I' => 'Inactive'
                        ],
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" class="btn"><?= __('Save') ?></button>
                </div>
            </div>
        </div>
        </form>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    let map, marker, geocoder;

    function initMap() {
        // Initialize geocoder
        geocoder = new google.maps.Geocoder();

        // Get existing latitude and longitude if available (edit mode)
        let lat = parseFloat("<?= h($showroom->latitude ?? '5.359952') ?>");
        let lng = parseFloat("<?= h($showroom->longitude ?? '-4.008256') ?>");
        let address = "<?= h($showroom->address) ?>";

        // Default location (Abidjan)
        const defaultLocation = { lat: 5.359952, lng: -4.008256 };
        const showroomLocation = (!isNaN(lat) && !isNaN(lng)) ? { lat, lng } : defaultLocation;

        // Initialize map
        map = new google.maps.Map(document.getElementById("map"), {
            center: showroomLocation,
            zoom: 14,
        });

        // Add draggable marker
        marker = new google.maps.Marker({
            position: showroomLocation,
            map: map,
            draggable: true,
        });

        // Update hidden fields initially
        updateLatLngInputs(showroomLocation.lat, showroomLocation.lng);

        // If address is available, geocode it
        if (address.trim() !== "") {
            geocodeAddress(address);
        }

        // Listen for marker drag event
        marker.addListener("dragend", () => {
            const position = marker.getPosition();
            updateLatLngInputs(position.lat(), position.lng());
            fetchAddress(position.lat(), position.lng());
        });

        // Listen for address input change (manual entry)
        document.getElementById("address").addEventListener("change", () => {
            const newAddress = document.getElementById("address").value;
            geocodeAddress(newAddress);
        });
    }

    // Function to update latitude and longitude in hidden fields
    function updateLatLngInputs(lat, lng) {
        document.getElementById("latitude").value = lat;
        document.getElementById("longitude").value = lng;
    }

    // Function to geocode an address
    function geocodeAddress(address) {
        geocoder.geocode({ address: address }, (results, status) => {
            if (status === "OK") {
                const location = results[0].geometry.location;
                map.setCenter(location);
                marker.setPosition(location);
                updateLatLngInputs(location.lat(), location.lng());
                document.getElementById("address").value = results[0].formatted_address;
            } else {
                console.error("Geocode failed: " + status);
                alert("Address not found.");
            }
        });
    }

    // Function to fetch an address from latitude and longitude
    function fetchAddress(lat, lng) {
        geocoder.geocode({ location: { lat, lng } }, (results, status) => {
            if (status === "OK" && results[0]) {
                document.getElementById("address").value = results[0].formatted_address;
            } else {
                document.getElementById("address").value = "Address not found";
            }
        });
    }

    // Load the map when the page loads
    window.onload = initMap;

    $(document).ready(function() {
        
        // Populate supervisor name on initial load if a zone is already selected
        updateSupervisorName($('#zoneSelect').find(':selected').data('supervisor'));

        // On change, update the supervisor name in the read-only textbox
        $('#zoneSelect').on('change', function() {
            const selectedOption = $(this).find(':selected');
            const supervisorName = selectedOption.data('supervisor');
            updateSupervisorName(supervisorName);
        });

        function updateSupervisorName(name) {
            $('#supervisorName').val(name || '');
        }
    });

    <?php
    $images = !empty($showroom->showroom_images) ? array_map(function ($image) {
        return [
            'id' => $image->id,
            'url' => $image->image
        ];
    }, $showroom->showroom_images) : [];
    ?>

    const existingImages = <?php echo json_encode($images, JSON_HEX_TAG); ?>;

    let allFiles = [];
    let deletedImages = [];

    function initializeExistingImages(existingImages) {
        existingImages.forEach((image, index) => {
            let file = {
                id: image.id,
                name: `existing-image-${index}.jpg`,
                type: 'image/jpeg',
                url: image.url
            };
            allFiles.push(file);
        });
        renderPreviews();
    }

    initializeExistingImages(existingImages);

    document.getElementById('imageInput').addEventListener('change', function (event) {
 
        var files = Array.from(event.target.files);
        
        if(files.length == 0)
        {
            allFiles = [];
            renderPreviews();
            return false;
        }
    
        var validFiles = [];
        var invalidFiles = [];

        let processedFiles = 0;
        let totalFiles = files.length;

        files.forEach(function(file, index) {

            var fileExtension = file.name.split('.').pop().toLowerCase();
            var allowedExtensions = ['jpg', 'jpeg', 'png'];

            // Check if the file has a valid extension
            if (allowedExtensions.includes(fileExtension)) {
                var img = new Image();
                img.src = URL.createObjectURL(file);

                img.onload = function () {
                    var width = img.naturalWidth;
                    var height = img.naturalHeight;
                    var fileSize = file.size / 1024 / 1024;  // Convert file size to MB

                    // Validate file size (max 10MB) and dimensions (width: 300-400px, height: 200-320px)
                    if (fileSize <= <?= $webImageSize ?> && width >= <?= $webImageMinWidth ?> && width <= <?= $webImageMaxWidth ?> && height >= <?= $webImageMinHeight ?> && height <= <?= $webImageMaxHeight ?>) {
                        validFiles.push(file);  // Add to valid files array
                    } else {

                        invalidFiles.push({file: file.name, reason: '<?= __('Image dimensions should be between '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight) ?>'});
                    }

                    processedFiles++;

                    // When all files are processed, update the file input and show alerts
                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }

                };

                img.onerror = function () {
                    invalidFiles.push({file: file, reason: '<?= __('Unable to load image') ?>'});
                    processedFiles++;

                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }
                };
            }
            else
            {

                invalidFiles.push({file: file.name, reason: '<?= __('Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.') ?>'});

                processedFiles++;

                if (processedFiles === totalFiles) {
                    finalizeFileProcessing(validFiles, invalidFiles);
                }
            }
        });

    });

    // Finalize file processing
    function finalizeFileProcessing(validFiles, invalidFiles) {
        

        var html = "<ul>";
        for(var i=0; i < invalidFiles.length; i++)
        {
           html += `<li>${invalidFiles[i].file} - ${invalidFiles[i].reason}</li>`; 
        }
        html += '</ul>';

        const wrapper = document.createElement('div');
        wrapper.innerHTML = html;

        if(invalidFiles.length > 0)
        {
            swal({
                title: "<?= __("Error") ?>", 
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>", 
                allowOutsideClick: "true" 
            });
        }

        var dataTransfer = new DataTransfer();
        
        validFiles.forEach(function(file) {
            dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
        });

        document.getElementById('imageInput').files = dataTransfer.files;  // Update input files

        let newFiles = validFiles;
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();

    }

    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);

            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            if (file.url) {
                li.innerHTML = `
                <img src="${file.url}" alt="Image Preview" class="preview-img"/>
                <span class="image-name" title="${fileName}">${shortName}</span>
                <button type="button" class="delete-img-btn" data-index="${index}">
                    <i class="fas fa-times"></i>
                </button>
            `;
            } else {
                let reader = new FileReader();
                reader.onload = function (e) {
                    li.innerHTML = `
                    <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                    <span class="image-name" title="${fileName}">${shortName}</span>
                    <button type="button" class="delete-img-btn" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                };
                reader.readAsDataURL(file);
            }

            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function (e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            let removedFile = allFiles.splice(index, 1)[0];
            if (removedFile.url && removedFile.id) {
                deletedImages.push(removedFile.id);
            }

            renderPreviews();
            updateFileInput();
            updateDeletedImagesInput();
        }
    });
    function updateDeletedImagesInput() {
        document.getElementById('deletedImagesInput').value = JSON.stringify(deletedImages);
    }

    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        allFiles.forEach(file => {
            if (!file.url) {
                dataTransfer.items.add(file);
            }
        });
        document.getElementById('imageInput').files = dataTransfer.files;
    }


    //FAX
    var inputFax = document.querySelector("#contact_number");
    var itiFax = window.intlTelInput(inputFax, {
        // initialCountry: "ci",
        separateDialCode: true,
        preferredCountries: ["ci", "us", "gb"],
        utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
    });

    var dialCode = $('#countryCountryCode').val();
    itiFax.setNumber('+'+dialCode+inputFax.value);

    function updateFaxCountryCode() {

        var countryData = itiFax.getSelectedCountryData();
        var countryCode = countryData.dialCode;
        var countryCodeText = `+${countryCode} `;

        $('#countryCountryCode').val(itiFax.getSelectedCountryData().dialCode);
    }

    inputFax.addEventListener("countrychange", updateFaxCountryCode);

    $(document).ready(function () {
        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });
    });

    $(document).ready(function () {

        $.validator.addMethod("multiemail", function(value, element) {
            if (this.optional(element)) {
                return true;
            }

            var emails = value.split(','), // Split by comma
                valid = true;

            // Regex pattern for email validation
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            // Validate each email
            for (var i = 0; i < emails.length; i++) {
                emails[i] = emails[i].trim(); // Trim whitespace
                if (!emailPattern.test(emails[i])) {
                    valid = false;
                    break;
                }
            }

            return valid;
        }, "<?= __('Please enter valid email addresses separated by a comma.') ?>");

        $.validator.addMethod("pattern", function (value, element, param) {
            if (this.optional(element)) {
                return true;
            }
            if (typeof param === "string") {
                param = new RegExp("^(?:" + param + ")$");
            }
            return param.test(value);
        }, "<?= __('Invalid format.') ?>");

        // Add custom validation method for contact number
        $.validator.addMethod("validPhoneContact", function(value, element) {
            return itiFax.isValidNumber(); // Use intlTelInput's isValidNumber method
        }, "<?= __('Please enter a valid contact number') ?>");

        $("#edit").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true
                },
                'showroom_supervisor': {
                    required: true
                },
                'address': {
                    required: true
                },
                'email': {
                    required: true,
                    multiemail: true
                },
                'contact_number': {
                    required: true,
                    validPhoneContact: true
                },
                'city_id': {
                    required: true
                },
                'showroom_manager': {
                    required: true
                }
            },
            messages: {
                'name': {
                    required: "<?= __('Please enter showroom name') ?>"
                },
                'showroom_supervisor': {
                    required: "<?= __('Please select a supervisor') ?>"
                },
                'address': {
                    required: "<?= __('Please enter address') ?>"
                },
                'email': {
                    required: "<?= __('Please enter email') ?>",
                    multiemail: "<?= __('Please enter valid email addresses separated by a comma.') ?>"
                },
                'contact_number': {
                    required: "<?= __('Please enter contact number') ?>"
                },
                'city_id': {
                    required: "<?= __('Please select a location') ?>"
                },
                'showroom_manager': {
                    required: "<?= __('Please select a showroom manager') ?>"
                }
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');

                // If it's a select2 or select box, also add to the container
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').addClass('is-invalid');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');

                // Remove from select2 or select container too
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').removeClass('is-invalid');
                }
            },
            submitHandler: function (form) {
                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            }
        });
    });
</script>

<!-- Load Google Maps API Asynchronously -->
<script
    src="https://maps.googleapis.com/maps/api/js?key=<?= h($mapApiKey) ?>&callback=initMap&libraries=places"
    async
    defer
></script>

<?php $this->end(); ?>