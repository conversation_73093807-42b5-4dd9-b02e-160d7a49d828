<?php

declare(strict_types=1);

namespace App\Controller;

/**
 * ProductVariantImages Controller
 *
 * @property \App\Model\Table\ProductVariantImagesTable $ProductVariantImages
 */
class ProductVariantImagesController extends AppController
{

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->ProductVariantImages->find()
            ->contain(['ProductVariants']);
        $productVariantImages = $this->paginate($query);

        $this->set(compact('productVariantImages'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Variant Image id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $productVariantImage = $this->ProductVariantImages->get($id, contain: ['ProductVariants']);
        $this->set(compact('productVariantImage'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $productVariantImage = $this->ProductVariantImages->newEmptyEntity();
        if ($this->request->is('post')) {
            $productVariantImage = $this->ProductVariantImages->patchEntity($productVariantImage, $this->request->getData());
            if ($this->ProductVariantImages->save($productVariantImage)) {
                $this->Flash->success(__('The product variant image has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product variant image could not be saved. Please, try again.'));
        }
        $productVariants = $this->ProductVariantImages->ProductVariants->find('list', limit: 200)->all();
        $this->set(compact('productVariantImage', 'productVariants'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Product Variant Image id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $productVariantImage = $this->ProductVariantImages->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $productVariantImage = $this->ProductVariantImages->patchEntity($productVariantImage, $this->request->getData());
            if ($this->ProductVariantImages->save($productVariantImage)) {
                $this->Flash->success(__('The product variant image has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product variant image could not be saved. Please, try again.'));
        }
        $productVariants = $this->ProductVariantImages->ProductVariants->find('list', limit: 200)->all();
        $this->set(compact('productVariantImage', 'productVariants'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Product Variant Image id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function deleteImage($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);  // Allow only POST or DELETE requests
        $response = ['success' => false, 'message' => 'The product variant image could not be deleted. Please, try again.'];

        $productVariantImage = $this->ProductVariantImages->get($id);

        if (!$productVariantImage) {
            $response = ['success' => false, 'message' => 'The product variant image does not exist.'];
        } else {
            if ($this->ProductVariantImages->delete($productVariantImage)) {
                $response = ['success' => true, 'message' => 'The product variant image has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The product variant image could not be deleted. Please, try again.'];
            }
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function getVariantImages()
    {
        $this->request->allowMethod(['get']);
        $productId = $this->request->getQuery('product_id');

        $productVariantImages = $this->ProductVariantImages->find('all', [
            'conditions' => [
                'ProductVariants.product_id' => $productId,
                'ProductVariantImages.status' => 'A',
                'ProductVariantImages.media_type' => 'Image'
            ],
            'contain' => ['ProductVariants'], 
            'order' => ['ProductVariantImages.image_default' => 'DESC']
        ]);

        if ($productVariantImages->count() == 0) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => false, 'message' => 'No variant images found for this product.']));
        }

        $variants = [];
        foreach ($productVariantImages as $image) {
            $fileName = basename($image->image);
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            $nameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

            $shortName = strlen($nameWithoutExtension) > 14 ? substr($nameWithoutExtension, 0, 11) : $nameWithoutExtension;
            $shortName .= '.' . $extension;

            $variantId = $image->product_variant->id;
            if (!isset($variants[$variantId])) {
                $variants[$variantId] = [
                    'variant_name' => $image->product_variant->variant_name,
                    'images' => []
                ];
            }

            $variants[$variantId]['images'][] = [
                'id' => $image->id,
                'src' => $this->Media->getCloudFrontURL($image->image),
                'default' => $image->image_default,
                'shortName' => $shortName
            ];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'variants' => $variants]));
    }

    public function makeDefault($id = null, $varinatId = null)
    {
        $this->request->allowMethod(['post']);
        $productVarImage = $this->ProductVariantImages->get($id);
        $response = ['success' => false, 'message' => 'The product variant image could not be made default. Please, try again.'];
        if ($productVarImage) {
            if ($this->ProductVariantImages->makeDefault($productVarImage)) {
                $response = ['success' => true, 'message' => 'The product variant image has been makred default.'];
            } else {
                $response = ['success' => false, 'message' => 'The product variant image could not be made default. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product variant image does not exist.'];
        }


        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    public function getVariantVideos()
    {
        $this->request->allowMethod(['get']);
        $productId = $this->request->getQuery('product_id');

        $productVariantImages = $this->ProductVariantImages->find('all', [
            'conditions' => [
                'ProductVariants.product_id' => $productId,
                'ProductVariantImages.status' => 'A',
                'ProductVariantImages.media_type' => 'Video'
            ],
            'contain' => ['ProductVariants'], 
            // 'order' => ['ProductVariantImages.image_default' => 'DESC']
        ]);

        if ($productVariantImages->count() == 0) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => false, 'message' => 'No variant videos found for this product.']));
        }

        $variants = [];
        foreach ($productVariantImages as $video) {
            $fileName = basename($video->video);
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            $nameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

            $shortName = strlen($nameWithoutExtension) > 14 ? substr($nameWithoutExtension, 0, 11) : $nameWithoutExtension;
            $shortName .= '.' . $extension;

            $variantId = $video->product_variant->id;
            if (!isset($variants[$variantId])) {
                $variants[$variantId] = [
                    'variant_name' => $video->product_variant->variant_name,
                    'videos' => []
                ];
            }

            $variants[$variantId]['videos'][] = [
                'id' => $video->id,
                'src' => $this->Media->getCloudFrontURL($video->video),
                // 'default' => $video->image_default,
                'shortName' => $shortName
            ];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'variants' => $variants]));
    }
}
