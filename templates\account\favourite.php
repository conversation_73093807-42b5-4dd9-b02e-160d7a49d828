<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myFav.css') ?>" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<style>
    .my-order-item-details {
        border: 1px solid orange !important;
        border-radius: 10px;
        padding: 10px;
        width: 690px;
    }
.close-btn-btn {
    background-color: #0D839B;
    color: white;
    padding: 10px 20px;
    border: none;
    outline: none;
    border-radius: 15px;
}
.my-order-item-details .img-container{
    overflow: hidden;
    box-shadow: 0px 1px 4px -2px #000;
    border-radius: 5px;
}
.my-order-item-details .img-container img{
    transform: scale(1.66);
}
</style>
<?php $this->end(); ?>

<div class="productCategoryListingC">
    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span
            class="productCategoryListing-home-span"><?= $this->Html->link('Home', ['controller' => 'Website', 'action' => 'home', 'prefix' => false]) ?></span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span Electronic-devices">My Favourite</span>
    </div>
</div>

<div class="my-a-my-o-c">
<?php echo $this->element('web_sidebar'); ?>

    <div class="my-a-my-o-main">

        <?php if(empty($wishlists)): ?>
            <div class="no-wishlist-msg"><?= __("Your wishlist is currently empty.") ?></div>
        <?php else: ?>
            <?php foreach($wishlists as $val): ?>
                <div class="my-order-item-details">
                    <div class="img-container">
                        <img src="<?php echo $val->product->product_image ?>" class="my-order-item-icon">
                    </div>
                    <div class="my-order-items-details-tpc">
                        <div class="my-order-item-details-title-container">
                            <div class="my-order-item-details-title"><?php echo $val->product->name ?></div>
                        </div>
                        <div class="my-order-item-details-price"><span style="color:#492600;"><?= !empty( $this->Price->setPriceFormat($val->product->promotion_price)) ?  $this->Price->setPriceFormat($val->product->promotion_price) :  $this->Price->setPriceFormat($val->product->sales_price); ?> </span><span class="discount-price" style="color: gray;"><?=  $this->Price->setPriceFormat($val->product->product_price) ?></span><span class="off"><?= $val->product->discount ?>% OFF</span></div>


                    </div>
                    <div class="add-to-cart-ctnr">
                        <button class="add-to-cart " data-product-id="<?php echo $val->product->id ?>">MOVE TO CART</button>
                        <button class="close-btn-btn remove-to-wishlist-btn" data-product-id="<?php echo $val->product->id ?>">REMOVE</button>
                    </div>

                </div>
            <?php endforeach; ?>
        <?php endif; ?>

    </div>

</div>
