<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ShowroomStockReturnController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Showrooms;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $Roles;
    protected $SupplierProducts;
    protected $StockMovements;
    protected $StockMovementItems;
    protected $StockReturns;
    protected $StockReturnItems;
    protected $ProductStocks;
    protected $Users;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->StockMovementItems = $this->fetchTable('StockMovementItems');
        $this->Roles = $this->fetchTable('Roles');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->StockReturns = $this->fetchTable('StockReturns');
        $this->StockReturnItems = $this->fetchTable('StockReturnItems');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Users = $this->fetchTable('Users');
    }

    public function index()
    {
        // Get the currently authenticated user
        $requested_user = $this->Authentication->getIdentity();

        $warehouse_id = null;
        $showroom_ids = [];

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'warehouse manager') {
                $warehouse = $this->Warehouses->find()
                    ->select(['id'])
                    ->where(['manager_id' => $requested_user->id])
                    ->first();
                $warehouse_id = $warehouse ? $warehouse->id : null;
            } elseif (strtolower($role->name) === 'warehouse assistant') {
                $warehouse = $this->Warehouses->find()
                    ->select(['id'])
                    ->where(['assistant_id' => $requested_user->id])
                    ->first();
                $warehouse_id = $warehouse ? $warehouse->id : null;
            } elseif (strtolower($role->name) === 'showroom manager') {
                $showroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();
                if ($showroom) {
                    $showroom_ids[] = $showroom->id;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->all();
                if (!$showrooms->isEmpty()) {
                    foreach ($showrooms as $showroom) {
                        $showroom_ids[] = $showroom->id;
                    }
                }
            }
        }

        // echo '<pre>';print_r($showroom_ids);die;

        $query = $this->StockReturns->find()
            ->where([
                'StockReturns.return_from' => 'Showroom' // Default for warehouse
            ])
            // ->leftJoin(
            //     ['Warehouses' => 'warehouses'],
            //     ['Warehouses.id = StockReturns.return_id_from']
            // )
            ->leftJoin(
                ['ReturnToWarehouses' => 'warehouses'],
                ['ReturnToWarehouses.id = StockReturns.return_id_to']
            )
            ->leftJoin(
                ['ReturnFromShowrooms' => 'showrooms'],
                ['ReturnFromShowrooms.id = StockReturns.return_id_from']
            )
            ->select([
                'StockReturns.id',
                'StockReturns.stock_request_id',
                'StockReturns.return_id_from',
                'StockReturns.return_id_to',
                'StockReturns.return_date',
                'StockReturns.return_from',
                'StockReturns.created',
                // 'ReturnFromWarehouseName' => 'Warehouses.name',
                'ReturnFromShowroomName' => 'ReturnFromShowrooms.name',
                'ReturnToWarehouseName' => 'ReturnToWarehouses.name'
            ])
            ->order(['StockReturns.created' => 'DESC']);

        // Apply filters based on role
        if (!empty($warehouse_id)) {
            // For warehouse manager/assistant
            $query->where([
                'StockReturns.return_id_from' => $warehouse_id
            ]);
        }

        if (!empty($showroom_ids)) {
            // For showroom manager/supervisor
            $query->where([
                'StockReturns.return_from' => 'Showroom',
                'StockReturns.return_id_from IN' => $showroom_ids
            ]);
        }

        $stock_returns = $query->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('currencySymbol', 'decimalSeparator', 'thousandSeparator', 'stock_returns', 'dateFormat', 'timeFormat'));
    }

    public function add()
    {
        $returnRequest = $this->StockReturns->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            // echo '<pre>';print_r($data);die;

            $requestedUser = $this->Authentication->getIdentity();

            // Format the return_date
            if (!empty($data['return_date'])) {
                try {
                    $data['return_date'] = (new \DateTime($data['return_date']))->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    $this->Flash->error(__('Invalid return date format.'));
                    return $this->redirect($this->referer());
                }
            }

            // Fetch Stock Movement
            $stockMovement = $this->StockMovements->find()
                ->select(['id'])
                ->where(['movement_type' => 'Incoming', 'StockMovements.referenceID' => $data['stock_request_id']])
                ->first();

            // echo '<pre>';print_r($stockMovement);die;

            if (!$stockMovement) {
                $this->Flash->error(__('Stock Movement not found.'));
                return $this->redirect($this->referer());
            }

            // Set additional return data
            $data['stock_movement_id'] = $stockMovement->id;
            $data['return_request_by'] = $requestedUser->id;
            $data['return_from'] = 'Showroom';
            $data['return_id_from'] = $data['showroom_id'];
            $data['return_to'] = 'Warehouse';
            $data['return_id_to'] = $data['warehouse_id'];
            $data['verify_status'] = 'Approved';

            // Patch entity
            $returnRequest = $this->StockReturns->patchEntity($returnRequest, $data);

            if ($this->StockReturns->save($returnRequest)) {
                // Save return items
                $this->saveStockReturnItems($returnRequest);

                $this->Flash->success(__('The return order request has been saved.'));

                return $this->redirect(['action' => 'index']); // redirect to appropriate index/list page
            }

            $this->Flash->error(__('The return order request could not be saved. Please, try again.'));
        }

        // $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
        //     ->where([
        //         'SupplierPurchaseOrders.delivery_status' => 'Delivered',
        //         // 'SupplierPurchaseOrders.id_deliver_to' => 5,
        //         'SupplierPurchaseOrders.status' => 'A'
        //     ])
        //     ->all();

        // $purchase_order_bills = [];

        // // Loop through categories
        // foreach ($supplierPurchaseOrders as $purchaseOrder) {
        //     $purchase_order_bills[$purchaseOrder->id] = $purchaseOrder->bill_no;
        // }

        // Get the logged-in user
        $requested_user = $this->Authentication->getIdentity();

        if($requested_user)
        {
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) == 'showroom supervisor') {
                
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.showroom_supervisor' => $requested_user->id,'Showrooms.status' => 'A'])
                    ->toArray();

            }else
            {
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();
            }
        }

        $all_showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();

        $this->set(compact('warehouses', 'requested_user', 'showrooms', 'role' ,'all_showrooms'));
    }

    public function getBillsByWarehouse()
    {
        $this->request->allowMethod(['ajax']);
        $warehouseId = $this->request->getQuery('warehouse_id');

        $purchase_order_bills = [];

        $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
            ->where([
                'SupplierPurchaseOrders.delivery_status' => 'Delivered',
                'SupplierPurchaseOrders.status' => 'A',
                'SupplierPurchaseOrders.id_deliver_to' => $warehouseId
            ])
            ->all();

        foreach ($supplierPurchaseOrders as $purchaseOrder) {
            $purchase_order_bills[$purchaseOrder->id] = $purchaseOrder->bill_no;
        }

        $response = [
            'status' => 'success',
            'purchase_order_bills' => $purchase_order_bills,
        ];

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    protected function saveStockReturnItems($return_request)
    {
        // echo '<pre>';print_r($return_request);die;

        if (!empty($return_request->product_id) && is_array($return_request->product_id)) {
            $stock_return_id = $return_request->id;
            $stock_request_id = $return_request->stock_request_id;
            $product_id = $return_request->product_id;
            $product_variant_id = $return_request->product_variant_id;
            $product_attribute_id = $return_request->product_attribute_id;
            $product_quantity = $return_request->quantity;
            $product_return_quantity = $return_request->return_quantity;
            $return_product_image = $return_request->return_product_image;
            $return_reason = $return_request->return_reason;
            $supplier_purchase_order_id = $return_request->purchase_order_id;
            $showroom_id = $return_request->showroom_id;
            $warehouse_id = $return_request->return_id_to;

            // Delete existing stock return records
            $this->StockReturnItems->deleteAll(['stock_return_id' => $stock_return_id]);

            for ($i = 0; $i < sizeof($product_id); $i++) {

                $return_images = [];

                if (!empty($return_product_image[$product_id[$i]])) { // Ensure images exist for the specific product
                    foreach ($return_product_image[$product_id[$i]] as $defect_image) { 
                        if ($defect_image instanceof \Laminas\Diactoros\UploadedFile && $defect_image->getError() === UPLOAD_ERR_OK) {
                            $fileName = trim($defect_image->getClientFilename());

                            if (!empty($fileName)) {
                                $imageTmpName = $defect_image->getStream()->getMetadata('uri');
                                $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                                $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                                $filePath = Configure::read('Constants.ORDER_RETURN_DEFECT_IMAGE');
                                $folderPath = $uploadFolder . $filePath;
                                $targetdir = WWW_ROOT . $folderPath;
                                $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                                $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                                // Upload the image using the Media component
                                $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);

                                if ($uploadResult !== 'Success') {
                                    $this->Flash->error(__('Image could not be uploaded. Please, try again.'));
                                    return $this->redirect(['action' => 'add']);
                                } else {
                                    $return_images[] = $folderPath . $imageFile; // Store uploaded image path
                                }
                            }
                        }
                    }
                }

                // If only one image was uploaded, store it as a single string; otherwise, store multiple images as a CSV string
                $return_image = !empty($return_images) ? implode(',', $return_images) : null;

                // Prepare data for insertion
                $stockReturnData = [
                    'stock_return_id' => $stock_return_id,
                    'product_id' => $product_id[$i],
                    'quantity' => $product_quantity[$i],
                    'return_quantity' => $product_return_quantity[$i],
                    'return_product_image' => $return_image,
                    'return_reason' => $return_reason[$i],
                ];

                // Only add product_variant_id if available
                // Ensure product_variant_id is an integer or null
                if (!empty($product_variant_id[$i]) && $product_variant_id[$i] !== 'null') {
                    $stockReturnData['product_variant_id'] = is_numeric($product_variant_id[$i]) ? (int) $product_variant_id[$i] : null;
                }

                // Ensure product_attribute_id is an integer or null
                if (!empty($product_attribute_id[$i]) && $product_attribute_id[$i] !== 'null') {
                    $stockReturnData['product_attribute_id'] = is_numeric($product_attribute_id[$i]) ? (int) $product_attribute_id[$i] : null;
                }

                // Save stock return item
                $stockReturnItem = $this->StockReturnItems->newEntity($stockReturnData);
                if (!$this->StockReturnItems->save($stockReturnItem)) {
                    return false;
                }

                $stockRequestItemQuery = $this->StockRequestItems->find()
                    ->where([
                        'stock_request_id' => $stock_request_id,
                        'product_id' => $product_id[$i],
                    ]);

                if (!empty($product_variant_id[$i]) && $product_variant_id[$i] !== 'null') {
                    $stockRequestItemQuery->where(['product_variant_id' => (int)$product_variant_id[$i]]);
                } elseif (!empty($product_attribute_id[$i]) && $product_attribute_id[$i] !== 'null') {
                    $stockRequestItemQuery->where(['product_attribute_id' => (int)$product_attribute_id[$i]]);
                }

                $stockRequestItem = $stockRequestItemQuery->first();

                if ($stockRequestItem) {
                    $stockRequestItem->fulfilled_quantity -= $product_return_quantity[$i];
                    if ($stockRequestItem->fulfilled_quantity < 0) {
                        $stockRequestItem->fulfilled_quantity = 0;
                    }
                    $this->StockRequestItems->save($stockRequestItem);
                }

                $productStockQuery = $this->ProductStocks->find()
                    ->where([
                        'showroom_id' => $showroom_id,
                        'product_id' => $product_id[$i],
                    ]);

                if (!empty($product_variant_id[$i]) && $product_variant_id[$i] !== 'null') {
                    $productStockQuery->where(['product_variant_id' => (int)$product_variant_id[$i]]);
                } elseif (!empty($product_attribute_id[$i]) && $product_attribute_id[$i] !== 'null') {
                    $productStockQuery->where(['product_attribute_id' => (int)$product_attribute_id[$i]]);
                }

                $productStock = $productStockQuery->first();

                if ($productStock) {
                    $productStock->quantity -= $product_return_quantity[$i];
                    if ($productStock->quantity < 0) {
                        $productStock->quantity = 0;
                    }
                    $this->ProductStocks->save($productStock);
                }

                //increase defective stock of warehouse
                $warehouseProductStockQuery = $this->ProductStocks->find()
                    ->where([
                        'warehouse_id' => $warehouse_id, // Add warehouse_id condition
                        'product_id' => $product_id[$i],
                    ]);

                if (!empty($product_variant_id[$i]) && $product_variant_id[$i] !== 'null') {
                    $warehouseProductStockQuery->where(['product_variant_id' => (int)$product_variant_id[$i]]);
                } elseif (!empty($product_attribute_id[$i]) && $product_attribute_id[$i] !== 'null') {
                    $warehouseProductStockQuery->where(['product_attribute_id' => (int)$product_attribute_id[$i]]);
                }

                $warehouseProductStock = $warehouseProductStockQuery->first();

                if ($warehouseProductStock) {
                    // Increase defective stock quantity
                    $warehouseProductStock->defective_stock += $product_return_quantity[$i]; // Assuming you have an array for the quantity increase

                    // Ensure that the defective stock does not go below zero
                    if ($warehouseProductStock->defective_stock < 0) {
                        $warehouseProductStock->defective_stock = 0;
                    }

                    $this->ProductStocks->save($warehouseProductStock);
                }
            }
        }

        return true;
    }

    public function view($id = null)
    {
        $return_request = $this->StockReturns->find()
            ->where(['StockReturns.id' => $id])
            ->leftJoin(
                ['Warehouses' => 'warehouses'],
                ['StockReturns.return_id_to = Warehouses.id']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['StockReturns.return_id_from = Showrooms.id']
            )
            ->leftJoin(
                ['Users' => 'users'],
                ['StockReturns.return_request_by = Users.id']
            )
            ->select([
                'StockReturns.id',
                'StockReturns.stock_request_id',
                'StockReturns.return_id_from',
                'StockReturns.return_id_to',
                'StockReturns.return_date',
                'StockReturns.return_from',
                'StockReturns.created',
                'WarehousesName' => 'Warehouses.name',
                'ShowroomsName' => 'Showrooms.name',
                'Users.first_name',
                'Users.last_name'
            ])
            ->first();

        if (!$return_request) {
            $this->Flash->error(__('Return request not found.'));
            return $this->redirect(['action' => 'index']);
        }

        $return_products = $this->StockReturnItems->find()
            ->where(['StockReturnItems.stock_return_id' => $id])
            ->contain([
                'Products' => [
                    'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
                    'ProductVariants' => [
                        'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
                        'conditions' => ['ProductVariants.id IS NOT NULL']
                    ]
                ]
            ])
            ->toArray();

        // Currency Format Settings
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '.';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? ',';

        foreach ($return_products as &$product) {
            // Handle Return Images
            if (!empty($product->return_product_image)) {
                $imageUrls = explode(',', $product->return_product_image);

                $imageUrls = array_map(function ($image) {
                    return $this->Media->getCloudFrontURL(trim($image));
                }, $imageUrls);

                $product->return_product_image = implode(',', $imageUrls);
            }

            // Handle Variants
            $variantDetails = null;
            if ($product->product_variant_id && !empty($product->product->product_variants)) {
                foreach ($product->product->product_variants as $variant) {
                    if ($variant->id == $product->product_variant_id) {
                        $variantDetails = [
                            'variant_name' => $variant->variant_name,
                            'sku' => $variant->sku
                        ];
                        break;
                    }
                }
            }

            if ($variantDetails) {
                $product->product->variant_name = $variantDetails['variant_name'];
                $product->product->sku = $variantDetails['sku'];
            }

            // Handle Attributes
            if ($product->product_attribute_id) {
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $product->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                $product->product->attributes = $attributes ?: [];
            } else {
                $product->product->attributes = [];
            }
        }
        unset($product); // Unset reference

        $this->set(compact('return_request', 'return_products'));
    }


    // public function view($id = null)
    // {
    //     $return_request = $this->StockReturns->find()
    //         ->where(['StockReturns.id' => $id])
    //         ->leftJoinWith('StockRequests.SupplierPurchaseOrders') // Join SupplierPurchaseOrders via StockRequests
    //         ->leftJoin(
    //             ['Suppliers' => 'suppliers'], 
    //             ['StockReturns.return_id_to = Suppliers.id'] // Join Suppliers based on return_id_to
    //         )
    //         ->leftJoin(
    //             ['Users' => 'users'],
    //             ['StockReturns.return_request_by = Users.id'] // Join Users to get requestor details
    //         )
    //         ->select([
    //             'StockReturns.id',
    //             'StockReturns.return_id_to',
    //             'StockReturns.return_date',
    //             'StockReturns.created',
    //             'SupplierPurchaseOrders.bill_no',
    //             'Suppliers.name', // Supplier name
    //             'Users.first_name',
    //             'Users.last_name'  // Requestor name
    //         ])
    //         ->first();

    //     // echo '<pre>';print_r($return_request);die;

    //     if (!$return_request) {
    //         $this->Flash->error(__('Return request not found.'));
    //         return $this->redirect(['action' => 'index']);
    //     }

    //     $return_products = $this->StockReturnItems->find()
    //         ->where(['StockReturnItems.stock_return_id' => $id])
    //         ->contain([
    //             'Products' => [
    //                 'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
    //                 'SupplierProducts' => [
    //                     'fields' => ['SupplierProducts.product_id', 'SupplierProducts.supplier_price'],
    //                     'Suppliers' => [
    //                         'fields' => ['Suppliers.id', 'Suppliers.name']
    //                     ]
    //                 ],
    //                 'ProductVariants' => [
    //                     'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
    //                     'conditions' => ['ProductVariants.id IS NOT NULL']
    //                 ]
    //             ]
    //         ])
    //         ->toArray();

    //     // echo '<pre>';print_r($return_products);die;

    //     // Currency Format Settings
    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
    //     $decimalSeparator = $currencyConfig['decimal_separator'] ?? '.';
    //     $thousandSeparator = $currencyConfig['thousand_separator'] ?? ',';

    //     foreach ($return_products as &$product) {
    //         // Format supplier price
    //         if (!empty($product->product->supplier_products)) {
    //             foreach ($product->product->supplier_products as &$supplierProduct) {
    //                 if (!empty($supplierProduct->supplier_price)) {
    //                     $supplierProduct->supplier_price = number_format(
    //                         (float)$supplierProduct->supplier_price,
    //                         2,
    //                         $decimalSeparator,
    //                         $thousandSeparator
    //                     ) . ' ' . $currencySymbol;
    //                 }
    //             }
    //             unset($supplierProduct); // Unset reference
    //         }

    //         // Handle Return Images
    //         if (!empty($product->return_product_image)) {
    //             $imageUrls = explode(',', $product->return_product_image);

    //             $imageUrls = array_map(function ($image) {
    //                 return $this->Media->getCloudFrontURL(trim($image));
    //             }, $imageUrls);

    //             $product->return_product_image = implode(',', $imageUrls);
    //         }

    //         // Handle Variants
    //         $variantDetails = null;
    //         if ($product->product_variant_id && !empty($product->product->product_variants)) {
    //             foreach ($product->product->product_variants as $variant) {
    //                 if ($variant->id == $product->product_variant_id) {
    //                     $variantDetails = [
    //                         'variant_name' => $variant->variant_name,
    //                         'sku' => $variant->sku
    //                     ];
    //                     break;
    //                 }
    //             }
    //         }

    //         if ($variantDetails) {
    //             $product->product->variant_name = $variantDetails['variant_name'];
    //             $product->product->sku = $variantDetails['sku'];
    //         }

    //         // Handle Attributes
    //         if ($product->product_attribute_id) {
    //             $attributes = $this->ProductAttributes->find()
    //                 ->where(['ProductAttributes.id' => $product->product_attribute_id])
    //                 ->contain([
    //                     'Attributes' => ['fields' => ['Attributes.name']],
    //                     'AttributeValues' => ['fields' => ['AttributeValues.value']]
    //                 ])
    //                 ->first();

    //             $product->product->attributes = $attributes ?: [];
    //         } else {
    //             $product->product->attributes = [];
    //         }
    //     }
    //     unset($product); // Unset reference

    //     // Now set data for view
    //     $this->set(compact('return_request', 'return_products'));
    // }


    // public function view($id = null)
    // {
    //     $return_request = $this->StockReturns->find()
    //         ->where(['StockReturns.id' => $id])
    //         ->leftJoinWith('StockRequests.SupplierPurchaseOrders') // Join SupplierPurchaseOrders via StockRequests
    //         ->leftJoin(
    //             ['Suppliers' => 'suppliers'], 
    //             ['StockReturns.return_id_to = Suppliers.id'] // Join Suppliers based on return_id_to
    //         )
    //         ->leftJoin(
    //             ['Users' => 'users'],
    //             ['StockReturns.return_request_by = Users.id'] // Join Users to get requestor details
    //         )
    //         ->select([
    //             'StockReturns.id',
    //             'StockReturns.return_id_to',
    //             'StockReturns.return_date',
    //             'StockReturns.created',
    //             'SupplierPurchaseOrders.bill_no',
    //             'Suppliers.name', // Fetch supplier name
    //             'Users.first_name',
    //             'Users.last_name' // Fetch return requestor name
    //         ])
    //         ->first();

    //     $return_products = $this->StockReturnItems->find()
    //         ->where(['StockReturnItems.stock_return_id' => $id])
    //         ->contain([
    //             'Products' => [
    //                 'fields' => ['Products.id', 'Products.name', 'Products.sku', 'Products.product_image'],
    //                 'SupplierProducts' => [
    //                     'fields' => ['SupplierProducts.product_id', 'SupplierProducts.supplier_price'],
    //                     'Suppliers' => [
    //                         'fields' => ['Suppliers.id', 'Suppliers.name']
    //                     ]
    //                 ],
    //                 'ProductVariants' => [
    //                     'fields' => ['ProductVariants.product_id', 'ProductVariants.id', 'ProductVariants.sku', 'ProductVariants.variant_name'],
    //                     'conditions' => ['ProductVariants.id IS NOT NULL']
    //                 ]
    //             ]
    //         ])->toArray();

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
    //     $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
    //     $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';

    //     foreach ($return_products as $product) {
    //         // Format supplier price
    //         if (!empty($product->product->supplier_products)) {
    //             foreach ($product->product->supplier_products as $supplierProduct) {
    //                 if (!empty($supplierProduct->supplier_price)) {
    //                     $supplierProduct->supplier_price = number_format(
    //                         (float)$supplierProduct->supplier_price, 
    //                         2, 
    //                         $decimalSeparator, 
    //                         $thousandSeparator
    //                     ) . ' ' . $currencySymbol;
    //                 }
    //             }
    //         }

    //         // if ($product->return_product_image) {
    //         //     $product->return_product_image = $this->Media->getCloudFrontURL($product->return_product_image);
    //         // }

    //         if (!empty($product->return_product_image)) {
    //             $imageUrls = explode(',', $product->return_product_image); // Convert string to array

    //             // Apply CloudFront URL transformation to each image
    //             $imageUrls = array_map(function ($image) {
    //                 return $this->Media->getCloudFrontURL(trim($image));
    //             }, $imageUrls);

    //             // Convert back to comma-separated string
    //             $product->return_product_image = implode(',', $imageUrls);
    //         }


    //         $variantDetails = null;

    //         // Fetch matching variant if variant_id exists
    //         if ($product->product_variant_id) {
    //             $filtered_variants = array_filter($product->product->product_variants, function ($variant) use ($product) {
    //                 return $variant->id == $product->product_variant_id;
    //             });

    //             if (!empty($filtered_variants)) {
    //                 $variant = reset($filtered_variants);
    //                 $variantDetails = [
    //                     'variant_name' => $variant->variant_name,
    //                     'sku' => $variant->sku
    //                 ];
    //             }
    //         }

    //         if ($variantDetails) {
    //             $product->product->variant_name = $variantDetails['variant_name'];
    //             $product->product->sku = $variantDetails['sku'];
    //         }

    //         // Fetch product attributes
    //         if ($product->product_attribute_id) {
    //             $attributes = $this->ProductAttributes->find()
    //                 ->where(['ProductAttributes.id' => $product->product_attribute_id])
    //                 ->contain([
    //                     'Attributes' => ['fields' => ['Attributes.name']],
    //                     'AttributeValues' => ['fields' => ['AttributeValues.value']]
    //                 ])
    //                 ->first();

    //             $product->product->attributes = $attributes ?: [];
    //         } else {
    //             $product->product->attributes = [];
    //         }
    //     }

    //     $response = [
    //         'status' => 'success',
    //         'return_request' => $return_request,
    //         'return_products' => $return_products
    //     ];

    //     return $this->response
    //         ->withType('application/json')
    //         ->withStringBody(json_encode($response));
    // }

    public function fetchRequestIds()
    {
        // Get showroom_id and warehouse_id from the request
        $showroomId = $this->request->getQuery('showroom_id');
        $warehouseId = $this->request->getQuery('warehouse_id');

        // Fetch stock requests based on conditions
        $stockRequests = $this->StockRequests->find()
            ->select(['id'])  // You can modify this to fetch required fields
            ->where([
                'showroom_id' => $showroomId,
                'warehouse_id' => $warehouseId,
                'status' => 'A',  // Active status
                'request_status' => 'Completed',
                'requestor_type' => 'Showroom'
            ])
            ->order(['id' => 'DESC'])
            ->all();

        $response = [
            'status' => 'success',
            'requestIds' => $stockRequests
        ];

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    public function getStockRequestItems()
    {
        $this->request->allowMethod(['post']);

        $stockRequestId = $this->request->getData('stock_request_id');

        if ($stockRequestId) {
            $stockRequestItems = $this->StockRequests->StockRequestItems->find()
                ->select([
                    'StockRequestItems.id',
                    'StockRequestItems.fulfilled_quantity',
                    'StockRequestItems.product_id',
                    'StockRequestItems.product_variant_id',
                    'StockRequestItems.product_attribute_id',
                    'StockRequestItems.stock_request_id',
                    'StockRequestItems.supervisor_approved_quantity',
                    'Products.name',
                    'Products.purchase_price',
                    'Products.sku',
                    'Products.supplier_id',
                    'ProductVariants.id',
                    'ProductVariants.variant_name',
                    'ProductVariants.purchase_price',
                    'ProductVariants.sku',
                    'Suppliers.name' 
                ])
                ->leftJoinWith('Products')
                ->leftJoinWith('Products.Suppliers')
                ->leftJoinWith('ProductVariants')
                ->where([
                    'StockRequestItems.stock_request_id' => $stockRequestId,
                    'StockRequestItems.status' => 'Approved'
                ])
                ->toArray();  

            $response = [];
            foreach ($stockRequestItems as $item) {
                $item->attributes = [];

                if ($item->product_attribute_id) {
                    // Fetch attributes related to the product
                    $attributes = $this->ProductAttributes->find()
                        ->where(['ProductAttributes.id' => $item->product_attribute_id])
                        ->contain([
                            'Attributes' => ['fields' => ['Attributes.name']],
                            'AttributeValues' => ['fields' => ['AttributeValues.value']]
                        ])
                        ->first();

                    if ($attributes) {
                        // Add attribute details to the item if found
                        $item->attributes = [
                            'attribute_name' => $attributes->attribute->name ?? '',
                            'attribute_value' => $attributes->attribute_value->value ?? ''
                        ];
                    }
                }

                $itemId = $item->id;
                $product_name = $item->_matchingData['Products']->name;
                $product_variant = $item->_matchingData['ProductVariants']->id ? $item->_matchingData['ProductVariants']->variant_name : 'N/A';
                $product_attribute = $item->attributes ? $item->attributes['attribute_name'] . ':' . $item->attributes['attribute_value'] : 'N/A';
                $sku = $item['product_variant_id'] ? $item->_matchingData['ProductVariants']->sku : $item->_matchingData['Products']->sku;
                $supplier_name = $item->_matchingData['Suppliers']->name;
                $fulfilled_quantity = $item->fulfilled_quantity;

                $response[] = [
                    'id' => $itemId,
                    'product_name' => $product_name,
                    'product_id' => $item['product_id'],
                    'product_variant' => $product_variant,
                    'product_variant_id' => $item['product_variant_id'],
                    'product_attribute' => $product_attribute,
                    'product_attribute_id' => $item['product_attribute_id'],
                    'sku' => $sku,
                    'fulfilled_quantity' => $fulfilled_quantity,
                ];
            }

            // Return the response as JSON
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => true, 'data' => $response]));
        } else {
            // If no stock_request_id is passed, return an error response
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => false, 'message' => 'Invalid request']));
        }
    }

    
}
