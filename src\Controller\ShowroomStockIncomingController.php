<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ShowroomStockIncomingController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Roles;
    protected $Suppliers;
    protected $Showrooms;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $StockMovements;
    protected $StockMovementItems;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Roles = $this->fetchTable('Roles');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->StockMovementItems = $this->fetchTable('StockMovementItems');
    }
    
    public function index()
    {
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);

            $stockMovementsQuery = $this->StockMovements->find()
                ->select([
                    'StockMovements.id',
                    'StockMovements.movement_type',
                    'StockMovements.movement_date',
                    'StockMovements.created',
                    'sender' => $this->StockMovements->query()->newExpr()->add([
                        "CASE 
                            WHEN StockRequests.to_showroomID IS NULL THEN Warehouses.name 
                            ELSE DestinationShowrooms.name 
                        END"
                    ]),
                    'receiver' => 'ReceiverShowrooms.name'
                ])
                ->join([
                    'StockRequests' => [
                        'table' => 'stock_requests',
                        'type' => 'INNER',
                        'conditions' => 'StockMovements.referenceID = StockRequests.id',
                    ],
                    'DestinationShowrooms' => [
                        'table' => 'showrooms',
                        'type' => 'LEFT',
                        'conditions' => 'StockRequests.to_showroomID = DestinationShowrooms.id',
                    ],
                    'Warehouses' => [
                        'table' => 'warehouses',
                        'type' => 'LEFT',
                        'conditions' => 'StockRequests.warehouse_id = Warehouses.id',
                    ],
                    'ReceiverShowrooms' => [
                        'table' => 'showrooms',
                        'type' => 'LEFT',
                        'conditions' => 'StockRequests.showroom_id = ReceiverShowrooms.id',
                    ],
                ]);

            // Handle Showroom Manager and Supervisor roles
            if (strtolower($role->name) === 'showroom manager') {
                // Fetch manager's assigned showroom
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $stockMovementsQuery->where([
                        'StockMovements.movement_type' => 'Incoming',
                        'StockMovements.showroom_id' => $managerShowroom->id
                    ]);
                } else {
                    // If no showroom is assigned, return an empty result
                    $stock_movements = [];
                    return $stock_movements;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {
                // Fetch all showrooms assigned to this supervisor
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->toArray(); // Using toArray() instead of extract()

                if (!empty($supervisorShowrooms)) {
                    $supervisorShowroomIds = array_map(function($showroom) {
                        return $showroom['id']; // Extract the 'id' from the result
                    }, $supervisorShowrooms);

                    $stockMovementsQuery->where([
                        'StockMovements.movement_type' => 'Incoming',
                        'StockMovements.showroom_id IN' => $supervisorShowroomIds
                    ]);
                } else {
                    // If no showrooms are assigned, return an empty result
                    $stock_movements = [];
                    return $stock_movements;
                }
            } else {
                // General condition for other roles
                $stockMovementsQuery->where([
                    'StockMovements.movement_type' => 'Incoming',
                    'StockMovements.showroom_id IS NOT' => null
                ]);
            }

            $stockMovementsQuery->order(['StockMovements.id' => 'DESC']);
            $stock_movements = $stockMovementsQuery->toArray();

            // If no records found, return an empty array
            if (empty($stock_movements)) {
                $stock_movements = [];
            }
        }


        $this->set(compact('stock_movements'));
    }

    public function add()
    {

        if ($this->request->is('post')) {

            $this->loadComponent('Stock'); // Load the Stock component

            $data = $this->request->getData();

            $stockRequestId = $data['stock_request_id'];
            $movementDate = $data['movement_date'];

            // Fetch stock request details
            $stockRequest = $this->StockRequests->find()
                ->where(['StockRequests.id' => $stockRequestId])
                ->contain([
                    'StockRequestItems' => function ($q) {
                        return $q->where(['StockRequestItems.status' => 'Approved']);
                    }
                ])
                ->first();

            if (!$stockRequest) {
                $this->Flash->error(__('The stock request not found.'));
            }

            // Prepare parameters for the Stock component
            $showroom_id = $stockRequest->showroom_id;
            $movement_date = $movementDate;
            $reference_type = 'stock_request';
            $referenceID = $stockRequestId;

            // Prepare stock items
            $stock_items = [];
            foreach ($stockRequest->stock_request_items as $item) {
                $stock_items[] = [
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_attribute_id' => $item->product_attribute_id,
                    'quantity' => $item->fulfilled_quantity
                ];
            }

            // Call the Stock component's method
            $result = $this->Stock->addShowroomInStock($showroom_id, $movement_date, $reference_type, $referenceID, $stock_items, $image = null, $driver_id = null);

            // Return the result as JSON
            if ($result) {

                // Step 1: Update request_status to 'Completed' in StockRequests table
                $stockRequest = $this->StockRequests->get($referenceID);

                if ($stockRequest) {

                    $stockRequest->request_status = 'Completed';
                    $this->StockRequests->save($stockRequest);
                }

                $this->Flash->success(__('The stocks have been saved.'));

                return $this->redirect(['action' => 'index']);
            
            } else {

                $this->Flash->error(__('Failed to process stock request. Please, try again.'));
            
            }
        }

        $requested_user = $this->Authentication->getIdentity();

        if($requested_user)
        {
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) == 'showroom supervisor') {
                
                $to_showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.showroom_supervisor' => $requested_user->id,'Showrooms.status' => 'A'])
                    ->toArray();

            }else
            {
                $to_showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();
            }
        }

        $this->set(compact('to_showrooms', 'requested_user', 'role')); 

    }

    // public function getByToShowroom($toShowroomId)
    // {
    //     $this->request->allowMethod(['get', 'ajax']); // Restrict to GET or AJAX calls

    //     // Fetch stock requests where requestor_type is Warehouse
    //     $warehouseRequests = $this->StockRequests->find()
    //         ->select([
    //             'id',
    //             'warehouse_name' => 'Warehouses.name'
    //         ])
    //         ->contain(['Warehouses'])
    //         ->where([
    //             'StockRequests.showroom_id' => $toShowroomId,
    //             'StockRequests.warehouse_id IS NOT' => null,
    //             'StockRequests.requestor_type' => 'Showroom',
    //             'StockRequests.status' => 'A',
    //             'StockRequests.request_status' => 'Approved'
    //         ])
    //         ->order(['StockRequests.request_date' => 'DESC'])
    //         ->toArray();

    //     // Fetch stock requests where requestor_type is Showroom
    //     $showroomRequests = $this->StockRequests->find()
    //         ->select([
    //             'id',
    //             'showroom_name' => 'ToShowroom.name',
    //         ])
    //         ->contain(['Showrooms'])
    //         ->where([
    //             'StockRequests.showroom_id' => $toShowroomId,
    //             'StockRequests.to_showroomID IS NOT' => null,
    //             'StockRequests.requestor_type' => 'Showroom',
    //             'StockRequests.status' => 'A',
    //             'StockRequests.request_status' => 'Approved'
    //         ])
    //         ->leftJoin(
    //             ['ToShowroom' => 'showrooms'],
    //             ['ToShowroom.id = StockRequests.to_showroomID']
    //         )
    //         ->order(['StockRequests.request_date' => 'DESC'])
    //         ->toArray();

    //     // Return the results in JSON format
    //     return $this->response->withType('application/json')
    //         ->withStringBody(json_encode([
    //             'success' => true,
    //             'warehouse_requests' => $warehouseRequests,
    //             'showroom_requests' => $showroomRequests
    //         ]));
    // }

    public function getByToShowroom($toShowroomId)
    {
        $this->request->allowMethod(['get', 'ajax']); // Restrict to GET or AJAX calls

        // Fetch stock requests where requestor_type is Warehouse
        $warehouseRequests = $this->StockRequests->find()
            ->select([
                'id',
                'warehouse_name' => 'Warehouses.name'
            ])
            ->contain(['Warehouses'])
            ->where([
                'StockRequests.showroom_id' => $toShowroomId,
                'StockRequests.warehouse_id IS NOT' => null,
                'StockRequests.requestor_type' => 'Showroom',
                'StockRequests.status' => 'A',
                'StockRequests.request_status' => 'Approved'
            ])
            ->order(['StockRequests.request_date' => 'DESC'])
            ->toArray();

        $filteredWarehouseRequests = [];
        foreach ($warehouseRequests as $request) {
            $movementExists = $this->StockMovements->exists([
                'referenceID' => $request->id,
                'movement_type' => 'Outgoing',
                'verify_status' => 'Approved'
            ]);

            if ($movementExists) {
                $filteredWarehouseRequests[] = $request;
            }
        }

        // Fetch stock requests where requestor_type is Showroom
        $showroomRequests = $this->StockRequests->find()
            ->select([
                'id',
                'showroom_name' => 'ToShowroom.name',
            ])
            ->contain(['Showrooms'])
            ->where([
                'StockRequests.showroom_id' => $toShowroomId,
                'StockRequests.to_showroomID IS NOT' => null,
                'StockRequests.requestor_type' => 'Showroom',
                'StockRequests.status' => 'A',
                'StockRequests.request_status' => 'Approved'
            ])
            ->leftJoin(
                ['ToShowroom' => 'showrooms'],
                ['ToShowroom.id = StockRequests.to_showroomID']
            )
            ->order(['StockRequests.request_date' => 'DESC'])
            ->toArray();

        $filteredShowroomRequests = [];
        foreach ($showroomRequests as $request) {
            $movementExists = $this->StockMovements->exists([
                'referenceID' => $request->id,
                'movement_type' => 'Outgoing',
                'verify_status' => 'Approved'
            ]);

            if ($movementExists) {
                $filteredShowroomRequests[] = $request;
            }
        }

        // Return the filtered results in JSON format
        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'success' => true,
                'warehouse_requests' => $filteredWarehouseRequests,
                'showroom_requests' => $filteredShowroomRequests
            ]));
    }


    public function getStockRequestItems()
    {
        $this->request->allowMethod(['get']);

        $stockRequestId = $this->request->getQuery('stock_request_id');

        if ($stockRequestId) {
            $stockRequestItems = $this->StockRequests->StockRequestItems->find()
                ->select([
                    'StockRequestItems.id',
                    'StockRequestItems.fulfilled_quantity',
                    'StockRequestItems.product_id',
                    'StockRequestItems.product_variant_id',
                    'StockRequestItems.product_attribute_id',
                    'StockRequestItems.stock_request_id',
                    'StockRequestItems.supervisor_approved_quantity',
                    'Products.name',
                    'Products.purchase_price', 
                    'Products.sku', 
                    'Products.supplier_id',  // Assuming Products has a supplier_id field
                    'ProductVariants.id', 
                    'ProductVariants.variant_name', 
                    'ProductVariants.purchase_price', 
                    'ProductVariants.sku', 
                    'Suppliers.name'  // Accessing supplier's name via Products
                ])
                ->leftJoinWith('Products')
                ->leftJoinWith('Products.Suppliers')  // Access Suppliers through Products
                ->leftJoinWith('ProductVariants')
                ->where([
                    'StockRequestItems.stock_request_id' => $stockRequestId,
                    'StockRequestItems.status' => 'Approved'
                ])
                ->toArray();

            // echo "<pre>";print_r($stockRequestItems);die;    

            // Prepare the data for the response
            $response = [];
            foreach ($stockRequestItems as $item) {

                $item->attributes = [];

                if ($item->product_attribute_id) {
                    // Fetch attributes related to the product
                    $attributes = $this->ProductAttributes->find()
                        ->where(['ProductAttributes.id' => $item->product_attribute_id])
                        ->contain([
                            'Attributes' => [
                                'fields' => ['Attributes.name']
                            ],
                            'AttributeValues' => [
                                'fields' => ['AttributeValues.value']
                            ]
                        ])
                        ->first();

                    if ($attributes) {
                        // Add attribute details to the item if found
                        $item->attributes = [
                            'attribute_name' => $attributes->attribute->name ?? '',
                            'attribute_value' => $attributes->attribute_value->value ?? ''
                        ];
                    }
                }

                $itemId = $item->id;
                $product_name = $item->_matchingData['Products']->name;
                $product_variant = $item->_matchingData['ProductVariants']->id ? $item->_matchingData['ProductVariants']->variant_name : 'N/A';
                $product_attribute = $item->attributes ? $item->attributes['attribute_name'] . ':' . $item->attributes['attribute_value'] : 'N/A';
                if ($item['product_variant_id'])
                {
                    $sku = $item->_matchingData['ProductVariants']->sku;
                }
                else
                {
                    $sku = $item->_matchingData['Products']->sku;
                }
                $supplier_name = $item->_matchingData['Suppliers']->name;
                $fulfilled_quantity = $item->fulfilled_quantity;

                $response[] = [
                    'id' => $itemId,
                    'product_name' => $product_name,
                    'product_variant' => $product_variant,
                    'product_attribute' => $product_attribute,
                    'sku' => $sku,
                    'fulfilled_quantity' => $fulfilled_quantity,
                ];
            }

            // Return the response as JSON
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'data' => $response]));
        } else {
            // If no stock_request_id is passed, return an error response
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => false, 'message' => 'Invalid request']));
        }
    }

    public function view($id = null)
    {
        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.created',
                'sender' => $this->StockMovements->query()->newExpr()->add([
                    "CASE 
                        WHEN StockRequests.to_showroomID IS NULL THEN Warehouses.name 
                        ELSE DestinationShowrooms.name 
                    END"
                ]),
                'receiver' => 'ReceiverShowrooms.name'
            ])
            ->join([
                'StockRequests' => [
                    'table' => 'stock_requests',
                    'type' => 'INNER',
                    'conditions' => 'StockMovements.referenceID = StockRequests.id',
                ],
                'DestinationShowrooms' => [
                    'table' => 'showrooms',
                    'type' => 'LEFT',
                    'conditions' => 'StockRequests.to_showroomID = DestinationShowrooms.id',
                ],
                'Warehouses' => [
                    'table' => 'warehouses',
                    'type' => 'LEFT',
                    'conditions' => 'StockRequests.warehouse_id = Warehouses.id',
                ],
                'ReceiverShowrooms' => [
                    'table' => 'showrooms',
                    'type' => 'LEFT',
                    'conditions' => 'StockRequests.showroom_id = ReceiverShowrooms.id',
                ],
            ])
            ->where(['StockMovements.id' => $id])
            ->first();

        // Fetch StockRequestItems for the StockRequest with id = 7
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockMovementItems.stock_movement_id' => $id])
            ->toArray();

        foreach ($StockMovementItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        // echo "<pre>";print_r($StockMovementItems);die; 

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('StockMovement', 'StockMovementItems', 'currencySymbol', 'decimalSeparator', 'thousandSeparator')); 
    }

    public function getVariants($productId)
    {
        $this->request->allowMethod(['get']);

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku']) // Fetch ID, name, and SKU of the variants
            ->where(['ProductVariants.product_id' => $productId]) // Filter by product_id
            ->andWhere(['ProductVariants.status' => 'A'])
            ->toArray();

        // Fetch product attributes and their values
        $productAttributes = $this->ProductAttributes->find()
            ->contain(['Attributes', 'AttributeValues'])
            ->where(['ProductAttributes.product_id' => $productId])
            ->andWhere(['ProductAttributes.status' => 'A'])
            ->toArray();

        // echo "<pre>";print_r($variants);die;

        // Prepare the response
        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $variantData = [];
        foreach ($variants as $variant) {
            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
            ];
        }

        foreach ($productAttributes as $productAttribute) {
            $response['attributes'][] = [
                'attribute_id' => $productAttribute->id,
                'attribute_name' => $productAttribute->attribute->name,
                'attribute_value' => $productAttribute->attribute_value->value,
            ];
        }

        $this->set([
                    'response' => $response,
                    '_serialize' => ['response'],
                ]);

        // Return JSON response
        return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
    }

    
}
