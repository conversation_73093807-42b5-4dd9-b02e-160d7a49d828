<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<!-- Lazysizes library for lazyloading images -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js" async></script>
<div class="ax-wrapper">
    <!-- Filters Section -->
    <div class="ax-filters">
        <input type="text" id="nameFilter" placeholder="Filter by name" onkeyup="filterShowrooms()">
        <input type="text" id="cityFilter" placeholder="Filter by city" onkeyup="filterShowrooms()">
    </div>

    <!-- Masonry Grid Section -->
    <div id="showroomList" class="ax-masonry-grid">
        <!-- Dynamically populated content will go here -->
    </div>

    <!-- Loading indicator (hidden by default) -->
    <div id="loading-indicator" style="display: none; text-align: center; padding: 20px; width: 100%;">
        <div class="spinner" style="margin: 0 auto; width: 40px; height: 40px; border: 4px solid rgba(0, 0, 0, 0.1); border-radius: 50%; border-top-color: #0d839b; animation: spin 1s ease-in-out infinite;"></div>
    </div>

    <!-- End of results message (hidden by default) -->
    <div id="end-of-results" style="display: none; text-align: center; padding: 20px; width: 100%; color: #666; font-size: 16px; ">
        <?= __('No more showrooms available') ?>
    </div>

    <!-- No results message (hidden by default) -->
    <div id="no-results" style="display: none; text-align: center; padding: 30px; width: 100%; color: #666; font-size: 16px; margin-top: 35px;">
        <i class="fas fa-search" style="color: #0d839b; margin-right: 8px; font-size: 24px;"></i>
        <p style="margin-top: 10px;"><?= __('No showrooms match your search criteria') ?></p>
        <p style="margin-top: 5px; font-size: 14px;"><?= __('Try adjusting your filters') ?></p>
    </div>
</div>

<style>
    #end-of-results{
        display: none;
        color: #6c757d;
        font-style: italic;
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
        margin-top: 35px;
    }
    /* Lazyload styling */
    .lazyload,
    .lazyloading {
        opacity: 0;
    }
    .lazyloaded {
        opacity: 1;
        transition: opacity 300ms;
    }
    /* Blur-up effect for lazyloaded images */
    .blur-up {
        -webkit-filter: blur(5px);
        filter: blur(5px);
        transition: filter 400ms, -webkit-filter 400ms;
    }
    .blur-up.lazyloaded {
        -webkit-filter: blur(0);
        filter: blur(0);
    }
    /* Placeholder for images while loading */
    .img-placeholder {
        background-color: #f3f3f3;
        position: relative;
    }

    /* Wrapper styling */
    .ax-wrapper {
        margin: 0 auto;
        width: 100%;
        max-width: 64%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Filters styling */
    .ax-filters {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        justify-content: center;
    }

    .ax-filters input, .ax-filters select {
        padding: 10px;
        /* border: 1px solid #ccc; */
        border: 1px solid orange;
        border-radius: 5px;
        width: 200px;
    }
    .ax-filters input::placeholder {
        color: #dbdad9;
        font-size: 14px;
    }

    /* Masonry grid layout */
    .ax-masonry-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        width: 100%;
    }

    /* Individual grid items */
    .ax-grid-item {
        display: flex;
        flex-direction: column;
        background-color: white;
        border: 1px solid #eee;
        border-radius: 10px;
        padding: 10px;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }

    .ax-grid-item:hover {
        transform: scale(1.02);
    }
    .ax-grid-item img{
        border: 1px solid #bbb;
        height: 239px;
    }
    /* Placeholder image styling */
    .ax-placeholder-img {
        width: 100%;
        /* height: 150px; */
        height: 239px;
        background-color: #f3f3f3;
        border-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-transform: uppercase;
        font-size: 14px;
        font-weight: bold;
        color: #aaa;
        overflow: hidden;
    }

    /* Grid item details */
    .ax-showroom-details {
        padding: 10px 0;
    }

    .ax-showroom-details h3 {
        margin: 0 0 10px;
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }

    .ax-showroom-details p {
        margin: 5px 0;
        font-size: 14px;
        color: #666;
        line-height: 1.4;
    }

    .ax-showroom-details i {
        margin-right: 5px;
        color: #f77f00;
    }

    /* "Load More" button styling */
    .ax-load-more {
        padding: 10px 20px;
        background-color: #0d839b;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 10px;
    }

    /* Spinner animation for loading indicator */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>


<script>
    let showroomIndex = 0;
    const recordsPerPage = 24;
    let filteredShowrooms = [];
    let isLoading = false;
    let allLoaded = false;

    // Sample Showroom Data
    const allShowrooms = <?= json_encode($showrooms) ?>;

    // Function to load showroom data into the grid
    const loadShowrooms = (reset = false) => {
        if (isLoading || (allLoaded && !reset)) return;

        isLoading = true;
        document.getElementById('loading-indicator').style.display = 'block';

        const container = document.getElementById('showroomList');

        if (reset) {
            showroomIndex = 0;
            container.innerHTML = ''; // Clear previous results
            allLoaded = false;

            // Hide both messages when resetting
            document.getElementById('end-of-results').style.display = 'none';
            document.getElementById('no-results').style.display = 'none';

            // Show no results message if there are no showrooms to display after filtering
            if (filteredShowrooms.length === 0) {
                document.getElementById('no-results').style.display = 'block';
                document.getElementById('loading-indicator').style.display = 'none';
                isLoading = false;
                return; // Exit early if no results
            }
        }

        const showroomsToDisplay = filteredShowrooms.slice(showroomIndex, showroomIndex + recordsPerPage);
        showroomIndex += recordsPerPage;

        showroomsToDisplay.forEach(({ name, address, contact_country_code, contact_number, email, showroom_timing, image, city }) => {
            const div = document.createElement('div');
            div.className = "ax-grid-item";

            // Handle image availability with lazyload
            const imageContent = image
                ? `<img data-src="${image}" alt="${name}" class="ax-showroom-img lazyload blur-up" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">`
                : `<img data-src="<?= $this->Url->webroot('img/store2.png') ?>" alt="No Image" class="ax-showroom-img lazyload blur-up" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">`;
            // Using a tiny transparent gif as placeholder and data-src for the actual image

            // Google Maps URL for redirection
            const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;

            // Format contact number properly
            const formattedContact = contact_number
                ? `${contact_country_code ? contact_country_code + " " : ""}${contact_number}`
                : "N/A";

            div.innerHTML = `
            ${imageContent}
            <div class="ax-showroom-details">
                ${name ? `<h3>${name}</h3>` : ''}

                ${address ? `<p><i class="fas fa-map-marker-alt"></i>
                    <a href="${googleMapsUrl}" target="_blank" style="text-decoration: none; color: #0d839b;">
                        ${address}
                    </a>
                </p>` : ''}

                ${formattedContact && formattedContact !== 'N/A' ? `<p><i class="fas fa-phone"></i> ${formattedContact}</p>` : ''}

                ${email ? `<p><i class="fas fa-envelope"></i> ${email}</p>` : ''}

                ${showroom_timing ? `<p><i class="fas fa-clock"></i> ${showroom_timing}</p>` : ''}

                ${city && city.city_name ? `<p><i class="fas fa-city"></i> ${city.city_name}</p>` : ''}
            </div>
        `;
            container.appendChild(div);
        });

        // Check if all records have been loaded
        if (showroomIndex >= filteredShowrooms.length) {
            allLoaded = true;

            // Show the end of results message if there were any showrooms displayed
            if (filteredShowrooms.length > 0) {
                document.getElementById('end-of-results').style.display = 'block';
            }
        } else {
            // Hide the end of results message if there are more showrooms to load
            document.getElementById('end-of-results').style.display = 'none';
        }

        // Update lazysizes to detect newly added images
        if (window.lazySizes) {
            lazySizes.autoInit = false;
            lazySizes.init();
        }

        // Hide loading indicator and reset loading state
        document.getElementById('loading-indicator').style.display = 'none';
        isLoading = false;
    };

    // Setup infinite scroll
    const setupInfiniteScroll = () => {
        // Function to check if we need to load more content
        const checkScroll = () => {
            if (isLoading || allLoaded) return;

            // Calculate distance from bottom of the page
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = Math.max(
                document.body.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.clientHeight,
                document.documentElement.scrollHeight,
                document.documentElement.offsetHeight
            );

            // If we're near the bottom (within 200px), load more content
            if (scrollTop + windowHeight > documentHeight - 200) {
                loadShowrooms();
            }
        };

        // Add scroll event listener with throttling
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (!scrollTimeout) {
                scrollTimeout = setTimeout(() => {
                    checkScroll();
                    scrollTimeout = null;
                }, 100); // Throttle to every 100ms
            }
        });
    };

    // Filtering function
    const filterShowrooms = () => {
        const nameFilter = document.getElementById('nameFilter').value.toLowerCase();
        const cityFilter = document.getElementById('cityFilter').value.toLowerCase();

        filteredShowrooms = allShowrooms.filter(({ name, city }) => (
            (!nameFilter || name.toLowerCase().includes(nameFilter)) &&
            (!cityFilter || (city && city.city_name.toLowerCase().includes(cityFilter)))
        ));

        // Hide messages when applying new filters
        document.getElementById('end-of-results').style.display = 'none';
        document.getElementById('no-results').style.display = 'none';

        // Reset loading state
        allLoaded = false;
        isLoading = false;

        // Load the filtered showrooms
        loadShowrooms(true);
    };

    // Initialize with full dataset
    filteredShowrooms = [...allShowrooms];
    loadShowrooms();

    // Setup infinite scroll after initial load
    setupInfiniteScroll();
</script>
