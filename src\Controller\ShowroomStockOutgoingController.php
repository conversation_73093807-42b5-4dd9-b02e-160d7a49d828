<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ShowroomStockOutgoingController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Showrooms;
    protected $Drivers;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $StockMovements;
    protected $StockMovementItems;
    protected $Roles;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Drivers = $this->fetchTable('Drivers');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->StockMovementItems = $this->fetchTable('StockMovementItems');
        $this->Roles = $this->fetchTable('Roles');
    }
    
    public function index()
    {
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);

            $stockMovementsQuery = $this->StockMovements->find()
                ->select([
                    'StockMovements.id',
                    'StockMovements.movement_type',
                    'StockMovements.movement_date',
                    'StockMovements.created',
                    'Showroom.name', // From Showroom (source)
                    'ToShowroom.name', // To Showroom (destination)
                    'total_items' => $this->StockMovements->StockMovementItems->find()
                        ->func()
                        ->sum('StockMovementItems.quantity'),
                    'total_value' => $this->StockMovements->StockMovementItems->find()
                        ->func()
                        ->sum(
                            '(CASE 
                                WHEN StockMovementItems.product_variant_id IS NULL 
                                    THEN Products.purchase_price * StockMovementItems.quantity 
                                ELSE ProductVariants.purchase_price * StockMovementItems.quantity 
                             END)'
                        )
                ])
                ->leftJoinWith('StockMovementItems')
                ->leftJoinWith('StockMovementItems.Products')
                ->leftJoinWith('StockMovementItems.ProductVariants')
                ->leftJoin(
                    ['StockRequests' => 'stock_requests'],
                    ['StockRequests.id = StockMovements.referenceId']
                )
                ->leftJoin(
                    ['Showroom' => 'showrooms'], // From Showroom
                    ['Showroom.id = StockMovements.showroom_id']
                )
                ->leftJoin(
                    ['ToShowroom' => 'showrooms'], // To Showroom
                    ['ToShowroom.id = StockRequests.showroom_id']
                );

            // Apply role-based filtering
            if (strtolower($role->name) === 'showroom manager') {
                // Restrict to the manager's showroom
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $stockMovementsQuery->where([
                        'StockMovements.movement_type' => 'Outgoing',
                        'StockMovements.showroom_id' => $managerShowroom->id
                    ]);
                } else {
                    // If no showroom is assigned, return an empty result
                    $stock_movements = null;
                    return $stock_movements;
                }
            } elseif (strtolower($role->name) === 'showroom supervisor') {
                // Restrict to the supervisor's showrooms (multiple showrooms)
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->toArray();

                if (!empty($supervisorShowrooms)) {
                    $showroomIds = array_map(function ($showroom) {
                        return $showroom->id;
                    }, $supervisorShowrooms);

                    $stockMovementsQuery->where([
                        'StockMovements.movement_type' => 'Outgoing',
                        'StockMovements.showroom_id IN' => $showroomIds
                    ]);
                } else {
                    // If no showrooms are assigned, return an empty result
                    $stock_movements = null;
                    return $stock_movements;
                }
            } else {
                // For non-showroom manager or supervisor roles, apply general conditions
                $stockMovementsQuery->where([
                    'StockMovements.movement_type' => 'Outgoing',
                    'StockMovements.showroom_id IS NOT' => null,
                ]);
            }

            // Common ordering
            $stockMovementsQuery->group([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.created',
                'Showroom.name',
                'ToShowroom.name'
            ]);

            $stockMovementsQuery->order(['StockMovements.id' => 'DESC']);

            // Execute and format results
            $stock_movements = $stockMovementsQuery->toArray();

            // Check if any stock movements are returned
            if (empty($stock_movements)) { // Check for empty array
                $stock_movements = null; // Explicitly set to null if no records are found
            }
        }


        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';   
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';         

        $this->set(compact('stock_movements', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }

    public function add()
    {

        if ($this->request->is('post')) {

            $this->loadComponent('Stock'); // Load the Stock component

            $data = $this->request->getData();

            $stockRequestId = $data['stock_request_id'];
            $driverId = $data['driver_id'];

            // Fetch stock request details
            $stockRequest = $this->StockRequests->find()
                ->where(['StockRequests.id' => $stockRequestId])
                ->contain([
                    'StockRequestItems' => function ($q) {
                        return $q->where(['StockRequestItems.status' => 'Approved']);
                    }
                ])
                ->first();

            if (!$stockRequest) {
                $this->Flash->error(__('The stock request not found.'));
            }

            // Prepare parameters for the Stock component
            $showroom_id = $stockRequest->to_showroomID;
            $movement_date = date('Y-m-d');
            $reference_type = 'stock_request';
            $referenceID = $stockRequestId;

            // Prepare stock items
            $stock_items = [];
            foreach ($stockRequest->stock_request_items as $item) {
                $stock_items[] = [
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_attribute_id' => $item->product_attribute_id,
                    'quantity' => $item->fulfilled_quantity ?? $item->fulfilled_quantity
                ];
            }

            // Call the Stock component's method
            $result = $this->Stock->addShowroomOutStock($showroom_id, $movement_date, $reference_type, $referenceID, $stock_items, $image = null, $driverId);

            // Return the result as JSON
            if ($result) {

                // ✅ Update verify_status to 'Approved' for the related stock movement
                $this->StockMovements->updateAll(
                    ['verify_status' => 'Approved'],
                    ['referenceID' => $stockRequestId, 'reference_type' => 'stock_request']
                );

                // Send dispatch email
                $this->sendStockDispatchEmail($stockRequest);

                $this->Flash->success(__('The stocks have been saved.'));

                return $this->redirect(['action' => 'index']);
            
            } else {

                $this->Flash->error(__('Failed to process stock request. Please, try again.'));
            
            }
        }

        $showrooms = $this->Showrooms->find()
            ->where(['Showrooms.status IN' => ['A', 'I']])
            ->order(['Showrooms.name' => 'ASC'])->toArray();

        // Query the Drivers table and join with Users to get driver names
        $query = $this->Drivers->find()
            ->select(['Drivers.id', 'Users.first_name', 'Users.last_name'])
            ->where(['Users.status' => 'A', 'Users.user_type' => 'Driver'])
            ->contain(['Users']);

        $driversList = $query->toArray();

        $drivers = [];
        foreach ($driversList as $driver) {
            $drivers[$driver->id] = $driver->user->first_name . ' ' . $driver->user->last_name;
        }

        $requested_user = $this->Authentication->getIdentity();

        // If the user is a showroom manager, fetch their showroom ID
        $managerShowroomId = null;
        if ($requested_user) {
            $role = $this->Roles->get($requested_user->role_id);

            if (strtolower($role->name) === 'showroom manager') {
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    $managerShowroomId = $managerShowroom->id;
                }
            }
        }

        $this->set(compact('showrooms', 'drivers', 'requested_user', 'role', 'managerShowroomId')); 

    }

    private function sendStockDispatchEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Dispatching Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);
        
        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Dispatch
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Dispatch
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Users']
            ]);

            $warehouseManagerEmail = $warehouse->user ? $warehouse->user->email : null;

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail; // Send to warehouse manager
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear Manager,";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for dispatched stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Dispatched',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'dispatch_date' => date('d-m-Y')
        ];

        $subject = "Stock Dispatch #{$stock_request->id} - Completed";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_dispatch_notification',
            $emailData
        );
    }

    public function getRequestIds()
    {
        $this->request->allowMethod(['ajax']); // Ensure the action is accessible only via AJAX

        $showroom_id = $this->request->getQuery('showroom_id');
        $to_showroomId = $this->request->getQuery('to_showroomId');

        if (!$showroom_id || !$to_showroomId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Both warehouse and showroom must be selected.'),
            ]));
        }

        // Query to fetch request IDs with specific conditions
        $requests = $this->StockRequests->find()
            ->select(['id'])
            ->where([
                'StockRequests.requestor_type' => 'Showroom',
                'StockRequests.request_status' => 'Approved',
                'StockRequests.showroom_id' => $to_showroomId,
                'StockRequests.to_showroomID' => $showroom_id,
            ])
            ->all();

        // Extract the request IDs into an array
        $requestIds = array_map(function($request) {
            return $request->id;
        }, $requests->toArray());

        // Query to check which of the request IDs exist in stock_movements.referenceID
        $existingReferences = $this->StockMovements->find()
            ->select(['referenceID'])
            ->where([
                'StockMovements.referenceID IN' => $requestIds
            ])
            ->all();

        // Extract reference IDs into an array
        $existingReferenceIds = array_map(function($movement) {
            return $movement->referenceID;
        }, $existingReferences->toArray());

        // Filter out the request IDs that are already in stock_movements.referenceID
        $filteredRequestIds = array_diff($requestIds, $existingReferenceIds);

        // Format the filtered request IDs as an array of objects with "id" keys
        $formattedRequestIds = [];
        foreach ($filteredRequestIds as $id) {
            $formattedRequestIds[] = ['id' => $id];
        }

        // Return the result in the correct format
        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => true,
            'data' => $formattedRequestIds, // Send the formatted request IDs
        ]));
    }

    public function getStockRequestItems()
    {
        $this->request->allowMethod(['get']);

        $stockRequestId = $this->request->getQuery('stock_request_id');

        if ($stockRequestId) {
            $stockRequestItems = $this->StockRequests->StockRequestItems->find()
                ->select([
                    'StockRequestItems.id',
                    'StockRequestItems.fulfilled_quantity',
                    'StockRequestItems.product_id',
                    'StockRequestItems.product_variant_id',
                    'StockRequestItems.product_attribute_id',
                    'StockRequestItems.stock_request_id',
                    'StockRequestItems.supervisor_approved_quantity',
                    'Products.name',
                    'Products.purchase_price', 
                    'Products.sku', 
                    'Products.supplier_id',  // Assuming Products has a supplier_id field
                    'ProductVariants.id', 
                    'ProductVariants.variant_name', 
                    'ProductVariants.purchase_price', 
                    'ProductVariants.sku', 
                    'Suppliers.name'  // Accessing supplier's name via Products
                ])
                ->leftJoinWith('Products')
                ->leftJoinWith('Products.Suppliers')  // Access Suppliers through Products
                ->leftJoinWith('ProductVariants')
                ->where([
                    'StockRequestItems.stock_request_id' => $stockRequestId,
                    'StockRequestItems.status' => 'Approved'
                ])
                ->toArray(); 

            // Prepare the data for the response
            $response = [];
            foreach ($stockRequestItems as $item) {

                $item->attributes = [];

                if ($item->product_attribute_id) {
                    // Fetch attributes related to the product
                    $attributes = $this->ProductAttributes->find()
                        ->where(['ProductAttributes.id' => $item->product_attribute_id])
                        ->contain([
                            'Attributes' => [
                                'fields' => ['Attributes.name']
                            ],
                            'AttributeValues' => [
                                'fields' => ['AttributeValues.value']
                            ]
                        ])
                        ->first();

                    if ($attributes) {
                        // Add attribute details to the item if found
                        $item->attributes = [
                            'attribute_name' => $attributes->attribute->name ?? '',
                            'attribute_value' => $attributes->attribute_value->value ?? ''
                        ];
                    }
                }

                $itemId = $item->id;
                $product_name = $item->_matchingData['Products']->name;
                $product_variant = $item->_matchingData['ProductVariants']->id ? $item->_matchingData['ProductVariants']->variant_name : 'N/A';
                $product_attribute = $item->attributes ? $item->attributes['attribute_name'] . ':' . $item->attributes['attribute_value'] : 'N/A';
                if ($item['product_variant_id'])
                {
                    $sku = $item->_matchingData['ProductVariants']->sku;
                }
                else
                {
                    $sku = $item->_matchingData['Products']->sku;
                }
                $supplier_name = $item->_matchingData['Suppliers']->name;
                $fulfilled_quantity = $item->fulfilled_quantity;

                $response[] = [
                    'id' => $itemId,
                    'product_name' => $product_name,
                    'product_variant' => $product_variant,
                    'product_attribute' => $product_attribute,
                    'sku' => $sku,
                    'fulfilled_quantity' => $fulfilled_quantity,
                ];
            }

            // Return the response as JSON
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'data' => $response]));
        } else {
            // If no stock_request_id is passed, return an error response
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => false, 'message' => 'Invalid request']));
        }
    }

    public function view($id = null)
    {
        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.created',
                'Showroom.name', // Fetch showroom name from StockMovements.showroom_id
                'ToShowroom.name', // Fetch to_showroom name from StockRequests.to_showroomID
                'DriverUser.first_name', // Fetch driver's first name from Users
                'DriverUser.last_name' // Fetch driver's last name from Users
            ])
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                ['StockRequests.id = StockMovements.referenceId']
            )
            ->leftJoin(
                ['Showroom' => 'showrooms'], // Join for StockMovements.showroom_id
                ['Showroom.id = StockMovements.showroom_id']
            )
            ->leftJoin(
                ['ToShowroom' => 'showrooms'], // Join for StockRequests.to_showroomID
                ['ToShowroom.id = StockRequests.showroom_id']
            )
            ->leftJoin(
                ['Drivers' => 'drivers'], // Join for Drivers table
                ['Drivers.id = StockMovements.driver_id']
            )
            ->leftJoin(
                ['DriverUser' => 'users'], // Join for Users table
                ['DriverUser.id = Drivers.user_id']
            )
            ->where(['StockMovements.id' => $id])
            ->first(); 

        // Fetch StockRequestItems for the StockRequest with id = 7
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockMovementItems.stock_movement_id' => $id])
            ->toArray();

        foreach ($StockMovementItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        // echo "<pre>";print_r($StockMovementItems);die; 

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('StockMovement', 'StockMovementItems', 'currencySymbol', 'decimalSeparator', 'thousandSeparator')); 
    }

    public function getVariants($productId)
    {
        $this->request->allowMethod(['get']);

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku']) // Fetch ID, name, and SKU of the variants
            ->where(['ProductVariants.product_id' => $productId]) // Filter by product_id
            ->andWhere(['ProductVariants.status' => 'A'])
            ->toArray();

        // Fetch product attributes and their values
        $productAttributes = $this->ProductAttributes->find()
            ->contain(['Attributes', 'AttributeValues'])
            ->where(['ProductAttributes.product_id' => $productId])
            ->andWhere(['ProductAttributes.status' => 'A'])
            ->toArray();

        // echo "<pre>";print_r($variants);die;

        // Prepare the response
        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $variantData = [];
        foreach ($variants as $variant) {
            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
            ];
        }

        foreach ($productAttributes as $productAttribute) {
            $response['attributes'][] = [
                'attribute_id' => $productAttribute->id,
                'attribute_name' => $productAttribute->attribute->name,
                'attribute_value' => $productAttribute->attribute_value->value,
            ];
        }

        $this->set([
                    'response' => $response,
                    '_serialize' => ['response'],
                ]);

        // Return JSON response
        return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
    }

    
}
