<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * ProductPaymentTerms Controller
 *
 * @property \App\Model\Table\ProductPaymentTermsTable $ProductPaymentTerms
 */
class ProductPaymentTermsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->ProductPaymentTerms->find()
            ->contain(['ProductPaymentSettings']);
        $productPaymentTerms = $this->paginate($query);

        $this->set(compact('productPaymentTerms'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Payment Term id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $productPaymentTerm = $this->ProductPaymentTerms->get($id, contain: ['ProductPaymentSettings']);
        $this->set(compact('productPaymentTerm'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $productPaymentTerm = $this->ProductPaymentTerms->newEmptyEntity();
        if ($this->request->is('post')) {
            $productPaymentTerm = $this->ProductPaymentTerms->patchEntity($productPaymentTerm, $this->request->getData());
            if ($this->ProductPaymentTerms->save($productPaymentTerm)) {
                $this->Flash->success(__('The product payment term has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product payment term could not be saved. Please, try again.'));
        }
        $productPaymentSettings = $this->ProductPaymentTerms->ProductPaymentSettings->find('list', limit: 200)->all();
        $this->set(compact('productPaymentTerm', 'productPaymentSettings'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Product Payment Term id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $productPaymentTerm = $this->ProductPaymentTerms->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $productPaymentTerm = $this->ProductPaymentTerms->patchEntity($productPaymentTerm, $this->request->getData());
            if ($this->ProductPaymentTerms->save($productPaymentTerm)) {
                $this->Flash->success(__('The product payment term has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product payment term could not be saved. Please, try again.'));
        }
        $productPaymentSettings = $this->ProductPaymentTerms->ProductPaymentSettings->find('list', limit: 200)->all();
        $this->set(compact('productPaymentTerm', 'productPaymentSettings'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Product Payment Term id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $productPaymentTerm = $this->ProductPaymentTerms->get($id);
        if ($this->ProductPaymentTerms->delete($productPaymentTerm)) {
            $this->Flash->success(__('The product payment term has been deleted.'));
        } else {
            $this->Flash->error(__('The product payment term could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
