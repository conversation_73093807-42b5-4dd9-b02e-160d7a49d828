<?php

declare(strict_types=1);

namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Controller\ComponentRegistry;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;

/**
 * WebsiteFunction component
 */
class WebsiteFunctionComponent extends Component
{

    protected $customer, $supportTicket, $OrderCancellationCategories, $OrderReturnCategories, $OrderItems, $Cities, $CustomerAddresses, $Customers, $Orders, $Categories, $Products, $ProductImages, $Reviews, $Wishlists, $Users, $ContentPages, $PartnerPaymentTerms;
    protected $request;
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $controller = $this->getController();
        if ($controller) {
            $this->request = $controller->getRequest();
        }

        $this->customer = TableRegistry::getTableLocator()->get('Customers');
        $this->supportTicket = TableRegistry::getTableLocator()->get('SupportTickets');
        $this->OrderCancellationCategories = TableRegistry::getTableLocator()->get('OrderCancellationCategories');
        $this->Cities = TableRegistry::getTableLocator()->get('Cities');
        $this->CustomerAddresses = TableRegistry::getTableLocator()->get('CustomerAddresses');
        $this->Customers = TableRegistry::getTableLocator()->get('Customers');
        $this->Orders = TableRegistry::getTableLocator()->get('Orders');
        $this->Categories = TableRegistry::getTableLocator()->get('Categories');
        $this->Products = TableRegistry::getTableLocator()->get('Products');
        $this->ProductImages = TableRegistry::getTableLocator()->get('ProductImages');
        $this->Reviews = TableRegistry::getTableLocator()->get('Reviews');
        $this->Wishlists = TableRegistry::getTableLocator()->get('Wishlists');
        $this->Users = TableRegistry::getTableLocator()->get('Users');
        $this->ContentPages = TableRegistry::getTableLocator()->get('ContentPages');
        $this->OrderItems = TableRegistry::getTableLocator()->get('OrderItems');
        $this->OrderReturnCategories = TableRegistry::getTableLocator()->get('OrderReturnCategories');

        $this->PartnerPaymentTerms = TableRegistry::getTableLocator()->get('PartnerPaymentTerms');
    }

    /**
     * Default configuration.
     *
     * @var array<string, mixed>
     */
    protected array $_defaultConfig = [];

    public function checkPaybyCreditAmount($data)
    {
        $c_price = $this->Products->getProductPrice($data['product_id']); // like 64500
        $c_payment_term = $this->PartnerPaymentTerms->getTermById($data['credit_payment_terms_id']);

        $P = $c_price;
        $annualRate = $c_payment_term['term_commission_percent'];
        $months = $c_payment_term['term_month'];

        $r = ($annualRate / 12) / 100; // Convert annual interest to monthly
        $n = $months;
        $emi = ($P * $r * pow(1 + $r, $n)) / (pow(1 + $r, $n) - 1);

        $emi = round($emi, 2);

        $totalCost = round($emi * $n, 2); // Total cost over the loan period

        $interest = $totalCost - $P; // Total interest paid

        if ($emi == $data['emi_interest_amount'] && $totalCost == $data['total_emi_amount']) {
            return [
                'status' => true,
                'emi' => is_finite($emi) ? number_format($emi, 2, '.', '') : "0.00",
                'total_cost' => is_finite($totalCost) ? number_format($totalCost, 2, '.', '') : "0.00",
                'interest' => is_finite($interest) ? number_format($interest, 2, '.', '') : "0.00",
            ];
        } else {
            return [
                'status' => false,
                ' $totalCost' => $totalCost,
                'emi' => is_finite($emi) ? number_format($emi, 2, '.', '') : "0.00",
                'total_cost' => is_finite($totalCost) ? number_format($totalCost, 2, '.', '') : "0.00",
                'interest' => is_finite($interest) ? number_format($interest, 2, '.', '') : "0.00",
            ];
        }
    }

    public function getProductDiscount($id)
    {
        return $this->Products->getDiscount($id);
    }

    public function getCategories()
    {
        return $this->Categories->parentCategories();
    }

    public function getHomeScrollText()
    {

        $scrollContentArr = $this->ContentPages->find()
            ->select('content')
            ->where([
                'ContentPages.status' => 'A',
                'ContentPages.content_category_identifier' => 'header_scroll_text',
                'ContentPages.published' => 'Yes',
                'ContentPages.content_category' => "Website Pages"
            ])
            ->all();
        return $scrollContentArr;
    }
    public function getTopSellItems($limit = 5)
    {
        $userId = null;
        $customer_id = null;
        if ($this->request) { // Check if request is available
            $session = $this->request->getSession();
            $identity = $session->read('Auth.User');

            if (!empty($identity) && isset($identity['id'])) {
                $userId = $identity['id'];
                $users = $this->Users->find()
                    ->contain([
                        'Customers' => function ($q) {
                            return $q->select(['id']); // Select only the Customer.id field
                        }
                    ])
                    ->select(['Users.id']) // Select the necessary fields from Users
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $identity->id])
                    ->first();

                $customer_id = $users->customer->id;
            }
        }



        $controller = $this->getController();

        // Load Media component if not already loaded
        if (!$controller->Media) {
            $controller->loadComponent('Media');
        }

        // Get parent categories
        $categories = $this->Categories->parentCategories();
        $categoryIds = array_column($categories, 'id');

        // Get top-selling products
        $products = $this->Products->getTopSellingProducts($limit, $categoryIds, 'best_selling');

        $modifiedProducts = [];
        foreach ($products as $product) {

            $product['whishlist'] = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product['id']);

            $product['promotion_price'] = $this->Products->getProductPrice($product['id']);
            $product['product_image'] = '';
            $product['rating'] = $this->Reviews->getAverageRating($product['id']);
            $image = $this->ProductImages->getDefaultProductImage($product['id']);
            if ($image) {
                // Use Media component through controller
                $product['product_image'] = $controller->Media->getCloudFrontURL($image);
            }

            $modifiedProducts[] = $product;
        }

        return $modifiedProducts;
    }



    /**
     * Send order confirmation email
     *
     * @param int|null $orderId Order ID
     * @return \Cake\Http\Response|null
     */
    public function OrderConfirmationEmail($orderId = null)
    {

        if ($orderId === null) {
            return false;
        }

        // Get order details
        $orderData = $this->Orders->orderDetail($orderId);
        if (!$orderData) {
            // order not found
            return false;
        }

        // Convert to array for easier access
        $order = $orderData->toArray();

        // Get customer details
        $customer = null;
        if (!empty($order['customer_id'])) {
            $customer = $this->Customers->get($order['customer_id'], [
                'contain' => ['Users']
            ]);
        }
        $controller = $this->getController();

        // Load Media component if not already loaded
        if (!$controller->Media) {
            $controller->loadComponent('Media');
        }

        // Format order date
        $orderDate = $order['order_date']->format('l, F j, Y');

        // Calculate delivery date (example: 7 days from order date)
        $deliveryDate = clone $order['order_date'];
        $deliveryDate->modify('+7 days');
        $formattedDeliveryDate = $deliveryDate->format('l, F j');

        // Get customer name and email
        $customerName = '';
        $customerEmail = '';

        if ($customer && !empty($customer->user)) {
            $customerName = $customer->user->first_name . ' ' . $customer->user->last_name;
            $customerEmail = $customer->user->email;
        }

        // Get customer address
        $customerAddress = [];
        if (!empty($order['customer_address_id'])) {
            $customerAddress = $this->CustomerAddresses->get($order['customer_address_id']);
        }

        // Format address for email
        $formattedAddress = [
            'name' => $customerName,
            'address' => '',
            'city' => '',
            'country' => ''
        ];
        $cc = null;

        if (!empty($customerAddress)) {
            $formattedAddress['address'] = $customerAddress->address_line1 . ' ' . $customerAddress->address_line2;
            if (!empty($customerAddress->city_id)) {
                $city = $this->Cities->get($customerAddress->city_id);
                $formattedAddress['city'] = $city->city_name;
            }
        } elseif (!empty($order['showroom'])) {
            $formattedAddress['address'] = 'Pickup from ' . $order['showroom']['name'];
            if (!empty($order['showroom']['city_id'])) {
                $city = $this->Cities->get($order['showroom']['city_id']);
                $formattedAddress['city'] = $city->city_name;
            }
        }

        foreach ($order['order_items'] as $key => &$value) {
            $value['thumb_image'] = $this->Products->getProductThumbnailImage($value['product_id'], $value['product_variant_id']);

            if (!empty($value['thumb_image'])) {
                $value['thumb_image'] = $controller->Media->getCloudFrontURL($value['thumb_image']);
            }
        }

        // Prepare email data
        $subject = 'Order Confirmation - #' . $order['order_number'];
        $template = 'order_confirmation';

        // Set variables for email template
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'order' => $order,
            'order_number' => $order['order_number'],
            'order_date' => $orderDate,
            'delivery_date' => $formattedDeliveryDate,
            'customer_name' => $customerName,
            'shipping_method' => $order['delivery_mode'] === 'pickup' ? 'Store Pickup' : 'Delivery',
            'delivery_preference' => $order['delivery_mode_type'] === 'standard' ? 'Standard Delivery' : 'Express Delivery',
            'address' => $formattedAddress,
            'order_type' => $order['order_type'],
            'order_total' => $order['total_amount'],
            'payment_method' => $order['payment_method'],
            'order_items' => $order['order_items'],
            'subtotal' => $order['subtotal_amount'],
            'delivery_charge' => $order['delivery_charge'],
            'discount' => $order['discount_amount'],
            'total' => $order['total_amount']
        ];

        try {
            $to = $customerEmail;
            // Load Global component if not already loaded
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
            //   $this->Flash->error(__('Failed to send order confirmation email: {0}', $e->getMessage()));
        }
    }

    public function orderReturnRequestEmail($orderItemId = null, $resionId = null, $reason = null, $returnDate = null)
    {

        $data['reason_id'] = $resionId ?? 1;
        $data['reason'] = $reason;
        $returnData['requested_at'] = $returnDate ?? date('Y-m-d H:i:s');
        $controller = $this->getController();
        if ($orderItemId === null) {
            return false;
        }

        // Get order details
        $orderData = $this->OrderItems->getOrderItemDetails($orderItemId);
        if (!$orderData) {
            return false;
        }

        // Convert to array for easier access
        $order = $orderData->toArray();

        // Get customer details
        $customer = null;
        if (!empty($order['order'])) {
            $customer = $order['order']['customer_id'];
        }

        $category = $this->OrderReturnCategories->find()
            ->select(['name'])
            ->where(['id' => $data['reason_id']])
            ->first();

        $reasonName = $category ? $category->name : __('No reason provided');

        $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
        $to = $order['order']['customer']['user']['email'] ?? $adminEmails[0]; // $adminEmails[0];
        $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
        $subject = 'Order Item Return Request';
        $template = 'order_item_return_email';
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'order_number' => $order['order']['order_number'],
            'order_item_product' => $order['product']['name'],
            'customer_name' => $order['order']['customer']['user']['first_name'] . ' ' . $order['order']['customer']['user']['last_name'],
            'reason' => $reasonName,
            'comment' => $data['reason'] ?? 'No comment provided',
            'requested_at' => $returnData['requested_at'],
            'processed_at' => $returnData['requested_at'],
            'canceled_at' => $returnData['requested_at'],
            'status' => 'Pending',
            'product_name' => $order['product_variant']['variant_name'] ?? $order['order']['product']['name'],
            'price' => $order['total_price'],
            'qty' =>  $order['quantity'],
        ];

        try {
            // Load Global component if not already loaded
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
        }
    }

    public function orderCancellationRequestEmail($orderItemId = null, $resionId = null, $reason = null, $returnDate = null)
    {
        $data['reason_id'] = $resionId ?? 1;
        $data['reason'] = $reason ?? "No comment provided";
        $cancellationData['canceled_at'] = $returnDate ?? date('Y-m-d H:i:s');
        $controller = $this->getController();
        if ($orderItemId === null) {
            $orderItemId = 758;
        }
        // Get order details
        $orderData = $this->OrderItems->getOrderItemDetails($orderItemId);
        if (!$orderData) {
            return false;
        }

        // Convert to array for easier access
        $order = $orderData->toArray();

        // Get customer details
        $customer = null;
        if (!empty($order['order'])) {
            $customer = $order['order']['customer_id'];
        }

        $category = $this->OrderCancellationCategories->find()
            ->select(['name'])
            ->where(['id' => $data['reason_id']])
            ->first();

        $reasonName = $category ? $category->name : __('No reason provided');

        $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
        $to = $order['order']['customer']['user']['email'] ?? $adminEmails[0]; // $adminEmails[0];
        $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
        $subject = 'Order Cancellation Request';
        $template = 'order_cancellation_email';
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'order_number' => $order['order']['order_number'],
            'customer_name' => $order['order']['customer']['user']['first_name'] . ' ' . $order['order']['customer']['user']['last_name'],
            'reason' => $reasonName,
            'comment' => $data['reason'] ?? 'No comment provided',
            'canceled_at' => $cancellationData['canceled_at'],
            'product_name' => $order['product_variant']['variant_name'] ?? $order['order']['product']['name'],
            'price' => $order['total_price'],
            'status' =>  'Pending',
            'qty' =>  $order['quantity'],
        ];


        try {
            // Load Global component if not already loaded
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
        }
    }

    public function userAccountDeleteEmail($email = null, $name = null, $comment = null)
    {
        $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
        $to = $email ?? $adminEmails[0];
        $controller = $this->getController();
        $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
        $subject = 'Your Account Has Been Successfully Deleted';
        $template = 'delete_customer_account_email';
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'customer_name' => $name,
            'email' => $email,
            'adminEmail' => $adminEmails[0],
            'comment' => $comment,
        ];

        try {
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
        }
    }


    /*** All Notification and Email Send Code Start for common and re-use  **/



    // send support request create notification
    public function sendSupportRequestNotification($ticketId)
    {
        $ticketInfo = $this->supportTicket->get($ticketId);
        $supportTicket = $ticketInfo->toArray();
        
        // return false; die;

        $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
        $to = $adminEmails[0];
        $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
print_r($cc); exit;
        $subject = "New support request from " . $supportTicket->issue_name . " Issue: " . substr($supportTicket->description, 0, 30) . (strlen($supportTicket->description) > 30 ? "..." : "");
        $template = 'support_request_email';
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'customer_name' => $supportTicket->user->first_name . ' ' . $supportTicket->user->last_name,
            'customer_email' => $supportTicket->user->email,
            'subject' => $supportTicket->subject,
            'message' => $supportTicket->message,
        ];

        try {
            $controller = $this->getController();
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
        }
    }



    public function sendNotification($deviceToken, $title, $body, $customData = [])
    {
        $controller = $this->getController();
        if (!$controller->Global) {
            $controller->loadComponent('Global');
        }
        $response = $controller->Global->sendNotification(
            [$deviceToken],
            $title,
            $body,
            $customData
        );
        return $response;
    }

    /**
     * Send order confirmation email
     *
     * @param int|null $orderId Order ID
     * @return \Cake\Http\Response|null
     */
    public function OrderApprovedEmail($orderId = null)
    {

        if ($orderId === null) {
            return false;
        }

        // Get order details
        $orderData = $this->Orders->orderDetail($orderId);
        if (!$orderData) {
            // order not found
            return false;
        }

        // Convert to array for easier access
        $order = $orderData->toArray();

        // Get customer details
        $customer = null;
        if (!empty($order['customer_id'])) {
            $customer = $this->Customers->get($order['customer_id'], [
                'contain' => ['Users']
            ]);
        }
        $controller = $this->getController();

        // Load Media component if not already loaded
        if (!$controller->Media) {
            $controller->loadComponent('Media');
        }

        // Format order date
        $orderDate = $order['order_date']->format('l, F j, Y');

        // Calculate delivery date (example: 7 days from order date)
        $deliveryDate = clone $order['order_date'];
        $deliveryDate->modify('+7 days');
        $formattedDeliveryDate = $deliveryDate->format('l, F j');

        // Get customer name and email
        $customerName = '';
        $customerEmail = '';

        if ($customer && !empty($customer->user)) {
            $customerName = $customer->user->first_name . ' ' . $customer->user->last_name;
            $customerEmail = $customer->user->email;
        }

        // Get customer address
        $customerAddress = [];
        if (!empty($order['customer_address_id'])) {
            $customerAddress = $this->CustomerAddresses->get($order['customer_address_id']);
        }

        // Format address for email
        $formattedAddress = [
            'name' => $customerName,
            'address' => '',
            'city' => '',
            'country' => ''
        ];
        $cc = null;

        if (!empty($customerAddress)) {
            $formattedAddress['address'] = $customerAddress->address_line1 . ' ' . $customerAddress->address_line2;
            if (!empty($customerAddress->city_id)) {
                $city = $this->Cities->get($customerAddress->city_id);
                $formattedAddress['city'] = $city->city_name;
            }
        } elseif (!empty($order['showroom'])) {
            $formattedAddress['address'] = 'Pickup from ' . $order['showroom']['name'];
            if (!empty($order['showroom']['city_id'])) {
                $city = $this->Cities->get($order['showroom']['city_id']);
                $formattedAddress['city'] = $city->city_name;
            }
        }

        foreach ($order['order_items'] as $key => &$value) {
            $value['thumb_image'] = $this->Products->getProductThumbnailImage($value['product_id'], $value['product_variant_id']);

            if (!empty($value['thumb_image'])) {
                $value['thumb_image'] = $controller->Media->getCloudFrontURL($value['thumb_image']);
            }
        }

        // Prepare email data
        $subject = 'Order Approved - #' . $order['order_number'];
        $template = 'order_approved';

        // Set variables for email template
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'order' => $order,
            'order_number' => $order['order_number'],
            'order_date' => $orderDate,
            'delivery_date' => $formattedDeliveryDate,
            'customer_name' => $customerName,
            'shipping_method' => $order['delivery_mode'] === 'pickup' ? 'Store Pickup' : 'Delivery',
            'delivery_preference' => $order['delivery_mode_type'] === 'standard' ? 'Standard Delivery' : 'Express Delivery',
            'address' => $formattedAddress,
            'order_type' => $order['order_type'],
            'order_total' => $order['total_amount'],
            'payment_method' => $order['payment_method'],
            'order_items' => $order['order_items'],
            'subtotal' => $order['subtotal_amount'],
            'delivery_charge' => $order['delivery_charge'],
            'discount' => $order['discount_amount'],
            'total' => $order['total_amount']
        ];

        try {
            $to = $customerEmail;
            // Load Global component if not already loaded
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
            //   $this->Flash->error(__('Failed to send order approval email: {0}', $e->getMessage()));
        }
    }


    public function OrderCancellationRejectedEmail($orderId = null)
    {

        if ($orderId === null) {
            return false;
        }

        // Get order details
        $orderData = $this->Orders->orderDetail($orderId);
        if (!$orderData) {
            // order not found
            return false;
        }

        // Convert to array for easier access
        $order = $orderData->toArray();

        // Get customer details
        $customer = null;
        if (!empty($order['customer_id'])) {
            $customer = $this->Customers->get($order['customer_id'], [
                'contain' => ['Users']
            ]);
        }
        $controller = $this->getController();

        // Load Media component if not already loaded
        if (!$controller->Media) {
            $controller->loadComponent('Media');
        }

        // Format order date
        $orderDate = $order['order_date']->format('l, F j, Y');

        // Calculate delivery date (example: 7 days from order date)
        $deliveryDate = clone $order['order_date'];
        $deliveryDate->modify('+7 days');
        $formattedDeliveryDate = $deliveryDate->format('l, F j');

        // Get customer name and email
        $customerName = '';
        $customerEmail = '';

        if ($customer && !empty($customer->user)) {
            $customerName = $customer->user->first_name . ' ' . $customer->user->last_name;
            $customerEmail = $customer->user->email;
        }

        // Get customer address
        $customerAddress = [];
        if (!empty($order['customer_address_id'])) {
            $customerAddress = $this->CustomerAddresses->get($order['customer_address_id']);
        }

        // Format address for email
        $formattedAddress = [
            'name' => $customerName,
            'address' => '',
            'city' => '',
            'country' => ''
        ];
        $cc = null;

        if (!empty($customerAddress)) {
            $formattedAddress['address'] = $customerAddress->address_line1 . ' ' . $customerAddress->address_line2;
            if (!empty($customerAddress->city_id)) {
                $city = $this->Cities->get($customerAddress->city_id);
                $formattedAddress['city'] = $city->city_name;
            }
        } elseif (!empty($order['showroom'])) {
            $formattedAddress['address'] = 'Pickup from ' . $order['showroom']['name'];
            if (!empty($order['showroom']['city_id'])) {
                $city = $this->Cities->get($order['showroom']['city_id']);
                $formattedAddress['city'] = $city->city_name;
            }
        }

        foreach ($order['order_items'] as $key => &$value) {
            $value['thumb_image'] = $this->Products->getProductThumbnailImage($value['product_id'], $value['product_variant_id']);

            if (!empty($value['thumb_image'])) {
                $value['thumb_image'] = $controller->Media->getCloudFrontURL($value['thumb_image']);
            }
        }

        // Prepare email data
        $subject = 'Order Approved - #' . $order['order_number'];
        $template = 'order_cancellation_reject';

        // Set variables for email template
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'order' => $order,
            'order_number' => $order['order_number'],
            'order_date' => $orderDate,
            'delivery_date' => $formattedDeliveryDate,
            'customer_name' => $customerName,
            'shipping_method' => $order['delivery_mode'] === 'pickup' ? 'Store Pickup' : 'Delivery',
            'delivery_preference' => $order['delivery_mode_type'] === 'standard' ? 'Standard Delivery' : 'Express Delivery',
            'address' => $formattedAddress,
            'order_type' => $order['order_type'],
            'order_total' => $order['total_amount'],
            'payment_method' => $order['payment_method'],
            'order_items' => $order['order_items'],
            'subtotal' => $order['subtotal_amount'],
            'delivery_charge' => $order['delivery_charge'],
            'discount' => $order['discount_amount'],
            'total' => $order['total_amount']
        ];

        try {
            $to = $customerEmail;
            // Load Global component if not already loaded
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
            //   $this->Flash->error(__('Failed to send order approval email: {0}', $e->getMessage()));
        }
    }

    public function OrderCancellationApprovedEmail($orderId = null)
    {

        if ($orderId === null) {
            return false;
        }

        // Get order details
        $orderData = $this->Orders->orderDetail($orderId);
        if (!$orderData) {
            // order not found
            return false;
        }

        // Convert to array for easier access
        $order = $orderData->toArray();

        // Get customer details
        $customer = null;
        if (!empty($order['customer_id'])) {
            $customer = $this->Customers->get($order['customer_id'], [
                'contain' => ['Users']
            ]);
        }
        $controller = $this->getController();

        // Load Media component if not already loaded
        if (!$controller->Media) {
            $controller->loadComponent('Media');
        }

        // Format order date
        $orderDate = $order['order_date']->format('l, F j, Y');

        // Calculate delivery date (example: 7 days from order date)
        $deliveryDate = clone $order['order_date'];
        $deliveryDate->modify('+7 days');
        $formattedDeliveryDate = $deliveryDate->format('l, F j');

        // Get customer name and email
        $customerName = '';
        $customerEmail = '';

        if ($customer && !empty($customer->user)) {
            $customerName = $customer->user->first_name . ' ' . $customer->user->last_name;
            $customerEmail = $customer->user->email;
        }

        // Get customer address
        $customerAddress = [];
        if (!empty($order['customer_address_id'])) {
            $customerAddress = $this->CustomerAddresses->get($order['customer_address_id']);
        }

        // Format address for email
        $formattedAddress = [
            'name' => $customerName,
            'address' => '',
            'city' => '',
            'country' => ''
        ];
        $cc = null;

        if (!empty($customerAddress)) {
            $formattedAddress['address'] = $customerAddress->address_line1 . ' ' . $customerAddress->address_line2;
            if (!empty($customerAddress->city_id)) {
                $city = $this->Cities->get($customerAddress->city_id);
                $formattedAddress['city'] = $city->city_name;
            }
        } elseif (!empty($order['showroom'])) {
            $formattedAddress['address'] = 'Pickup from ' . $order['showroom']['name'];
            if (!empty($order['showroom']['city_id'])) {
                $city = $this->Cities->get($order['showroom']['city_id']);
                $formattedAddress['city'] = $city->city_name;
            }
        }

        foreach ($order['order_items'] as $key => &$value) {
            $value['thumb_image'] = $this->Products->getProductThumbnailImage($value['product_id'], $value['product_variant_id']);

            if (!empty($value['thumb_image'])) {
                $value['thumb_image'] = $controller->Media->getCloudFrontURL($value['thumb_image']);
            }
        }

        // Prepare email data
        $subject = 'Order Approved - #' . $order['order_number'];
        $template = 'order_cancellation_approved';

        // Set variables for email template
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'order' => $order,
            'order_number' => $order['order_number'],
            'order_date' => $orderDate,
            'delivery_date' => $formattedDeliveryDate,
            'customer_name' => $customerName,
            'shipping_method' => $order['delivery_mode'] === 'pickup' ? 'Store Pickup' : 'Delivery',
            'delivery_preference' => $order['delivery_mode_type'] === 'standard' ? 'Standard Delivery' : 'Express Delivery',
            'address' => $formattedAddress,
            'order_type' => $order['order_type'],
            'order_total' => $order['total_amount'],
            'payment_method' => $order['payment_method'],
            'order_items' => $order['order_items'],
            'subtotal' => $order['subtotal_amount'],
            'delivery_charge' => $order['delivery_charge'],
            'discount' => $order['discount_amount'],
            'total' => $order['total_amount']
        ];

        try {
            $to = $customerEmail;
            // Load Global component if not already loaded
            if (!$controller->Global) {
                $controller->loadComponent('Global');
            }
            $sendEmail = $controller->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
            return true;
        } catch (\Exception $e) {
            return true;
            //   $this->Flash->error(__('Failed to send order approval email: {0}', $e->getMessage()));
        }
    }







    /**** */
}
