<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>

    .select2-container--default .select2-selection--single.is-invalid {
        border-color: #dc3545 !important;
        border-width: 1px;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    .error-msg {
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 4px;
        display: block;
    }

    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #toRemovePadding {
        margin-bottom: 0px !important;
        padding-bottom: 0px !important;
    }

    #remove-border {
        border: none;
        background-color: transparent;
    }

    #thead {
        background-color: #0d839b !important;
        color: white;
    }

    .select2  {
        width: 100% !important;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div
        class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item"><a
                    href="<?= $this->Url->build(['controller' => 'ReturnsRefunds', 'action' => 'index']) ?>"><?= __('Return and Refunds') ?></a>
            </li>
            <li class="breadcrumb-item active"><?= __('Add') ?></li>
        </ul>
        <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
            <small class="p-10 fw-bold"><?= __('BACK') ?></small>
            <span class="rotate me-2">⤣</span>
        </button>
    </div>

    <div class="section-body">
        <div class="container-fluid">
            <div class="card" id="toRemovePadding">
                <div>
                    <div>
                        <div class="modal-header">
                            <h5
                                class="modal-title"
                                id="stockRefillModalLabel"
                            >
                                <?= __('Add Return Request') ?>
                            </h5>
                        </div>
                        <form id="add_return_order_form" action="<?= $this->Url->build([
                            'controller' => 'ReturnsCancellation',
                            'action' => 'add'
                        ]); ?>" method="post" enctype="multipart/form-data">
                        <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                        <div class="modal-body pb-0">

                            <div class="form-group row">
                                <label for="showroomSelect" class="col-sm-2 col-form-label fw-bold">
                                    <?= __("Order ID") ?> <sup class="text-danger font-11">*</sup>
                                </label>
                                <div class="col-sm-5">
                                    <?= $this->Form->control('order_id', [
                                        'type' => 'text',
                                        'id' => 'order-id-input',
                                        'label' => false,
                                        'placeholder' => __('Enter Order ID'),
                                        'class' => 'form-control',
                                        'autocomplete' => 'off'
                                    ]) ?>
                                    <span style="display:none;color: red;" id="order_select_error"><?= __('Please enter a valid Order ID.') ?></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label fw-bold"><?= __('Pickup Charge') ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="pickup_charge" id="pickupChargeYes" value="Yes" checked>
                                        <label class="form-check-label" for="pickupChargeYes"><?= __('Yes') ?></label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="pickup_charge" id="pickupChargeNo" value="No">
                                        <label class="form-check-label" for="pickupChargeNo"><?= __('No') ?></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label fw-bold"><?= __('Pickup Required') ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="pickup_required" id="pickupRequiredYes" value="Yes">
                                        <label class="form-check-label" for="pickupRequiredYes"><?= __('Yes') ?></label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="pickup_required" id="pickupRequiredNo" value="No" checked>
                                        <label class="form-check-label" for="pickupRequiredNo"><?= __('No') ?></label>
                                    </div>
                                </div>
                            </div>

            
                            <div id="pickupFromSection" style="display: none;">
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label fw-bold"><?= __('Return To') ?></label>
                                <div class="col-sm-5">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="return_to" id="requestToWarehouse" value="Warehouse" checked>
                                        <label class="form-check-label" for="requestToWarehouse"><?= __('Warehouse') ?></label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="return_to" id="requestToShowroom" value="Showroom">
                                        <label class="form-check-label" for="requestToShowroom"><?= __('Showroom') ?></label>
                                    </div>
                                </div>
                            </div>
                            </div>

                            <div class="form-group row toWarehouseSelectDiv">
                                <label for="warehouseSelect" class="col-sm-2 col-form-label fw-bold"><?= __("Warehouse") ?> <sup class="text-danger font-11">*</sup></label>
                                    <div class="col-sm-5">
                                        <select class="form-control form-select select2 d-flex align-items-center" id="warehouseSelect" name="warehouse_id">
                                            <option><?= __('-- Select Warehouse --') ?></option>
                                            <?php foreach ($warehouses as $warehouse): ?>
                                                <option value="<?= $warehouse->id; ?>">
                                                    <?= htmlspecialchars($warehouse->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <span id="warehouse_error" style="color: red;display: none;"><?= __('Select Warehouse') ?></span>
                                    </div>
                            </div>

                            <div class="form-group row toShowroomSelectDiv" style="display: none;">
                                <label for="toShowroomSelect" class="col-sm-2 col-form-label fw-bold"><?= __("Showroom") ?> <sup class="text-danger font-11">*</sup></label>
                                    <div class="col-sm-5">
                                        <select class="form-control form-select select2 d-flex align-items-center" id="toShowroomSelect" name="showroom_id">
                                            <option><?= __('-- Select Showroom --') ?></option>
                                            <?php foreach ($showrooms as $showroom): ?>
                                                <option value="<?= $showroom->id; ?>">
                                                    <?= htmlspecialchars($showroom->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <span id="to_showroom_error" style="color: red;display: none;"><?= __('Select Showroom') ?></span>
                                    </div>
                            </div>

                            <div class="form-group row">
                                <label for="pickupNote" class="col-sm-2 col-form-label fw-bold"><?= __('Note') ?></label>
                                <div class="col-sm-5">
                                    <textarea class="form-control" name="note" id="pickupNote" rows="3" placeholder="<?= __('Enter any note...') ?>"></textarea>
                                </div>
                            </div>


                            <table id="add_product_table" class="add_product_table table-responsive table table-striped">
                                <thead>
                                    <tr id="thead">
                                      <td class="fw-bold"><?= __("Select") ?></td>
                                      <td class="fw-bold"><?= __("Product") ?></td>
                                      <td class="fw-bold"><?= __("Product Variant") ?></td>
                                      <td class="fw-bold"><?= __("Product Attribute") ?></td>
                                      <td class="fw-bold"><?= __("SKU") ?></td>
                                      <td class="fw-bold"><?= __("Quantity") ?></td>
                                      <td class="fw-bold"><?= __("Return Quantity") ?></td>
                                      <td class="fw-bold"><?= __("Upload Image for Defect") ?></td>
                                      <td class="fw-bold"><?= __("Reason") ?></td>
                                    </tr>
                                </thead>
                                <tbody id="product_tbody">

                                </tbody>   
                              </table>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn add_return_order_btn" id="add_return_order_btn"><?= __('Save') ?></button>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    $(document).ready(function () {
        function togglePickupRequiredSection() {
            if ($('#pickupRequiredYes').is(':checked')) {
                $('#pickupFromSection').show();

                // Trigger current selection logic
                const selectedPickupValue = $('input[name="return_to"]:checked').val();
                togglePickupSelectBox(selectedPickupValue);
            } else {
                $('#pickupFromSection').hide();
                $('.toWarehouseSelectDiv').hide();
                $('.toShowroomSelectDiv').hide();

                // Remove name attributes to prevent accidental form submission
                $('#toShowroomSelect').removeAttr('name');
                $('#warehouseSelect').removeAttr('name');
            }
        }

        // Bind change events to pickup required radio buttons
        $('input[name="pickup_required"]').change(togglePickupRequiredSection);

        // Initial setup
        togglePickupRequiredSection();
    });


    var orderReturnCategories = <?= json_encode($order_return_categories_ids) ?>;

    $('#order-id-input').on('keypress', function (e) {
        var charCode = e.which ? e.which : e.keyCode;

        // Allow: backspace (8), tab (9), delete (46), arrows (37-40)
        if (
            charCode === 8 || charCode === 9 || charCode === 46 ||
            (charCode >= 37 && charCode <= 40)
        ) {
            return;
        }

        // Allow only digits (0–9)
        if (charCode < 48 || charCode > 57) {
            e.preventDefault();
        }
    });

    $('#order-id-input').on('paste', function (e) {
        var pastedData = e.originalEvent.clipboardData.getData('text');
        if (!/^\d+$/.test(pastedData)) {
            e.preventDefault();
        }
    });

    $('#order-id-input').on('keyup', function (e) {

        var order_id = $(this).val();

        if (order_id === '' || isNaN(order_id) || parseInt(order_id) <= 0) {
            $('#order_select_error').show();
            $('#order-id-input').addClass('is-invalid');
            $('#product_tbody').html('');
            return;
        }
        else
        {
            $('#order-id-input').removeClass('is-invalid');
        }

        $('#order_select_error').hide();

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'getOrderItemsById']) ?>/" + order_id,
            type: 'GET',
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function (data) {

                if (data.status === 'error') {
                    swal('<?= __('Not Allowed') ?>', data.message, 'error');
                    $('#product_tbody').html('');
                    return;
                }

                if (data.status === 'success') {

                        $('#order_select_error').hide();

                        $('#product_tbody').html('');

                        if (data.order_products.length === 0) {
                            swal('<?= __('No Products') ?>', '<?= __('There are no products available to return.') ?>', 'warning');
                            return; // Stop execution
                        }

                        var html = "";
                        for(var i=0; i < data.order_products.length; i++)
                        {
                            let isDisabled = (data.order_products[i].status === 'Pending Return' || data.order_products[i].status === 'Return Approved') ? "disabled" : "";


                            // Construct variants
                            var variantHtml = '';
                            if (data.order_products[i].product.variant_name !== undefined) {

                                variantHtml = data.order_products[i].product.variant_name;

                            } else {
                                variantHtml = 'N/A';
                            }

                            // Construct attributes
                            var attributesHtml = '';
                            if (data.order_products[i].product_attribute_id !== null) {

                                attributesHtml = data.order_products[i].product.attributes.attribute.name + ': ' + data.order_products[i].product.attributes.attribute_value.value;

                            } else {
                                attributesHtml = 'N/A';
                            }

                            html += "<tr style='position:relative;top:10px;'>";

                            html += `<td>
                                <input type='hidden' value="${data.order_products[i].id}" name='order_item_id[]'>
                                <input type='hidden' value="${data.order_products[i].price}" name='price[]'>
                                <input type="checkbox" style="width: 15px;" name="selected_products[]" value="${data.order_products[i].product_id}" class="product-checkbox" ${isDisabled}></td>`;

                            html += "<td><input type='hidden' value='"+data.order_products[i].product_id+"' name='product_id[]'>"+data.order_products[i].product.name+"</td>";

                            html += "<td><input type='hidden' value='"+data.order_products[i].product_variant_id+"' name='product_variant_id[]'>"+variantHtml+"</td>";

                            html += "<td><input type='hidden' value='"+data.order_products[i].product_attribute_id+"' name='product_attribute_id[]'>"+attributesHtml+"</td>";

                            html += "<td><input type='hidden' value='"+data.order_products[i].product.sku+"' name='sku[]'>"+data.order_products[i].product.sku+"</td>";

                            html += `<td><input type='hidden' value='${data.order_products[i].quantity}' name='quantity[]'>${data.order_products[i].quantity}</td>`;

                            html += `<td>
                                    <input type='number' class='stock_return_qty_input' data-quantity="${data.order_products[i].quantity}" data-id="${data.order_products[i].id}"" name='return_quantity[]' ${isDisabled}>
                                    <span class="return_quantity_error" style="color: red;display: none;" id="return_quantity_error_${data.order_products[i].id}"></span>

                                    </td>
                            `;

                            html += `<td>

                                    <input data-id="${data.order_products[i].id}" style='border:none;' type='file' name='return_product_image[]' class="return-product-image" onchange="handleImagePreview(event)"
                                        multiple 
                                        accept=".jpg, .jpeg, .png">

                                    <span class="return_image_error" style="color: red;display: none;" id="return_image_error_${data.order_products[i].id}"><?= __('choose a image') ?></span>


                                    <div class="return-image-preview" style="margin-top: 10px;"></div>

                                    </td>`;

                            var selectHtml = `<select class='stock_return_reason_input' data-id="${data.order_products[i].id}" name='return_reason[]' ${isDisabled}>
                                <option value=""><?= __('Select Reason') ?></option>`;

                            for (const [id, name] of Object.entries(orderReturnCategories)) {
                                selectHtml += `<option value="${id}">${name}</option>`;
                            }

                            selectHtml += `</select>`;

                            html += `<td>
                                ${selectHtml}
                                <span class="return_reason_error" style="color: red;display: none;" id="return_reason_error_${data.order_products[i].id}"><?= __('Select a reason') ?></span>
                            </td>`;

                            html += "</tr>";
                        }

                        $('#product_tbody').append(html);

                } else {

                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch order detail. Please try again.') ?>', 'error');
                }
            },
            error: function () {
                swal('<?= __('Failed') ?>', '<?= __('Failed to fetch order detail. Please try again.') ?>', 'error');
            }
        });
    });

    // Initialize based on the currently checked radio button
    var selectedPickupValue = $('input[name="return_to"]:checked').val();
    togglePickupSelectBox(selectedPickupValue);

    // Handle radio button change event to toggle select boxes
    $('input[name="return_to"]').change(function() {
        var selectedPickupValue = $('input[name="return_to"]:checked').val();
        togglePickupSelectBox(selectedPickupValue);
    });

    // Function to toggle between showroom and warehouse dropdowns
    function togglePickupSelectBox(value) {        

        // Reset both select boxes
        $('#toShowroomSelect').val("-- Select Showroom --").trigger('change');
        $('#warehouseSelect').val("-- Select Warehouse --").trigger('change');

        $("#orderAssignTable tbody .details-row").remove();

        if (value === '<?= __('Showroom') ?>') {

            $('.toShowroomSelectDiv').show();
            $('.toWarehouseSelectDiv').hide();

            $('#toShowroomSelect').attr('name','return_to_id');
            $('#warehouseSelect').removeAttr('name');

        } else if (value === '<?= __('Warehouse') ?>') {

            $('.toShowroomSelectDiv').hide();
            $('.toWarehouseSelectDiv').show();

            $('#warehouseSelect').attr('name','return_to_id');
            $('#toShowroomSelect').removeAttr('name');
        }
    }

    // $('#order-select').on('change', function () {
            
    //         var purchase_order_id = $(this).val();

    //         if(purchase_order_id == 0 || purchase_order_id == '' || purchase_order_id == null)
    //         {
    //             return false;
    //         }

    //         $.ajax({
    //             url: "<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'getOrderItemsById']) ?>"+"/"+purchase_order_id,
    //             type: 'GET',
    //             headers: {
    //                 'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
    //             },
    //             success: function(data) {

    //                 if (data.status === 'success') {

    //                     $('#order_select_error').hide();

    //                     $('#product_tbody').html('');

    //                     if (data.order_products.length === 0) {
    //                         swal('<?= __('No Products') ?>', '<?= __('There are no products available to return.') ?>', 'warning');
    //                         return; // Stop execution
    //                     }

    //                     var html = "";
    //                     for(var i=0; i < data.order_products.length; i++)
    //                     {
    //                         // Construct variants
    //                         var variantHtml = '';
    //                         if (data.order_products[i].product.variant_name !== undefined) {

    //                             variantHtml = data.order_products[i].product.variant_name;

    //                         } else {
    //                             variantHtml = 'N/A';
    //                         }

    //                         // Construct attributes
    //                         var attributesHtml = '';
    //                         if (data.order_products[i].product_attribute_id !== null) {

    //                             attributesHtml = data.order_products[i].product.attributes.attribute.name + ': ' + data.order_products[i].product.attributes.attribute_value.value;

    //                         } else {
    //                             attributesHtml = 'N/A';
    //                         }

    //                         html += "<tr style='position:relative;top:10px;'>";

    //                         html += `<td>
    //                             <input type='hidden' value="${data.order_products[i].id}" name='order_item_id[]'>
    //                             <input type='hidden' value="${data.order_products[i].price}" name='price[]'>
    //                             <input type="checkbox" style="width: 15px;" name="selected_products[]" value="${data.order_products[i].product_id}" class="product-checkbox"></td>`;

    //                         html += "<td><input type='hidden' value='"+data.order_products[i].product_id+"' name='product_id[]'>"+data.order_products[i].product.name+"</td>";

    //                         html += "<td><input type='hidden' value='"+data.order_products[i].product_variant_id+"' name='product_variant_id[]'>"+variantHtml+"</td>";

    //                         html += "<td><input type='hidden' value='"+data.order_products[i].product_attribute_id+"' name='product_attribute_id[]'>"+attributesHtml+"</td>";

    //                         html += "<td><input type='hidden' value='"+data.order_products[i].product.sku+"' name='sku[]'>"+data.order_products[i].product.sku+"</td>";

    //                         html += `<td><input type='hidden' value='${data.order_products[i].quantity}' name='quantity[]'>${data.order_products[i].quantity}</td>`;

    //                         html += `<td>
    //                                 <input type='number' class='stock_return_qty_input' data-quantity="${data.order_products[i].quantity}" data-id="${data.order_products[i].id}"" name='return_quantity[]'>
    //                                 <span class="return_quantity_error" style="color: red;display: none;" id="return_quantity_error_${data.order_products[i].id}"></span>

    //                                 </td>
    //                         `;

    //                         html += `<td>

    //                                 <input data-id="${data.order_products[i].id}" style='border:none;' type='file' name='return_product_image[]' class="return-product-image" onchange="handleImagePreview(event)"
    //                                     multiple 
    //                                     accept=".jpg, .jpeg, .png">

    //                                 <span class="return_image_error" style="color: red;display: none;" id="return_image_error_${data.order_products[i].id}"><?= __('choose a image') ?></span>


    //                                 <div class="return-image-preview" style="margin-top: 10px;"></div>

    //                                 </td>`;

    //                         html += `<td>
    //                                     <textarea class='stock_return_reason_input' data-id="${data.order_products[i].id}" name='return_reason[]'></textarea>
    //                                     <span class="return_reason_error" style="color: red;display: none;" id="return_reason_error_${data.order_products[i].id}"><?= __('Enter a reason') ?></span>
    //                                 </td>`;

    //                         html += "</tr>";
    //                     }

    //                     $('#product_tbody').append(html);

    //                 } else {

    //                     swal('<?= __('Failed') ?>', '<?= __('Failed to fetch order detail. Please try again.') ?>', 'error');
    //                 }
    //             },
    //             error: function(xhr, status, error) {

    //                 swal('<?= __('Failed') ?>', '<?= __('Failed to fetch order detail. Please try again.') ?>', 'error');
    //             }
    //         });
    // });

    function handleImagePreview(event) {
        const input = event.target;
        const previewContainer = input.closest('td').querySelector('.return-image-preview');
        const files = input.files;

        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Validate file type (only allow images)
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                continue;
            }

            const reader = new FileReader();

            reader.onload = function (e) {
                const imgContainer = document.createElement('div');
                imgContainer.style.position = 'relative';
                imgContainer.style.marginRight = '10px';

                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.width = '100px';
                img.style.height = '100px';
                img.style.margin = '5px';
                img.classList.add('preview-thumbnail');

                // Create remove button
                const removeButton = document.createElement('button');
                removeButton.innerHTML = '&times;';
                removeButton.style.color = 'black';
                removeButton.style.background = 'lightgray';
                removeButton.style.border = 'none';
                removeButton.style.cursor = 'pointer';
                removeButton.style.width = '25px';
                removeButton.style.height = '25px';
                removeButton.style.borderRadius = '50%';
                removeButton.style.position = 'absolute';
                removeButton.style.right = '5px';
                removeButton.style.top = '5px';
                removeButton.style.fontSize = '18px';
                removeButton.style.fontWeight = 'bold';
                removeButton.type = 'button';

                removeButton.onclick = function () {
                    imgContainer.remove();
                };

                imgContainer.appendChild(img);
                imgContainer.appendChild(removeButton);
                previewContainer.appendChild(imgContainer);
            };

            reader.readAsDataURL(file);
        }
    }

    $('#add_return_order_form').on('submit', function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var error = 0;

        var selectedProducts = $('.product-checkbox:checked');

        if (selectedProducts.length === 0) {
            swal('Error', '<?= __('Please select at least one product.') ?>', 'error');
            return false;
        }

        formData.delete('order_item_id[]'); 
        formData.delete('price[]'); 
        formData.delete('product_id[]'); 
        formData.delete('product_variant_id[]');
        formData.delete('product_attribute_id[]');
        formData.delete('sku[]');
        formData.delete('quantity[]');
        formData.delete('return_quantity[]');
        formData.delete('return_reason[]');
        formData.delete('return_product_image[]');
        formData.delete('selected_products[]');

        if ($("#order-id-input").val() == '' || $("#order-id-input").val() == null) {
            error = 1;
            $('#order_select_error').show();
            $('#order-id-input').addClass('is-invalid');
        }

        // if($('input[name="return_to"]:checked').val() == '<?= __('Showroom') ?>')
        // {
        //     if($("#toShowroomSelect option:selected").val() == '' || $("#toShowroomSelect option:selected").val() == null || $("#toShowroomSelect option:selected").val() == '<?= __('-- Select Showroom --') ?>')
        //     {
        //         error = 1;
        //         $('#to_showroom_error').show();
        //         $('#toShowroomSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');
        //     }
        // }
        // else
        // {
        //     if($("#warehouseSelect option:selected").val() == '' || $("#warehouseSelect option:selected").val() == null || $("#warehouseSelect option:selected").val() == '<?= __('-- Select Warehouse --') ?>')
        //     {
        //         error = 1;
        //         $('#warehouse_error').show();
        //         $('#warehouseSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');
        //     }
        // }

        // Check if pickup is required
        if ($('input[name="pickup_required"]:checked').val() === 'Yes') {
            const selectedReturnTo = $('input[name="return_to"]:checked').val();

            if (selectedReturnTo === '<?= __('Showroom') ?>') {
                const selectedShowroom = $("#toShowroomSelect option:selected").val();
                if (!selectedShowroom || selectedShowroom === '<?= __('-- Select Showroom --') ?>') {
                    error = 1;
                    $('#to_showroom_error').show();
                    $('#toShowroomSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');
                }
            } else if (selectedReturnTo === '<?= __('Warehouse') ?>') {
                const selectedWarehouse = $("#warehouseSelect option:selected").val();
                if (!selectedWarehouse || selectedWarehouse === '<?= __('-- Select Warehouse --') ?>') {
                    error = 1;
                    $('#warehouse_error').show();
                    $('#warehouseSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');
                }
            }
        }

        selectedProducts.each(function() {
            var row = $(this).closest('tr');
            var dataId = row.find('input[name="return_quantity[]"]').data('id');
            var quantity = row.find('input[name="return_quantity[]"]').data('quantity');
            var returnQuantity = row.find('input[name="return_quantity[]"]').val();
            var returnReason = row.find('textarea[name="return_reason[]"]').val();
            const price = row.find('input[name="price[]"]').val();
            const orderItemId = row.find('input[name="order_item_id[]"]').val();
            var imageFiles = row.find('input[name="return_product_image[]"]')[0].files;  // Multiple files
            var pickupRequired = $('input[name="pickup_required"]:checked').val() || 'No';
            var pickupCharge = $('input[name="pickup_charge"]:checked').val() || 'No';

            if (returnQuantity == '' || returnQuantity == null) {
                error = 1;
                $('#return_quantity_error_' + dataId).html('Enter return quantity.').show();
            } else if (quantity < returnQuantity) {
                error = 1;
                $('#return_quantity_error_' + dataId).html('Return quantity is greater than available.').show();
            }
            else {
                $('#return_quantity_error_' + dataId).hide();
            }

            // if (!imageFiles.length) {
            //     error = 1;
            //     $('#return_image_error_' + dataId).show();
            // }

            // if (returnReason == '' || returnReason == null) {
            //     error = 1;
            //     $('#return_reason_error_' + dataId).show();
            // }

            var returnReason = $(`select[name='return_reason[]'][data-id='${dataId}']`).val();

            if (!returnReason || returnReason.trim() === '') {
                error = 1;
                $('#return_reason_error_' + dataId).show();
            } else {
                $('#return_reason_error_' + dataId).hide();
            }


            var productID = row.find('input[name="product_id[]"]').val();
            var variantID = row.find('input[name="product_variant_id[]"]').val();
            var attributeID = row.find('input[name="product_attribute_id[]"]').val();
            var sku = row.find('input[name="sku[]"]').val();
            var quantity = row.find('input[name="quantity[]"]').val();
            var returnQuantity = row.find('input[name="return_quantity[]"]').val();
            var returnReason = row.find('select[name="return_reason[]"]').val();

            formData.append(`order_item_id[]`, orderItemId);
            formData.append(`price[]`, price);
            formData.append(`product_id[]`, productID);
            formData.append(`product_variant_id[]`, variantID);
            formData.append(`product_attribute_id[]`, attributeID);
            formData.append(`sku[]`, sku);
            formData.append(`quantity[]`, quantity);
            formData.append(`return_quantity[]`, returnQuantity);
            formData.append(`return_reason[]`, returnReason);
            formData.append(`selected_products[]`, productID);

            // Append multiple images for each product
            for (let file of imageFiles) {
                formData.append(`return_product_image[${orderItemId}][]`, file);
            }
        });

        if (error == 0) {
            $('.return_quantity_error').hide();
            $('.return_image_error').hide();
            $('.return_reason_error').hide();

            $('#return_initiated_date_error').hide();
            $('#bill_select_error').hide();
            $('#return_showroom_error').hide();
            $('#return_warehouse_error').hide();
            $('.add_return_order_btn').attr('disabled', 'disabled');

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    swal('<?= __('Success') ?>', '<?= __('The return order request has been saved.') ?>', 'success')
                    .then(() => {
                        window.location.href = '<?= $this->Url->build(['controller' => 'ReturnsRefunds', 'action' => 'index']) ?>';
                    });
                },
                error: function(xhr, status, error) {
                    $('.add_return_order_btn').removeAttr('disabled');
                    swal('<?= __('Failed') ?>', '<?= __('Failed to save return order request. Please try again.') ?>', 'error');
                }
            });
        }
    });

    // Remove error messages when a checkbox is unchecked
    $(document).on('change', '.product-checkbox', function() {
        var row = $(this).closest('tr');
        var dataId = row.find('input[name="return_quantity[]"]').data('id');

        if (!$(this).is(':checked')) {
            $('#return_quantity_error_' + dataId).hide().html('');
            $('#return_image_error_' + dataId).hide().html('');
            $('#return_reason_error_' + dataId).hide().html('');
        }
    });

</script>

<?php $this->end(); ?>