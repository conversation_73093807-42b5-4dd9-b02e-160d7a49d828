<?php
/**
 * @var \App\Model\Entity\Expense $expense
 */

use App\Enum\ExpensePaymentMethod;
use App\Enum\ExpensePaymentStatus;
use App\Enum\ExpenseStatus;
use Cake\Routing\Router;

?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style type="text/css">
    .is-invalid {
        border: 1px solid red !important;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    select.is-invalid {
        border-color: #dc3545 !important;
    }

    .error {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 4px;
    }
</style>
<?php $this->end(); ?>

<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __('Expenses') ?></li>
        <li class="breadcrumb-item active"><?= __('Add') ?></li>
    </ul>
    <a href="<?= Router::url(['controller' => 'Expenses', 'action' => 'index'])?>" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
    </a>
</div>

<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20"><?= __('Add Expense Details') ?></h6>
            <?= $this->Form->create($expense, ['id' => 'editExpenseForm', 'novalidate' => true, 'type' => 'file']) ?>

            <?php if (!empty($expense->getErrors())) : ?>
                <div class="alert alert-danger">
                    <?= __('Please fix the errors below:') ?>
                </div>
            <?php endif; ?>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Name') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'placeholder' => __('Name of an expense'),
                        'label' => false,
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Payment Date') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('payment_date', [
                        'type' => 'text',
                        'class' => 'form-control datepicker',
                        'label' => false,
                        'disabled' => true,
                        'readonly' => true,
                        'value' => date('Y-m-d')
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Description') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('description', [
                        'type' => 'textarea',
                        'class' => 'form-control',
                        'placeholder' => __('Optional description for the expense'),
                        'label' => false,
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Category') ?> <sup class="text-danger">*</sup></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('expense_category_id', [
                        'type' => 'select',
                        'class' => 'form-control',
                        'options' => $expenseCategories,
                        'empty' => __('Select a Category'),
                        'label' => false,
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Amount') ?> <sup class="text-danger">*</sup></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('amount', [
                        'type' => 'number',
                        'step' => '0.01',
                        'class' => 'form-control',
                        'placeholder' => __('Amount'),
                        'label' => false,
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Transaction Number') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('payment_confirmation_number', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'placeholder' => __('Transaction Number'),
                        'label' => false,
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Receipt') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('receipt[]', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'label' => false,
                        'multiple' => true,
                        'id' => 'imageInput',
                        'accept' => 'image/*,application/pdf',
                    ]) ?>
                    <div id="previeContainer">
                        <ul id="imagePreviewContainer">

                        </ul>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Payment method') ?> <sup class="text-danger">*</sup></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('payment_method', [
                        'type' => 'select',
                        'class' => 'form-control select2',
                        'label' => false,
                        'options' => ExpensePaymentMethod::toRadioButtonArray(),
                    ]) ?>
                </div>
            </div>

            <div class="form-group row" id="chequeNumberField" style="display: none;">
                <label class="col-sm-2 col-form-label"><?= __('Cheque Number') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('cheque_number', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'placeholder' => __('Cheque number'),
                        'label' => false,
                    ]) ?>
                </div>
            </div>


            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Showroom') ?> <sup class="text-danger">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control(
                        'showroom_id',
                        [
                            'label' => false,
                            'div' => false,
                            'id' => 'showroom_id',
                            'type' => 'select',
                            'options' => $showrooms,
                            'data-live-search' => "true",
                            'class' => 'form-control select2',
                            'empty' => __('Select Showroom'),
                        ])
                    ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Payment status') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('payment_status', [
                        'type' => 'select',
                        'options' => ExpensePaymentStatus::toDropdownArray(),
                        'class' => 'form-control',
                        'label' => false,
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 col-form-label"><?= __('Status') ?></label>
                <div class="col-sm-5">
                    <?= $this->Form->control('status', [
                        'type' => 'select',
                        'options' => ExpenseStatus::toArray(),
                        'class' => 'form-control',
                        'label' => false,
                    ]) ?>
                </div>
            </div>

            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" id="btnSubmit" class="btn"><?= __('Save') ?></button>
                    <button type="reset" class="btn"><?= __('Reset') ?></button>
                </div>
            </div>

            <?= $this->Form->end() ?>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>
    var validationMessages = {
        categoryRequired: "<?= __('Please select category') ?>",
        amount: "<?= __('Please enter amount') ?>",
        showRoomRequired: "<?= __('Please select showroom') ?>"
    };
    $(document).ready(function () {
        $('.select2').select2();

        function toggleChequeNumberField() {
            var paymentMethod = $('#payment-method').val();
            if (paymentMethod == 'cheque') {
                $('#chequeNumberField').show();
            } else {
                $('#chequeNumberField').hide();
            }
        }

        toggleChequeNumberField();

        $('#payment-method').on('change', function () {
            toggleChequeNumberField();
        });

        $("#editExpenseForm").validate({
            debug: true,
            rules: {
                'expense_category_id': {
                    required: true
                },
                'amount': {
                    required: true,
                    number: true
                },
                'showroom_id': {
                    required: true
                },
            },
            messages: {
                'expense_category_id': {
                    required: validationMessages.categoryRequired
                },
                'amount': {
                    required: validationMessages.amount
                },
                'showroom_id': {
                    required: validationMessages.showRoomRequired
                }
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');

                // If it's a select2 or select box, also add to the container
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').addClass('is-invalid');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');

                // Remove from select2 or select container too
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').removeClass('is-invalid');
                }
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".col-sm-5"));
            },
            submitHandler: function (form) {

                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
        });
    });

    let allFiles = [];

    document.getElementById('imageInput').addEventListener('change', function (event) {

        var files = Array.from(event.target.files);

        if(files.length == 0)
        {
            allFiles = [];
            renderPreviews();
            return false;
        }

        var validFiles = [];
        var invalidFiles = [];

        let processedFiles = 0;
        let totalFiles = files.length;

        files.forEach(function(file, index) {

            var fileExtension = file.name.split('.').pop().toLowerCase();
            var allowedExtensions = ['jpg', 'jpeg', 'png'];

            // Check if the file has a valid extension
            if (allowedExtensions.includes(fileExtension)) {
                var img = new Image();
                img.src = URL.createObjectURL(file);

                img.onload = function () {
                    var width = img.naturalWidth;
                    var height = img.naturalHeight;
                    var fileSize = file.size / 1024 / 1024;  // Convert file size to MB
                    validFiles.push(file);
                    processedFiles++;

                    // When all files are processed, update the file input and show alerts
                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }
                };

                img.onerror = function () {
                    invalidFiles.push({file: file, reason: '<?= __('Unable to load image') ?>'});
                    processedFiles++;

                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }
                };
            }
            else
            {

                invalidFiles.push({file: file.name, reason: '<?= __('Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.') ?>'});

                processedFiles++;

                if (processedFiles === totalFiles) {
                    finalizeFileProcessing(validFiles, invalidFiles);
                }
            }
        });

        // Finalize file processing
        function finalizeFileProcessing(validFiles, invalidFiles) {


            var html = "<ul>";
            for(var i=0; i < invalidFiles.length; i++)
            {
                html += `<li>${invalidFiles[i].file} - ${invalidFiles[i].reason}</li>`;
            }
            html += '</ul>';

            const wrapper = document.createElement('div');
            wrapper.innerHTML = html;

            if(invalidFiles.length > 0)
            {
                swal({
                    title: "<?= __("Error") ?>",
                    content: wrapper,
                    icon: "error",
                    confirmButtonText: "<?= __("OK") ?>",
                    allowOutsideClick: "true"
                });
            }

            var dataTransfer = new DataTransfer();

            validFiles.forEach(function(file) {
                dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
            });

            document.getElementById('imageInput').files = dataTransfer.files;  // Update input files

            let newFiles = validFiles;
            allFiles = [...allFiles, ...newFiles];
            renderPreviews();

        }

        // Function to update the file input with only valid files
        function updateFileInput(validFiles) {

            var dataTransfer = new DataTransfer();

            validFiles.forEach(function(file) {
                dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
            });

            document.getElementById('imageInput').files = dataTransfer.files;  // Update input files

            let newFiles = validFiles;
            allFiles = [...allFiles, ...newFiles];
            renderPreviews();
        }

        function renderPreviews() {
            let previewContainer = document.getElementById('imagePreviewContainer');
            previewContainer.innerHTML = '';

            allFiles.forEach((file, index) => {
                let li = document.createElement('li');
                li.classList.add('image-thumbnail');

                let fileName = file.name;
                let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
                let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);

                let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
                shortName += '.' + extension;

                if (file.url) {
                    li.innerHTML = `
                        <img src="${file.url}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                } else {
                    let reader = new FileReader();
                    reader.onload = function (e) {
                        li.innerHTML = `
                        <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    };
                    reader.readAsDataURL(file);
                }

                previewContainer.appendChild(li);
            });
        }

        document.getElementById('imagePreviewContainer').addEventListener('click', function (e) {
            if (e.target.closest('.delete-img-btn')) {
                let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
                allFiles.splice(index, 1);

                //Remove file from input
                var dataTransfer = new DataTransfer();

                allFiles.forEach(function(file) {
                    dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
                });

                document.getElementById('imageInput').files = dataTransfer.files;

                renderPreviews();
            }
        });

    });

</script>
<?php $this->end(); ?>
