<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Supplier Entity
 *
 * @property int $id
 * @property string $name
 * @property string $contact_name
 * @property string $contact_email
 * @property string $phone_number
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\SupplierGroup $supplier_group
 * @property \App\Model\Entity\Product[] $products
 * @property \App\Model\Entity\SupplierCreditNote[] $supplier_credit_notes
 * @property \App\Model\Entity\SupplierPayment[] $supplier_payments
 * @property \App\Model\Entity\SupplierPurchaseOrder[] $supplier_purchase_orders
 * @property \App\Model\Entity\SupplierShowroom[] $supplier_showrooms
 * @property \App\Model\Entity\SupplierStock[] $supplier_stocks
 */
class SupplierPayment extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'supplier_id' => true,
        'supplier_purchase_order_id' => true,
        'showroom_id' => true,
        'payment_mode' => true,
        'cheque_no' => true,
        'cheque_date' => true,
        'bank_name' => true,
        'cheque_copy' => true,
        'payee_name' => true,
        'payment_date' => true,
        'amount' => true,
        'receipt_number' => true,
        'receipt' => true,
        'paid_by' => true,
        'created' => true,
        'modified' => true,
    ];
}
