<?php

namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\ORM\TableRegistry;
use Cake\Mailer\Mailer;
use Cake\Core\Configure;
use Cake\Utility\Text;
use Cake\Event\Event;
use Cake\Utility\Security;
use Cake\Auth\DefaultPasswordHasher;
//use QRcode;
use Cake\Http\Session;
use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Twilio\Rest\Client;
use SendGrid\Mail\Mail;
use Cake\View\View;
use Cake\Routing\Router;

//require_once(ROOT .DS. "vendor" . DS  . 'qrcode' . DS . 'qrlib.php');
// Testing

class MtnComponent extends Component
{
    protected $PaymentMethods;
    protected $PaymentMethodSettings;
    protected $MtnTokens;
    protected $client_id;
    protected $client_secret;
    protected $subscription_key;
    protected $targetEnv ;
    protected $currency ;
    protected $baseApiUrl;

    public function initialize($config): void
    {
        parent::initialize($config);

        $this->PaymentMethods = TableRegistry::getTableLocator()->get('PaymentMethods');
        $this->PaymentMethodSettings = TableRegistry::getTableLocator()->get('PaymentMethodSettings');
        $this->MtnTokens = TableRegistry::getTableLocator()->get('MtnTokens');

        // Fetch settings specific to MTN MoMo
        $settings_data = $this->fetchSettings('MTN MoMo');
        // Store settings in the component
        foreach ($settings_data as $setting) {
            $settings[$setting->attribute] = $setting->value;
        }
        $this->client_id = $settings['client_id'] ?? null;
        $this->client_secret = $settings['client_secret'] ?? null;
        $this->subscription_key = $settings['subscription_key'] ?? null;

        // Get the environment setting (sandbox or live)
        $environment = Configure::read('Settings.PG_ENVIRONMENT'); // 'sandbox' or 'live'

        // Define the base URL based on the environment
        if ($environment === 'sandbox') {
            $this->baseApiUrl = 'https://sandbox.momodeveloper.mtn.com';  // Sandbox API base URL
            //X-Target-Environment
            $this->targetEnv = 'sandbox';
            $this->currency  = 'EUR';

        } else {
            $this->baseApiUrl = 'https://proxy.momoapi.mtn.com';  // Live API base URL
            //X-Target-Environment
            $this->targetEnv = 'mtnivorycoast';
            $this->currency  = 'XOF';
        }
    }

    protected function fetchSettings(string $paymentName)
    {
        // Fetch the payment method by name
        $paymentMethod = $this->PaymentMethods->find()
            ->select(['id'])
            ->where(['name' => $paymentName])
            ->first();

        if (!$paymentMethod) {
            throw new \RuntimeException("Payment method '{$paymentName}' not found.");
        }

        // Fetch settings for the payment method
        $settings = $this->PaymentMethodSettings->find()
            ->select(['attribute', 'value'])
            ->where(['payment_method_id' => $paymentMethod->id])
            ->all();

        return $settings;
    }

    //Step 1: Get an Access Token (with Error Handling)
    public function getAccessToken() {

        $client_id = $this->client_id ?? null;
        $client_secret = $this->client_secret ?? null;
        $subscription_key = $this->subscription_key ?? null;

        // Check if a token is already stored and still valid
        // Get the most recent token
        $token = $this->MtnTokens->find()
            ->order(['created_at' => 'DESC'])
            ->first();

        if ($token && strtotime($token->expires_at) > time()) {
            return $token->access_token;
        }

        // Generate a new token
        $auth_url = $this->baseApiUrl."/collection/token/";
        $credentials = base64_encode("$client_id:$client_secret");

        $headers = [
            "Authorization: Basic $credentials",
            "Ocp-Apim-Subscription-Key: $subscription_key"
        ];

        $curl = curl_init($auth_url);
        curl_setopt_array($curl, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POST => true,
        ]);

        $response = curl_exec($curl);
        if (curl_errno($curl)) {
            echo "Error: " . curl_error($curl);
            return null;
        }

        curl_close($curl);
        $response_data = json_decode($response, true);

        if (isset($response_data['access_token'])) {
            // Store the token and its expiration time in db
            $accessToken = $response_data['access_token'];
            $expiresIn = $response_data['expires_in'];

            $tokenEntity = $this->MtnTokens->newEntity([
                'access_token' => $accessToken,
                'expires_at' => date('Y-m-d H:i:s', time() + $expiresIn)
            ]);
            $this->MtnTokens->save($tokenEntity);

            return $response_data['access_token'];
        } else {
            echo "Failed to retrieve access token. Response: " . $response;
            return null;
        }
    }

    //Step 2: Initiate Payment Request (with Error Handling)
    public function initiatePayment($order_no, $amount, $customer_phone) {

        $subscription_key = $this->subscription_key ?? null;
        //access token
        $access_token = $this->getAccessToken();
        if(!$access_token) {
            return null;
        }
        $targetEnv = $this->targetEnv;
        $currency  = $this->currency;

        //X-Reference-Id
        $referenceId = $this->generateUuidV4();
        //X-Callback-Url  (URL to the server where the callback should be sent.)
        $callbackUrl = Configure::read('Settings.SITE_URL').'Webhooks/mtnWebhookListener';
        //$callbackUrl = Router::url(['controller' => 'Webhooks', 'action' => 'mtnWebhookListener'], true);

        $payment_url = $this->baseApiUrl."/collection/v1_0/requesttopay";

        $headers = [
            "Authorization: Bearer $access_token",
            "X-Reference-Id: $referenceId",
            "X-Target-Environment: $targetEnv",
            /*"X-Callback-Url: $callbackUrl",*/
            "Content-Type: application/json",
            "Ocp-Apim-Subscription-Key: $subscription_key"
        ];

        $payment_data = [
            "amount" => $amount,
            "currency" => $currency,
            "externalId" => $order_no,
            "payer" => [
                "partyIdType" => "MSISDN",
                "partyId" => $customer_phone
            ],
            "payerMessage" => "Payment for Order $order_no",
            "payeeNote" => "Thank you for your purchase"
        ];

        //print_r(expression)

        $curl = curl_init($payment_url);
        curl_setopt_array($curl, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => json_encode($payment_data),
            CURLOPT_POST => true,
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            echo "Error: " . curl_error($curl);
            return null;
        }

        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        $response_data = json_decode($response, true);

        if ($httpCode == 202) {

            //$res_result = ['httpcode'=>$httpCode, 'referenceId'=>$referenceId, 'message'=>'Request Accepted'];

            //get payment status
            $res_result = $this->checkPaymentStatus($referenceId, $access_token);

        } else {
            $res_result = ['status' => 'error', 'httpcode'=>$httpCode, 'referenceId'=>$referenceId, 'message'=>$response_data['message'], 'error_code'=>$response_data['code']];
        }

        return $res_result;
    }

    //Step 3: Check Transaction/Payment Status (with Error Handling)
    public function checkPaymentStatus($referenceId, $access_token) {

        $subscription_key = $this->subscription_key ?? null;
        //access token
        /*$access_token = $this->getAccessToken();
        if(!$access_token) {
            return null;
        }*/

        $targetEnv = $this->targetEnv;
        $status_url = $this->baseApiUrl."/collection/v1_0/requesttopay/$referenceId";
        $headers = [
            "Authorization: Bearer $access_token",
            "Ocp-Apim-Subscription-Key: $subscription_key",
            "X-Target-Environment: $targetEnv"
        ];

        $curl = curl_init($status_url);
        curl_setopt_array($curl, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
        ]);

        $response = curl_exec($curl);
        if (curl_errno($curl)) {
            echo "Error: " . curl_error($curl);
            return null;
        }

        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        $response_data = json_decode($response, true);

        if ($httpCode == 200) { //PENDING, SUCCESSFUL, FAILED
            if($response_data['status'] == 'FAILED') {
                $message = __('Payment Failed.');
                $res_result = ['status'=>'error', 'httpcode'=>$httpCode, 'data'=>$response_data, 'payment_status'=>$response_data['status'], 'reason'=>$response_data['reason'], 'message'=>$message];

            } elseif($response_data['status'] == 'SUCCESSFUL') {
                $message = __('Payment was successful.');
                $res_result = ['status'=>'success', 'httpcode'=>$httpCode, 'data'=>$response_data, 'payment_status'=>$response_data['status'], 'message'=>$message];

            } else{
                $message = __('Payment is still pending.');
                $res_result = ['status'=>'success', 'httpcode'=>$httpCode, 'data'=>$response_data, 'payment_status'=>$response_data['status'], 'message'=>$message];
            }
        } else {
            $res_result = ['status'=>'error', 'httpcode'=>$httpCode, 'message'=>$response_data['message'], 'error_code'=>$response_data['code']];
        }
        return $res_result;
    }

    public function generateUuidV4() {
        // Generate 16 random bytes
        $data = random_bytes(16);

        // Set the version to 4 (randomly generated UUID)
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);  // 4th version (0x40 indicates version 4)

        // Set the variant to RFC4122 standard (start with 0x80)
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);  // variant is 10xx (0x80)

        // Format the bytes into a UUID string
        return vsprintf(
            '%s%s-%s-%s-%s-%s%s%s',
            str_split(bin2hex($data), 4)
        );
    }

}

?>
