<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;

/**
 * ProductVariants Controller
 *
 * @property \App\Model\Table\ProductVariantsTable $ProductVariants
 */
class ProductVariantsController extends AppController
{

    protected $ProductVariantImages;
    protected $ProductStocks;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->ProductVariantImages = $this->fetchTable('ProductVariantImages');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
    }
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->ProductVariants->find()
            ->contain(['Products']);
        $productVariants = $this->paginate($query);

        $this->set(compact('productVariants'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Variant id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function viewProductVariants($id = null)
    {
        $this->request->allowMethod(['get']);
        $productVariant = $this->ProductVariants->get($id, [
            // 'contain' => ['Suppliers']
        ]);
        if ($productVariant) {
            $response = [
                'status' => 'success',
                'data' => [
                    'variant_name' => $productVariant->variant_name,
                    'variant_sku' => $productVariant->sku,
                    'variant_size' => $productVariant->variant_size,
                    'variant_weight' => $productVariant->variant_weight,
                    'variant_description' => $productVariant->variant_description
                ]
            ];
        } else {
            $response = ['status' => 'error', 'message' => 'Product Variant not found'];
        }
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function addProductVariants()
    {
        $productVariant = $this->ProductVariants->newEmptyEntity();
        if ($this->request->is('post')) {
            $productVariant = $this->ProductVariants->patchEntity($productVariant, $this->request->getData());
            if ($this->ProductVariants->save($productVariant)) {
                if (!empty($this->request->getData('variant_media'))) {
                    $this->handleVariantFileUploads($productVariant->id, 'Image');
                }
                $response = ['status' => 'success', 'message' => __('The product variant has been saved.')];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                return $this->response;
            }
            $this->log('saved' . json_encode($productVariant->getErrors()), 'debug');
            $response = ['status' => 'error', 'message' => __('The product variant could not be saved. Please, try again.')];
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Product Variant id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function editProductVariants($id = null)
    {
        if ($id === null) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Invalid ID']));
        }

        $productVariant = $this->ProductVariants->get($id, [
            'contain' => ['ProductVariantImages' => function ($q) {
                return $q->where(['ProductVariantImages.status' => 'A', 'ProductVariantImages.media_type' => 'Image']);
            }]
        ]);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $productVariant = $this->ProductVariants->patchEntity($productVariant, $this->request->getData());
            if ($this->ProductVariants->save($productVariant)) {
                if (!empty($this->request->getData('variant_media'))) {
                    $this->handleVariantFileUploads($productVariant->id, 'Image');
                }
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([
                        'status' => 'success',
                        'productVariant' => $productVariant,
                        'message' => 'Product variant saved'
                    ]));
            }
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Could not save the product variant.']));
        }

        if (!empty($productVariant->product_variant_images)) {
            foreach ($productVariant->product_variant_images as &$image) {

                $fileName = basename($image->image);
                $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                $nameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

                $shortName = strlen($nameWithoutExtension) > 14 ? substr($nameWithoutExtension, 0, 11) : $nameWithoutExtension;
                $shortName .= '.' . $extension;

                $image->fileName = $fileName;
                $image->extension = $extension;
                $image->nameWithoutExtension = $nameWithoutExtension;
                $image->shortName = $shortName;
                $image->image = $this->Media->getCloudFrontURL($image->image);
            }
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'productVariant' => $productVariant,
                'productVariantImages' => $productVariant->product_variant_images
            ]));
    }

    /**
     * Delete method
     *
     * @param string|null $id Product Variant id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function deleteProductVariants($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $productVariant = $this->ProductVariants->get($id);
        $response = ['success' => false, 'message' => 'The product variant could not be deleted. Please, try again.'];
        if ($productVariant) {
            if ($this->ProductVariants->delete($productVariant)) {
                $response = ['success' => true, 'message' => 'The product variant has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The product variant could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product variant does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function getVariantsData()
    {
        $product_id = $this->request->getQuery('product_id');
        $variants = $this->ProductVariants->find('all')
            ->where(['ProductVariants.product_id' => $product_id])
            ->where(['ProductVariants.status !=' => 'D'])
            // ->contain(['Suppliers'])
            ->select([
                'id' => 'ProductVariants.id',
                'name' => 'ProductVariants.variant_name',
                'sku' => 'ProductVariants.sku',
                'variant_size' => 'ProductVariants.variant_size',
                'reference_name' => 'ProductVariants.reference_name',
                // 'supplier' => 'Suppliers.name',
                'price' => 'ProductVariants.sales_price',
                'status' => 'ProductVariants.status'
            ])
            ->order(['ProductVariants.variant_name' => 'ASC'])
            ->toArray();

        $response = ['variants' => $variants];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function getProductPrices()
    {
        $this->request->allowMethod(['post']);
        $id = $this->request->getData('id');
        // $showrooms = $this->request->getData('showrooms') ?? [];

        $product_variant = $this->ProductVariants->find()
            ->where(['id' => $id])
            ->first();

        $supplier_latest_price = $this->ProductVariants->SupplierProducts
            ->find()
            ->select(['supplier_price'])
            ->where(['product_variant_id' => $id, 'status' => 'A'])
            ->order(['id' => 'DESC'])
            ->first();


        if ($product_variant) {
            $response = [
                'status' => 'success',
                'message' => __('Product data retrieved successfully.'),
                'data' => $product_variant,
                'supplier_latest_price' => isset($supplier_latest_price) ? $supplier_latest_price->supplier_price : '0.00'
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => __('No product found.'),
                'data' => null,
                'supplier_latest_price' => ''
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function addProductPrices()
    {

        $product_variant = $this->ProductVariants->newEmptyEntity();
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $product_variant_id = $data['product_var_id'];
            $product_variant = $this->ProductVariants->get($product_variant_id);
            $product_variant = $this->ProductVariants->patchEntity($product_variant, $this->request->getData());
            $this->log('POST data: ' . json_encode($product_variant), 'debug');
            if ($this->ProductVariants->save($product_variant)) {
                $response = ['status' => 'success', 'message' => __('The product variant prices have been saved.')];
            } else {
                $response = ['status' => 'error', 'message' => __('The product variant prices could not be saved. Please, try again.')];
            }
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($response));
        }
    }

    public function saveVariantImages()
    {
        $variantId = $this->request->getData('variant_id');
        $uploadedImages = $this->handleVariantFileUploads($variantId, 'Image');

        if (!empty($uploadedImages)) {
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'success', 'message' => __('Images uploaded successfully.')]));
        } else {
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => __('No images were uploaded.')]));
        }

        return $this->response;
    }


    public function saveVariantVideos()
    {
        $variantId = $this->request->getData('variant_id');
        $uploadedVideos = $this->handleVariantFileUploads($variantId, 'Video');

        if (!empty($uploadedVideos)) {
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'success', 'message' => __('Images uploaded successfully.')]));
        } else {
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => __('No images were uploaded.')]));
        }

        return $this->response;
    }


    private function handleVariantFileUploads($variantId, $type = '')
    {
        $files = $this->request->getData('variant_media');
        $uploadedImages = [];
        $i = 0;

        if ($type == 'Image') {


            $exists = $this->ProductVariantImages->find()
                ->where([
                    'variant_id' => $variantId,
                    'media_type' => 'Image',
                    'image_default' => 1,
                    'status' => 'A'
                ])
                ->first();

            foreach ($files as $file) {
                if ($file->getError() === UPLOAD_ERR_OK) {
                    $fileName = trim($file->getClientFilename());

                    if (!empty($fileName)) {
                        $imageTmpName = $file->getStream()->getMetadata('uri');

                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.PRODUCT_VARIANT_IMAGE');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                        } else {
                            $variantImage = $this->ProductVariantImages->newEmptyEntity();
                            $variantImage->variant_id = $variantId;
                            $variantImage->image = $folderPath . $imageFile;
                            $variantImage->media_type = 'Image';

                            if (!$exists && $i == 0) {
                                $variantImage->image_default = 1;
                            } else {
                                $variantImage->image_default = 0;
                            }
                            $i++;
                            if ($this->ProductVariantImages->save($variantImage)) {
                                $uploadedImages[] = $folderPath . $imageFile;
                            } else {
                                $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                            }
                        }
                    }
                }
            }
        } else if ($type == 'Video') {
            foreach ($files as $file) {
                if ($file->getError() === UPLOAD_ERR_OK) {
                    $fileName = trim($file->getClientFilename());

                    if (!empty($fileName)) {
                        $imageTmpName = $file->getStream()->getMetadata('uri');

                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.PRODUCT_VARIANT_VIDEO');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__('Video ' . $fileName . ' could not be uploaded. Please, try again.'));
                        } else {
                            $variantImage = $this->ProductVariantImages->newEmptyEntity();
                            $variantImage->variant_id = $variantId;
                            $variantImage->video = $folderPath . $imageFile;
                            $variantImage->media_type = 'Video';
                            $i++;
                            if ($this->ProductVariantImages->save($variantImage)) {
                                $uploadedImages[] = $folderPath . $imageFile;
                            } else {
                                $this->Flash->error(__('Video ' . $fileName . ' could not be saved. Please, try again.'));
                            }
                        }
                    }
                }
            }
        }
        return $uploadedImages;
    }

    public function getVariants()
    {
        $this->request->allowMethod(['get']);
        $productId = $this->request->getQuery('product_id');

        if (!$productId) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => false, 'message' => 'Invalid product ID']));
        }

        $variants = $this->ProductVariants->find('list', [
            'keyField' => 'id',
            'valueField' => 'variant_name',
            'conditions' => ['product_id' => $productId, 'status' => 'A']
        ])->toArray();

        if (empty($variants)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => false, 'message' => 'No variants found for this product.']));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'variants' => $variants]));
    }

    public function getProductVarPrices()
    {
        $this->autoRender = false;
        $this->request->allowMethod(['post']);

        $productVarId = $this->request->getData('id');
        $productId = $this->request->getData('productId');
        $showrooms = $this->request->getData('showrooms') ?? [];

        $product_variant = $this->ProductVariants
            ->find()
            ->where(['id' => $productVarId, 'status' => 'A'])
            ->first();

        if ($showrooms) {
            $avlQuantity = $this->ProductStocks->find()
                ->select([
                    'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
                ])
                ->where(['showroom_id IN' => $showrooms, 'product_id' => $productId, 'product_variant_id is' => $productVarId, 'product_attribute_id is' => null])
                ->first();
        } else {
            $avlQuantity = $this->ProductStocks->find()
                ->select([
                    'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
                ])
                ->where(['product_id' => $productId, 'product_variant_id is' => $productVarId, 'product_attribute_id is' => null])
                ->first();
        }

        $availableQuantity = $avlQuantity ? max(0, (int) $avlQuantity->available_quantity) : 0;

        if ($product_variant) {
            $response = [
                'status' => 'success',
                'promotion_price' => $product_variant->promotion_price,
                'variant_size' => $product_variant->variant_size,
                'variant_weight' => $product_variant->variant_weight,
                'discount_price' => ($product_variant->sales_price ?? 0) - ($product_variant->promotion_price ?? 0),
                'availableQuantity' => $availableQuantity ?? 0
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Product not found.'
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }
}
