<?php
declare(strict_types=1);

namespace App\Enum;

enum ExpenseStatus: string
{
    case Active = 'A';
    case Inactive = 'I';
    case Deleted = 'D';

    public static function toArray(): array
    {
        return [
            self::Active->value => 'Active',
            self::Inactive->value => 'Inactive',
            self::Deleted->value => 'Deleted',
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::Active => 'Active',
            self::Inactive => 'Inactive',
            self::Deleted => 'Deleted',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::Active => 'col-green',
            self::Inactive => 'col-blue',
            self::Deleted => 'col-red',
        };
    }

    /**
     * Returns an associative array of enum labels for use in a dropdown.
     *
     * This method returns an array where the keys are the enum values (the string values)
     * and the values are the corresponding human-readable labels. This is useful for
     * populating a dropdown list in a form.
     *
     * @return array<string> An associative array where the keys are enum values and
     *                       the values are human-readable labels.
     */
    public static function toDropdownArray(): array
    {
        return array_map(fn ($status) => $status->label(), self::cases());
    }

    /**
     * Returns an array of enum values for use in validation (for the `inList` rule).
     *
     * This method returns an array where the values are the actual enum values
     * (e.g., 'Unpaid', 'Paid'). This is useful for validation rules like `inList`.
     *
     * @return array<string> An array of enum values.
     */
    public static function toDropdownValues(): array
    {
        return array_keys(self::toArray());
    }
}
