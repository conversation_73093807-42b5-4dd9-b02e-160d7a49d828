<?php

declare(strict_types=1);

namespace App\Controller;

/**
 * RelatedProducts Controller
 *
 * @property \App\Model\Table\RelatedProductsTable $RelatedProducts
 */
class RelatedProductsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->RelatedProducts->find()
            ->contain(['Products']);
        $relatedProducts = $this->paginate($query);

        $this->set(compact('relatedProducts'));
    }

    /**
     * View method
     *
     * @param string|null $id Related Product id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $relatedProduct = $this->RelatedProducts->get($id, contain: ['Products']);
        $this->set(compact('relatedProduct'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $relatedProduct = $this->RelatedProducts->newEmptyEntity();
        if ($this->request->is('post')) {
            $relatedProduct = $this->RelatedProducts->patchEntity($relatedProduct, $this->request->getData());
            if ($this->RelatedProducts->save($relatedProduct)) {
                $this->Flash->success(__('The related product has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The related product could not be saved. Please, try again.'));
        }
        $products = $this->RelatedProducts->Products->find('list', limit: 200)->all();
        $this->set(compact('relatedProduct', 'products'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Related Product id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $relatedProduct = $this->RelatedProducts->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $relatedProduct = $this->RelatedProducts->patchEntity($relatedProduct, $this->request->getData());
            if ($this->RelatedProducts->save($relatedProduct)) {
                $this->Flash->success(__('The related product has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The related product could not be saved. Please, try again.'));
        }
        $products = $this->RelatedProducts->Products->find('list', limit: 200)->all();
        $this->set(compact('relatedProduct', 'products'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Related Product id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function deleteRelatedProducts($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $rel_prod = $this->RelatedProducts->get($id);
        $response = ['success' => false, 'message' => 'The related product relation could not be deleted. Please, try again.'];
        if ($rel_prod) {
            if ($this->RelatedProducts->delete($rel_prod)) {
                $response = ['success' => true, 'message' => 'The related product relation has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The related product relation could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The related product relation does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function addRelatedProduct()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $data = $this->request->getData();
        $productId = $data['product_id'];
        $relatedIds = $data['related_id'];

        $successCount = 0;
        $errorMessages = [];

        foreach ($relatedIds as $relatedId) {

            $relatedProduct = $this->RelatedProducts->newEmptyEntity();
            $relatedProduct->product_id = $productId;
            $relatedProduct->related_id = $relatedId;
            $relatedProduct->status = 'A';

            if ($this->RelatedProducts->save($relatedProduct)) {
                $successCount++;
            } else {
                $errorMessages[] = __('Error saving related product for Product ID: ') . $productId;
            }
        }

        if ($successCount > 0) {
            $response = [
                'status' => 'success',
                'message' => __('Successfully added {0} related products.', $successCount),
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => !empty($errorMessages) ? implode(', ', $errorMessages) : __('Failed to add related products.')
            ];
        }

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));

        return $this->response;
    }

    public function getRelatedProducts()
    {
        $this->request->allowMethod(['get']);
        $product_id = $this->request->getQuery('product_id');

        $related_products = $this->RelatedProducts->find('all')
            ->contain(['Products', 'RelatedProduct']) 
            ->where(['RelatedProducts.product_id' => $product_id]) 
            ->where(['RelatedProducts.status <>' => 'D'])
            ->select([
                'rel_prod_id' => 'RelatedProducts.id',
                'id' => 'RelatedProducts.related_id',
                'main_product_name' => 'Products.name', 
                'main_product_sku' => 'Products.sku',
                'main_product_reference_name' => 'Products.reference_name',
                'related_product_name' => 'RelatedProduct.name', 
                'related_product_sku' => 'RelatedProduct.sku',
                'related_product_reference_name' => 'RelatedProduct.reference_name',
                'status' => 'RelatedProducts.status'
            ])
            ->order(['RelatedProduct.name' => 'ASC'])
            ->toArray();

        $response = ['related_products' => $related_products];

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));

        return $this->response;
    }


    public function toggleRelatedProdStatus($rel_prod_id)
    {
        $this->request->allowMethod(['ajax']);

        $relatedProduct = $this->RelatedProducts->get($rel_prod_id);

        if ($relatedProduct->status == 'A') {
            $relatedProduct->status = 'I';
            $responseMessage = 'The related product has been made inactive.';
        } else if ($relatedProduct->status == 'I') {
            $relatedProduct->status = 'A';
            $responseMessage = 'The related product has been made active.';
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Invalid status for the related product.'
            ];
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        if ($this->RelatedProducts->save($relatedProduct)) {
            $response = [
                'status' => 'success',
                'message' => $responseMessage
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Unable to change the status of the related product. Please try again.'
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }
}
