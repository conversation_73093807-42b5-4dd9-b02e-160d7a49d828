<?php

namespace App\Controller\Component;

use Cake\Controller\Component;

use Cake\ORM\TableRegistry;
use Cake\Mailer\Mailer;
use Cake\Core\Configure;
use Cake\Utility\Text;
use Cake\Event\Event;
use Cake\Utility\Security;
use Cake\Auth\DefaultPasswordHasher;
//use QRcode;
use Cake\Http\Session;
use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Twilio\Rest\Client;
use SendGrid\Mail\Mail;
use Cake\View\View;

//require_once(ROOT .DS. "vendor" . DS  . 'qrcode' . DS . 'qrlib.php');
// Testing

class StockComponent extends Component
{

	protected $StockMovements;
	protected $StockMovementItems;
	protected $ProductStocks;

	public function initialize($config): void
    {
        parent::initialize($config);
        
        $this->StockMovements = TableRegistry::getTableLocator()->get('StockMovements');
        $this->StockMovementItems = TableRegistry::getTableLocator()->get('StockMovementItems');
        $this->ProductStocks = TableRegistry::getTableLocator()->get('ProductStocks');


        //$this->Products = $this->getController()->loadModel('Products');
    }


	/*
		* Add Incoming/Outgoing Stock to/from Warehouse/Showroom
		* reference_type ('stock_request', 'purchase', 'return')
		* referenceID (stock_request_id, order_id, stock_return_id)
		* movement_type (Incoming, Outgoing)
		* $ws (Warehouse or Showroom), $ws_id (warehouse_id or showroom_id)
	*/

	//Add Incoming Stock to Warehouse	
    public function addWarehouseInStock ($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id){

    	$ws = 'Warehouse';
    	$movement_type = 'Incoming';
    	$addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
    	if($addStockID) {

    		foreach ($stock_items as $key => $value) {

    			$addData = array();
    			$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value);

    			// Base condition with product_id
		        $conditions = [
		            'warehouse_id' => $warehouse_id,
		            'product_id' => $value['product_id']
		        ];

		        // Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
				    $conditions['product_variant_id'] = $value['product_variant_id'];
				}

				// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
				    $conditions['product_attribute_id'] = $value['product_attribute_id'];
				}

		        // Find the existing product stock
		        $stockExist = $this->ProductStocks->find()
		            ->where($conditions)
		            ->first();

    			if($stockExist){
    				$productStockID = $stockExist->id;
    				$existQuantity  = $stockExist->quantity;
    				$increaseQuantity = $existQuantity + $value['quantity'];
    				$addData['quantity'] = $increaseQuantity;
    				$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $addData);

    			} else {
    				$addProductStock = $this->ProductStocks->addProductStock($ws, $warehouse_id, $value);
    			}    			    			
    		}
    		return true;
    	} else {
    		return false;
    	}
    }

    public function addWarehouseInStockWithoutUpdatingProductStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id) {
	    $ws = 'Warehouse';
	    $movement_type = 'Incoming';
	    
	    $addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
	    
	    if ($addStockID) {
	        foreach ($stock_items as $value) {
	            // Add stock movement items
	            $this->StockMovementItems->addStockItems($addStockID, $value);
	        }
	        return true;
	    }
	    return false;
	}

	public function addWarehouseOutStockWithoutUpdatingProductStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id) {
	    $ws = 'Warehouse';
	    $movement_type = 'Outgoing';

	    $addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
	    
	    if ($addStockID) {
	        foreach ($stock_items as $value) {
	            // Add stock movement items
	            $this->StockMovementItems->addStockItems($addStockID, $value);
	        }
	        return true;
	    }
	    return false;
	}

    //Add Incoming Stock to Showroom
    public function addShowroomInStock ($showroom_id, $movement_date, $reference_type, $referenceID, $stock_items, $image, $driver_id){

    	$ws = 'Showroom';
    	$movement_type = 'Incoming';
    	$addStockID = $this->StockMovements->addStock($ws, $showroom_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
    	if($addStockID) {
    		foreach ($stock_items as $key => $value) {

    			$addData = array();
    			$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value);
    			
	            //find the existing product stock
	    		// $stockExist = $this->ProductStocks->find()
			    //     ->where(['showroom_id' => $showroom_id, 'product_id' => $value['product_id']])
			    //     ->first();

    			// Base condition with product_id
		        $conditions = [
		            'showroom_id' => $showroom_id,
		            'product_id' => $value['product_id']
		        ];

		        // Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
				    $conditions['product_variant_id'] = $value['product_variant_id'];
				}

				// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
				    $conditions['product_attribute_id'] = $value['product_attribute_id'];
				}
				
		        // Find the existing product stock
		        $stockExist = $this->ProductStocks->find()
		            ->where($conditions)
		            ->first();

    			if($stockExist){
    				$productStockID = $stockExist->id;
    				$existQuantity  = $stockExist->quantity;
    				$increaseQuantity = $existQuantity + $value['quantity'];
    				$addData['quantity'] = $increaseQuantity;
    				$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $addData);
    			} else {
    				$addProductStock = $this->ProductStocks->addProductStock($ws, $showroom_id, $value);
    			}    			    			
    		}
    		return true;
    	} else {
    		return false;
    	}
    }


    //Add Outgoing Stock from Warehouse
    public function addWarehouseOutStock ($warehouse_id, $movement_date, $reference_type, $referenceID, $stock_items, $image, $driver_id){

    	$ws = 'Warehouse';
    	$movement_type = 'Outgoing';
    	$addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
    	if($addStockID) {
    		foreach ($stock_items as $key => $value) {

    			$reduceData = array();
    			$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value);

    			// Base condition with product_id
		        $conditions = [
		            'warehouse_id' => $warehouse_id,
		            'product_id' => $value['product_id']
		        ];

		        // Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
				    $conditions['product_variant_id'] = $value['product_variant_id'];
				}

				// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
				    $conditions['product_attribute_id'] = $value['product_attribute_id'];
				}

		        // Find the existing product stock
		        $stockExist = $this->ProductStocks->find()
		            ->where($conditions)
		            ->first();

    			if($stockExist){
    				$productStockID = $stockExist->id;
    				$existQuantity  = $stockExist->quantity;
    				$decreaseQuantity = $existQuantity - $value['quantity'];
    				// $reduceData['quantity'] = $decreaseQuantity;

    				// Prepare update data
				    $reduceData = [
				        'quantity' => $decreaseQuantity,
				        'reduce_reserved_stock' => $value['quantity']  // Deduct from reserved stock
				    ];

    				$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $reduceData);
    			} else {
    				$addProductStock = $this->ProductStocks->addProductStock($ws, $warehouse_id, $value);
    			}    			    			
    		}
    		return true;
    	} else {
    		return false;
    	}
    }

    
    //Add Outgoing Stock from Showroom
    public function addShowroomOutStock ($showroom_id, $movement_date, $reference_type, $referenceID, $stock_items, $image, $driver_id){

    	$ws = 'Showroom';
    	$movement_type = 'Outgoing';
    	$addStockID = $this->StockMovements->addStock($ws, $showroom_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);

    	if($addStockID) {
    		foreach ($stock_items as $key => $value) {

    			$reduceData = array();
    			$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value);
    			
	            //find the existing product stock
	    		// $stockExist = $this->ProductStocks->find()
			    //     ->where(['showroom_id' => $showroom_id, 'product_id' => $value['product_id']])
			    //     ->first();

    			// Base condition with product_id
		        $conditions = [
		            'showroom_id' => $showroom_id,
		            'product_id' => $value['product_id']
		        ];

		        // Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
				    $conditions['product_variant_id'] = $value['product_variant_id'];
				}

				// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
				    $conditions['product_attribute_id'] = $value['product_attribute_id'];
				}
				
		        // Find the existing product stock
		        $stockExist = $this->ProductStocks->find()
		            ->where($conditions)
		            ->first();

    			if($stockExist){
    				$productStockID = $stockExist->id;
    				$existQuantity  = $stockExist->quantity;
    				$decreaseQuantity = $existQuantity - $value['quantity'];
    				// $reduceData['quantity'] = $decreaseQuantity;

    				// Prepare update data
				    $reduceData = [
				        'quantity' => $decreaseQuantity,
				        'reduce_reserved_stock' => $value['quantity']  // Deduct from reserved stock
				    ];
    					
    				$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $reduceData);
    			} else {
    				$addProductStock = $this->ProductStocks->addProductStock($ws, $showroom_id, $value);
    			}    			    			
    		}
    		return true;
    	} else {
    		return false;
    	}
    }


}

?>