<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Order $order
 * @var \Cake\Collection\CollectionInterface|string[] $customers
 * @var \Cake\Collection\CollectionInterface|string[] $customerAddresses
 * @var \Cake\Collection\CollectionInterface|string[] $offers
 * @var \Cake\Collection\CollectionInterface|string[] $showrooms
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/intlTelInput/css/intlTelInput.min.css') ?>">
    <script src="<?= $this->Url->webroot('bundles/intlTelInput/js/intlTelInput.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"></script>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
    <link rel="stylesheet"
        href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">

    <style>
        .details {
            display: none;
            /* Hide details initially */
        }

        .qr-code-section {
            display: none;
            margin-top: 10px;
        }

        .label {
            width: max-content !important;
        }

        .bg-green-light {
            background-color: rgb(173 253 173 / 61%);
            /* A light green color */
        }

        .fist-table-header {
            border-top-left-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        .end-table-header {
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .card,
        .card-body {
            border-radius: 10px !important;
        }

        #checkbox {
            height: max-content !important;
            width: max-content !important;
        }

        .bg-light-purple {
            background-color: #d1b3e0;
            /* A lighter purple */
        }

        .card {
            background-color: #fff !important;
            box-shadow: 0 .46875rem 2.1875rem rgba(90, 97, 105, .1), 0 .9375rem 1.40625rem rgba(90, 97, 105, .1), 0 .25rem .53125rem rgba(90, 97, 105, .12), 0 .125rem .1875rem rgba(90, 97, 105, .1) !important;
        }

        tbody {
            background-color: #FDF3E8 !important;
        }

        #add-customer-btn {
            padding: 3px 9px !important;
            font-size: 8px !important;
            font-weight: bold;
        }

        .table thead th,
        .table tbody td {
            height: 30px;
            /* Adjust as needed */
        }

        .pickup-from-location {
            background-color: #F9F5FF;
            /* Lighter purple */
            color: #53389E;
            /* Darker purple */
            font-weight: bold;
            padding: 10px 15px;
            border-radius: 5px;
            border: 1px solid #53389E;
            margin-right: 15px;
        }

        #final-order-summary {
            background-color: #fff7ee !important;
            box-shadow: 0 !important;

        }

        .custom-input {
            border: 1px solid #F77F00;
            height: 30px;
            width: 170px;
            border-radius: 10px;
        }

        .custom-input::placeholder {
            font-style: roboto !important;
            font-size: 12px;
        }

        #float-rigt-btn {}

        #add-new-cust-btn {
            background-color: #0d839b !important;
            color: white !important;
            margin-top: 25px;
        }

        #add-new-cust-icon {
            background-color: #0d839b !important;
            color: white;
        }

        .or-border {
            border-bottom: 1px solid #F77F00;
            width: 100px !important;
            margin-top: 20px;
        }

        #or-text {
            background-color: #fff;
            position: relative;
            top: 12px;
            left: 38px;
            padding: 5px;
        }

        .tel {
            width: 100%;
        }

        .iti {
            width: 100%;
        }

        .iti .iti-flag {
            width: 20px;
            height: 14px;
            margin-right: 5px;
        }

        .iti .iti-country {
            display: flex;
            align-items: center;
        }

        .iti .iti-country .iti-flag {
            margin-right: 5px;

            .iti .iti-country .iti-dial-code {
                font-size: 16px;
                margin-left: 5px;
            }

            .iti {
                width: 100%;
                box-sizing: border-box;
            }

            .iti__input {
                width: 100%;
                box-sizing: border-box;
            }

            .tel {
                width: 100% !important;
                box-sizing: border-box;
            }

        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            display: none;
        }

        .custom-input-select {
            border: 1px solid #F77F00 !important;
            height: 30px !important;
            width: 170px !important;
            border-radius: 10px !important;
            min-height: 30px !important;
        }

        .custom-input-select2 {
            border: 1px solid #F77F00 !important;
            height: 30px !important;
            width: 220px !important;
        }

        .is-invalid-order {
            border-color: #dc3545 !important;
            padding-right: calc(1.5em + .75rem);
            background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }

        .showroom-select {
            width: 100% !important;
            margin-bottom: 10px !important;
        }

        .add-row {
            font-size: 8px !important;
            border-radius: 8px;
            padding: 3px 7px !important;
            font-weight: bold;
        }

        .select2 {
            width: 100% !important;
        }

        .select2-selection {
            border: 1px solid #F77F00 !important;
        }

        #select2-customerDropdown-container {
            display: flex;
            align-items: center;
        }

        #select2-salespersonDropdown-container {
            display: flex;
            align-items: center;
        }

        .sorting_disabled {
            width: 175px !important;
        }

        .is-invalid-select {
            border-color: #dc3545 !important;
            padding-right: calc(1.5em + .75rem);
            background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }

        .address-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
            transition: all 0.3s ease-in-out;
            cursor: pointer;
        }

        .address-box:hover {
            border-color: #007bff;
            background-color: #eef6ff;
        }

        .address-box p {
            margin: 5px 0;
            font-size: 14px;
        }

        .address-box strong {
            font-size: 15px;
            color: #333;
        }

        #map {

            height: 200px;
            width: 50%;
        }
    </style>

</head>
<?php $this->end(); ?>

<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboard', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0">Dashboard</h4>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']) ?>">
                Orders
            </a>
        </li>
        <li class="breadcrumb-item active">
            Add
        </li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
    </a>
</div>
<div class="section-header d-flex justify-content-between align-items-center mb-3 mt-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0 text-dark">ADD ORDER</h4>
        </li>
    </ul>
</div>
<?php echo $this->Form->create($order, ['id' => 'addOrderForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
<div>
    <div class="row">
        <div class="col-12 col-md-6 col-lg-12">

            <div class="card m-t-10 customer-card">
                <div class="card-body">

                    <div style="border-bottom: 1px solid #F77F00;" class="d-flex justify-content-between">

                        <div>
                            <div class="form-group">
                                <label style="color: #F77F00; width: 100px;" for="orderNumber">Order ID</label>
                                <strong class="text-dark"><input type="hidden" id="orderNumber" name="order_number" value=<?= $uniqueOrderId ?>><?= $uniqueOrderId ?></strong>
                            </div>


                            <div class="form-group d-flex">
                                <label style="color: #F77F00; width: 100px;" for="order-date" class="col-sm-2 col-form-label">Order Date</label>
                                <div class="col-sm-9">
                                    <?php echo $this->Form->control('order_date', [
                                        'type' => 'datetime',
                                        'class' => 'form-control datemask',
                                        'id' => 'order-date',
                                        'label' => false,
                                        'required' => true,
                                        'value' => date('Y-m-d\TH:i'),
                                        'max' => date('Y-m-d\TH:i'),
                                        'style' => 'border: 1px solid #F77F00;',
                                        'required' => true,
                                        'readonly' => 'readonly'
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group d-flex">
                                <label for="showroom" class="col-form-label fw-bold">Sales Person</label>
                                <div class="col-sm-8">
                                    <?php echo $this->Form->control('sales_person_id', [
                                        'type' => 'select',
                                        'id' => 'salespersonDropdown',
                                        'options' => $salesperson,
                                        'class' => 'form-input select2 extcus',
                                        'label' => false,
                                        'empty' => 'Select a sales person',
                                        'style' => 'border: 1px solid #F77F00;'
                                    ]) ?>
                                    <div class="invalid-feedback">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="or-border">
                                <span class="text-dark fw-bold" id="or-text">OR</span>
                            </div>
                        </div>

                        <div id="float-rigt-btn">
                            <a class="btn menu-toggle fw-bold" id="add-new-cust-btn" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                                <i class="fas fa-user" id="add-new-cust-icon"></i>
                                ADD NEW CUSTOMER
                            </a>

                        </div>

                    </div>

                    <div class=" d-flex m-t-25 justify-content-between">
                        <?php echo $this->Form->control('customer_id', [
                            'type' => 'hidden',
                            'class' => 'form-control',
                            'id' => 'customer-id',
                            'placeholder' => '',
                            'label' => false,
                            'readonly' => true
                        ]); ?>
                        <input type="hidden" id="customer-group" name="customer_group" value="">

                        <?php echo $this->Form->control('customer_address_id', [
                            'type' => 'hidden',
                            'class' => 'form-control',
                            'id' => 'customer-address-id',
                            'placeholder' => '',
                            'label' => false,
                            'readonly' => true
                        ]); ?>

                        <div class="form-group d-flex">
                            <label for="showroom" class="col-form-label fw-bold">Existing Customer<sup
                                    class="text-danger font-11">*</sup></label>
                            <div class="col-sm-8">
                                <?php echo $this->Form->control('customer_dropdown', [
                                    'type' => 'select',
                                    'id' => 'customerDropdown',
                                    'options' => $customers,
                                    'class' => 'form-input select2 extcus',
                                    'label' => false,
                                    'empty' => 'Select a customer',
                                    'style' => 'border: 1px solid #F77F00;',
                                    'required' => true
                                ]) ?>
                                <div class="invalid-feedback">
                                </div>
                            </div>
                        </div>

                        <div class="form-group d-flex m-r-40">
                            <label for="showroom" class="col-form-label fw-bold">Email</label>
                            <div class="col-sm-8">
                                <?php echo $this->Form->control('email', [
                                    'type' => 'text',
                                    'class' => 'form-input',
                                    'id' => 'customer-email',
                                    'placeholder' => 'Email',
                                    'label' => false,
                                    'readonly' => true,
                                    'style' => 'border: 1px solid #F77F00;',
                                ]); ?>
                            </div>
                        </div>

                    </div>

                    <div class=" d-flex justify-content-between">

                        <div class="form-group d-flex">
                            <label for="showroom" class="col-form-label fw-bold">Name</label>
                            <div class="col-sm-8">
                                <?php echo $this->Form->control('name', [
                                    'type' => 'text',
                                    'class' => 'form-input',
                                    'id' => 'customer-name',
                                    'placeholder' => 'Name',
                                    'label' => false,
                                    'readonly' => true,
                                    'style' => 'border: 1px solid #F77F00;'
                                ]); ?>
                                <div class="invalid-feedback">
                                </div>
                            </div>
                        </div>

                        <div class="form-group d-flex m-r-40">
                            <label for="showroom" class="col-form-label fw-bold">Mobile</label>
                            <div class="col-sm-8">
                                <?php echo $this->Form->control('mobile', [
                                    'type' => 'text',
                                    'class' => 'form-input',
                                    'id' => 'customer-mobile',
                                    'placeholder' => 'Mobile',
                                    'label' => false,
                                    'readonly' => true,
                                    'style' => 'border: 1px solid #F77F00;'
                                ]); ?>

                            </div>
                        </div>

                    </div>

                </div>


            </div>



            <div class="card m-t-20">

                <div class="card-header">
                    <h4 style="color: black !important;">BILLING INFORMATION</h4>
                </div>
                <div class="card-body pt-0 add-order-billing-group">
                    <div class="form-group-radio delievry_mode_options">
                        <ul class="breadcrumb breadcrumb-style m-0 form-group">
                            <li class="form-group d-flex align-items-center justify-content-between">
                                <strong class="pickup-from-location d-flex align-items-center">
                                    <i class="fas fa-store" alt="Download Invoice" style="margin-right: 5px;"></i>
                                    Pickup From Showroom
                                    <input id="pickup-showroom" type="radio" class="form-check-input" name="delivery_mode" value="pickup" style="margin-left: 20px !important;padding:10px" required>
                                </strong>
                            </li>

                            <li class="form-group d-flex align-items-center justify-content-between">
                                <strong class="pickup-from-location d-flex align-items-center">
                                    <i class="fas fa-store" alt="Deliver To Address" style="margin-right: 5px;"></i>
                                    Deliver To Address
                                    <input id="delivery" type="radio" class="form-check-input" name="delivery_mode" value="delivery" style="margin-left: 20px !important;padding:10px" required>
                                </strong>
                            </li>

                        </ul>
                        <div class="invalid-feedback">
                        </div>
                    </div>

                    <div id="showroom-list" class="visually-hidden">
                        <div class="form-group row">
                            <label for="showroom" class="col-sm-2 col-form-label fw-bold">Showroom</label>
                            <div class="col-sm-5">

                                <?php echo $this->Form->control('showroom_id', [
                                    'type' => 'select',
                                    'options' => $showrooms,
                                    'id' => 'showroom-dropdown',
                                    'class' => 'select2',
                                    'empty' => 'Select a Showroom',
                                    'label' => false,
                                    'value' => $showroomPreSel !== '' ? $showroomPreSel : '',
                                    'readonly' => $IsShowroomanager !== '' ? 'readonly' : false,
                                ]); ?>

                                <div class="invalid-feedback">
                                </div>

                            </div>
                        </div>

                    </div>
                    <div id="delivery-div" class="visually-hidden">
                        <div class="form-group row">
                            <label for="house-no" class="col-sm-2 col-form-label fw-bold">House No/Area</label>
                            <div class="col-sm-5">

                                <?php echo $this->Form->control('delivery_address_line_1', [
                                    'type' => 'textarea',
                                    'class' => 'form-control',
                                    'id' => 'house-no',
                                    'placeholder' => 'House No/Area',
                                    'label' => false,
                                    'style' => 'border: 1px solid #F77F00;',
                                    'disabled' => true
                                ]); ?>
                                <div class="invalid-feedback">
                                </div>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="delivery-address-line1" class="col-sm-2 col-form-label fw-bold">Address Line 1</label>
                            <div class="col-sm-5">

                                <?php echo $this->Form->control('address_line1', [
                                    'type' => 'textarea',
                                    'class' => 'form-control',
                                    'id' => 'delivery-address-line1',
                                    'placeholder' => 'Delivery Address Line 1',
                                    'label' => false,
                                    'style' => 'border: 1px solid #F77F00;',
                                    'disabled' => true
                                ]); ?>
                                <div class="invalid-feedback">
                                </div>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="delivery-address-line2" class="col-sm-2 col-form-label fw-bold">Address Line 2</label>
                            <div class="col-sm-5">

                                <?php echo $this->Form->control('address_line2', [
                                    'type' => 'textarea',
                                    'class' => 'form-control',
                                    'id' => 'delivery-address-line2',
                                    'placeholder' => 'Delivery Address Line 1',
                                    'label' => false,
                                    'style' => 'border: 1px solid #F77F00;',
                                    'disabled' => true
                                ]); ?>
                                <div class="invalid-feedback">
                                </div>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="city" class="col-sm-2 col-form-label fw-bold">City</label>
                            <div class="col-sm-5">

                                <?php echo $this->Form->control('city_id', [
                                    'type' => 'select',
                                    'id' => 'city',
                                    'options' => $cities,
                                    'class' => 'form-control select2',
                                    'label' => false,
                                    'empty' => __('Please select a City'),
                                    'disabled' => true
                                ]) ?>
                                <div class="invalid-feedback">
                                </div>

                            </div>
                        </div>
                        <div class="form-group row municipality-row" style="display: none;">
                            <label for="municipality" class="col-sm-2 col-form-label fw-bold">Municipality</label>
                            <div class="col-sm-5">

                                <?php echo $this->Form->control('municipality_id', [
                                    'type' => 'select',
                                    'id' => 'municipality',
                                    'options' => $municipalities,
                                    'class' => 'form-control select2',
                                    'label' => false,
                                    'empty' => __('Please select a Municipality'),
                                    'disabled' => true
                                ]) ?>
                                <div class="invalid-feedback">
                                </div>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="delivery-zip-code" class="col-sm-2 col-form-label fw-bold">Zip Code</label>
                            <div class="col-sm-5">

                                <?php echo $this->Form->control('zipcode', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'id' => 'delivery-zip-code',
                                    'placeholder' => 'Delivery Zip Code',
                                    'label' => false,
                                    'style' => 'border: 1px solid #F77F00;',
                                    'disabled' => true
                                ]); ?>
                                <div class="invalid-feedback">
                                </div>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="delivery-zip-code" class="col-sm-2 col-form-label fw-bold">Delivery Mode:</label>
                            <div class="col-sm-5">
                                <input type="radio" name="delivery_mode_type" class="form-check-input p-0" value="standard" id="standard" checked>
                                <label for="standard">Standard</label>
                                <input type="radio" name="delivery_mode_type" class="form-check-input p-0" value="express" id="express">
                                <label for="express">Express</label>
                            </div>
                        </div>
                        <div class="form-group row changeAddrrow" style="display: none;">
                            <a id="changeAddr" class="btn" style="width: 20%;">Change Address</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card m-t-20">

                <div class="card-header pb-0">
                    <h4 style="color: black !important;">ORDER DETAILS</h4>
                </div>

                <div class="card-header pt-0">
                    <h4 style="color: black !important;" class="m-l-10">Product List</h4>
                    <div class="card-header-form">
                        <form>
                            <div class="input-group" style="margin-left:17px;">
                                <button class="btn menu-toggle fw-bold" type="submit" style="background-color: #0d839b !important;color: white !important;">
                                    <i class="fas fa-barcode" style="background-color: #0d839b !important;color: white;"></i>
                                    Scan Product
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <div id="product-table_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                            <table class="table table-striped dataTable no-footer order-table" id="product-table" role="grid" aria-describedby="product-table_info">
                                <thead>
                                    <tr role="row">
                                        <th>Product Name</th>
                                        <th>Product Variant</th>
                                        <th>Product Attributes</th>
                                        <th>Quantity to place current order</th>
                                        <th>Quantity Required</th>
                                        <th>Available Stock</th>
                                        <th>Unit Price</th>
                                        <th>Total Price</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="order-row" role="row">
                                        <td class="mb-0 font-13 fw-bold">
                                            <?php echo $this->Form->control('order_items[0][product_id]', [
                                                'type' => 'select',
                                                'options' => $products,
                                                'id' => 'productsdropdown_0',
                                                'class' => 'custom-input select2 row_original_0',
                                                'placeholder' => '🔎︎ Search by name or sku id',
                                                'empty' => '🔎︎ Search by name or sku id',
                                                'label' => false,
                                                'required' => true
                                            ]); ?>
                                            <label class="row_alt_0 visually-hidden" for="productsdropdown_0" id="lblproductsdropdown_0"></label>
                                            <input type="hidden" id="h_product_size_0" name="order_items[0][product_size]" value="">
                                            <input type="hidden" id="h_product_weight_0" name="order_items[0][product_weight]" value="0">
                                            <input type="hidden" id="h_product_discount_0" name="h_product_discount_0" value="0">
                                        </td>
                                        <td class="mb-0 font-13 fw-bold">
                                            <!-- Product Variant Dropdown -->
                                            <?php echo $this->Form->control('order_items[0][product_variant_id]', [
                                                'type' => 'select',
                                                // 'options' => $productVariants,
                                                'id' => 'variantsdropdown_0',
                                                'class' => 'custom-input select2 row_original_0',
                                                'placeholder' => 'Select Variant',
                                                'empty' => 'No Varinats available',
                                                'label' => false
                                            ]); ?>
                                            <label class="row_alt_0 visually-hidden" for="variantsdropdown_0" id="lblvariantsdropdown_0"></label>
                                            <!-- <input type="hidden" id="h_variant_product_size_0" name="order_items[0][variant_product_size]" value="">
                                            <input type="hidden" id="h_variant_product_weight_0" name="order_items[0][variant_product_weight]" value="0"> -->
                                        </td>
                                        <td class="mb-0 font-13 fw-bold">
                                            <!-- Product Attribute Dropdown -->
                                            <?php echo $this->Form->control('order_items[0][product_attribute_id]', [
                                                'type' => 'select',
                                                // 'options' => $productAttributes,
                                                'id' => 'attributesdropdown_0',
                                                'class' => 'custom-input select2 row_original_0',
                                                'placeholder' => 'Select Attribute',
                                                'empty' => 'No Attributes available',
                                                'label' => false
                                            ]); ?>
                                            <label class="row_alt_0 visually-hidden" for="attributesdropdown_0" id="lblattributesdropdown_0"></label>
                                        </td>
                                        <td class="mb-0 font-13 fw-bold">
                                            <?php echo $this->Form->control('order_items[0][quantity]', [
                                                'type' => 'number',
                                                'class' => 'custom-input row_original_0',
                                                'min' => '1',
                                                'id' => 'quantity_0',
                                                'label' => false,
                                                'placeholder' => 'Enter quantity',
                                                'required' => true
                                            ]); ?>
                                            <label class="row_alt_0 visually-hidden" for="quantity_0" id="lblquantity_0"></label>
                                            <input type="hidden" id="h_prev_quantity_0" name="h_prev_quantity_0" value="0">
                                        </td>
                                        <td class="mb-0 font-13 fw-bold">
                                            <?php echo $this->Form->control('order_items[0][required_quantity]', [
                                                'type' => 'number',
                                                'class' => 'custom-input row_original_0',
                                                'min' => '1',
                                                'id' => 'required_quantity_0',
                                                'label' => false,
                                                'placeholder' => 'Enter quantity required',
                                            ]); ?>
                                            <label class="row_alt_0 visually-hidden" for="required_quantity_0" id="lblrequired_quantity_0"></label>
                                        </td>
                                        <td class="mb-0 font-13 fw-bold">
                                            <?php echo $this->Form->label('order_items[0][available_stock]', '0', [
                                                'for' => 'available_stock_0',
                                                'id' => 'available_stock_0',
                                                'style' => 'width:30%'
                                            ]); ?>
                                            <input type="hidden" id="h_available_stock_0" name="order_items[0][available_stock]" value="0">
                                        </td>
                                        <td class="mb-0 font-13 fw-bold">
                                            <?php echo $this->Form->label('order_items[0][price]', '0', [
                                                'for' => 'unit_price_0',
                                                'id' => 'unit_price_0',
                                                'style' => 'width:30%'
                                            ]); ?>
                                            <span> <?= $currencySymbol ?> </span>
                                            <input type="hidden" id="h_unit_price_0" name="order_items[0][price]" value="0">
                                        </td>
                                        <td class="mb-0 font-13 fw-bold">
                                            <?php echo $this->Form->label('order_items[0][total_price]', '0', [
                                                'for' => 'total_price_0',
                                                'id' => 'total_price_0',
                                                'style' => 'width:30%'
                                            ]); ?>
                                            <span> <?= $currencySymbol ?> </span>
                                            <input type="hidden" id="h_total_price_0" name="order_items[0][total_price]" value="0">
                                            <input type="hidden" id="h_total_discount_price_0" name="h_total_discount_price_0" value="0">
                                        </td>
                                        <td>
                                            <div class="row_original_0">
                                                <a href="javascript:void(0);" id="add-product-btn" class="btn btn-secondary p-10 add-row" data-bs-toggle="tooltip" data-bs-target="#add-customer-modal" style="font-size: 8px !important;border-radius: 8px;">
                                                    <i class="fas fa-plus" style="font-size: 8px !important; color: #fff !important;"></i>Add Product
                                                </a>
                                            </div>
                                            <div class="row_alt_0 visually-hidden">
                                                <a href="javascript:void(0);" data-bs-toggle="tooltip" title="" class="edit-row" aria-label="Edit" data-bs-original-title="Edit"><i class="fas fa-pencil-alt"></i></a>
                                                <a href="javascript:void(0);" data-bs-toggle="tooltip" title="" class="delete-row" aria-label="Delete" data-bs-original-title="Delete"><i class="far fa-trash-alt"></i></a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>



                </div>

            </div>



            <div class="card m-t-20 p-4">
                <div class="card m-t-20" style="border: 1px solid #F77F00;background-color: #fff7ee !important; box-shadow: none !important;">

                    <div class="card-header pb-0">
                        <h4 style="color: #8E4F0C !important;">Final Order Summary</h4>
                        <!-- <a style="width:80%; text-align:end;" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#FieldValueModal"><i class="fas fa-pencil-alt"></i></a> -->
                    </div>

                    <div class="card-body" id="final-order-summary">
                        <div class="row mb-2 text-dark fw-bold">
                            <div class="col-10">
                                <strong>Subtotal</strong>
                            </div>
                            <div class="col-2 text-right text-end">
                                <span id="sub-total">0</span> <span> <?= $currencySymbol ?> </span>
                                <input type="hidden" id="subtotal_amount" name="subtotal_amount" value="0">
                            </div>
                        </div>
                        <div class="row mb-2 text-dark fw-bold">
                            <div class="col-10">
                                <strong>Shipping Costs</strong>
                            </div>
                            <div class="col-2 text-right text-end">
                                <span id="shipping-costs">0</span><span> <?= $currencySymbol ?> </span>
                                <input type="hidden" id="delivery_charge" name="delivery_charge" value="0">
                            </div>
                        </div>

                        <div class="row mb-2 border-top pt-2">
                            <div class="col-10">
                                <strong>Discount Amount</strong>
                            </div>
                            <div class="col-2 text-right text-end">
                                <span id="discount-amount">0</span><span> <?= $currencySymbol ?> </span>
                                <input type="hidden" id="hidden-discount-amount" name="discount_amount" value="0">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-10">
                                <strong>Coupon Amount - <span style="color: #0D839B;cursor:pointer" data-bs-toggle="modal" data-bs-target="#couponsModal">Apply Coupon</span></strong>&nbsp;<small><span id="coupon_code_text"></span></small><small><a href="javascript:void(0);" onclick="clearCoupons();">(Remove)</a></small>
                            </div>
                            <div class="col-2 text-right text-end">
                                <span id="coupon-amount">0</span><span> <?= $currencySymbol ?> </span>
                                <input type="hidden" id="offer-id" name="offer_id" value="">
                                <input type="hidden" id="offer-amount" name="offer_amount" value="0">
                                <input type="hidden" id="coupon-amount-hidden" name="offer_amount_initial" value="">
                                <input type="hidden" id="coupon-type" name="coupon-type" value="">
                                <input type="hidden" id="coupon-min-cart-value" name="coupon-min-cart-value" value="">
                                <input type="hidden" id="coupon-cat-products" name="coupon-cat-products" value="">
                                <input type="hidden" id="coupon-max-allowed" name="coupon-max-allowed" value="">
                                <input type="hidden" id="is_free_shipping" name="is_free_shipping" value="0">
                                <input type="hidden" id="offer_code" name="offer_code" value="">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-10">
                                <strong>Loyality Point - <span style="color: #0D839B;cursor:pointer" data-bs-toggle="modal" data-bs-target="#redeemPointsModal"> Redeem Loyalty Point</span></strong>
                            </div>
                            <div class="col-2 text-right text-end">
                                <span id="loyality-point">0</span><span> <?= $currencySymbol ?> </span>
                                <input type="hidden" id="loyalty_points_redeemed" name="loyalty_points_redeemed" value="0">
                                <input type="hidden" id="loyalty_amount" name="loyalty_amount" value="0">
                            </div>
                        </div>
                        <div class="row mt-3 border-top pt-2 text-dark fw-bold">
                            <div class="col-10">
                                <strong>Total Amount</strong>
                            </div>
                            <div class="col-2 text-right text-end">
                                <span id="total-amount">0</span><span> <?= $currencySymbol ?> </span>
                                <input type="hidden" id="total_amount" name="total_amount" value="0">
                            </div>
                        </div>

                        <div class="card-header p-0 d-flex">
                            <h4 style="color: black !important;" class="col-10">Final Amount</h4>
                            <h4 style="color: black !important;" class="col-2 text-right text-end p-0"><span id="final-amount">0</span><span> <?= $currencySymbol ?> </span></h4>
                        </div>
                    </div>

                </div>
            </div>

            <div class="card m-t-20 p-0">

                <div class="card-header">
                    <h4 style="color: black !important;">PAYMENT INFORMATION</h4>
                </div>
                <div class="card-body pt-0">
                    <div class="form-group-radio form-group">
                        <div class="add-order-radio-group d-flex m-b-20">

                            <!-- <label class="add-order-radio-group-label fw-bold text-dark d-flex">
                                <input class="add-order-radio-group-byCard form-check-input" type="radio" name="payment_method" value="Card" style="padding: 7px;" required>
                                Pay by Card
                            </label> -->
                            <label class="add-order-radio-group-label fw-bold text-dark d-flex">
                                <input class="add-order-radio-group-byCash form-check-input" type="radio" name="payment_method" value="Cash" style="padding: 7px;" required>
                                Pay by Cash
                            </label>
                            <label class="add-order-radio-group-label fw-bold text-dark d-flex">
                                <input class="add-order-radio-group-bymobile form-check-input" type="radio" name="payment_method" value="MTN MoMo" style="padding: 7px;" required>
                                Pay by MTN MoMo
                            </label>
                            <label class="add-order-radio-group-label fw-bold text-dark d-flex">
                                <input class="add-order-radio-group-bymobile form-check-input" type="radio" name="payment_method" value="Wave" style="padding: 7px;" required>
                                Pay by Wave
                            </label>
                            <label class="add-order-radio-group-label fw-bold text-dark d-flex">
                                <input class="add-order-radio-group-bymobile form-check-input" type="radio" name="payment_method" value="Cash on Delivery" style="padding: 7px;" required>
                                Cash on Delivery
                            </label>

                        </div>
                        <div class="invalid-feedback">
                        </div>
                    </div>

                    <!-- <div class="additional-fields-add-Order" id="card-fields">
                        <label class="additional-fields-add-Order-label fw-bold text-dark" for="card-digits">Card Number:</label>
                        <div class="col-sm-5">
                            <input class="additional-fields-add-Order-input form-control m-b-20" type="text" id="card-digits" name="card-digits" placeholder=" Last 4 digits">
                        </div>
                    </div> -->

                    <div class="additional-fields-status form-group" id="status-fields">
                        <label class="additional-fields-add-Order-label fw-bold text-dark" for="status">Status</label>
                        <div class="col-sm-5">
                            <?php echo $this->Form->control('transactions[0][payment_status]', [
                                'type' => 'select',
                                'options' => $payment_statuses,
                                'class' => 'form-control select2',
                                'label' => false,
                                'empty' => 'Select a Payment Status',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <!-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button> -->
        <button id="AddOrderBtn" type="button" class="btn">Save</button>
        <button id="SaveandOrderBtn" type="button" class="btn">Save & Order</button>
    </div>

</div>
</form>
</div>
<!-- Customer Modal -->


<!-- Customer Modal -->
<div class="modal fade" id="addCustomerModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel">Add New Customer</h5>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <input type="hidden" name="user_type" value="Customer" />
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-6 form-group">
                                <label for="firstName" class="form-label">First Name<sup
                                        class="text-danger font-11">*</sup></label>
                                <input type="text" class="form-control" id="firstName" name="first_name"
                                    placeholder="Enter Customer First Name" maxlength='100' required>
                                <div class="invalid-feedback">
                                </div>
                            </div>
                            <div class="col-6 form-group">
                                <label for="lastName" class="form-label">Last Name<sup
                                        class="text-danger font-11">*</sup></label>
                                <input type="text" class="form-control" id="lastName" name="last_name"
                                    placeholder="Enter Customer Last Name" maxlength='100' required>
                                <div class="invalid-feedback">
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="mb-3">
                        <div class="form-group">
                            <label for="customerEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="customerEmail" name="email"
                                placeholder="Enter email">
                            <div class="invalid-feedback">
                            </div>
                        </div>

                    </div>
                    <div class="mb-3 form-group">
                        <label for="customerPhone" class="form-label">Phone Number</label>
                        <div class="input-group">
                            <?php echo $this->Form->control('mobile_no', [
                                'type' => 'tel',
                                'class' => 'form-control tel',
                                'id' => 'customerPhone',
                                'label' => false,
                                'placeholder' => 'Enter phone number',
                                'maxlength' => '15'
                            ]); ?>
                        </div>
                        <div class="invalid-feedback">
                        </div>
                        <input type="hidden" id="country-code" name="country_code" value="">
                    </div>

            </div>
            <div class="modal-footer">
                <button type="reset" class="btn btn-secondary" data-bs-dismiss="modal" id="modalClose">Close</button>
                <button type="button" class="btn" id="saveCustomerBtn">Save Customer</button>
            </div>
            </form>


        </div>
    </div>
</div>

<!-- Redeem Modal -->
<div class="modal fade" id="redeemPointsModal" tabindex="-1" aria-labelledby="redeemPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <h5 class="modal-title text-center text-dark" id="redeemPointsModalLabel-emoji">🏅</h5>

            <h5 class="modal-title text-center text-dark" id="redeemPointsModalLabel">Redeem Loyalty Points</h5>

            <div class="modal-body">
                <input type="hidden" id="loyalty-category" name="loyalty_category" value="">
                <input type="hidden" id="loyalty-redeem-point-value" name="loyalty_redeem_point_value" value="">
                <input type="hidden" id="loyalty-redeem-points" name="loyalty_redeem_points" value="">
                <div class="view-order-pop-up-date-valid">Valid Untill : <span id="loyalty-validity"></span></div>

                <div class="view-order-pop-up-price-details modal-header">
                    <div id="l-bg-lite-orange">
                        <label class="text-center fw-bold font-color-orange">Total Loyalty Points</label>
                        <h4 class="modal-title text-center text-dark"><span id="loyalty-points">0</span></h4>
                    </div>

                    <div id="l-bg-lite-orange">
                        <label class="text-center fw-bold font-color-orange">Loyalty Points Equivalent</label>
                        <h4 class="modal-title text-center text-dark"><span id="loyalty-points-converted">0</span><span> <?= $currencySymbol ?> </span></h4>
                    </div>

                </div>

                <div class="card-body pb-0 p-l-50 p-r-50">
                    <input id="redeem-points-input" class="p-10 text-center" type="type" placeholder="Enter Points To Redeem" />
                </div>

            </div>
            <div class="modal-footer pt-0" id="view-order-pop-up">
                <div class="d-flex justify-content-center align-items-center flex-column">
                    <label class="fw-bold loyal-points-worth text-center">
                        <i class="fa fa-credit-card mb-2"></i>
                        Points Worth : <span id="calculated-loyalty-points">0</span><span> <?= $currencySymbol ?>
                    </label>
                </div>
                <button type="button" class="btn fw-bold" id="redeem-points-btn" style="width: 200px;border-radius: 15px;">Redeem Points</button>
                <button type="button" class="fw-bold" id="view-order-pop-up-c-btn" data-bs-dismiss="modal">CANCEL</button>
            </div>
        </div>
    </div>
</div>

<!-- Coupons Modal -->
<div class="modal fade" id="couponsModal" tabindex="-1" aria-labelledby="couponsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
            </div>

            <h5 class="modal-title text-center text-dark" id="couponsModalLabel-emoji">🏅</h5>

            <h5 class="modal-title text-center text-dark" id="couponsModalLabel">Coupons Points</h5>

            <div class="modal-body">
                <div class="card-body pb-0 p-l-50 p-r-50">
                    <input class="p-10 text-center" id="CouponCode" type="type" placeholder="Enter Coupon code To Redeem" />
                </div>

            </div>
            <div class="modal-footer pt-0" id="view-order-pop-up">
                <button type="button" class="btn fw-bold" id="claimCoupons" style="width: 200px;border-radius: 15px;">Claim Coupon</button>
                <button type="button" class="fw-bold" id="view-order-pop-up-c-btn" data-bs-dismiss="modal">CANCEL</button>
            </div>
        </div>
    </div>
</div>

<div id="customerAddressModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Customer Addresses</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="customer_id" value="">
                <p><a href="#" id="addNewAddress">+ Add New Address</a></p>
                <div id="addressList">
                    <!-- Addresses will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="newAddressModal" tabindex="-1" aria-labelledby="newAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newAddressModalLabel">Add New Address</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newAddressForm">
                    <?php echo $this->Form->control('customer_id', [
                        'type' => 'hidden',
                        'class' => 'form-control',
                        'id' => 'addr-customer-id',
                        'placeholder' => '',
                        'label' => false,
                        'readonly' => true
                    ]); ?>
                    <!-- <input type="hidden" id="latitude" name="latitude" value="">
                    <input type="hidden" id="longitude" name="longitude" value="">

                    <div class="form-group row">
                        <label for="map" class="col-sm-2 col-form-label fw-bold"><?= __('Address') ?><sup class="text-danger font-11">*</sup></label>
                        <div id="map" class="col-sm-5"></div>
                    </div> -->
                    <div class="form-group row">
                        <label for="new-house-no" class="col-sm-2 col-form-label fw-bold">House No/Area<sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5">
                            <?= $this->Form->control('house_no', [
                                'type' => 'textarea',
                                'class' => 'form-control',
                                'id' => 'new-house-no',
                                'placeholder' => 'House No/Area',
                                'label' => false,
                                // 'style' => 'border: 1px solid #F77F00;',
                                'required' => true
                            ]); ?>
                            <div class="invalid-feedback">
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="new-delivery-address-line1" class="col-sm-2 col-form-label fw-bold">Address Line 1<sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5">
                            <?= $this->Form->control('address_line1', [
                                'type' => 'textarea',
                                'class' => 'form-control',
                                'id' => 'new-delivery-address-line1',
                                'placeholder' => 'Delivery Address Line 1',
                                'label' => false,
                                // 'style' => 'border: 1px solid #F77F00;',
                                'required' => true
                            ]); ?>
                            <div class="invalid-feedback">
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="new-delivery-address-line2" class="col-sm-2 col-form-label fw-bold">Address Line 2</label>
                        <div class="col-sm-5">
                            <?= $this->Form->control('address_line2', [
                                'type' => 'textarea',
                                'class' => 'form-control',
                                'id' => 'new-delivery-address-line2',
                                'placeholder' => 'Delivery Address Line 2',
                                'label' => false,
                                // 'style' => 'border: 1px solid #F77F00;'
                            ]); ?>
                            <div class="invalid-feedback">
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="new-city" class="col-sm-2 col-form-label fw-bold">City<sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5">
                            <?= $this->Form->control('city_id', [
                                'type' => 'select',
                                'id' => 'new-city',
                                'options' => $cities,
                                'class' => 'form-control select2',
                                'label' => false,
                                'empty' => __('Please select a City'),
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">
                            </div>
                        </div>
                    </div>

                    <div class="form-group row new-municipality-row" style="display: none;">
                        <label for="new-municipality" class="col-sm-2 col-form-label fw-bold">Municipality</label>
                        <div class="col-sm-5">
                            <?= $this->Form->control('municipality_id', [
                                'type' => 'select',
                                'id' => 'new-municipality',
                                'options' => $municipalities,
                                'class' => 'form-control select2',
                                'label' => false,
                                'empty' => __('Please select a Municipality')
                            ]) ?>
                            <div class="invalid-feedback">
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="new-delivery-zip-code" class="col-sm-2 col-form-label fw-bold">Zip Code<sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5">
                            <?= $this->Form->control('zipcode', [
                                'type' => 'text',
                                'class' => 'form-control',
                                'id' => 'new-delivery-zip-code',
                                'placeholder' => 'Delivery Zip Code',
                                'label' => false,
                                // 'style' => 'border: 1px solid #F77F00;',
                                'required' => true
                            ]); ?>
                            <div class="invalid-feedback">
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" id="newAddressSubmit" class="btn btn-primary">Save Address</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDfID1uL0BtSDUOytUk7olLgEMTQ9QQZDc&libraries=places"></script>

<script>
    // let map;
    // let marker;
    // let geocoder;

    // function initMap() {
    //     // Initialize map at a default location
    //     const defaultLocation = {
    //         lat: 5.359952,
    //         lng: -4.008256
    //     };
    //     // Abidjan, Ivory Coast

    //     // Initialize map
    //     map = new google.maps.Map(document.getElementById("map"), {
    //         center: defaultLocation,
    //         zoom: 14,
    //     });

    //     // Initialize geocoder
    //     geocoder = new google.maps.Geocoder();

    //     // Add a draggable marker
    //     marker = new google.maps.Marker({
    //         position: defaultLocation,
    //         map: map,
    //         draggable: true,
    //     });

    //     // Fetch address on marker drag
    //     marker.addListener("dragend", () => {
    //         const position = marker.getPosition();
    //         fetchAddress(position.lat(), position.lng());
    //     });

    //     // Fetch address on initial load
    //     fetchAddress(defaultLocation.lat, defaultLocation.lng);
    // }

    // function fetchAddress(lat, lng) {
    //     const latlng = {
    //         lat: parseFloat(lat),
    //         lng: parseFloat(lng)
    //     };

    //     geocoder.geocode({
    //         location: latlng
    //     }, (results, status) => {
    //         if (status === "OK") {
    //             if (results[0]) {
    //                 $('#latitude').val(parseFloat(lat));
    //                 $('#longitude').val(parseFloat(lng));
    //                 // Display the address in the input field
    //                 // document.getElementById("address").value = results[0].formatted_address;
    //                 // console.log("Address:", results[0].formatted_address);
    //             } else {
    //                 console.log("No results found");
    //                 document.getElementById("address").value = "No address found";
    //             }
    //         } else {
    //             console.log("Geocoder failed due to: " + status);
    //             document.getElementById("address").value = "Error fetching address";
    //         }
    //     });
    // }

    $(document).ready(function() {
        const urlParams = new URLSearchParams(window.location.search);
        const orderItems = {};

        // Parse query params into orderItems[index]
        urlParams.forEach((value, key) => {
            const match = key.match(/^order_items\[(\d+)\]\[(\w+)(?:\]\[(\w+)\])?\]$/);
            if (match) {
                const index = match[1];
                const field = match[2];
                const subField = match[3] || null;

                if (!orderItems[index]) orderItems[index] = {};
                if (subField) {
                    if (!orderItems[index][field]) orderItems[index][field] = {};
                    orderItems[index][field][subField] = value;
                } else {
                    orderItems[index][field] = value;
                }
            } else {
                const $el = $('[name="' + key + '"]');
                if ($el.length) {
                    if ($el.attr('type') === 'radio') {
                        $el.filter('[value="' + value + '"]').prop('checked', true).trigger('change');
                    } else {
                        $el.val(value).trigger('change');
                    }
                }
            }
        });

        const indexes = Object.keys(orderItems).map(i => parseInt(i)).sort((a, b) => a - b);

        // Utility to wait until an element exists
        const waitForElement = (selector) => {
            return new Promise((resolve) => {
                const interval = setInterval(() => {
                    if ($(selector).length) {
                        clearInterval(interval);
                        resolve();
                    }
                }, 100);
            });
        };

        async function populateRow(index) {
            const data = orderItems[index];
            if (!data) return;

            await waitForElement('#productsdropdown_' + index);

            if (data.product_id) {
                $('#productsdropdown_' + index).val(data.product_id).trigger('change');
            }
            if (data.product_variant_id) {
                $('#variantsdropdown_' + index).val(data.product_variant_id).trigger('change');
            }
            if (data.product_attribute_id) {
                $('#attributesdropdown_' + index).val(data.product_attribute_id).trigger('change');
            }
            if (data.required_quantity) {
                $('#quantity_' + index).val(data.required_quantity);
                $('#lblquantity_' + index).text(data.required_quantity);
                $('#required_quantity_' + index).val(0);
                $('#lblrequired_quantity_' + index).text(0);
                $('#h_prev_quantity_' + index).val(0);
            }
        }

        async function processRows() {
            for (let i = 0; i < indexes.length; i++) {
                if (i > 0) {
                    $('.add-row').last().trigger('click');
                    await new Promise((r) => setTimeout(r, 400));
                }
                await populateRow(indexes[i]);
            }
        }

        processRows();
    });


    $(document).ready(function() {
        $('#pickup-showroom').change(function() {
            if ($(this).is(':checked')) {
                $('#showroom-dropdown').val('').trigger('change'); // Clears Select2 dropdown if used
            }
        });
    });

    $(document).ready(function() {

        $('.select2').select2({
            // placeholder: 'Select an option',
            // allowClear: true,
            minimumResultsForSearch: 0
        });

        $('.customer-card .select2').each(function() {
            $(this).next('.select2-container').find('.select2-selection--single').addClass('custom-input-select2');
        });

        $('.order-table .select2').each(function() {
            $(this).next('.select2-container').find('.select2-selection--single').addClass('custom-input-select');
        });

        $('#showroom-list .select2').each(function() {
            $(this).next('.select2-container').addClass('showroom-select');
            $(this).next('.select2-container').find('.select2-selection--single').addClass('custom-input-select2');
            $(this).next('.select2-container').find('.select2-selection--single').attr('style', 'width: 100% !important');
            $(this).find('.select2-selection__arrow').attr('style', 'display: block !important');
        });

        $('.additional-fields-status .select2').each(function() {
            $(this).next('.select2-container').addClass('showroom-select');
            $(this).next('.select2-container').find('.select2-selection--single').addClass('custom-input-select2');
            $(this).next('.select2-container').find('.select2-selection--single').attr('style', 'width: 100% !important');
        });

        $('#modalClose').on('click', function() {
            var modal = $(this).closest('.modal');
            $(this).closest('.modal').find('form')[0].reset();
            modal.find('.is-invalid').removeClass('is-invalid');
            modal.find('.invalid-feedback').hide();
            modal.find(':disabled').prop('disabled', false);
            setTimeout(function() {
                updateCountryCode();
            }, 0);
        });

        var input = document.querySelector("#customerPhone");
        var iti = window.intlTelInput(input, {
            initialCountry: "ci",
            separateDialCode: true,
            preferredCountries: ["ci", "us", "gb"],
            utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
        });

        function updateCountryCode() {
            $('#country-code').val(iti.getSelectedCountryData().dialCode);
        }
        input.addEventListener("countrychange", updateCountryCode);

        updateCountryCode();

        $('#customerDropdown').change(function() {
            var customerId = $(this).val();
            if (customerId) {
                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'Customers', 'action' => 'getCustomerDetails']) ?>",
                    type: 'POST',
                    data: {
                        id: customerId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#customer-id').val(customerId);
                            $('#customer_id').val(customerId);
                            $('#addr-customer-id').val(customerId);
                            $('#customer-address-id').val(response.customer
                                .customer_address_id);
                            $('#customer-name').val(response.customer.first_name + ' ' +
                                response.customer.last_name);

                            $('#customer-email').val(response.customer.email);
                            $('#customer-mobile').val(response.customer.phone_number);

                            $('#house-no').val(response.customer.house_no);
                            $('#delivery-address-line1').val(response.customer.address_line1);
                            $('#delivery-address-line2').val(response.customer.address_line2);
                            $('#city').prop('disabled', false).val(response.customer.city_id).trigger('change');
                            $('#city').prop('disabled', true);
                            if (response.customer.municipality_id !== '') {
                                $('.municipality-row').css('display', 'flex');
                                $('#municipality').prop('disabled', false).val(response.customer.municipality_id).trigger('change');
                                $('#municipality').prop('disabled', true);
                            } else {
                                $('.municipality-row').css('display', 'none');
                                $('#municipality').val('').prop('disabled', true).trigger('change');
                            }

                            $('#delivery-zip-code').val(response.customer.zipcode);
                            $('.changeAddrrow').css('display', 'block');
                            var loyalty_category = response.customer.loyalty_category;
                            var loyalty_points = parseFloat(response.customer.points) || 0;
                            var loyalty_points_converted = 0;

                            var loyalty_redeem_point_value = parseFloat(response.customer.loyalty_redeem_point_value) || 0;
                            var loyalty_redeem_points = parseFloat(response.customer.loyalty_redeem_points) || 1; // avoid division by zero

                            var redeemable_sets = Math.floor(loyalty_points / loyalty_redeem_points);
                            loyalty_points_converted = redeemable_sets * loyalty_redeem_point_value;

                            // if (loyalty_category === 'Standard') {
                            //     loyalty_points_converted = Math.round(loyalty_points * standardRedeem);
                            // } else if (loyalty_category === 'VIP') {
                            //     loyalty_points_converted = Math.round(loyalty_points * vipRedeem);
                            // }

                            $('#loyalty-category').val(loyalty_category);
                            $('#loyalty-redeem-point-value').val(loyalty_redeem_point_value);
                            $('#loyalty-redeem-points').val(loyalty_redeem_points);
                            $('#loyalty-validity').text(response.customer.validity);
                            $('#loyalty-points').text(loyalty_points);
                            $('#loyalty-points-converted').text(formatCurrency(Math.round(loyalty_points_converted)));
                            $('#customer-group').val(response.customer.customer_groups);

                        } else {
                            swal('<?= __('Failed') ?>', '<?= __('Failed to fetch customer details. Please try again.') ?>', 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        swal('<?= __('Failed') ?>', '<?= __('Failed to fetch customer details. Please try again.') ?>', 'error');
                    }
                });
            }
            clearCoupons();
        });

        $('#AddOrderBtn').click(function() {
            SaveOrders(0);
        });

        $('#SaveandOrderBtn').on('click', function() {
            SaveOrders(1);
        });

        function SaveOrders(flag = 0) {
            let isValid = true;
            $('#product-table tbody tr:last').remove();
            $('#addOrderForm').find('input[required], select[required], input[type=radio][required]').each(function() {

                if ($(this).attr('type') === 'radio') {
                    let name = $(this).attr('name');
                    if ($('input[name="' + name + '"]:checked').length === 0) {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group-radio').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group-radio').find('label').text().trim();
                        fieldName = fieldName.replace(/\*$/, '');
                        feedback.text('<?= __("Please choose one ") ?>').show();
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group-radio').find('.invalid-feedback');
                        feedback.hide();
                    }
                } else {
                    let value = $(this).val().trim();
                    let isSelect2 = $(this).hasClass('select2');

                    if (value === '') {
                        $(this).addClass('is-invalid-order');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                        if (isSelect2) {
                            $(this).next('.select2-container').find('.select2-selection--single').addClass('is-invalid-order');
                            //$(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                        }
                    } else {
                        $(this).removeClass('is-invalid-order');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                        if (isSelect2) {
                            $(this).next('.select2-container').find('.select2-selection--single').removeClass('is-invalid-order');
                            //$(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                        }
                    }
                }
            });
            var deliveryMode = $('input[name="delivery_mode"]:checked').val();

            if (deliveryMode === 'pickup') {
                var showroomDropdown = $('#showroom-dropdown').val();
                // var transportationCost = $('#transportation-cost').val();

                if (!showroomDropdown) {
                    if (!showroomDropdown) {
                        $('#showroom-dropdown').next('.select2-container').find('.select2-selection--single').addClass('is-invalid-order');
                        let feedback = $('#showroom-dropdown').closest('.form-group').find('.invalid-feedback');
                        feedback.text('<?= __("Please select a showroom.") ?>').show();
                        isValid = false;
                    }
                    // if (!transportationCost) {
                    //     $('#transportation-cost').addClass('is-invalid-order');
                    //     let feedback = $('#transportation-cost').closest('.form-group').find('.invalid-feedback');
                    //     feedback.text('<?= __("Please enter the transportation cost.") ?>').show();
                    //     isValid = false;
                    // }
                } else {
                    $('#showroom-dropdown, #showroom-dropdown').removeClass('is-invalid-order');
                    $('#showroom-dropdown, #showroom-dropdown').closest('.form-group').find('.invalid-feedback').hide();
                }
            } else if (deliveryMode === 'delivery') {
                var deliveryAddress = $('#delivery-address-line1').val();

                if (!deliveryAddress) {
                    $('#delivery-address-line1').addClass('is-invalid-order');
                    let feedback = $('#delivery-address-line1').closest('.form-group').find('.invalid-feedback');
                    let fieldName = $('#delivery-address-line1').closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                    feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                    isValid = false;
                } else {
                    $('#delivery-address-line1').removeClass('is-invalid-order');
                    let feedback = $('#delivery-address-line1').closest('.form-group').find('.invalid-feedback');
                    feedback.hide();
                }
            }
            if (isValid) {
                var formData = $('#addOrderForm').find(':input').serialize();

                formData += '&customer_id=' + encodeURIComponent($('#customer-id').val()) +
                    '&customer_address_id=' + encodeURIComponent($('#customer-address-id').val());

                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'Orders', 'action' => 'add']) ?>",
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success === true) {
                            if (flag == 1) {
                                var queryString = formData;
                                var newTabUrl = window.location.origin + window.location.pathname + '?' + queryString;
                                window.open(newTabUrl, '_blank');
                            }
                            window.location.href = "<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']) ?>";


                        } else {
                            swal("Failed!", 'Failed to save order. Please try again.', "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error saving customer:', error);
                        swal("Failed!", 'Failed to save order. Please try again.', "error");
                    }
                });
            }

        }

        $('#saveCustomerBtn').click(function() {
            let isValid = true;

            $('#addCustomerForm').find('input[required], select[required]').each(function() {
                let value = $(this).val().trim();
                let isSelect2 = $(this).hasClass('select2');
                if (value === '') {
                    $(this).addClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                    feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                    isValid = false;
                    if (isSelect2) {
                        $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                    }
                } else {
                    if ($(this).is('input[type="tel"]')) {
                        if ($(this).val().length <= 8) {
                            $(this).addClass('is-invalid');
                            let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                            let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                            feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                            isValid = false;
                        } else {
                            $(this).removeClass('is-invalid');
                            let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                            feedback.hide();
                            if (isSelect2) {
                                $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                            }
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                        }
                    }

                }
            });
            if (isValid) {
                var customerEmail = $('#customerEmail').val();
                var customerPhone = $('#customerPhone').val();
                if (customerEmail == '' && customerPhone == '') {
                    $('#customerEmail').addClass('is-invalid');
                    let feedback = $('#customerEmail').closest('.form-group').find('.invalid-feedback');
                    feedback.show();

                    $('#customerPhone').addClass('is-invalid-select');
                    feedback = $('#customerPhone').closest('.form-group').find('.invalid-feedback');
                    feedback.show();
                    isValid = false;
                } else {
                    $('#customerEmail').removeClass('is-invalid');
                    let feedback = $('#customerEmail').closest('.form-group').find('.invalid-feedback');
                    feedback.hide();

                    $('#customerPhone').removeClass('is-invalid-select');
                    feedback = $('#customerPhone').closest('.form-group').find('.invalid-feedback');
                    feedback.hide();
                    if (customerEmail !== '') {
                        var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        if (!emailPattern.test(customerEmail)) {
                            $('#customerEmail').addClass('is-invalid');
                            let feedback = $('#customerEmail').closest('.form-group').find('.invalid-feedback');
                            feedback.text('<?= __("Please enter a valid email.") ?>').show();
                            isValid = false;
                        } else {
                            $('#customerEmail').removeClass('is-invalid');
                            let feedback = $('#customerEmail').closest('.form-group').find('.invalid-feedback');
                            feedback.hide();
                        }

                    } else if (customerPhone !== '') {
                        if ($('#customerPhone').val().length <= 8) {
                            $('#customerPhone').addClass('is-invalid-select');
                            let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                            let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                            feedback.text('<?= __("Please enter valid") ?>' + fieldName.toLowerCase() + '.').show();
                            isValid = false;
                        } else {
                            $(this).removeClass('is-invalid-select');
                            let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                            feedback.hide();
                        }
                    }

                }
            }
            if (isValid) {
                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'Customers', 'action' => 'addCustomer']) ?>",
                    type: 'POST',
                    data: $('#addCustomerForm').serialize(),
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {

                            $('#addCustomerForm').trigger('reset');
                            $('#modalClose').trigger('click');

                            let customerDropdown = $(
                                '#customerDropdown');
                            customerDropdown.empty();
                            $.each(response.customers, function(index, customer) {
                                customerDropdown.append($('<option></option>').attr(
                                    'value', customer.id).text(customer
                                    .display));
                            });
                            customerDropdown.val('');
                            swal({
                                title: "Success!",
                                text: response.message,
                                icon: "success",
                                timer: 2000, // Closes automatically after 2 seconds
                                buttons: false
                            });
                        } else {
                            swal("Failed!", response.message, "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error saving customer:', error);
                        swal("Failed!", 'Failed to save customer. Please try again.', "error");
                    }
                });
            }
        });

        $('#couponsModal').on('hidden.bs.modal', function() {
            $(this).find('input, textarea, select').val(''); // Clear text inputs, textareas, selects
            $(this).find('input[type="checkbox"], input[type="radio"]').prop('checked', false); // Uncheck checkboxes & radios
            $(this).find('.select2').val(null).trigger('change'); // Reset Select2 dropdowns
            $(this).find('input[type="hidden"]').val(''); // Clear hidden inputs
        });

        $('#redeemPointsModal').on('hidden.bs.modal', function() {
            $(this).find('#redeem-points-input').val('');
            $(this).find('#calculated-loyalty-points').text('0');
        });

        $('#claimCoupons').on('click', function() {

            var couponCode = $('#CouponCode').val();
            var orderDate = $('#order-date').val();
            let subTotal = parseFloat($('#subtotal_amount').val()) || 0;
            let loggedInShowroomId = <?= isset($loggedInShowroomId) ? json_encode($loggedInShowroomId) : "''" ?>;
            let showroom_id = loggedInShowroomId !== '' ? loggedInShowroomId : $('#showroom-dropdown').val();
            let customer_groups = $('#customer-group').val();
            var productIds = []; // Initialize the array here
            // alert(orderDate);

            $('select[id^="productsdropdown_"]').each(function() {
                var selectedValue = $(this).val();
                if (selectedValue) {
                    productIds.push(selectedValue);
                }
            });

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Offers', 'action' => 'getCouponClaim']) ?>",
                type: 'POST',
                data: {
                    coupon_code: couponCode,
                    order_date: orderDate,
                    subTotal: subTotal,
                    showroom_id: showroom_id,
                    product_ids: productIds,
                    customer_groups: customer_groups
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.success == true) {
                        swal("Success!", response.message, "success");
                        var offer_amount = parseFloat(response.offer_amount);
                        $('#offer-id').val(response.id);
                        $('#coupon-amount-hidden').text((Math.round(offer_amount)));
                        $('#coupon-type').val(response.offer_type);
                        $('#coupon-min-cart-value').val(response.min_cart_value);
                        $('#coupon-cat-products').val(JSON.stringify(response.cat_products));
                        $('#coupon-max-allowed').val(response.max_allowed);
                        $('#is_free_shipping').val(response.is_free_shipping);
                        $('#offer_code').val(response.offer_code);
                        calculateCityDeliveryCharges();

                        calculateTotalAmount();
                    } else {
                        // alert('no')
                        swal("Failed!", response.message, "error");
                    }

                    $('#couponsModal').modal('hide');
                },
                error: function(xhr) {
                    var errorMessage = xhr.responseJSON ? xhr.responseJSON.message : 'An error occurred. Please try again.';
                    swal("Error!", errorMessage, "error");
                }
            });
        });

        $('#redeem-points-input').on('input', function() {
            var redeemPoints = parseFloat($(this).val()) || 0;
            var loyalty_category = $('#loyalty-category').val();
            var loyalty_redeem_point_value = $('#loyalty-redeem-point-value ').val();
            var loyalty_redeem_points = $('#loyalty-redeem-points ').val();
            var loyalty_points = parseFloat($('#loyalty-points').text()) || 0;
            var loyalty_points_worth = 0;



            if (redeemPoints > loyalty_points) {
                swal("Error!", 'Redeem points should be less than or equal to ' + loyalty_points, "error");
                $('#redeem-points-input').val('');
                $('#calculated-loyalty-points').text('0');
                return;
            }
            var redeemable_sets = Math.floor(redeemPoints / loyalty_redeem_points);
            loyalty_points_worth = redeemable_sets * loyalty_redeem_point_value;
            $('#calculated-loyalty-points').text(formatCurrency(Math.round(loyalty_points_worth)));
        });

    });

    $(document).on('change', 'select[id^="productsdropdown_"]', function() {

        var productId = $(this).val();
        var pickupshowroom = $('#showroom-dropdown').val();
        var delivery_mode = $('input[name="delivery_mode"]:checked').val();

        if (delivery_mode !== 'delivery') {
            var showrooms = pickupshowroom ?? [];
        } else {
            var showrooms = [];
        }

        var product = $(this).find('option:selected').text();
        var row = $(this).closest('tr');

        var unitPriceField = row.find('label[id^="unit_price_"]');
        var stockField = row.find('label[id^="available_stock_"]');
        var h_stockField = row.find('input[id^="h_available_stock_"]');
        var h_unitPriceField = row.find('input[id^="h_unit_price_"]');
        var quantityField = row.find('input[id^="quantity_"]');
        var totalPriceField = row.find('label[id^="total_price_"]');
        var h_totalPriceField = row.find('input[id^="total_price_"]');
        var ProductLabel = row.find('label[id^="lblproductsdropdown_"]');
        var h_product_size = row.find('input[id^="h_product_size_"]');
        var h_product_weight = row.find('input[id^="h_product_weight_"]');
        var h_product_discount = row.find('input[id^="h_product_discount_"]');

        var variantsDropdown = row.find('select[id^="variantsdropdown_"]');
        var attributesDropdown = row.find('select[id^="attributesdropdown_"]');

        if (productId) {
            // Fetch Product Price
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Products', 'action' => 'getProductPrice']) ?>",
                type: 'POST',
                data: {
                    product_id: productId,
                    showrooms: showrooms
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        var purchasePrice = parseFloat(response.promotion_price || 0);
                        var productSize = response.product_size || '';
                        var productWeight = response.product_weight || '';

                        unitPriceField.text(formatCurrency(Math.round(purchasePrice)));
                        h_unitPriceField.val(Math.round(purchasePrice));
                        h_product_size.val(productSize);
                        h_product_weight.val(productWeight);
                        ProductLabel.text(product);
                        h_product_discount.val(response.discount_price);

                        var quantity = parseFloat(quantityField.val() || 0);
                        var totalPrice = purchasePrice * quantity;

                        totalPriceField.text(formatCurrency(Math.round(totalPrice)));
                        h_totalPriceField.val(Math.round(totalPrice));
                        stockField.text(response.availableQuantity);
                        h_stockField.val(response.availableQuantity);
                        // var selectedCityId = $('#city').val();
                        updateStock();
                        calculateCityDeliveryCharges();
                    } else {
                        swal('Error', 'Failed to fetch product price.', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching product price:', error);
                    swal('Error', 'Failed to fetch product price. Please try again.', 'error');
                },
                complete: function() {
                    updateRowTotal(row);
                }
            });

            // Fetch Variants
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'getVariants']) ?>",
                type: 'GET',
                data: {
                    product_id: productId
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    // variantsDropdown.empty();
                    if (response.success) {
                        variantsDropdown.empty();
                        $.each(response.variants, function(key, value) {
                            variantsDropdown.append('<option value="' + key + '">' + value + '</option>');
                        });
                        var firstVariantKey = Object.keys(response.variants)[0];
                        if (firstVariantKey) {
                            variantsDropdown.val(firstVariantKey).trigger('change');
                        }
                    } else {
                        variantsDropdown.empty().append('<option value="">No Varinats available</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching product variants:', error);
                    swal('Error', 'Failed to fetch product variants. Please try again.', 'error');
                }
            });

            //Fetch Attributes
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'ProductAttributes', 'action' => 'getProductAttributes']) ?>",
                type: 'POST',
                data: {
                    product_id: productId
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        attributesDropdown.empty();
                        response.attributes.forEach(function(attribute) {
                            attributesDropdown.append('<option value="' + attribute.id + '">' + attribute.name + '</option>');
                        });
                        setTimeout(function() {
                            if (response.attributes.length > 0) {
                                attributesDropdown.val(response.attributes[0].id).trigger('change');
                            }
                        }, 300); // Adjust delay as needed

                    } else {
                        attributesDropdown.empty().append('<option value="">No Attributes available</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching product attributes:', error);
                    attributesDropdown.empty().append('<option value="">No Attributes available</option>');
                }
            });

        } else {
            unitPriceField.text('');
            h_unitPriceField.val('');
            totalPriceField.text((0));
            h_totalPriceField.val((0));
            variantsDropdown.empty().append('<option value="">No Varinats available</option>');
            attributesDropdown.empty().append('<option value="">No Attributes available</option>');
            updateRowTotal(row);
        }
    });

    $(document).on('change', 'select[id^="variantsdropdown_"]', function() {
        var variantId = $(this).val();
        var pickupshowroom = $('#showroom-dropdown').val();
        var delivery_mode = $('input[name="delivery_mode"]:checked').val();

        if (delivery_mode !== 'delivery') {
            var showrooms = pickupshowroom ?? [];
        } else {
            var showrooms = [];
        }
        var variant = $(this).find('option:selected').text();
        var row = $(this).closest('tr');
        var productId = row.find('select[id^="productsdropdown_"]').val();

        var unitPriceField = row.find('label[id^="unit_price_"]');
        var stockField = row.find('label[id^="available_stock_"]');
        var h_stockField = row.find('input[id^="h_available_stock_"]');
        var h_unitPriceField = row.find('input[id^="h_unit_price_"]');
        var quantityField = row.find('input[id^="quantity_"]');
        var totalPriceField = row.find('label[id^="total_price_"]');
        var h_totalPriceField = row.find('input[id^="total_price_"]');

        var VariantLabel = row.find('label[id^="lblvariantsdropdown_"]');
        var h_product_size = row.find('input[id^="h_product_size_"]');
        var h_product_weight = row.find('input[id^="h_product_weight_"]');
        var h_product_discount = row.find('input[id^="h_product_discount_"]');
        // var h_variantProductSize = row.find('input[id^="h_variant_product_size_"]');
        // var h_variantProductWeight = row.find('input[id^="h_variant_product_weight_"]');
        if (variantId) {
            // Fetch Variant Details
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'ProductVariants', 'action' => 'getProductVarPrices']) ?>",
                type: 'POST',
                data: {
                    id: variantId,
                    productId: productId,
                    showrooms: showrooms
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        var purchasePrice = parseFloat(response.promotion_price || 0);
                        var variantSize = response.variant_size || '';
                        var variantWeight = response.variant_weight || '';

                        unitPriceField.text(formatCurrency(Math.round(purchasePrice)));
                        h_unitPriceField.val(Math.round(purchasePrice));

                        var quantity = parseFloat(quantityField.val() || 0);
                        var totalPrice = purchasePrice * quantity;

                        totalPriceField.text(formatCurrency(Math.round(totalPrice)));
                        h_totalPriceField.val(Math.round(totalPrice));

                        // h_variantProductSize.val(variantSize);
                        // h_variantProductWeight.val(variantWeight);

                        h_product_size.val(variantSize);
                        h_product_weight.val(variantWeight);
                        h_product_discount.val(response.discount_price);
                        stockField.text(response.availableQuantity);
                        h_stockField.val(response.availableQuantity);
                        VariantLabel.text(variant);
                        // var selectedCityId = $('#city').val();
                        updateStock();
                        calculateCityDeliveryCharges();

                    } else {
                        swal('Error', 'Failed to fetch variant details.', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching variant details:', error);
                    swal('Error', 'Failed to fetch variant details. Please try again.', 'error');
                },
                complete: function() {
                    updateRowTotal(row);
                }
            });

        } else {
            // h_variantProductSize.val('');
            // h_variantProductWeight.val('');
            VariantLabel.text('');
            updateRowTotal(row);
        }
    });

    $(document).on('change', 'select[id^="attributesdropdown_"]', function() {
        var attributeId = $(this).val();
        var pickupshowroom = $('#showroom-dropdown').val();
        var delivery_mode = $('input[name="delivery_mode"]:checked').val();

        if (delivery_mode !== 'delivery') {
            var showrooms = pickupshowroom ?? [];
        } else {
            var showrooms = [];
        }
        var attribute = $(this).find('option:selected').text();
        var row = $(this).closest('tr');
        var productId = row.find('select[id^="productsdropdown_"]').val();
        var productVarId = row.find('select[id^="variantsdropdown_"]').val();

        var stockField = row.find('label[id^="available_stock_"]');
        var h_stockField = row.find('input[id^="h_available_stock_"]');

        var AttributeLabel = row.find('label[id^="lblattributesdropdown_"]');

        if (attributeId) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'ProductAttributes', 'action' => 'getProductAttrPrices']) ?>",
                type: 'POST',
                data: {
                    id: attributeId,
                    productId: productId,
                    productVarId: productVarId,
                    showrooms: showrooms
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {

                        stockField.text(response.availableQuantity);
                        h_stockField.val(response.availableQuantity);
                        updateStock();

                    } else {
                        swal('Error', 'Failed to fetch attribute details.', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching attribute details:', error);
                    swal('Error', 'Failed to fetch attribute details. Please try again.', 'error');
                }
            });
            AttributeLabel.text(attribute);

        } else {
            AttributeLabel.text('');
            updateRowTotal(row);
        }
    });

    function calculateTotalAmount() {
        let subTotal = parseFloat($('#subtotal_amount').val()) || 0;
        let shippingCosts = parseFloat($('#delivery_charge').val()) || 0;
        // let discountAmount = parseFloat($('#discount-amount').text()) || 0;
        let couponAmount = parseFloat($('#coupon-amount-hidden').text()) || 0;
        let couponType = $('#coupon-type').val() || '';
        let couponMincart = $('#coupon-min-cart-value').val() || 0;
        let loyalitypoint = parseFloat($('#loyalty_amount').val()) || 0;
        let couponMaxAllowed = $('#coupon-max-allowed').val();
        let offerCode = $('#offer_code').val();
        let coupon_code = offerCode ? `(${offerCode})` : '';

        if (!isNaN(couponAmount) && couponAmount > 0) {
            if (subTotal < couponMincart) {
                swal("Error!", 'Coupon cart value does not match with order cart, hence removing coupon applied', "error");
                $('#coupon-amount-hidden').text('0');
                $('#offer-id').val('');
                $('#offer-amount').val(0);
                $('#coupon-amount').text('0');
                $('#coupon-min-cart-value').val('');
                clearCoupons();
            }
        }


        couponMaxAllowed = $('#coupon-max-allowed').val();
        offerCode = $('#offer_code').val();
        coupon_code = offerCode ? `(${offerCode})` : '';

        couponAmount = parseFloat($('#coupon-amount-hidden').text()) || 0;
        couponType = $('#coupon-type').val() || '';
        couponMincart = $('#coupon-min-cart-value').val() || 0;

        let totalAmount = subTotal + shippingCosts - loyalitypoint;

        var couponCatProductsStr = $('#coupon-cat-products').val();
        // If the value is empty, default to an empty array.
        var couponCatProducts = couponCatProductsStr ? JSON.parse(couponCatProductsStr) : [];

        if (Array.isArray(couponCatProducts) && couponCatProducts.length > 0) {
            let discountOnCategoryProducts = 0;
            let validProductsTotal = 0;
            let productTotalPrice = 0;

            couponCatProducts.forEach(function(productId) {
                let productSelect = $('select[id^="productsdropdown_"]').filter(function() {
                    return $(this).val() == productId;
                });

                let productRow = productSelect.closest('tr');
                productTotalPrice += parseFloat(productRow.find('[id^="total_price_"]').text()) || 0;
            });

            if (couponType == 'Percentage') {
                let catcouponAmount = parseFloat(productTotalPrice) * (parseFloat(couponAmount) / 100);
                couponAmount = parseFloat(catcouponAmount);
                if (parseFloat(couponAmount) > parseFloat(couponMaxAllowed)) {
                    couponAmount = couponMaxAllowed;
                }
            }
            $('#offer-amount').val(Math.round(couponAmount));
            $('#coupon-amount').text(formatCurrency(Math.round(couponAmount)));
            totalAmount = totalAmount - couponAmount;
        } else {
            if (couponType == 'Flat') {
                totalAmount = totalAmount - couponAmount;
                $('#offer-amount').val(Math.round(couponAmount));
                $('#coupon-amount').text(formatCurrency(Math.round(couponAmount)));
            } else if (couponType == 'Percentage') {
                let couponAmtVal = parseFloat(subTotal) * (parseFloat(couponAmount) / 100);

                if (parseFloat(couponAmtVal) > parseFloat(couponMaxAllowed)) {
                    couponAmtVal = couponMaxAllowed;
                }
                totalAmount -= couponAmtVal;
                var couponAmtValNumber = parseFloat(couponAmtVal);
                if ((couponAmtValNumber)) {
                    $('#offer-amount').val(Math.round(couponAmtValNumber));
                    $('#coupon-amount').text(formatCurrency(Math.round((couponAmtValNumber))));
                } else {
                    $('#offer-id').val('');
                    $('#offer-amount').val(0);
                    $('#coupon-amount').text('0');
                }
            }
        }
        $('#total-amount').text(formatCurrency(Math.round(totalAmount)));
        $('#total_amount').val(Math.round(totalAmount));
        $('#final-amount').text(formatCurrency(Math.round(totalAmount)));
        $('#coupon_code_text').text(coupon_code);
    }


    function calculateFields() {
        let subtotal = 0;
        let discount_total = 0;

        $('[id^="total_price_"]').each(function() {
            let total = parseFloat($(this).text().toString().replace(/\s+/g, '')) || 0;
            subtotal += total;
        });
        $('input[id^="h_total_discount_price_"]').each(function() {
            let dis_total = parseFloat($(this).val()) || 0;
            discount_total += dis_total;
        });

        $('#discount-amount').text(formatCurrency(Math.round(discount_total)));
        $('#hidden-discount-amount').val(Math.round(discount_total));
        $('#sub-total').text(formatCurrency(Math.round(subtotal)));
        $('#subtotal_amount').val(Math.round(subtotal));
        calculateTotalAmount();
    }
    //$(document).on('input', '[id^="total_price_"]', calculateFields);

    function updateRowTotal(row) {
        let quantity = parseFloat(row.find('input[id^="quantity_"]').val()) || 0;
        let unitPrice = parseFloat(row.find('label[id^="unit_price_"]').text().toString().replace(/\s+/g, '')) || 0;

        let totalPrice = quantity * parseInt(unitPrice);
        let discount = parseFloat(row.find('input[id^="h_product_discount"]').val()) || 0;
        let total_discount = quantity * discount;
        row.find('input[id^="h_total_discount_price_"]').val(Math.round(total_discount));
        row.find('label[id^="total_price_"]').text(formatCurrency(Math.round(totalPrice)));
        calculateFields();
    }

    $(document).on('input', '[id^="quantity_"]', function() {
        let row = $(this).closest('tr');
        var quantityInput = $(this);
        var quantityLabel = row.find('label[id^="lblquantity_"]');
        var availableStock = parseInt(row.find('[id^="h_available_stock_"]').val(), 10);
        var enteredQuantity = parseInt(quantityInput.val(), 10);
        var delivery_mode = $('input[name="delivery_mode"]:checked').val();

        if (delivery_mode !== 'delivery') {
            if (enteredQuantity > availableStock) {
                quantityInput.val(availableStock);
                quantityLabel.text(availableStock);
            } else {
                quantityLabel.text(enteredQuantity);
            }
        }

        updateRowTotal(row);

        var showroomId = $('#showroom-dropdown').val();
        if (showroomId) {
            var delivery_mode = 'showroom';
            // calculateShowroomDeliveryCharges(showroomId, delivery_mode);
        }

        // var selectedCityId = $('#city').val();
        calculateCityDeliveryCharges();
    });

    $(document).on('input', '[id^="required_quantity_"]', function() {
        let row = $(this).closest('tr');
        var quantityInput = $(this);
        var quantityLabel = row.find('label[id^="lblrequired_quantity_"]');
        var enteredQuantity = parseInt(quantityInput.val(), 10);
        quantityLabel.text(enteredQuantity);
    });

    $('.calculate-total').on('input', function() {
        calculateTotalAmount();
    });


    let rowIndex = 1;

    $(document).on('click', '.add-row', function() {
        var currentRow = $(this).closest('tr');
        var productDropdown = currentRow.find('select[id^="productsdropdown_"]');
        var quantityField = currentRow.find('input[id^="quantity_"]');
        var lastRow = $('#product-table tbody tr').last();

        var prev_quantityField = currentRow.find('input[id^="h_prev_quantity_"]');
        var productId = currentRow.find('select[id^="productsdropdown_"]').val();
        var productVarId = currentRow.find('select[id^="variantsdropdown_"]').val();
        var attributeId = currentRow.find('select[id^="attributesdropdown_"]').val();
        var prevquantity = prev_quantityField.val() ?? 0;
        var current_quantity = quantityField.val() ?? 0;
        var pickupshowroom = $('#showroom-dropdown').val();
        var delivery_mode = $('input[name="delivery_mode"]:checked').val();

        var stockupdate = false;

        if (delivery_mode !== 'delivery') {
            var showrooms = pickupshowroom ?? [];
        } else {
            var showrooms = [];
        }

        if (delivery_mode !== 'delivery') {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'ProductStocks', 'action' => 'ReserveStock']) ?>",
                type: 'POST',
                data: {
                    productId: productId,
                    productVarId: productVarId,
                    attributeId: attributeId,
                    showrooms: showrooms,
                    prevquantity: prevquantity,
                    current_quantity: current_quantity
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'error') {
                        prev_quantityField.val(response.prevquantity);
                        swal('Error', 'There is no stock, please check again', 'error');

                        return false;
                    } else {
                        prev_quantityField.val(response.prevquantity);
                        addnewRow(currentRow, productDropdown, quantityField, lastRow);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching stock details:', error);
                    swal('Error', 'Failed to fetch stock details. Please try again.', 'error');
                }
            });
        } else {
            addnewRow(currentRow, productDropdown, quantityField, lastRow);
        }
        updateStock();

    });

    function addnewRow(currentRow, productDropdown, quantityField, lastRow) {
        if (productDropdown.val() && quantityField.val()) {
            currentRow.find('[class*="row_original_"]').addClass('visually-hidden');
            currentRow.find('[class*="row_original_"]').next('.select2-container').addClass('visually-hidden');
            currentRow.find('[class*="row_alt_"]').removeClass('visually-hidden');
            if (currentRow.is(lastRow)) {
                let newRow = `
    <tr class="order-row" id="row_${rowIndex}">
        <td class="mb-0 font-13 fw-bold">
            <select name="order_items[${rowIndex}][product_id]" id="productsdropdown_${rowIndex}" class="custom-input select2 row_original_${rowIndex}" style="width: 100%;" required>
                <option value="">🔎︎ Search by name or sku id</option>
                <?php foreach ($products as $key => $value) : ?>
                    <option value="<?= $key; ?>"><?= $value; ?></option>
                <?php endforeach; ?>
            </select>
            <label class="row_alt_${rowIndex} visually-hidden" for="productsdropdown_${rowIndex}" id="lblproductsdropdown_${rowIndex}"></label>
            <input type="hidden" id="h_product_size_${rowIndex}" name="order_items[${rowIndex}][product_size]" value="">
            <input type="hidden" id="h_product_weight_${rowIndex}" name="order_items[${rowIndex}][product_weight]" value="0">
            <input type="hidden" id="h_product_discount_${rowIndex}" name="h_product_discount_${rowIndex}" value="0">
        </td>
        <td class="mb-0 font-13 fw-bold">
            <select name="order_items[${rowIndex}][product_variant_id]" id="variantsdropdown_${rowIndex}" class="custom-input select2 row_original_${rowIndex}" style="width: 100%;">
                <option value="">No Varinats available</option>
            </select>
            <label class="row_alt_${rowIndex} visually-hidden" for="variantsdropdown_${rowIndex}" id="lblvariantsdropdown_${rowIndex}"></label>
        </td>
        <td class="mb-0 font-13 fw-bold">
            <select name="order_items[${rowIndex}][product_attribute_id]" 
        id="attributesdropdown_${rowIndex}" 
        class="custom-input select2 row_original_${rowIndex}" 
        style="width: 100%;">
    <option value="">No Attributes available</option>
</select>
            <label class="row_alt_${rowIndex} visually-hidden" for="attributesdropdown_${rowIndex}" id="lblattributesdropdown_${rowIndex}"></label>
        </td>
        <td class="mb-0 font-13 fw-bold">
            <input type="number" name="order_items[${rowIndex}][quantity]" id="quantity_${rowIndex}" class="custom-input row_original_${rowIndex}" min="1" placeholder="Enter quantity" required />
            <label class="row_alt_${rowIndex} visually-hidden" for="quantity_${rowIndex}" id="lblquantity_${rowIndex}"></label>
            <input type="hidden" id="h_prev_quantity_${rowIndex}" name="h_prev_quantity_${rowIndex}" value="0">
        </td>
         <td class="mb-0 font-13 fw-bold">
            <input type="number" name="order_items[${rowIndex}][required_quantity]" id="required_quantity_${rowIndex}" class="custom-input row_original_${rowIndex}" min="1" placeholder="Enter quantity" />
            <label class="row_alt_${rowIndex} visually-hidden" for="required_quantity_${rowIndex}" id="lblrequired_quantity_${rowIndex}"></label>
        </td>
        <td class="mb-0 font-13 fw-bold">
            <label for="available_stock_${rowIndex}" id="available_stock_${rowIndex}" style="width:30%">0</label>
            <input type="hidden" id="available_stock_${rowIndex}" name="order_items[${rowIndex}][available_stock]" value="0">
        </td>
        <td class="mb-0 font-13 fw-bold">
            <label for="unit_price_${rowIndex}" id="unit_price_${rowIndex}" style="width:30%">0</label>
            <span><?= $currencySymbol ?></span>
            <input type="hidden" id="h_unit_price_${rowIndex}" name="order_items[${rowIndex}][price]" value="0">
        </td>
        <td class="mb-0 font-13 fw-bold">
            <label for="total_price_${rowIndex}" id="total_price_${rowIndex}" style="width:30%">0</label>
            <span><?= $currencySymbol ?></span>
            <input type="hidden" id="h_total_price_${rowIndex}" name="order_items[${rowIndex}][total_price]" value="0">
            <input type="hidden" id="h_total_discount_price_${rowIndex}" name="h_total_discount_price_${rowIndex}" value="0">
        </td>
        <td>
            <div class="row_original_${rowIndex}">
                <a href="javascript:void(0);" id="add-product-btn_${rowIndex}" class="btn btn-secondary p-10 add-row" data-bs-toggle="tooltip" title="Add Product" style="font-size: 8px !important;border-radius: 8px;">
                    <i class="fas fa-plus" style="font-size: 8px !important; color: #fff !important;"></i> Add Product
                </a>
            </div>
            <div class="row_alt_${rowIndex} visually-hidden">
                <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Edit" class="edit-row" aria-label="Edit">
                    <i class="fas fa-pencil-alt"></i>
                </a>
                <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Delete" class="delete-row" aria-label="Delete">
                    <i class="far fa-trash-alt"></i>
                </a>
            </div>
        </td>
    </tr>
    `;

                $('#product-table tbody').append(newRow);
            }

            $('#productsdropdown_' + rowIndex).select2();
            $('#variantsdropdown_' + rowIndex).select2();
            $('#attributesdropdown_' + rowIndex).select2();
            rowIndex++;
            $('.order-table .select2').each(function() {
                $(this).next('.select2-container').find('.select2-selection--single').addClass('custom-input-select');
            });
        } else {
            quantityField.removeClass('is-invalid-order');
            productDropdown.next('.select2-container').find('.select2-selection--single').removeClass('is-invalid-order');
            if (!quantityField.val()) {
                quantityField.addClass('is-invalid-order');
            }
            if (!productDropdown.val()) {
                productDropdown.next('.select2-container').find('.select2-selection--single').addClass('is-invalid-order');
            }
        }
    }

    $(document).on('click', '.edit-row', function() {
        var currentRow = $(this).closest('tr');
        currentRow.find('[class*="row_original_"]').removeClass('visually-hidden');
        currentRow.find('[class*="row_original_"]').next('.select2-container').removeClass('visually-hidden');
        currentRow.find('[class*="row_alt_"]').addClass('visually-hidden');
        currentRow.find('#add-product-btn').html('<i class="fas fa-save" style="color: white;"></i> Update Product');
    });

    var table = $('#product-table').DataTable({
        paging: false,
        searching: false,
        info: false,
        ordering: false,
        lengthChange: false,
        bFilter: false
    });

    $(document).on('click', '.delete-row', function() {
        // Locate the row containing the clicked button
        var row = $(this).closest('tr');

        // Use DataTable's row removal method
        if (row.length) {
            var productDropdown = row.find('select[id^="productsdropdown_"]');
            var quantityField = row.find('input[id^="quantity_"]');
            var productId = row.find('select[id^="productsdropdown_"]').val();
            var productVarId = row.find('select[id^="variantsdropdown_"]').val();
            var attributeId = row.find('select[id^="attributesdropdown_"]').val();
            var current_quantity = quantityField.val() ?? 0;
            var pickupshowroom = $('#showroom-dropdown').val();
            var delivery_mode = $('input[name="delivery_mode"]:checked').val();
            if (delivery_mode !== 'delivery') {
                var showrooms = pickupshowroom ?? [];
            } else {
                var showrooms = [];
            }
            if (delivery_mode !== 'delivery') {
                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'ProductStocks', 'action' => 'clearStock']) ?>",
                    type: 'POST',
                    data: {
                        productId: productId,
                        productVarId: productVarId,
                        attributeId: attributeId,
                        showrooms: showrooms,
                        current_quantity: current_quantity
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'error') {
                            swal('Error', 'There is no stock, please check again', 'error');
                            return false;
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching stock details:', error);
                        swal('Error', 'Failed to fetch stock details. Please try again.', 'error');
                    }
                });
            }
            // Check if the row exists in DataTable
            var dtRow = table.row(row);
            row.remove();
        } else {
            console.error("Row element not found.");
        }

        // Additional actions if needed
        var showroomId = $('#showroom-dropdown').val();
        if (showroomId) {
            var delivery_mode = 'showroom';
            // Uncomment the line below to call the respective function
            // calculateShowroomDeliveryCharges(showroomId, delivery_mode);
        }
        calculateFields();
        // var selectedCityId = $('#city').val();
        calculateCityDeliveryCharges();
    });

    $('.add-order-radio-group input').on('change', function() {
        const $statusFields = $('#status-fields');

        // if (this.value === 'Card') {
        //     $statusFields.show();
        // } 

        if (this.value === 'Cash' || this.value === 'MTN MoMo' || this.value === 'Wave' || this.value === 'Cash on Delivery') {
            $statusFields.show();
        } else {
            $statusFields.hide();
        }
    });

    $('.delievry_mode_options input[type="radio"]').on('change', function() {
        const $deliveryFields = $('#delivery-div');
        const $showroomFields = $('#showroom-list');

        if (this.value === 'pickup') {
            $deliveryFields.addClass('visually-hidden');
            $showroomFields.removeClass('visually-hidden');
        } else if (this.value === 'delivery') {
            $showroomFields.addClass('visually-hidden');
            $deliveryFields.removeClass('visually-hidden');
        } else {
            $deliveryFields.addClass('visually-hidden');
            $showroomFields.addClass('visually-hidden');
        }
    });


    $('#customerEmail').on('focusout', function() {
        var email = $(this).val();

        if (email) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Customers', 'action' => 'checkEmailExists']) ?>",
                type: 'POST',
                data: {
                    email: email
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status == 'error') {
                        $('#customerEmail').addClass('is-invalid');
                        let feedback = $('#customerEmail').closest('.form-group').find('.invalid-feedback');
                        feedback.text('<?= __("Email already exists. Please try a different one.") ?>').show();
                        // $('#saveCustomerBtn').attr('disabled', true);
                    } else {
                        $('#customerEmail').removeClass('is-invalid');
                        let feedback = $('#customerEmail').closest('.form-group').find('.invalid-feedback');
                        feedback.text('');
                        feedback.hide();
                        //$('#saveCustomerBtn').attr('disabled', false);
                    }
                },
                error: function(xhr) {
                    swal("Error!", "An error occurred while checking the email. Please try again.", "error");
                }
            });
        }
    });

    $('#showroom-dropdown').on('change', function() {
        var selectedId = $(this).val();
        var delivery_mode = 'showroom';
        updateStock();
    });

    $('#redeem-points-btn').on('click', function() {
        var redeemPoints = $('#redeem-points-input').val();
        var loyalty_category = $('#loyalty-category').val();
        var loyalty_redeem_point_value = $('#loyalty-redeem-point-value ').val();
        var loyalty_redeem_points = $('#loyalty-redeem-points ').val();
        var loyalty_points = parseFloat($('#loyalty-points').text()) || 0;
        var loyalty_points_worth = 0;

        if (loyalty_points < redeemPoints) {
            swal("Error!", 'Redeem points should be less than ' + loyalty_points, "error");
            $('#redeem-points-input').val('');
        } else {
            var redeemable_sets = Math.floor(redeemPoints / loyalty_redeem_points);
            loyalty_points_worth = redeemable_sets * loyalty_redeem_point_value;
        }


        $('#loyalty_amount').val(Math.round(loyalty_points_worth));
        $('#loyalty_points_redeemed').val(redeemPoints);
        $('#loyality-point').text(formatCurrency(Math.round(loyalty_points_worth)));
        $('#redeemPointsModal').modal('hide');
        calculateTotalAmount();
    });

    $('#city').on('change', function() {
        calculateCityDeliveryCharges();
    });
    $('#delivery').on('change', function() {
        $('#showroom-dropdown').val('').trigger('change');
        updateStock();
        clearCoupons();
        calculateCityDeliveryCharges();
    });
    $('#pickup-showroom').on('change', function() {
        updateStock();
        clearCoupons();
        calculateCityDeliveryCharges();
    });
    $('#express').on('change', function() {
        calculateCityDeliveryCharges();
    });
    $('#standard').on('change', function() {
        calculateCityDeliveryCharges();
    });

    function calculateCityDeliveryCharges() {
        var selectedCityId = $('#city').val();
        var weightQuantityArray = [];
        var sizeQuantityArray = [];
        var delivery_mode = $('input[name="delivery_mode_type"]:checked').val();
        $('input[type="hidden"][id^="h_product_weight_"]').each(function() {
            var index = $(this).attr('id').split('h_product_weight_')[1];
            var weight = $(this).val();
            var quantity = $('input[id="quantity_' + index + '"]').val();
            if (quantity) {
                weightQuantityArray.push({
                    weight: weight,
                    quantity: quantity
                });
            }
        });
        $('input[type="hidden"][id^="h_product_size_"]').each(function() {
            var index = $(this).attr('id').split('h_product_size_')[1];
            var size = $(this).val();
            var quantity = $('input[id="quantity_' + index + '"]').val();
            if (quantity) {
                sizeQuantityArray.push({
                    size: size,
                    quantity: quantity
                });
            }
        });
        var isdelivery = $('#delivery').is(':checked'); // returns true or false
        var is_free_shipping = $('#is_free_shipping').val();
        $('#shipping-costs').text('0');
        $('#delivery_charge').val(0);



        if (selectedCityId && weightQuantityArray.length > 0 && isdelivery && is_free_shipping == 0) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'DeliveryCharges', 'action' => 'getDeliveryChargeFromCity']) ?>",
                type: 'POST',
                data: {
                    id: selectedCityId,
                    delivery_mode: delivery_mode,
                    weightQuantityArray: weightQuantityArray,
                    sizeQuantityArray: sizeQuantityArray
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#shipping-costs').text(formatCurrency(Math.round(response.total_delivery_charge)));
                        $('#delivery_charge').val(Math.round(response.total_delivery_charge));
                        calculateTotalAmount();
                    } else {
                        // swal('<?= __('Failed') ?>', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch delivery charges. Please try again.') ?>', 'error');
                }
            });
        } else {
            $('#shipping-costs').text(0);
            $('#delivery_charge').val(0);
            calculateTotalAmount();
        }
    }

    $('#changeAddr').on('click', function() {
        let customerId = $('#customer_id').val(); // Get customer ID from hidden field
        if (customerId) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Customers', 'action' => 'getCustomerAddresses']) ?>/" + customerId,
                type: 'GET',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success' && response.addresses.length > 0) {
                        let addressHtml = '';
                        response.addresses.forEach(function(address) {
                            addressHtml += `
                <div class="address-box" data-address='${JSON.stringify(address)}'>
                    <p><strong>${address.house_no}, ${address.address_line1}</strong></p>
                    <p>${address.address_line2 ? address.address_line2 + ', ' : ''}${address.landmark ? address.landmark + ', ' : ''}${address.municipality_name ? address.municipality_name + ', ' : ''}${address.city_name} - ${address.zipcode}</p>
                </div>`;
                        });

                        $('#addressList').html(addressHtml);
                    } else {
                        $('#addressList').html('<p>No addresses found.</p>');
                    }

                    $('#customerAddressModal').modal('show');
                },

                error: function() {
                    swal('Failed', 'Could not fetch addresses. Please try again.', 'error');
                }
            });
        }
    });

    $(document).on('click', '.address-box', function() {
        var addressData = $(this).data('address');
        let abidjanCityId = <?= json_encode($ABIDJAN_CITY_ID) ?>;
        $('#customer-address-id').val(addressData.id);
        $('#house-no').val(addressData.house_no);
        $('#delivery-address-line1').val(addressData.address_line1);
        $('#delivery-address-line2').val(addressData.address_line2);
        $('#delivery-zip-code').val(addressData.zipcode);
        $('#city').prop('disabled', false).val(addressData.city_id).trigger('change');
        $('#city').prop('disabled', true);
        $('#municipality').prop('disabled', false).val(addressData.municipality_id).trigger('change');
        $('#municipality').prop('disabled', true)
        if (addressData.city_id == abidjanCityId) {
            $('.municipality-row').show();
        } else {
            $('.municipality-row').hide();
        }
        $('#customerAddressModal').modal('hide');
    });



    $('#newAddressModal').on('shown.bs.modal', function() {
        setTimeout(() => {
            if ($.fn.select2) {
                $('#new-city').select2({
                    dropdownParent: $('#newAddressModal')
                });
            } else {
                console.error("Select2 is not loaded");
            }
        }, 200);

        setTimeout(() => {
            if ($.fn.select2) {
                $('#new-municipality').select2({
                    dropdownParent: $('#newAddressModal')
                });
            } else {
                console.error("Select2 is not loaded");
            }
        }, 200);

        // setTimeout(() => {
        //     if (typeof google !== "undefined" && typeof google.maps !== "undefined") {
        //         initMap();
        //         google.maps.event.trigger(map, "resize");
        //     } else {
        //         console.error("Google Maps API not loaded");
        //     }
        // }, 200);
    });


    $('#addNewAddress').on('click', function(e) {
        e.preventDefault();
        $('#customerAddressModal').modal('hide');
        $('#newAddressModal').modal('show');
    });

    $('#new-city').on('change', function() {
        let selectedCity = $(this).val();
        let abidjanCityId = <?= json_encode($ABIDJAN_CITY_ID) ?>;

        if (selectedCity == abidjanCityId) {
            $('.new-municipality-row').show();
        } else {
            $('.new-municipality-row').hide();
        }
    });

    $('#newAddressSubmit').click(function() {
        // e.preventDefault();
        let isValid = true;

        $('#newAddressForm').find('input[required], select[required], textarea[required]').each(function() {
            let value = $(this).val().trim();
            let isSelect2 = $(this).hasClass('select2');
            if (value === '') {
                $(this).addClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                isValid = false;
                if (isSelect2) {
                    $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                }
            } else {
                $(this).removeClass('is-invalid');
                let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                feedback.hide();
                if (isSelect2) {
                    $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                }
            }
        });
        if (isValid) {

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'CustomerAddresses', 'action' => 'addCustomerAddress']) ?>",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                data: $('#newAddressForm').serialize(),
                success: function(response) {
                    if (response.status === 'success') {
                        $('#newAddressModal').modal('hide').on('hidden.bs.modal', function() {
                            setTimeout(() => {
                                swal('Success', response.message, 'success');
                            }, 200);
                            $('#changeAddr').trigger('click');
                        });
                    } else {
                        swal('Error', response.message, 'error');
                    }
                }
            });
        }
    });

    function clearAddressFields() {
        $('#newAddressForm').find('input[type="text"], select, textarea').val('');
        $('#newAddressForm').find('input[type="checkbox"], input[type="radio"]').prop('checked', false);

    }

    $('#newAddressModal').on('hidden.bs.modal', function() {
        clearAddressFields();
    });

    function clearCoupons() {
        $('#offer-id').val('');
        $('#coupon-amount-hidden').text('0');
        $('#coupon-type').val('');
        $('#coupon-min-cart-value').val('');
        $('#coupon-cat-products').val('');
        $('#coupon-max-allowed').val('');
        $('#offer-amount').val('');
        $('#is_free_shipping').val('0');
        $('#coupon-amount').text('0');
        $('#offer_code').val('');
        calculateCityDeliveryCharges();

        calculateTotalAmount();
    }

    function updateStock() {
        var items = [];
        $('tr.order-row').each(function(index) {
            let rowIndex = index; // or extract from ID if dynamic
            let product_id = $(this).find('select[id^="productsdropdown_"]').val();
            let variant_id = $(this).find('select[id^="variantsdropdown_"]').val();
            let attribute_id = $(this).find('select[id^="attributesdropdown_"]').val();

            if (product_id) {
                items.push({
                    row_index: rowIndex,
                    product_id: product_id,
                    variant_id: variant_id || null,
                    attribute_id: attribute_id || null
                });
            }
        });

        let showroom_id = $('#showroom-dropdown').val();

        if (items.length > 0) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'ProductStocks', 'action' => 'FetchStock']) ?>",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                data: {
                    items: items,
                    showroom_id: showroom_id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $.each(response.data, function(i, item) {
                            $('#available_stock_' + item.row_index).text(item.available_stock);
                            $('#h_available_stock_' + item.row_index).val(item.available_stock);
                        });
                    } else {
                        swal('Error', response.message, 'error');
                    }
                },
                error: function() {
                    swal('Error', 'Failed to fetch stock information.', 'error');
                }
            });
        }
    }

    function formatCurrency(amount) {
        var decimalSeparator = "<?= addslashes($decimalSeparator) ?>";
        var thousandSeparator = "<?= addslashes($thousandSeparator) ?>";
        amount = Math.round(parseFloat(amount) || 0);

        var parts = amount.toString().split('.');

        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);

        return parts[0]; // No need to join decimals since it's rounded
    }
</script>

<?php $this->end(); ?>