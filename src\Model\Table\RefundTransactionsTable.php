<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;

class RefundTransactionsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('refund_transactions');
        $this->setPrimaryKey('id');
        $this->addBehavior('Timestamp');

        $this->belongsTo('OrderReturns', [
            'foreignKey' => 'order_return_id',
        ]);

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('order_return_id')
            ->notEmptyString('order_return_id');

        $validator
            ->integer('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->scalar('method')
            ->maxLength('method', 10)
            ->notEmptyString('method');

        $validator
            ->numeric('delivery_charge')
            ->allowEmptyString('delivery_charge');

        $validator
            ->numeric('product_refund')
            ->allowEmptyString('product_refund');

        $validator
            ->numeric('total_refund')
            ->notEmptyString('total_refund');

        $validator
            ->scalar('note')
            ->allowEmptyString('note');

        return $validator;
    }
}
