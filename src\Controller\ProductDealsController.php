<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;

/**
 * ProductDeals Controller
 *
 * @property \App\Model\Table\ProductDealsTable $ProductDeals
 */
class ProductDealsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    protected \App\Model\Table\ProductsTable $Products;
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Products = $this->fetchTable('Products');
    }
    
    public function index()
    {
        $query = $this->ProductDeals->find()
            ->contain(['Products'])->where(['ProductDeals.status !=' => 'D'])->applyOptions(['order' => ['ProductDeals.start_date' => 'DESC']]);
        $productDeals = $query->all();
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('productDeals', 'dateFormat', 'timeFormat', 'status', 'statusMap'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Deal id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $productDeal = $this->ProductDeals->get($id, contain: ['Products']);
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';
        $statuses = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $this->set(compact('productDeal','currencySymbol','decimalSeparator','thousandSeparator','dateFormat','statuses'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $productDeal = $this->ProductDeals->newEmptyEntity();
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status'=>'A'])->all()->toArray();
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        if (empty($products)) {
            $products = ['' => 'No products available'];
        }
        if ($this->request->is('post')) {
            $productDeal = $this->ProductDeals->patchEntity($productDeal, $this->request->getData());
            if ($this->ProductDeals->save($productDeal)) {
                $this->Flash->success(__('The product deal has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product deal could not be saved. Please, try again.'));
        }
        $this->set(compact('productDeal', 'products','currencySymbol'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Product Deal id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $productDeal = $this->ProductDeals->get($id, contain: []);
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status'=>'A'])->all()->toArray();
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        if (empty($products)) {
            $products = ['' => 'No products available'];
        }
        if ($this->request->is(['patch', 'post', 'put'])) {
            $productDeal = $this->ProductDeals->patchEntity($productDeal, $this->request->getData());
            if ($this->ProductDeals->save($productDeal)) {
                $this->Flash->success(__('The product deal has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product deal could not be saved. Please, try again.'));
        }
        // $products = $this->ProductDeals->Products->find('list', limit: 200)->all();
        $this->set(compact('productDeal','products', 'currencySymbol','status'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Product Deal id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $productDeal = $this->ProductDeals->get($id);
        $response = ['success' => false, 'message' => 'The product deal could not be deleted. Please, try again.'];
        if ($productDeal) {
            if ($this->ProductDeals->delete($productDeal)) {
                $response = ['success' => true, 'message' => 'The product deal has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The product deal could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The product deal does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
