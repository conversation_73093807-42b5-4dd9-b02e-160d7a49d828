<?php
// print_r($supportTicketsData['support_ticket_updates']); die;
?>
<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<?php $this->end(); ?>

<?php $this->start('add_script'); ?>
<script>
    // Function to open image in new page
    function openImageInNewPage(imageUrl) {
        window.open(imageUrl, '_blank');
    }

    // Scroll to bottom of message container on load
    document.addEventListener('DOMContentLoaded', function() {
        // Function to scroll to bottom of message container
        function scrollToBottom() {
            var messageContainer = document.getElementById('message-container');
            if (messageContainer) {
                // Force scroll to absolute bottom
                messageContainer.scrollTop = 999999;
                console.log('Scrolled to bottom. ScrollHeight: ' + messageContainer.scrollHeight);
            }
        }

        // Add a small visual indicator that scrolling is happening
        var scrollIndicator = document.createElement('div');
        scrollIndicator.className = 'scroll-indicator';
        scrollIndicator.innerHTML = 'Scrolling to latest messages...';
        document.body.appendChild(scrollIndicator);

        // Try scrolling immediately
        scrollToBottom();

        // Create a more aggressive approach to ensure scrolling works
        // Try multiple times with increasing delays
        [50, 100, 300, 500, 1000, 2000].forEach(function(delay) {
            setTimeout(scrollToBottom, delay);
        });

        // Remove the indicator after a delay
        setTimeout(function() {
            scrollIndicator.style.opacity = '0';
            setTimeout(function() {
                if (scrollIndicator.parentNode) {
                    document.body.removeChild(scrollIndicator);
                }
            }, 500);
        }, 2500);

        // Add click event for the scroll-to-bottom button
        var scrollButton = document.getElementById('scroll-to-bottom');
        if (scrollButton) {
            scrollButton.addEventListener('click', function(e) {
                e.preventDefault();
                scrollToBottom();

                // Show a brief indicator
                scrollIndicator.style.opacity = '1';
                document.body.appendChild(scrollIndicator);
                setTimeout(function() {
                    scrollIndicator.style.opacity = '0';
                    setTimeout(function() {
                        if (scrollIndicator.parentNode) {
                            document.body.removeChild(scrollIndicator);
                        }
                    }, 500);
                }, 500);
            });
        }

        // Show/hide scroll button based on scroll position
        var messageContainer = document.getElementById('message-container');
        if (messageContainer) {
            messageContainer.addEventListener('scroll', function() {
                var scrollButton = document.getElementById('scroll-to-bottom');
                if (scrollButton) {
                    // If we're near the bottom, hide the button
                    if (messageContainer.scrollHeight - messageContainer.scrollTop - messageContainer.clientHeight < 50) {
                        scrollButton.style.opacity = '0';
                    } else {
                        scrollButton.style.opacity = '1';
                    }
                }
            });
        }
    });
</script>
<?php $this->end(); ?>

<?php
// Get current user ID from session
$currentUserId = null;
if ($this->request->getSession()->check('Auth.User')) {
    $currentUserId = $this->request->getSession()->read('Auth.User.id');
}
?>

<style>
/* Support Desk View Page Specific Styles */
.support-ticket-img{
    height: 60px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.support-ticket-img:hover {
    transform: scale(1.1);
}

.right-show {
    text-align: right;
    float: right;
}

.btn-back-to-tickets {
    display: inline-block;
    margin-bottom: 20px;
    padding: 10px 20px;
    background-color: #0d839b;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.btn-back-to-tickets:hover {
    background-color: #e67e22;
}

/* Message layout styles */
.ticket-body {
    clear: both;
    margin-bottom: 25px !important;
    overflow: hidden;
    position: relative;
    width: 100%; /* Ensure full width in flex container */
    flex-shrink: 0; /* Prevent shrinking in flex container */
}

/* Current user message styles - left side */
.ticket-body.customer-message {
    float: left;
    max-width: 80%;
    margin-right: 20%;
}

.ticket-body.customer-message > div:nth-child(2) {
    background-color: #f1f1f1;
    padding: 15px;
    border-radius: 15px 15px 15px 0;
    margin-top: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    clear: both;
    position: relative;
}

/* Other users message styles - right side */
.ticket-body.admin-message {
    float: right;
    max-width: 80%;
    margin-left: 20%;
}

.ticket-body.admin-message > div:nth-child(2) {
    background-color: #e3f2fd;
    padding: 15px;
    border-radius: 15px 15px 0 15px;
    margin-top: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    clear: both;
    position: relative;
}

/* Additional styling for message containers */
.ticket-body > div:first-child {
    margin-bottom: 10px;
    overflow: hidden;
}

.ticket-body.customer-message .right-show {
    display: none; /* Hide timestamp on customer messages */
}

.ticket-body.admin-message .left-show {
    float: right; /* Move profile to right for admin messages */
}

.ticket-body.admin-message .left-show-box {
    text-align: right;
}

/* Image container styling */
.body-support-images {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Clear fix for floating elements */
.ticket-card:after {
    content: "";
    display: table;
    clear: both;
}

/* Current user indicator styling */
.current-user-indicator {
    font-size: 0.85em;
    color: #0d839b;
    font-style: italic;
}

/* Message container wrapper and container */
.message-container-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.message-container {
    height: 400px; /* Fixed height for the message container */
    max-height: 400px; /* Ensure max-height is also set */
    overflow-y: auto !important; /* Force vertical scrolling */
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    background-color: #fafafa;
    scroll-behavior: smooth; /* Smooth scrolling */
    display: flex;
    flex-direction: column; /* Stack messages vertically */
}

/* Scroll to bottom button */
.scroll-to-bottom-btn {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #0d839b;
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0; /* Initially hidden */
}

.scroll-to-bottom-btn:hover {
    background-color: #e67e22;
}

/* Styling for no messages text */
.no-messages {
    text-align: center;
    color: #888;
    padding: 20px;
    font-style: italic;
}

/* Scroll indicator styling */
.scroll-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: rgba(13, 131, 155, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 1000;
    transition: opacity 0.5s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

    .ticket-card {
        border: 1px solid #ddd;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.3s ease;
    }

    .ticket-card:hover {
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .ticket-header {
        display: flex;
        text-align: center;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
        background-color: #F9DAB999;
        color: black;
        font-weight: 600;
    }

    .ticket-header > div {
        margin-bottom: 8px;
        width: auto;
        flex: 1;
    }

    .ticket-header strong {
        font-weight: 600;
        color: #f88d1d;
        display: block;
    }

    .ticket-status-badge {
        display: inline-block;
        padding: 0.4em 0.6em;
        font-size: 0.8em;
        font-weight: 700;
        line-height: 1;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    .status-open {
        background-color: #28a745; /* Green */
    }

    .status-pending {
        background-color: #ffc107; /* Yellow */
        color: #000;
    }

    .status-closed {
        background-color: #dc3545; /* Red */
    }

    .ticket-body {
        margin-bottom: 20px;
        line-height: 1.6;
        color: #555;
    }

    .ticket-body strong {
        font-weight: 600;
        color: #333;
    }

    .ticket-images {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .ticket-images img {
        max-width: 120px;
        border: 1px solid #eee;
        padding: 5px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        transition: transform 0.2s ease-in-out;
    }

    .ticket-images img:hover {
        transform: scale(1.1);
    }
</style>
<div class="productCategoryListingC">
    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span class="productCategoryListing-home-span">Home</span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span Electronic-devices">My Account</span>
    </div>
</div>

<div class="my-a-my-o-c">
    <?php echo $this->element('web_sidebar'); ?>
    <div class="my-a-my-o-main" id="my-a-my-o-main">
        <div class="nameandaddress-container">

            <!-- Main content goes here -->
            <a href="<?= $this->Url->build(['controller' => 'WebsiteCustomerSupport', 'action' => 'supportDesk']) ?>" class="btn-back-to-tickets">
                Back To My Ticket
            </a>

            <?php if (!empty($supportTicketsData)) : ?>

                    <div class="ticket-card">
                    <div class="flash-messages">
                        <?= $this->Flash->render() ?>
                    </div>
                        <div class="ticket-header">
                            <div>
                                <strong>Ticket ID:</strong> <?= h($supportTicketsData['ticketID']) ?>
                            </div>
                            <div>
                                <strong>Date & Time:</strong> <?= $supportTicketsData['created'] ? $supportTicketsData['created']->format('Y-m-d H:i:s') : '' ?>
                            </div>
                            <div>
                                <strong>Ticket Owner:</strong> <?= $supportTicketsData->created_by_user->first_name .' '.$supportTicketsData->created_by_user->last_name ?? 'N/A' ?>
                            </div>
                            <div>
                                <strong>Query Type:</strong> <?= h($supportTicketsData['support_category']['name']) ?>
                            </div>
                            <div>
                                <strong>Ticket Status:</strong> <span class="ticket-status-badge status-<?= strtolower($supportTicketsData['status']) ?>"><?= h($supportTicketsData['status']) ?></span>
                            </div>
                        </div>

                        <!-- Message container with fixed height and scrolling -->
                        <div class="message-container-wrapper">
                            <div id="message-container" class="message-container">
                            <button id="scroll-to-bottom" class="scroll-to-bottom-btn" title="Scroll to bottom">
                                <i class="fa fa-arrow-down"></i>
                            </button>
                            <?php if (!empty($supportTicketsData['support_ticket_updates'])) : ?>
                                <?php foreach ($supportTicketsData['support_ticket_updates'] as $val) : ?>
                                <?php
                                    // Check if this message is from the current user
                                    $isCurrentUser = isset($val['updated_by_user']['id']) &&
                                                    $val['updated_by_user']['id'] == $currentUserId;

                                    // If we can't determine current user, fall back to role-based check
                                    if (!$currentUserId) {
                                        $isCurrentUser = isset($val['updated_by_user']['_matchingData']['Roles']['name']) &&
                                                        strtolower($val['updated_by_user']['_matchingData']['Roles']['name']) === 'customer';
                                    }

                                    // Add a debug comment to help troubleshoot
                                    $userInfo = '';
                                    if (isset($val['updated_by_user']['id'])) {
                                        $userInfo .= 'User ID: ' . $val['updated_by_user']['id'];
                                    }
                                    if (isset($val['updated_by_user']['_matchingData']['Roles']['name'])) {
                                        $userInfo .= ' | Role: ' . $val['updated_by_user']['_matchingData']['Roles']['name'];
                                    }

                                    $messageClass = $isCurrentUser ? 'customer-message' : 'admin-message';
                                ?>
                                <div class="ticket-body <?= $messageClass ?>">
                                    <div>
                                    <span class="left-show">

                                        <?php if (!empty($val['_matchingData']['Customers']['profile_photo'])) : ?>
                                            <img src="<?php echo $this->WishListView->getCloudFrontURLView($val['_matchingData']['Customers']['profile_photo']); ?>" style="border-radius: 50%; margin-right: 10px; height:40px;" alt="Ticket Image">
                                        <?php else: ?>
                                            <img src="<?= $this->Url->webroot('assets/profile-icon.png') ?>" style="border-radius: 50%; margin-right: 10px; height:40px;" alt="Ticket Image">
                                        <?php endif; ?>

                                        <span class="left-show-box">
                                            <strong><?= h($val['updated_by_user']['first_name']) ?></strong>
                                            <?php if ($isCurrentUser): ?>
                                                <span class="current-user-indicator" title="This is you">&nbsp;(You)</span>
                                            <?php endif; ?>
                                            <br />
                                            <!-- <strong><?= h($val['updated_by_user']['_matchingData']['Roles']['name']) ?></strong> -->
                                            <!-- Debug info: <?= $userInfo ?> -->
                                        </span>
                                    </span>


                                        <span class="right-show">
                                            <?= $val['updated_at'] ? $val['updated_at']->format('d M Y h i A') : '' ?>
                                        </span>
                                    </div>
                                    <div><?= h($val['comment']) ?></div>

                                    <div class="body-support-images">
                                        <?php if (!empty($val['support_ticket_images'])) : ?>
                                            <?php foreach ($val['support_ticket_images'] as $image) : ?>
                                                <?php $imageUrl = $this->WishListView->getCloudFrontURLView($image['image']); ?>
                                              <a href="<?= $imageUrl ?>" target="_blank">
                                                <img class="support-ticket-img"
                                                     src="<?= $imageUrl ?>"
                                                     alt="Ticket Image"
                                                     onclick="openImageInNewPage('<?= $imageUrl ?>');"
                                                 >
                                              </a>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="no-messages">No messages found for this ticket.</p>
                            <?php endif; ?>
                            </div><!-- End of message-container -->
                        </div><!-- End of message-container-wrapper -->

                        <div class="ticket-images">
                            <?php if (!empty($ticket['SupportTicketImages'])) : ?>
                                <?php foreach ($ticket['SupportTicketImages'] as $image) : ?>
                                    <?php $imageUrl = $this->WishListView->getCloudFrontURLView($image['image']); ?>
                                   <a href="<?= $imageUrl ?>" target="_blank">
                                    <img src="<?= $imageUrl ?>"
                                         alt="Ticket Image"
                                         style="cursor: pointer;">
                                   </a>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

            <?php else : ?>
                <p>No tickets found.</p>
            <?php endif; ?>

            <?php if ($supportTicketsData['status'] !== 'Closed') : ?>
            <div class="ticket-reply a1">

            <?= $this->Form->create(null, [
                    'type' => 'file',
                    'url' => [
                        'controller' => 'WebsiteCustomerSupport',
                        'action' => 'reply'
                    ]
                ]) ?>
                <?= $this->Form->hidden('id', ['value' => $supportTicketsData['id']]) ?>
                <div class="form-group">
                    <?= $this->Form->control('description', ['type' => 'textarea', 'required' => true, 'class' => 'form-control', 'placeholder' => __("Reply"), 'label' => false]) ?>
                </div>
                <div class="form-group" style="display: inline-grid; justify-content: space-between; align-items: center;">
                <input type="file" name="images[]" multiple class="form-control" id="brand_logo" placeholder="Brand Logo" accept="image/jpg,image/jpeg,image/png,image/svg" aria-label="Brand Logo">

                    <?= $this->Form->button('Send Reply', ['class' => 'btn btn-primary', 'style' => 'flex: 0 0 auto;']) ?>
                </div>
            <?= $this->Form->end() ?>
            </div>
            <?php endif; ?>

<style>
.ticket-reply {
margin-top: 20px;
padding: 20px;
border: 1px solid #ddd;
border-radius: 8px;
box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ticket-reply h3 {
margin-bottom: 15px;
font-size: 1.5em;
color: #333;
}

.ticket-reply .form-group {
margin-bottom: 15px;
}

.ticket-reply .form-control {
width: 100%;
padding: 10px;
border: 1px solid #ccc;
border-radius: 4px;
}

.ticket-reply .btn-primary {
background-color: #0d839b;
border-color: #0d839b;
padding: 10px 20px;
border-radius: 5px;
transition: background-color 0.3s;
}

.ticket-reply .btn-primary:hover {
background-color: #e67e22;
border-color: #e67e22;
}

.attachment-label {
cursor: pointer;
font-size: 1.5em;
color: #0d839b;
margin-right: 10px;
}

.attachment-label:hover {
color: #e67e22;
}
</style>


        </div>
    </div>
</div>

<!-- Direct script to ensure scrolling works -->
<script>
    // Immediate execution
    (function() {
        function scrollMessageContainer() {
            var container = document.getElementById('message-container');
            if (container) {
                container.scrollTop = 999999;
                console.log('Direct scroll executed. ScrollTop set to maximum.');
            }
        }

        // Try immediately
        scrollMessageContainer();

        // And with a slight delay
        setTimeout(scrollMessageContainer, 100);
        setTimeout(scrollMessageContainer, 500);
        setTimeout(scrollMessageContainer, 1000);
        setTimeout(scrollMessageContainer, 2000);

        // Also try on window load (after all resources like images are loaded)
        window.onload = function() {
            console.log('Window loaded - executing final scroll');
            scrollMessageContainer();
            setTimeout(scrollMessageContainer, 100);

            // Set up a MutationObserver to detect when content changes
            var container = document.getElementById('message-container');
            if (container && window.MutationObserver) {
                var observer = new MutationObserver(function(mutations) {
                    console.log('Content changed - scrolling to bottom');
                    scrollMessageContainer();
                });

                observer.observe(container, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    characterData: true
                });
            }
        };
    })();
</script>
