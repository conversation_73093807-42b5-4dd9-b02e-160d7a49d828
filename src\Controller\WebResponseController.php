<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Controller\Controller;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Core\Configure;
use Cake\Core\Configure\Engine\PhpConfig;
use Cake\Core\Exception\Exception;
use Cake\I18n\Time;
//use Cake\Http\Response;
//use Cake\Http\Exception\BadRequestException;
//use Cake\Http\Exception\NotFoundException;
use Cake\View\JsonView;
use Cake\Utility\Security;
use Cake\Routing\Router;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;

class WebResponseController extends Controller
{
    protected $Orders;
    protected $Users;

    public function initialize(): void
    {
        parent::initialize();
//        $this->Authentication->addUnauthenticatedActions(['success','error','wavePaymentSuccess', 'wavePaymentError']);
//        $this->Transactions = $this->fetchTable('Transactions');
        $this->Orders = $this->fetchTable('Orders');
        $this->Users = $this->fetchTable('Users');
//        $this->PaymentMethodSettings = $this->fetchTable('PaymentMethodSettings');
      //  $this->loadComponent('Wave');
        $this->viewBuilder()->setLayout('web');
        $this->loadComponent('Flash');
    }

    public function success()
    {
        $orderId = $this->request->getQuery('order_id');
        $identity = $this->request->getSession()->read('Auth.User');
        $customerId = $identity ? $identity->id : null;

        if (!$customerId) {
            $this->Flash->websiteError(__('Please login to access this page.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        if (!$orderId) {
            $this->Flash->websiteError(__('Invalid order ID. Please check your cart.'));
            return $this->redirect(['controller' => 'Account', 'action' => 'cart']);
        }

        // Get user and customer information
        $user = $this->Users->find()
            ->contain([
                'Customers' => function ($q) {
                    return $q->select(['id', 'user_id']); // Select necessary Customer fields
                }
            ])
            ->select(['Users.id', 'Users.first_name', 'Users.last_name', 'Users.email'])
            ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
            ->first();

        if (!$user || !$user->customer) {
            $this->Flash->websiteError(__('Customer information not found.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        $customer_id = $user->customer->id;

        // Get order information with all related data
        $orderInfo = $this->Orders->find()
            ->where([
                'Orders.id' => $orderId,
                'Orders.customer_id' => $customer_id,
                'Orders.created >=' => FrozenTime::now()->subMinutes(5),
                'Orders.created <=' => FrozenTime::now()
            ])
            ->contain([
                'Customers' => ['Users'],
                'OrderItems' => [
                    'Products' => [
                        'ProductCategories' => [
                            'Categories'
                        ],
                        'Brands'
                    ],
                    'ProductVariants',
                    'ProductAttributes',
                    // 'OrderItemsCoupons' => [
                    //     'Coupons' => [
                    //         'CouponTypes',
                    //         'CouponCategories',
                    //         'CouponProducts'
                    //     ]
                    // ]
                ]
            ])
            ->first();

        if (!$orderInfo) {
             $this->Flash->websiteError(__('Order not found or access denied. Please check your cart.'));
            return $this->redirect(['controller' => 'Account', 'action' => 'cart']);
        }

        // Additional validation to ensure order has essential data
        if (empty($orderInfo->id) || empty($orderInfo->customer_id)) {
            $this->Flash->websiteError(__('Order data is incomplete. Please check your cart.'));

            return $this->redirect(['controller' => 'Account', 'action' => 'cart']);
        }

        // Prepare data for Google Analytics 4 purchase tracking
        $affiliation = ''; // Your store name
        $currency = 'FCFA'; // West African CFA franc

        // Calculate totals
        $total = $orderInfo->total_amount ?? 0;
        $tax = $orderInfo->tax_amount ?? 0;
        $shipping = $orderInfo->delivery_charge ?? 0;

        // Get coupon code if any
        $couponCode = null;
        if (!empty($orderInfo->order_items)) {
            foreach ($orderInfo->order_items as $orderItem) {
                if (!empty($orderItem->order_items_coupons)) {
                    foreach ($orderItem->order_items_coupons as $couponItem) {
                        if (!empty($couponItem->coupon->code)) {
                            $couponCode = $couponItem->coupon->code;
                            break 2; // Break out of both loops
                        }
                    }
                }
            }
        }

        // Prepare order items for GA4
        $orderItems = [];
        if (empty($orderInfo->order_items)) {
            $this->Flash->websiteError(__('Order data is incomplete. Please check your cart.'));
            return $this->redirect(['controller' => 'Account', 'action' => 'cart']);
        }

        if (!empty($orderInfo->order_items)) {
            foreach ($orderInfo->order_items as $orderItem) {
                $itemCoupon = null;

                // Get item-specific coupon if any
                if (!empty($orderItem->order_items_coupons)) {
                    foreach ($orderItem->order_items_coupons as $couponItem) {
                        if (!empty($couponItem->coupon->code)) {
                            $itemCoupon = $couponItem->coupon->code;
                            break;
                        }
                    }
                }

                // Get product details
                $product = $orderItem->product ?? null;
                $variant = $orderItem->product_variant ?? null;
                $attribute = $orderItem->product_attribute ?? null;

                // Determine item name - prioritize variant title, then product name
                $itemName = $product->name ?? 'Unknown Product';
                if ($variant && !empty($variant->variant_name)) {
                    $itemName = $variant->variant_name;
                }

                // Determine variant info based on what's available
                $itemVariant = '';
                $variantParts = [];

                // Add variant information if available
                if ($variant && !empty($variant->variant_name)) {
                    $variantParts[] = $variant->variant_name;
                }

                // Add attribute information if available
                if ($attribute && !empty($attribute->name)) {
                    $variantParts[] = $attribute->name;
                }

                // Combine variant parts
                $itemVariant = implode(' - ', $variantParts);

                // Get category and brand information
                $itemCategory = 'General';
                $itemBrand = 'BabikenCake';

                if ($product) {
                    // Try to get category from product
                    if (isset($product->category) && !empty($product->category->name)) {
                        $itemCategory = $product->category->name;
                    } elseif (isset($product->categories) && !empty($product->categories)) {
                        // If categories is an array, get the first one
                        $firstCategory = is_array($product->categories) ? $product->categories[0] : $product->categories;
                        if (isset($firstCategory->name)) {
                            $itemCategory = $firstCategory->name;
                        }
                    }

                    // Try to get brand from product
                    if (isset($product->brand) && !empty($product->brand->name)) {
                        $itemBrand = $product->brand->name;
                    } elseif (isset($product->brands) && !empty($product->brands)) {
                        // If brands is an array, get the first one
                        $firstBrand = is_array($product->brands) ? $product->brands[0] : $product->brands;
                        if (isset($firstBrand->name)) {
                            $itemBrand = $firstBrand->name;
                        }
                    }
                }

                $orderItems[] = [
                    'id' => $product->id ?? $orderItem->id,
                    'name' => $itemName,
                    'price' => $orderItem->price ?? 0,
                    'category' => $itemCategory,
                    'brand' => $itemBrand,
                    'variant' => $itemVariant,
                    'quantity' => $orderItem->quantity ?? 1,
                    'coupon' => $itemCoupon
                ];
            }
        }

        $this->set(compact(
            'orderId',
            'orderInfo',
            'affiliation',
            'total',
            'currency',
            'tax',
            'shipping',
            'couponCode',
            'orderItems'
        ));
        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('success');
    }
    public function error()
    {
        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('error');
    }


}
