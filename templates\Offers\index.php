<?php

/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Offer> $offers
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #customer-group-filter,
    #status-filter,
    #redeem-mode-filter {
        width: 200px;
    }
</style>
<?php $this->end(); ?>
<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                    <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
                </a>
            </li>
            <li class="breadcrumb-item active">
                <?= __('Coupons And Discounts') ?>
            </li>
        </ul>
    </div>
    <div class="section-body1">
        <div class="container-fluid">
            <?= $this->Flash->render() ?>
        </div>
    </div>

    <div class="section-body" id="list">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4><?= __('Manage Coupons And Discounts') ?></h4>
                    <div class="card-header-form d-flex align-items-center">
                        <form class="d-flex align-items-center m-l-10">
                            <div class="input-group me-2">
                                <input type="text" class="form-control search-control" placeholder="<?= __('Search') ?>"
                                    id="customSearchBox" />
                                <div class="input-group-btn">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <?php if ($canAdd): ?>
                                    <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'add']) ?>"
                                        class="btn btn-primary me-2" style="
                                                        position: relative;
                                                        left: 10px;
                                                        height:10%
                                                    ">
                                        <i class="fas fa-plus"></i>
                                        <?= __('Add Coupon') ?>
                                    </a>
                                <?php endif; ?>
                                <button class="btn menu-toggle fw-bold" type="submit" style="
                                                        position: relative;
                                                        left: 26px;
                                                        height:10%;
                                                    ">
                                    <i class="fas fa-filter"></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div id="filter-body-container">
                    <div class="input-group m-l-25">
                        <form accept-charset="utf-8" class="form-inline filter-rating attribute" id="filter-search">
                            <div class="d-flex">
                                <div class="form-group d-flex align-items-center">

                                    <?php echo $this->Form->control('group_name', [
                                        'type' => 'select',
                                        'options' => $customerGroup,
                                        'id' => 'customer-group-filter',
                                        'class' => 'form-control form-select p-10',
                                        'label' => false,
                                        'empty' => __('Customer Group'),
                                        'data' => ['bs-toggle' => 'dropdown'],
                                        'aria-expanded' => 'false'
                                    ]) ?>

                                </div>

                                <div class="form-group d-flex align-items-center m-l-20">
                                    <?php echo $this->Form->control('status', [
                                        'type' => 'select',
                                        'options' => $status,
                                        'id' => 'status-filter',
                                        'class' => 'form-control form-select p-10',
                                        'label' => false,
                                        'empty' => __('Coupon Status'),
                                        'data' => ['bs-toggle' => 'dropdown'],
                                        'aria-expanded' => 'false'
                                    ]) ?>
                                </div>
                                <div class="form-group d-flex align-items-center m-l-20">
                                    <?php echo $this->Form->control('redeem_mode', [
                                        'type' => 'select',
                                        'options' => $redeemmodes,
                                        'id' => 'redeem-mode-filter',
                                        'class' => 'form-control form-select p-10',
                                        'label' => false,
                                        'empty' => __('Redeem Method'),
                                        'data' => ['bs-toggle' => 'dropdown'],
                                        'aria-expanded' => 'false'
                                    ]) ?>

                                </div>
                                <div class="form-group ms-4" style="margin-top:0.5%">

                                    <button type="submit" id="btnFilter" class="btn btn-primary">
                                        <i class="fa fa-filter" aria-hidden="true"></i>
                                    </button>
                                    <button type="reset" id="btnReset" class="btn btn-primary">
                                        <i class="fas fa-redo-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                        <hr />
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="tblOffers" role="grid">
                            <thead>
                                <tr role="row">
                                    <th><?= __('Sl no') ?></th>
                                    <th><?= __('Offer Name') ?></th>
                                    <th><?= __('Redeem Method / Showroom(s)') ?></th>
                                    <th><?= __('Start Date') ?></th>
                                    <th><?= __('End Date') ?></th>
                                    <th><?= __('Coupon Code') ?></th>
                                    <th><?= __('Is Active') ?></th>
                                    <th style="display: none;"><?= __('Customer Groups') ?></th>
                                    <th><?= __('Action') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                foreach ($offers as $list): ?>
                                    <tr>
                                        <td><?= h($list->id ?? '') ?></td>
                                        <td><?= h($list->offer_name ?? '') ?></td>
                                        <td>
                                            <?= h($list->redeem_mode ?? '') ?>
                                            <?php if (in_array($list->redeem_mode, ['Store', 'Both'])): ?>
                                                <?php
                                                $selectedShowrooms = [];
                                                foreach ($list->offer_showrooms as $offerShowroom) {
                                                    $selectedShowrooms[] = $offerShowroom->showroom->name;
                                                }
                                                $selectedShowrooms = !empty($selectedShowrooms) ? implode(', ', $selectedShowrooms) : 'All';
                                                ?>
                                                (<?= h($selectedShowrooms) ?>)
                                            <?php endif; ?>
                                        </td>


                                        <td><?= h($list->offer_start_date ? $list->offer_start_date->format($dateFormat) : '') ?>
                                        </td>
                                        <td><?= h($list->offer_end_date ? $list->offer_end_date->format($dateFormat) : '') ?>
                                        </td>
                                        <td><?= h($list->offer_code ?? '') ?></td>

                                        <td>
                                            <?php
                                            $statusval = $statusMap[$list->status] ?? ['label' => 'Unknown', 'class' => 'label-danger'];
                                            ?>
                                            <div class="badge-outline <?= h($statusval['class'] ?? '') ?>">
                                                <?= h($statusval['label'] ?? '') ?>
                                            </div>
                                        </td>

                                        <td style="display: none;"><?php
                                            $selectedCustomerGroups = [];
                                            foreach ($list->offer_customer_groups as $offerCustomerGroup) {
                                                $selectedCustomerGroups[] = $offerCustomerGroup->customer_group->name;
                                            }
                                            $selectedCustomerGroups = !empty($selectedCustomerGroups) ? implode(', ', $selectedCustomerGroups) : '';
                                            ?>
                                            <?= h($selectedCustomerGroups) ?></td>

                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'view', $list->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __('View') ?>"><i
                                                        class="far fa-eye m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canEdit): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'edit', $list->id]) ?>"
                                                    class="" data-toggle="tooltip" title="<?= __('Edit') ?>"><i
                                                        class="fas fa-pencil-alt m-r-10"></i></a>
                                            <?php endif; ?>
                                            <?php if ($canDelete): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'delete', $list->id]) ?>"
                                                    class="delete-btn" data-bs-toggle="tooltip" title="Delete" data-id="<?= $list->id ?>" data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                    <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>">
</script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js'); ?>"></script>

<script>
    $(document).ready(function() {
        var paginationCount = <?= json_encode($paginationCount) ?>;
        var table = $("#tblOffers").DataTable({
            columnDefs: [{
                    orderable: false,
                    targets: [-1]
                }, // Make the last column non-sortable
            ],
            order: [],
            dom: "rtip", // Remove the default search box
            pageLength: paginationCount,
            language: {
                infoFiltered: ""
            }
        });

        //table.column(6).search('Active', true, false, false).draw();

        $("#customSearchBox").on("keyup", function() {
            table.search(this.value).draw();
        });

        $("#btnReset").on("click", function() {
            table.search('').columns().search('').draw();
            //table.column(6).search('Active', true, false, false).draw();
        });

        $("#btnFilter").on("click", function() {
            event.preventDefault();
            var customerGroup = $("#customer-group-filter").val();
            var status = $("#status-filter").val();
            var redeemMode = $("#redeem-mode-filter").val();

            var customerGroupMap = <?= json_encode($customerGroup) ?>;
            var statusMap = <?= json_encode($status) ?>;
            var redeemmodesMap = <?= json_encode($redeemmodes) ?>;


            var customerGroupVal = customerGroupMap[customerGroup] !== undefined ? customerGroupMap[
                customerGroup] : '';
            var statusVal = statusMap[status] !== undefined ? statusMap[status] : '';
            var redeemModeVal = redeemmodesMap[redeemMode] !== undefined ? redeemmodesMap[redeemMode] : '';

            if (statusVal == '') {
                table.search('').columns().search('').draw();
                //table.column(6).search('Active', true, false, false).draw();
            } else {
                table.column(6).search(statusVal, true, false, false).draw();
            }
            if (customerGroupVal !== '') {
                table.column(7).search(customerGroupVal).draw();
            }
            table.column(2).search(redeemModeVal).draw();
        });
    });
</script>
<?php $this->end(); ?>