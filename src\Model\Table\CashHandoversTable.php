<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CashHandovers Model
 *
 * @property \App\Model\Table\DriversTable&\Cake\ORM\Association\BelongsTo $Drivers
 * @property \App\Model\Table\ShowroomsTable&\Cake\ORM\Association\BelongsTo $Showrooms
 *
 * @method \App\Model\Entity\CashHandover newEmptyEntity()
 * @method \App\Model\Entity\CashHandover newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\CashHandover> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CashHandover get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\CashHandover findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\CashHandover patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\CashHandover> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\CashHandover|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\CashHandover saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\CashHandover>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashHandover>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CashHandover>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashHandover> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CashHandover>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashHandover>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CashHandover>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashHandover> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class CashHandoversTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('cash_handovers');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Drivers', [
            'foreignKey' => 'driver_id',
            'joinType' => 'LEFT'
        ]);
        $this->belongsTo('Showrooms', [
            'foreignKey' => 'showroom_id',
        ]);

        $this->belongsTo('Managers', [
            'className' => 'Users',
            'foreignKey' => 'showroom_manager',
            'propertyName' => 'manager',
            'joinType' => 'LEFT'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('driver_id')
            ->allowEmptyString('driver_id');

        $validator
            ->nonNegativeInteger('showroom_manager')
            ->allowEmptyString('showroom_manager');

        $validator
            ->nonNegativeInteger('handed_to_supervisor')
            ->requirePresence('handed_to_supervisor', 'create')
            ->notEmptyString('handed_to_supervisor');

        $validator
            ->nonNegativeInteger('showroom_id')
            ->allowEmptyString('showroom_id');

        $validator
            ->decimal('amount')
            ->requirePresence('amount', 'create')
            ->notEmptyString('amount');

        $validator
            ->dateTime('handover_date')
            ->requirePresence('handover_date', 'create')
            ->notEmptyDateTime('handover_date');

        $validator
            ->scalar('remarks')
            ->allowEmptyString('remarks');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['driver_id'], 'Drivers'), ['errorField' => 'driver_id']);
        $rules->add($rules->existsIn(['showroom_id'], 'Showrooms'), ['errorField' => 'showroom_id']);

        return $rules;
    }

    //S
    public function cashHandoverToday($driverId) {

        $current_date = date('Y-m-d');
        $totalAmount = $this->find()
        ->select([
            'total' => $this->find()->func()->sum('ROUND(CashHandovers.amount, 2)') // Round to 2 decimals
        ])
        ->where([
            'CashHandovers.driver_id' => $driverId,
            'DATE(CashHandovers.handover_date)' => $current_date
        ])
        ->first();
        return $totalAmount;
    }

    //S
    public function cashHandoverDetail($supervisorId, $showroom_id) {
         
        $today_date = date('Y-m-d');
        return $this->find()
            ->select(['CashHandovers.id', 'CashHandovers.driver_id', 'CashHandovers.amount', 'CashHandovers.handover_date', 'Managers.id', 'Managers.first_name', 'Managers.last_name', 'Drivers.id', 'Drivers.user_id'])
            ->contain([
                'Managers',
                'Drivers' => [
                    'Users' => function ($q) {
                        return $q->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                    }
                ],
                'Showrooms'
            ])
            ->where([
                'CashHandovers.handed_to_supervisor' => $supervisorId, 
                'DATE(CashHandovers.handover_date)' => $today_date,
                'OR' => [
                    'CashHandovers.showroom_id IS NULL',                      // For drivers
                    'CashHandovers.showroom_id' => $showroom_id              // For managers
                ]
            ])
            ->order(['CashHandovers.id' => 'DESC'])
            ->all();
    }
}
