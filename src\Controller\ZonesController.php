<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ZonesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Showrooms;
    protected $ZoneShowrooms;
    protected $Users;
    protected $Municipalities;
    protected $ZoneMunicipalities;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->ZoneShowrooms = $this->fetchTable('ZoneShowrooms');
        $this->Users = $this->fetchTable('Users');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->ZoneMunicipalities = $this->fetchTable('ZoneMunicipalities');
    }
    
    public function index()
    {
        $zones = $this->Zones->find()
            ->where(['Zones.status IN' => ['A', 'I']])
            ->contain([
                'Showrooms' => function ($q) {
                    return $q->select(['Showrooms.id', 'Showrooms.name']);
                },
                'Municipalities' => function ($q) {
                    return $q->select(['Municipalities.id', 'Municipalities.name']);
                }
            ])
            ->order(['Zones.name' => 'ASC'])->toArray();

        $this->set(compact('zones'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $zone = $this->Zones->get($id, contain: [
            'Showrooms'
        ]);
        $this->set(compact('zone'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $zone = $this->Zones->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $zone = $this->Zones->patchEntity($zone, $data);

            if ($this->Zones->save($zone)) {
                $this->saveShowroomMapping($zone);
                $this->saveMunicipalitiesMapping($zone);
                $this->Flash->success(__('The zone has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The zone could not be saved. Please, try again.'));
        }

        $showrooms = $this->Showrooms->getNotAssignedToZoneShowrooms();
        // $supervisors = $this->Users->fetchSupervisor();
        $municipalities = $this->Municipalities->fetchMunicipalities();   
        
        $this->set(compact('zone', 'showrooms', 'municipalities'));
    }

    protected function saveShowroomMapping($zone)
    {

        if (!empty($zone->showroom_id) && is_array($zone->showroom_id)) {

            $this->ZoneShowrooms->deleteAll(['zone_id' => $zone->id]);

            foreach ($zone->showroom_id as $showroomId) {
                $mapping = $this->ZoneShowrooms->find()
                    ->where(['zone_id' => $zone->id, 'showroom_id' => $showroomId])
                    ->first();

                if ($mapping) {
                    $mapping->showroom_id = $showroomId;
                } else {
                    $mapping = $this->ZoneShowrooms->newEntity([
                        'zone_id' => $zone->id,
                        'showroom_id' => $showroomId
                    ]);
                }

                if (!$this->ZoneShowrooms->save($mapping)) {
                    return false;
                }
            }
        }

        return true;
    }

    protected function saveMunicipalitiesMapping($zone)
    {

        if (!empty($zone->municipality_id) && is_array($zone->municipality_id)) {

            $this->ZoneMunicipalities->deleteAll(['zone_id' => $zone->id]);

            foreach ($zone->municipality_id as $municipalityId) {
                $mapping = $this->ZoneMunicipalities->find()
                    ->where(['zone_id' => $zone->id, 'municipality_id' => $municipalityId])
                    ->first();

                if ($mapping) {
                    $mapping->municipality_id = $municipalityId;
                } else {
                    $mapping = $this->ZoneMunicipalities->newEntity([
                        'zone_id' => $zone->id,
                        'municipality_id' => $municipalityId
                    ]);
                }

                if (!$this->ZoneMunicipalities->save($mapping)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Edit method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $zone = $this->Zones->get($id, contain: ['Showrooms','Municipalities']);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $zone = $this->Zones->patchEntity($zone, $this->request->getData());
            if ($this->Zones->save($zone)) {
                $this->saveShowroomMapping($zone);
                $this->saveMunicipalitiesMapping($zone);
                $this->Flash->success(__('The zone has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The zone could not be saved. Please, try again.'));
        }

        $selectedShowrooms = [];
        if (!empty($zone->showrooms)) {
            foreach ($zone->showrooms as $showroom) {
                $selectedShowrooms[] = $showroom->id;
            }
        }

        $selectedMunicipalities = [];
        if (!empty($zone->municipalities)) {
            foreach ($zone->municipalities as $municipality) {
                $selectedMunicipalities[] = $municipality->id;
            }
        }

        // Get unmapped showrooms, allowing the current zone's mappings
        $showrooms = $this->Showrooms->getUnmappedShowrooms($id);
        $municipalities = $this->Municipalities->fetchMunicipalities();
        // $showrooms = $this->Showrooms->getShowrooms();

        // $supervisors = $this->Users->fetchSupervisor();

        $this->set(compact('zone', 'showrooms' , 'selectedShowrooms', 'municipalities', 'selectedMunicipalities'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The zone could not be deleted. Please, try again.'];

        try {
            $record = $this->Zones->get($id);
            $record->status = 'D';

            if ($this->Zones->save($record)) {
                $response = ['success' => true, 'message' => 'The zone has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
