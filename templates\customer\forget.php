<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/otpVerify.css') ?>">


  <style>
    .ax-field-wrapper {
      width: 100%;
      max-width: 425px;
      margin-top: 1%;
    }

    .ax-input-box {
      display: flex;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
    }

    .ax-country-select {
      border: none;
      padding: 10px 0px 10px 10px;
      font-size: 12px;
      outline: none;
      appearance: none;
      flex-shrink: 0;
      width: auto;
    }

    .ax-phone-input {
      flex: 1;
      padding: 10px 10px 10px 0px;
      font-size: 13px;
      border: none;
      outline: none;
    }

    .ax-width-measure {
      visibility: hidden;
      white-space: nowrap;
      position: absolute;
      top: -9999px;
      left: -9999px;
      font-size: 14px;
      font-family: Arial, sans-serif;
      padding: 10px 8px;
    }

    .ax-button {
      margin-top: 15px;
      padding: 10px 15px;
      font-size: 16px;
      border-radius: 4px;
      border: 1px solid #999;
      cursor: pointer;
    }
  </style>
<style>
    .select2-container--default .select2-selection--single {

    font-size: 13px !important;
}
    .mobile-input {
        flex: 1;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    label.error {
        display: block;
        text-align: left;
        color: red;
        font-size: 14px;
        font-weight: 500;
    }
    #emailInput-error{
        padding-left: 0px;
    }
    .sign-up-links-input-mail::placeholder {
        padding-left: 0% !important;
    }
    .account-body {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        /* height: 100vh; */
        height: 100%;
    }
    /* .account-body #account-body-subcontainer {
        padding-right: 130px;
        height: 100%;
    } */

    .account-body-subcontainer {
        /* display: flex;
        flex-direction: row;
        max-width: 900px;
        width: 100%; */
        padding: 20px;
    }

    .camera-img-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* .camera-img {
        max-width: 100%;
        height: auto;
    } */

    .form-container {
        flex: 2;
        padding-left: 20px;
    }
    div#email-container {
        padding-left: 0;
    }
    .toggle-input {
        margin-bottom: 15px;
        padding-left: 20px;
    }

    .input-field {
        width: 405px;
        padding: 10px;
        margin-top: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    .submit-button {
        width: 428px;
        padding: 10px;
        background-color: #0D839B;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .submit-button:hover {
        background-color: #075f6d;
    }

    .forgot-password-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .forgot-password-instruction {
        font-size: 16px;
        margin-bottom: 20px;
    }

    .country-code-dropdown {
        width: 30%;
        padding: 10px;
        margin-right: 10px;
    }

    .country-code-dropdown,
    .mobile-input {
        display: inline-block;
        width: 60%;
    }
</style>
<style>
    select {
        color: black;
        background-color: transparent;
    }

</style>
<style>
    /* label.error {
        display: block;
        text-align: left;
        color: red;
    } */
    label#email-error{
        margin-left: 20px;
    }
</style>

<?php $this->end(); ?>

<accountbody class="account-body">
    <div class="account-body-subcontainer" id="account-body-subcontainer">
        <!-- Left Image Section -->
        <div class="camera-img-container">
            <img src="<?= isset($forgetPageImage['web_image']) && !empty($forgetPageImage['web_image'])
                ? $forgetPageImage['web_image']
                : $this->Url->webroot('assets/camera.png'); ?>"
                 class="camera-img">
        </div>

        <!-- Form Section -->
        <div class="form-container">
            <h2 class="forgot-password-title"><?= __("Forgot Password") ?></h2>
            <p class="forgot-password-instruction"><?= __("Please enter your mobile number or email to reset your password.") ?></p>
            <?= $this->Form->create(null, ['id' => 'forgotPasswordForm', 'novalidate' => true]) ?>

            <?= $this->Flash->render() ?>
            <?php
                // Check URL parameter 'type' to determine which radio button should be checked
                $resetType = $this->request->getQuery('type') ?? 'mobile';
            ?>
            <div class="input-selection">
                <label>
                    <input type="radio" name="reset_type" value="mobile" id="mobile-option" <?= $resetType !== 'email' ? 'checked' : '' ?>> <?= __("Mobile Number") ?>
                </label>
                <label>
                    <input type="radio" name="reset_type" value="email" id="email-option" <?= $resetType === 'email' ? 'checked' : '' ?>> <?= __("Email") ?>
                </label>
            </div>

            <div id="mobile-container" class="toggle-input">
                <div class="ax-field-wrapper">
                    <div class="ax-input-box mobile-input-wrapper">
                        <select id="ax-country-select" name="phone_code" class="ax-country-select searchable-select">
                           <option value="1">+1 <small>(USA, Canada)</small></option>
                            <option value="7">+7 <small>(Russia, Kazakhstan)</small></option>
                            <option value="20">+20 <small>(Egypt)</small></option>
                            <option value="27">+27 <small>(South Africa)</small></option>
                            <option value="30">+30 <small>(Greece)</small></option>
                            <option value="31">+31 <small>(Netherlands)</small></option>
                            <option value="32">+32 <small>(Belgium)</small></option>
                            <option value="33">+33 <small>(France)</small></option>
                            <option value="34">+34 <small>(Spain)</small></option>
                            <option value="36">+36 <small>(Hungary)</small></option>
                            <option value="39">+39 <small>(Italy)</small></option>
                            <option value="40">+40 <small>(Romania)</small></option>
                            <option value="41">+41 <small>(Switzerland)</small></option>
                            <option value="43">+43 <small>(Austria)</small></option>
                            <option value="44">+44 <small>(United Kingdom)</small></option>
                            <option value="45">+45 <small>(Denmark)</small></option>
                            <option value="46">+46 <small>(Sweden)</small></option>
                            <option value="47">+47 <small>(Norway)</small></option>
                            <option value="48">+48 <small>(Poland)</small></option>
                            <option value="49">+49 <small>(Germany)</small></option>
                            <option value="51">+51 <small>(Peru)</small></option>
                            <option value="52">+52 <small>(Mexico)</small></option>
                            <option value="53">+53 <small>(Cuba)</small></option>
                            <option value="54">+54 <small>(Argentina)</small></option>
                            <option value="55">+55 <small>(Brazil)</small></option>
                            <option value="56">+56 <small>(Chile)</small></option>
                            <option value="57">+57 <small>(Colombia)</small></option>
                            <option value="58">+58 <small>(Venezuela)</small></option>
                            <option value="60">+60 <small>(Malaysia)</small></option>
                            <option value="61">+61 <small>(Australia)</small></option>
                            <option value="62">+62 <small>(Indonesia)</small></option>
                            <option value="63">+63 <small>(Philippines)</small></option>
                            <option value="64">+64 <small>(New Zealand)</small></option>
                            <option value="65">+65 <small>(Singapore)</small></option>
                            <option value="66">+66 <small>(Thailand)</small></option>
                            <option value="81">+81 <small>(Japan)</small></option>
                            <option value="82">+82 <small>(South Korea)</small></option>
                            <option value="84">+84 <small>(Vietnam)</small></option>
                            <option value="86">+86 <small>(China)</small></option>
                            <option value="90">+90 <small>(Turkey)</small></option>
                            <option value="91">+91 <small>(India)</small></option>
                            <option value="92">+92 <small>(Pakistan)</small></option>
                            <option value="93">+93 <small>(Afghanistan)</small></option>
                            <option value="94">+94 <small>(Sri Lanka)</small></option>
                            <option value="95">+95 <small>(Myanmar)</small></option>
                            <option value="98">+98 <small>(Iran)</small></option>
                            <option value="211">+211 <small>(South Sudan)</small></option>
                            <option value="212">+212 <small>(Morocco)</small></option>
                            <option value="213">+213 <small>(Algeria)</small></option>
                            <option value="216">+216 <small>(Tunisia)</small></option>
                            <option value="218">+218 <small>(Libya)</small></option>
                            <option value="220">+220 <small>(Gambia)</small></option>
                            <option value="221">+221 <small>(Senegal)</small></option>
                            <option value="222">+222 <small>(Mauritania)</small></option>
                            <option value="223">+223 <small>(Mali)</small></option>
                            <option value="224">+224 <small>(Guinea)</small></option>
                            <option value="225" selected="selected">+225 <small>(Ivory Coast)</small></option>
                            <option value="226">+226 <small>(Burkina Faso)</small></option>
                            <option value="227">+227 <small>(Niger)</small></option>
                            <option value="228">+228 <small>(Togo)</small></option>
                            <option value="229">+229 <small>(Benin)</small></option>
                            <option value="230">+230 <small>(Mauritius)</small></option>
                            <option value="231">+231 <small>(Liberia)</small></option>
                            <option value="232">+232 <small>(Sierra Leone)</small></option>
                            <option value="233">+233 <small>(Ghana)</small></option>
                            <option value="234">+234 <small>(Nigeria)</small></option>
                            <option value="235">+235 <small>(Chad)</small></option>
                            <option value="236">+236 <small>(Central African Republic)</small></option>
                            <option value="237">+237 <small>(Cameroon)</small></option>
                            <option value="238">+238 <small>(Cape Verde)</small></option>
                            <option value="239">+239 <small>(Sao Tome and Principe)</small></option>
                            <option value="240">+240 <small>(Equatorial Guinea)</small></option>
                            <option value="241">+241 <small>(Gabon)</small></option>
                            <option value="242">+242 <small>(Congo - Brazzaville)</small></option>
                            <option value="243">+243 <small>(Congo - Kinshasa)</small></option>
                            <option value="244">+244 <small>(Angola)</small></option>
                            <option value="245">+245 <small>(Guinea-Bissau)</small></option>
                            <option value="246">+246 <small>(British Indian Ocean Territory)</small></option>
                            <option value="248">+248 <small>(Seychelles)</small></option>
                            <option value="249">+249 <small>(Sudan)</small></option>
                            <option value="250">+250 <small>(Rwanda)</small></option>
                            <option value="251">+251 <small>(Ethiopia)</small></option>
                            <option value="252">+252 <small>(Somalia)</small></option>
                            <option value="253">+253 <small>(Djibouti)</small></option>
                            <option value="254">+254 <small>(Kenya)</small></option>
                            <option value="256">+256 <small>(Uganda)</small></option>
                            <option value="257">+257 <small>(Burundi)</small></option>
                            <option value="258">+258 <small>(Mozambique)</small></option>
                            <option value="260">+260 <small>(Zambia)</small></option>
                            <option value="261">+261 <small>(Madagascar)</small></option>
                            <option value="262">+262 <small>(Réunion, Mayotte)</small></option>
                            <option value="263">+263 <small>(Zimbabwe)</small></option>
                            <option value="264">+264 <small>(Namibia)</small></option>
                            <option value="265">+265 <small>(Malawi)</small></option>
                            <option value="266">+266 <small>(Lesotho)</small></option>
                            <option value="267">+267 <small>(Botswana)</small></option>
                            <option value="268">+268 <small>(Eswatini)</small></option>
                            <option value="269">+269 <small>(Comoros)</small></option>
                            <option value="290">+290 <small>(Saint Helena)</small></option>
                            <option value="291">+291 <small>(Eritrea)</small></option>
                            <option value="297">+297 <small>(Aruba)</small></option>
                            <option value="298">+298 <small>(Faroe Islands)</small></option>
                            <option value="299">+299 <small>(Greenland)</small></option>

                        </select>
        <input type="tel" name="mobile_no" id="ax-mobile-input" class="ax-phone-input" placeholder="Enter mobile number" required>
        <span id="ax-width-measure" class="ax-width-measure"></span>
      </div>
    </div>
    <label id="ax-mobile-input-error" class="error" for="ax-mobile-input" style="display: none;"></label>

            </div>
            <div id="email-container" class="toggle-input" style="display: none;">
                <input type="email" name="email" id="email" placeholder="<?= __("Enter your email") ?>" class="input-field">
            </div>
            <button type="submit" class="submit-button"><?= __("Submit") ?></button>
            <?= $this->Form->end() ?>
        </div>
    </div>
</accountbody>
<?php $this->start('add_js'); ?>
<script>
    $(document).ready(function () {
        const emailContainer = $('#email-container');
        const mobileContainer = $('#mobile-container');

        // Set initial state based on selected radio button
        function updateInputContainers() {
            if ($('#email-option').is(':checked')) {
                emailContainer.show();
                mobileContainer.hide();
            } else {
                emailContainer.hide();
                mobileContainer.show();
            }
        }

        // Initialize containers based on the selected radio button
        updateInputContainers();

        // Toggle input fields based on selected radio button
        $('input[name="reset_type"]').change(function () {
            updateInputContainers();
        });

        // jQuery Validation for Forgot Password Form
        $('#forgotPasswordForm').validate({
            rules: {
                email: {
                    required: function () {
                        return $('#email-option').is(':checked');
                    },
                    email: true
                },
                mobile_no: {
                    required: function () {
                        return $('#mobile-option').is(':checked');
                    },
                    digits: true,
                    minlength: 10,
                    maxlength: 15
                }
            },
            messages: {
                email: {
                    required: "Please enter your email.",
                    email: "Please enter a valid email address."
                },
                mobile_no: {
                    required: "Please enter your mobile number.",
                    digits: "Mobile number must be numeric.",
                    minlength: "Mobile number must be at least 10 digits.",
                    maxlength: "Mobile number must not exceed 15 digits."
                }
            },
            submitHandler: function (form) {
                form.submit();
            }
        });
    });
</script>

  <script>
    const axCountrySelect = document.getElementById('ax-country-select');
    const axWidthMeasure = document.getElementById('ax-width-measure');

    function axAdjustSelectWidth() {
      const selectedText = axCountrySelect.options[axCountrySelect.selectedIndex].text;
      axWidthMeasure.textContent = selectedText;
      const newWidth = axWidthMeasure.offsetWidth;
      axCountrySelect.style.width = `${newWidth + 0}px`; // 20px for padding/buffer
    }

    // Initial width adjustment
    axAdjustSelectWidth();

    // Update on change
    axCountrySelect.addEventListener('change', axAdjustSelectWidth);
  </script>
<?php $this->end(); ?>
